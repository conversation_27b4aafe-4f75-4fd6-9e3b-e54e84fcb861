/**
 * Device fingerprinting utility for the browser extension
 * This helps identify unique devices to prevent abuse of the free tier
 */

class DeviceFingerprint {
  /**
   * Generate a device fingerprint based on browser properties
   * @returns {Promise<string>} A hash representing the device fingerprint
   */
  static async generate() {
    try {
      // Collect various browser and device properties
      const components = await this.collectComponents();
      
      // Create a hash of these components
      return this.hashComponents(components);
    } catch (error) {
      console.error('Error generating device fingerprint:', error);
      // Return a fallback fingerprint based on user agent only
      return this.hashComponents([navigator.userAgent]);
    }
  }

  /**
   * Collect various components for fingerprinting
   * @returns {Promise<Array>} Array of components
   */
  static async collectComponents() {
    // Basic browser information
    const components = [
      navigator.userAgent,
      navigator.language,
      navigator.languages?.join(','),
      navigator.platform,
      navigator.hardwareConcurrency?.toString(),
      navigator.deviceMemory?.toString(),
      navigator.maxTouchPoints?.toString(),
      navigator.pdfViewerEnabled?.toString(),
      navigator.cookieEnabled?.toString(),
      navigator.doNotTrack || navigator.msDoNotTrack || window.doNotTrack,
      screen.colorDepth?.toString(),
      screen.pixelDepth?.toString(),
      screen.width + 'x' + screen.height,
      screen.availWidth + 'x' + screen.availHeight,
      window.devicePixelRatio?.toString(),
      new Date().getTimezoneOffset().toString()
    ];

    // Add canvas fingerprint if available
    try {
      const canvasFingerprint = await this.getCanvasFingerprint();
      if (canvasFingerprint) {
        components.push(canvasFingerprint);
      }
    } catch (e) {
      console.warn('Canvas fingerprinting not available:', e);
    }

    // Add WebGL fingerprint if available
    try {
      const webglFingerprint = this.getWebGLFingerprint();
      if (webglFingerprint) {
        components.push(webglFingerprint);
      }
    } catch (e) {
      console.warn('WebGL fingerprinting not available:', e);
    }

    // Add audio fingerprint if available
    try {
      const audioFingerprint = await this.getAudioFingerprint();
      if (audioFingerprint) {
        components.push(audioFingerprint);
      }
    } catch (e) {
      console.warn('Audio fingerprinting not available:', e);
    }

    // Add font fingerprint
    try {
      const fontFingerprint = this.getFontFingerprint();
      if (fontFingerprint) {
        components.push(fontFingerprint);
      }
    } catch (e) {
      console.warn('Font fingerprinting not available:', e);
    }

    return components.filter(Boolean); // Remove any undefined/null values
  }

  /**
   * Create a hash from the components
   * @param {Array} components - Array of components to hash
   * @returns {string} Hashed fingerprint
   */
  static hashComponents(components) {
    const fingerprint = components.join('###');
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      hash = ((hash << 5) - hash) + fingerprint.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }
    
    // Convert to hex string and ensure it's positive
    return Math.abs(hash).toString(16);
  }

  /**
   * Get canvas fingerprint
   * @returns {Promise<string>} Canvas fingerprint
   */
  static async getCanvasFingerprint() {
    return new Promise((resolve) => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          resolve('');
          return;
        }
        
        // Set canvas size
        canvas.width = 200;
        canvas.height = 50;
        
        // Draw text with different styles
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillStyle = '#f60';
        ctx.fillRect(10, 10, 100, 30);
        ctx.fillStyle = '#069';
        ctx.fillText('BrowzyAI Fingerprint', 2, 15);
        ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
        ctx.fillText('BrowzyAI Fingerprint', 4, 17);
        
        // Get data URL and extract base64 part
        const dataURL = canvas.toDataURL();
        const base64 = dataURL.split(',')[1];
        
        // We don't need the full image data, just a hash of it
        let hash = 0;
        for (let i = 0; i < base64.length; i += 5) { // Sample every 5th character
          hash = ((hash << 5) - hash) + base64.charCodeAt(i);
          hash |= 0;
        }
        
        resolve(hash.toString(16));
      } catch (e) {
        console.warn('Error creating canvas fingerprint:', e);
        resolve('');
      }
    });
  }

  /**
   * Get WebGL fingerprint
   * @returns {string} WebGL fingerprint
   */
  static getWebGLFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        return '';
      }
      
      const info = [
        gl.getParameter(gl.VENDOR),
        gl.getParameter(gl.RENDERER),
        gl.getParameter(gl.VERSION),
        gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
      ].join('|');
      
      // Get supported extensions
      const extensions = gl.getSupportedExtensions() || [];
      const extensionsString = extensions.join('|');
      
      return this.hashComponents([info, extensionsString]);
    } catch (e) {
      console.warn('Error creating WebGL fingerprint:', e);
      return '';
    }
  }

  /**
   * Get audio fingerprint
   * @returns {Promise<string>} Audio fingerprint
   */
  static async getAudioFingerprint() {
    return new Promise((resolve) => {
      try {
        if (!window.AudioContext && !window.webkitAudioContext) {
          resolve('');
          return;
        }
        
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const analyser = audioContext.createAnalyser();
        const gain = audioContext.createGain();
        
        // Configure audio nodes
        oscillator.type = 'triangle';
        oscillator.frequency.value = 440;
        gain.gain.value = 0; // Mute the sound
        
        // Connect nodes
        oscillator.connect(analyser);
        analyser.connect(gain);
        gain.connect(audioContext.destination);
        
        // Start oscillator
        oscillator.start(0);
        
        // Get frequency data
        analyser.fftSize = 256;
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        analyser.getByteFrequencyData(dataArray);
        
        // Stop oscillator
        oscillator.stop(0);
        audioContext.close();
        
        // Convert frequency data to string
        const frequencyData = Array.from(dataArray).slice(0, 20).join(',');
        resolve(this.hashComponents([frequencyData]));
      } catch (e) {
        console.warn('Error creating audio fingerprint:', e);
        resolve('');
      }
    });
  }

  /**
   * Get font fingerprint
   * @returns {string} Font fingerprint
   */
  static getFontFingerprint() {
    try {
      const fontList = [
        'Arial', 'Arial Black', 'Arial Narrow', 'Calibri', 'Cambria', 'Cambria Math',
        'Comic Sans MS', 'Consolas', 'Courier', 'Courier New', 'Georgia', 'Helvetica',
        'Impact', 'Lucida Console', 'Lucida Sans Unicode', 'Microsoft Sans Serif',
        'Palatino Linotype', 'Segoe UI', 'Tahoma', 'Times', 'Times New Roman',
        'Trebuchet MS', 'Verdana', 'Wingdings'
      ];
      
      // Create a span element to test fonts
      const span = document.createElement('span');
      span.innerHTML = 'mmmmmmmmmmlli';
      span.style.fontSize = '72px';
      document.body.appendChild(span);
      
      // Check which fonts are available
      const availableFonts = fontList.filter(font => {
        try {
          span.style.fontFamily = `'${font}', monospace`;
          return document.fonts.check(`12px '${font}'`);
        } catch (e) {
          return false;
        }
      });
      
      // Remove the span
      document.body.removeChild(span);
      
      return this.hashComponents([availableFonts.join('|')]);
    } catch (e) {
      console.warn('Error creating font fingerprint:', e);
      return '';
    }
  }
}

// Export the class
window.DeviceFingerprint = DeviceFingerprint;
