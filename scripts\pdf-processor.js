'use strict';

/**
 * Advanced PDF Processor class for enhanced PDF handling, summarization, and question answering
 */
class PDFProcessor {
  /**
   * Initialize the PDF Processor
   * @param {APIManager} apiManager - The API manager instance
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(apiManager, uiManager) {
    this.apiManager = apiManager;
    this.uiManager = uiManager;
    this.currentPDFContent = null;
    this.currentPDFMetadata = null;
    this.annotations = new Map(); // Map of page number to annotations
  }

  /**
   * Load PDF content from the current tab or a provided URL
   * @param {string} [pdfUrl] - Optional URL of the PDF to load
   * @returns {Promise<Object>} - The loaded PDF content and metadata
   */
  async loadPDFContent(pdfUrl = null) {
    try {
      // If PDF content is already loaded, return it
      if (this.currentPDFContent && !pdfUrl) {
        return {
          content: this.currentPDFContent,
          metadata: this.currentPDFMetadata
        };
      }

      // If a URL is provided, try to load the PDF from that URL
      if (pdfUrl) {
        const extractedContent = await this.apiManager.extractPDFContent(pdfUrl);
        if (extractedContent) {
          this.currentPDFContent = extractedContent;
          this.currentPDFMetadata = {
            title: pdfUrl.split('/').pop() || 'PDF Document',
            url: pdfUrl,
            isPDF: true
          };
          return {
            content: this.currentPDFContent,
            metadata: this.currentPDFMetadata
          };
        }
      }

      // Try to get PDF content from the current tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // Check if the current tab is a PDF
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') || 
                   tab.url.toLowerCase().includes('pdf') || 
                   tab.url.toLowerCase().includes('viewer.html');
      
      if (!isPdf) {
        throw new Error('The current tab does not appear to be a PDF document.');
      }

      // Try to inject the PDF content script if not already loaded
      try {
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content/pdf-content.js']
        });

        // Wait a moment for the script to initialize
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (injectionError) {
        console.log('PDF content script might already be loaded:', injectionError);
      }

      // Try to get PDF text from the active tab
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'getFullPDFContent'
      }).catch(e => {
        console.log('Could not get PDF content directly:', e);
        return null;
      });

      if (response && response.success && response.content) {
        this.currentPDFContent = response.content;
        this.currentPDFMetadata = response.metadata || {
          title: tab.title || 'PDF Document',
          url: tab.url,
          isPDF: true
        };
        return {
          content: this.currentPDFContent,
          metadata: this.currentPDFMetadata
        };
      }

      // If we couldn't get the content through the content script, try a more aggressive approach
      const result = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          try {
            // Try multiple methods to get all text
            if (window.PDFViewerApplication) {
              try {
                const pdfViewer = window.PDFViewerApplication.pdfViewer;
                const pageCount = pdfViewer.pagesCount || 0;
                let allText = [];
                let structuredContent = [];

                // Extract text from each page
                for (let i = 1; i <= pageCount; i++) {
                  try {
                    const pageIndex = i - 1;
                    const page = pdfViewer.getPageView(pageIndex);
                    if (page && page.textLayer && page.textLayer.textContent) {
                      allText.push(page.textLayer.textContent);
                      
                      // Try to extract structured content
                      structuredContent.push({
                        pageNumber: i,
                        content: page.textLayer.textContent,
                        // Try to identify headings and sections based on font size and position
                        elements: page.textLayer.textDivs ? 
                          Array.from(page.textLayer.textDivs).map(div => ({
                            text: div.textContent,
                            fontSize: parseFloat(window.getComputedStyle(div).fontSize),
                            isBold: window.getComputedStyle(div).fontWeight >= 600,
                            position: {
                              top: div.style.top,
                              left: div.style.left
                            }
                          })) : []
                      });
                    }
                  } catch (pageError) {
                    console.log(`Error extracting text from page ${i}:`, pageError);
                  }
                }

                // Get PDF metadata
                const metadata = {
                  title: window.PDFViewerApplication.documentInfo?.Title || document.title,
                  author: window.PDFViewerApplication.documentInfo?.Author || 'Unknown',
                  numPages: pageCount,
                  currentPage: window.PDFViewerApplication.page || 1,
                  url: window.location.href,
                  isPDF: true
                };

                if (allText.length > 0) {
                  return {
                    fullText: allText.join('\n\n'),
                    structuredContent: structuredContent,
                    metadata: metadata
                  };
                }
              } catch (pdfJsError) {
                console.log('Could not extract text using PDF.js:', pdfJsError);
              }
            }

            // Fallback to basic extraction
            return {
              fullText: document.body.innerText || '',
              metadata: {
                title: document.title,
                url: window.location.href,
                isPDF: true
              }
            };
          } catch (error) {
            console.error('Error in PDF extraction:', error);
            return null;
          }
        }
      });

      if (result && result[0] && result[0].result) {
        const extractionResult = result[0].result;
        if (extractionResult) {
          this.currentPDFContent = extractionResult.fullText;
          this.currentPDFMetadata = extractionResult.metadata;
          
          // Store structured content if available
          if (extractionResult.structuredContent) {
            this.structuredContent = extractionResult.structuredContent;
          }
          
          return {
            content: this.currentPDFContent,
            metadata: this.currentPDFMetadata,
            structuredContent: this.structuredContent
          };
        }
      }

      throw new Error('Could not extract content from the PDF document.');
    } catch (error) {
      console.error('Error loading PDF content:', error);
      throw new Error(`Failed to load PDF content: ${error.message}`);
    }
  }

  /**
   * Generate a summary of the PDF content
   * @param {string} [summaryType='executive'] - Type of summary to generate (executive, detailed, chapter)
   * @param {number} [maxLength=1500] - Maximum length of the summary in characters
   * @returns {Promise<string>} - The generated summary
   */
  async summarizePDF(summaryType = 'executive', maxLength = 1500) {
    try {
      // Load PDF content if not already loaded
      const { content, metadata } = await this.loadPDFContent();
      
      if (!content || content.trim().length === 0) {
        throw new Error('No PDF content available to summarize.');
      }

      // Prepare the prompt based on summary type
      let prompt = '';
      let systemPrompt = '';
      
      switch (summaryType.toLowerCase()) {
        case 'executive':
          systemPrompt = `You are an expert at creating concise executive summaries of documents. 
          Focus on the key points, main arguments, and conclusions. 
          Keep your summary under ${maxLength} characters.`;
          
          prompt = `Please create an executive summary of this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Create a concise executive summary that captures the main points, key findings, and conclusions.`;
          break;
          
        case 'detailed':
          systemPrompt = `You are an expert at creating comprehensive document summaries. 
          Include all important sections, key arguments, evidence, and conclusions. 
          Organize your summary with clear headings. 
          Keep your summary under ${maxLength} characters.`;
          
          prompt = `Please create a detailed summary of this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Create a comprehensive summary that covers all major sections and important details.
          Use headings to organize the summary by topic or section.`;
          break;
          
        case 'chapter':
          systemPrompt = `You are an expert at creating chapter-by-chapter or section-by-section summaries of documents. 
          Identify each major section and summarize it separately. 
          Keep your summary under ${maxLength} characters.`;
          
          prompt = `Please create a chapter-by-chapter summary of this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Identify the major chapters or sections in this document and create a summary for each one.
          Format your response with clear headings for each chapter/section.`;
          break;
          
        default:
          systemPrompt = `You are an expert at creating concise document summaries. 
          Focus on the key points and main arguments. 
          Keep your summary under ${maxLength} characters.`;
          
          prompt = `Please summarize this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Create a clear and concise summary that captures the essential information.`;
      }

      // Get the summary from the AI
      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: systemPrompt
      });

      return response.text;
    } catch (error) {
      console.error('Error summarizing PDF:', error);
      throw new Error(`Failed to summarize PDF: ${error.message}`);
    }
  }

  /**
   * Answer a question about the PDF content
   * @param {string} question - The question to answer
   * @returns {Promise<string>} - The answer to the question
   */
  async answerPDFQuestion(question) {
    try {
      if (!question || question.trim().length === 0) {
        throw new Error('Please provide a question to answer.');
      }

      // Load PDF content if not already loaded
      const { content, metadata } = await this.loadPDFContent();
      
      if (!content || content.trim().length === 0) {
        throw new Error('No PDF content available to analyze.');
      }

      // Prepare the prompt
      const prompt = `Please answer this question about the PDF document:
      
      Title: ${metadata.title || 'PDF Document'}
      
      Question: ${question}
      
      PDF Content:
      ${this.truncateContent(content, 15000)}
      
      Provide a clear, accurate, and comprehensive answer to the question based only on the information in the PDF.
      If the answer is not found in the PDF content, please state that clearly.`;

      // Get the answer from the AI
      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at analyzing documents and answering questions about their content.
        Provide accurate, comprehensive answers based solely on the information in the document.
        If the document doesn't contain the answer, clearly state that the information is not available in the document.`
      });

      return response.text;
    } catch (error) {
      console.error('Error answering PDF question:', error);
      throw new Error(`Failed to answer question: ${error.message}`);
    }
  }

  /**
   * Extract key information from the PDF
   * @param {string} [infoType='all'] - Type of information to extract (all, topics, entities, dates, etc.)
   * @returns {Promise<string>} - The extracted information
   */
  async extractPDFInfo(infoType = 'all') {
    try {
      // Load PDF content if not already loaded
      const { content, metadata } = await this.loadPDFContent();
      
      if (!content || content.trim().length === 0) {
        throw new Error('No PDF content available to analyze.');
      }

      // Prepare the prompt based on info type
      let prompt = '';
      let systemPrompt = '';
      
      switch (infoType.toLowerCase()) {
        case 'topics':
          systemPrompt = `You are an expert at identifying the main topics and themes in documents.`;
          prompt = `Please identify the main topics and themes in this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          List and briefly explain the main topics and themes covered in this document.`;
          break;
          
        case 'entities':
          systemPrompt = `You are an expert at extracting named entities (people, organizations, locations, etc.) from documents.`;
          prompt = `Please extract all important named entities from this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Extract and categorize all important named entities (people, organizations, locations, products, etc.) mentioned in this document.
          Format your response as a categorized list.`;
          break;
          
        case 'dates':
          systemPrompt = `You are an expert at identifying dates, timelines, and chronological information in documents.`;
          prompt = `Please extract all dates and timeline information from this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Extract all dates, time periods, and chronological information mentioned in this document.
          Organize them into a timeline if possible.`;
          break;
          
        case 'statistics':
          systemPrompt = `You are an expert at identifying numerical data, statistics, and quantitative information in documents.`;
          prompt = `Please extract all statistics and numerical data from this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Extract all statistics, numerical data, percentages, and quantitative information mentioned in this document.
          Organize them by topic or category.`;
          break;
          
        case 'citations':
          systemPrompt = `You are an expert at identifying citations, references, and sources in academic documents.`;
          prompt = `Please extract all citations and references from this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Extract all citations, references, and sources mentioned in this document.
          Format them in a proper bibliographic style.`;
          break;
          
        case 'all':
        default:
          systemPrompt = `You are an expert at extracting key information from documents.`;
          prompt = `Please extract key information from this PDF document:
          
          Title: ${metadata.title || 'PDF Document'}
          
          Content:
          ${this.truncateContent(content, 15000)}
          
          Extract and organize the following information:
          1. Main topics and themes
          2. Key people, organizations, and locations mentioned
          3. Important dates and timeline information
          4. Significant statistics and numerical data
          5. Key findings, conclusions, or recommendations
          
          Format your response with clear headings for each category.`;
      }

      // Get the extracted information from the AI
      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: systemPrompt
      });

      return response.text;
    } catch (error) {
      console.error('Error extracting PDF information:', error);
      throw new Error(`Failed to extract information: ${error.message}`);
    }
  }

  /**
   * Create a table of contents for the PDF
   * @returns {Promise<string>} - The generated table of contents
   */
  async createPDFTableOfContents() {
    try {
      // Load PDF content if not already loaded
      const { content, metadata } = await this.loadPDFContent();
      
      if (!content || content.trim().length === 0) {
        throw new Error('No PDF content available to analyze.');
      }

      // Prepare the prompt
      const prompt = `Please create a detailed table of contents for this PDF document:
      
      Title: ${metadata.title || 'PDF Document'}
      
      Content:
      ${this.truncateContent(content, 15000)}
      
      Identify all major sections, chapters, and subsections in this document.
      Create a hierarchical table of contents that reflects the document's structure.
      For each entry, include a brief description of what that section covers.`;

      // Get the table of contents from the AI
      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at analyzing document structure and creating detailed tables of contents.
        Identify all major sections, chapters, and subsections, and organize them hierarchically.
        Include brief descriptions of each section's content.`
      });

      return response.text;
    } catch (error) {
      console.error('Error creating PDF table of contents:', error);
      throw new Error(`Failed to create table of contents: ${error.message}`);
    }
  }

  /**
   * Compare two PDF documents
   * @param {string} secondPdfUrl - URL of the second PDF to compare
   * @returns {Promise<string>} - The comparison results
   */
  async comparePDFs(secondPdfUrl) {
    try {
      if (!secondPdfUrl) {
        throw new Error('Please provide the URL of a second PDF to compare.');
      }

      // Load the first PDF content if not already loaded
      const { content: firstContent, metadata: firstMetadata } = await this.loadPDFContent();
      
      if (!firstContent || firstContent.trim().length === 0) {
        throw new Error('No content available for the first PDF.');
      }

      // Load the second PDF content
      const secondContent = await this.apiManager.extractPDFContent(secondPdfUrl);
      
      if (!secondContent || secondContent.trim().length === 0) {
        throw new Error('Could not extract content from the second PDF.');
      }

      // Get the title of the second PDF
      const secondTitle = secondPdfUrl.split('/').pop() || 'Second PDF Document';

      // Prepare the prompt
      const prompt = `Please compare these two PDF documents:
      
      First Document: ${firstMetadata.title || 'First PDF Document'}
      Second Document: ${secondTitle}
      
      First Document Content:
      ${this.truncateContent(firstContent, 7500)}
      
      Second Document Content:
      ${this.truncateContent(secondContent, 7500)}
      
      Compare these documents and identify:
      1. Key similarities in content, structure, and arguments
      2. Major differences in content, approach, and conclusions
      3. Unique information present in each document
      4. Overall assessment of how these documents relate to each other
      
      Format your response with clear headings for each category.`;

      // Get the comparison from the AI
      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at comparing documents and identifying similarities and differences.
        Provide a comprehensive comparison that highlights key similarities, major differences, and unique information in each document.
        Organize your comparison with clear headings and be as specific as possible.`
      });

      return response.text;
    } catch (error) {
      console.error('Error comparing PDFs:', error);
      throw new Error(`Failed to compare PDFs: ${error.message}`);
    }
  }

  /**
   * Add an annotation to the PDF
   * @param {number} pageNumber - The page number to annotate
   * @param {string} text - The text to annotate
   * @param {string} annotationText - The annotation text
   * @param {string} [type='highlight'] - The type of annotation (highlight, note, comment)
   * @returns {Promise<boolean>} - Whether the annotation was added successfully
   */
  async addAnnotation(pageNumber, text, annotationText, type = 'highlight') {
    try {
      if (!pageNumber || !text || !annotationText) {
        throw new Error('Page number, text to annotate, and annotation text are required.');
      }

      // Get the current annotations for this page
      let pageAnnotations = this.annotations.get(pageNumber) || [];
      
      // Add the new annotation
      pageAnnotations.push({
        text: text,
        annotation: annotationText,
        type: type,
        timestamp: new Date().toISOString()
      });
      
      // Update the annotations map
      this.annotations.set(pageNumber, pageAnnotations);
      
      // Save annotations to storage
      await this.saveAnnotations();
      
      return true;
    } catch (error) {
      console.error('Error adding annotation:', error);
      throw new Error(`Failed to add annotation: ${error.message}`);
    }
  }

  /**
   * Get all annotations for a PDF
   * @param {number} [pageNumber] - Optional page number to get annotations for
   * @returns {Promise<Object>} - The annotations
   */
  async getAnnotations(pageNumber = null) {
    try {
      // Load annotations from storage if not already loaded
      if (this.annotations.size === 0) {
        await this.loadAnnotations();
      }
      
      // If a page number is provided, return annotations for that page
      if (pageNumber) {
        return this.annotations.get(pageNumber) || [];
      }
      
      // Otherwise, return all annotations
      const allAnnotations = {};
      for (const [page, annotations] of this.annotations.entries()) {
        allAnnotations[page] = annotations;
      }
      
      return allAnnotations;
    } catch (error) {
      console.error('Error getting annotations:', error);
      throw new Error(`Failed to get annotations: ${error.message}`);
    }
  }

  /**
   * Save annotations to storage
   * @returns {Promise<boolean>} - Whether the annotations were saved successfully
   */
  async saveAnnotations() {
    try {
      // Convert the annotations map to an object for storage
      const annotationsObj = {};
      for (const [page, annotations] of this.annotations.entries()) {
        annotationsObj[page] = annotations;
      }
      
      // Save to storage
      await chrome.storage.local.set({
        [`pdf_annotations_${this.currentPDFMetadata.url}`]: annotationsObj
      });
      
      return true;
    } catch (error) {
      console.error('Error saving annotations:', error);
      throw new Error(`Failed to save annotations: ${error.message}`);
    }
  }

  /**
   * Load annotations from storage
   * @returns {Promise<boolean>} - Whether the annotations were loaded successfully
   */
  async loadAnnotations() {
    try {
      // Load PDF content and metadata if not already loaded
      if (!this.currentPDFMetadata) {
        await this.loadPDFContent();
      }
      
      // Load from storage
      const result = await chrome.storage.local.get([
        `pdf_annotations_${this.currentPDFMetadata.url}`
      ]);
      
      const annotationsObj = result[`pdf_annotations_${this.currentPDFMetadata.url}`] || {};
      
      // Convert the object back to a map
      this.annotations = new Map();
      for (const [page, annotations] of Object.entries(annotationsObj)) {
        this.annotations.set(parseInt(page), annotations);
      }
      
      return true;
    } catch (error) {
      console.error('Error loading annotations:', error);
      throw new Error(`Failed to load annotations: ${error.message}`);
    }
  }

  /**
   * Generate a study guide from the PDF
   * @returns {Promise<string>} - The generated study guide
   */
  async generateStudyGuide() {
    try {
      // Load PDF content if not already loaded
      const { content, metadata } = await this.loadPDFContent();
      
      if (!content || content.trim().length === 0) {
        throw new Error('No PDF content available to analyze.');
      }

      // Prepare the prompt
      const prompt = `Please create a comprehensive study guide for this document:
      
      Title: ${metadata.title || 'PDF Document'}
      
      Content:
      ${this.truncateContent(content, 15000)}
      
      Create a detailed study guide that includes:
      1. A summary of key concepts and main ideas
      2. Important definitions and terminology
      3. Key facts, figures, and statistics
      4. Major arguments and supporting evidence
      5. 10-15 potential quiz/exam questions with answers
      
      Format your response with clear headings for each section.`;

      // Get the study guide from the AI
      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert educator who creates comprehensive study guides.
        Focus on the most important concepts, definitions, facts, and arguments.
        Include potential quiz/exam questions with answers to help with studying.
        Organize your study guide with clear headings and make it easy to navigate.`
      });

      return response.text;
    } catch (error) {
      console.error('Error generating study guide:', error);
      throw new Error(`Failed to generate study guide: ${error.message}`);
    }
  }

  /**
   * Truncate content to a maximum length
   * @param {string} content - The content to truncate
   * @param {number} maxLength - The maximum length
   * @returns {string} - The truncated content
   */
  truncateContent(content, maxLength = 15000) {
    if (!content) return '';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '... [content truncated due to length]';
  }
}
