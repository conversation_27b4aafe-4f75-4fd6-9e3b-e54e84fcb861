version: '3.8'

services:
  # API Service
  browzyai-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: browzyai-api
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - API_PORT=5000
      - CORS_ORIGIN=http://localhost:5173,chrome-extension://*
    volumes:
      - ./api:/app/api
      - ./logs:/app/logs
      - api_node_modules:/app/api/node_modules
    networks:
      - browzyai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Dashboard Service
  browzyai-dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    container_name: browzyai-dashboard
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=production
      - VITE_API_URL=http://localhost:5000
      - VITE_APP_NAME=BrowzyAI Dashboard
    volumes:
      - ./dashboard:/app/dashboard
      - dashboard_node_modules:/app/dashboard/node_modules
    networks:
      - browzyai-network
    depends_on:
      - browzyai-api
    restart: unless-stopped

  # Extension Development Server (for hot reload)
  browzyai-extension:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: browzyai-extension
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - .:/app
      - /app/node_modules
      - extension_node_modules:/app/node_modules
    networks:
      - browzyai-network
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: browzyai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - browzyai-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL Database (optional)
  postgres:
    image: postgres:15-alpine
    container_name: browzyai-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=browzyai
      - POSTGRES_USER=browzyai
      - POSTGRES_PASSWORD=browzyai_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - browzyai-network
    restart: unless-stopped

volumes:
  api_node_modules:
  dashboard_node_modules:
  extension_node_modules:
  redis_data:
  postgres_data:

networks:
  browzyai-network:
    driver: bridge
