/* Modern Navigation Bar - New Pill Design */
:root {
  --nav-bg: #283b48; /* Medium Teal/Blue */
  --nav-active-bg: #00a6c0; /* Bright Turquoise */
  --nav-text: #FFFFFF; /* Pure White */
  --nav-text-inactive: rgba(255, 255, 255, 0.7); /* Pure White with transparency */
  --nav-active-text: #FFFFFF; /* Pure White */
  --nav-border-radius: 28px;
  --nav-height: 56px;
  --nav-icon-size: 20px;
  --nav-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  --nav-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  --nav-container-radius: 16px;
  --nav-divider-color: rgba(242, 244, 247, 0.2); /* Fog Gray with transparency */
}

/* Modern Navigation Container */
.tabs {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15px auto 10px;
  background-color: var(--nav-bg);
  border-radius: var(--nav-container-radius);
  padding: 8px 12px;
  box-shadow: var(--nav-shadow);
  position: relative;
  max-width: 90%;
  height: var(--nav-height);
  border: 1px solid rgba(242, 244, 247, 0.15); /* Fog Gray with transparency */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Tab container to center the main tabs */
.tabs::before {
  content: '';
  flex: 1;
}

/* Tab Button Styling */
.tab-btn {
  background-color: transparent;
  border: none;
  color: var(--nav-text-inactive);
  cursor: pointer;
  font-size: 0;
  padding: 0;
  transition: var(--nav-transition);
  border-radius: 50%;
  flex: 0 0 auto;
  text-align: center;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 15px;
}

/* Tab Button Hover State */
.tab-btn:hover {
  color: var(--nav-text);
  background-color: rgba(255, 255, 255, 0.05);
}

/* Active Tab Button */
.tab-btn.active {
  color: var(--nav-active-text);
  background-color: var(--nav-active-bg);
  font-weight: 600;
  position: relative;
  z-index: 2;
  border-radius: var(--nav-border-radius);
  width: auto;
  padding: 0 16px;
  min-width: 110px;
  font-size: 0.9rem;
}

/* Show text only for active tab */
.tab-btn span {
  display: none;
}

.tab-btn.active span {
  display: inline;
  margin-left: 6px;
}

/* Remove the underline indicator for active tab */
.tab-btn.active::after {
  display: none;
}

/* Icon Styling */
.tab-btn i {
  font-size: var(--nav-icon-size);
  transition: var(--nav-transition);
}

/* Divider line below navigation */
.tabs::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 15%;
  right: 15%;
  height: 1px;
  background-color: var(--nav-divider-color);
}

/* Sidebar Toggle Button */
.sidebar-toggle-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--nav-text-inactive);
  margin-left: 8px;
  transition: var(--nav-transition);
  flex: 1;
  justify-self: flex-end;
}

.sidebar-toggle-btn:hover {
  color: var(--nav-text);
  background-color: rgba(255, 255, 255, 0.05);
  transform: none;
  box-shadow: none;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .tab-btn.active {
    min-width: 80px;
    padding: 0 10px;
    font-size: 0.8rem;
  }

  .tab-btn i {
    font-size: calc(var(--nav-icon-size) - 2px);
  }
}
