'use strict';

/**
 * Enhanced NLP Manager for Smart Search
 * Provides advanced natural language understanding for search queries
 */
class EnhancedNLPManager {
  constructor() {
    // Initialize search patterns with more comprehensive keywords and variations
    this.searchPatterns = {
      // Video Platforms
      youtube: {
        keywords: ['youtube', 'yt', 'video', 'videos', 'watch', 'channel', 'youtuber', 'tube'],
        url: 'https://www.youtube.com/results?search_query=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['youtube', 'yt', 'videos on', 'watch on youtube', 'youtube videos of', 'youtube channel']),
        // Conversation patterns that indicate YouTube intent
        conversationPatterns: [
          'want to see videos',
          'want to watch videos',
          'looking for videos',
          'find videos',
          'search for videos',
          'bored',
          'funny videos',
          'music videos',
          'tutorial videos',
          'how to videos',
          'want to see something',
          'want to watch something'
        ]
      },

      // Social Media Platforms
      instagram: {
        keywords: ['instagram', 'insta', 'ig', 'instagram profile', 'instagram account', 'instagram user', 'instagram hashtag', 'insta'],
        url: 'https://www.instagram.com/explore/search/keyword/?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['instagram', 'insta', 'ig', 'on instagram', 'instagram profile of']),
        conversationPatterns: [
          'want to see photos',
          'want to check photos',
          'looking for pictures',
          'find pictures',
          'search for images',
          'want to see posts',
          'check out photos',
          'see what people are posting',
          'check someone',
          'look at pictures',
          'see images'
        ]
      },
      twitter: {
        keywords: ['twitter', 'tweet', 'tweets', 'x.com', 'x platform', 'x profile', 'twitter profile', 'twitter account'],
        url: 'https://twitter.com/search?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['twitter', 'tweet', 'tweets', 'x.com', 'x platform', 'on twitter', 'twitter profile of'])
      },
      pinterest: {
        keywords: ['pinterest', 'pin', 'pins', 'pinterest board', 'pinterest idea', 'pinterest inspiration'],
        url: 'https://www.pinterest.com/search/pins/?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['pinterest', 'pin', 'pins', 'on pinterest', 'pinterest ideas for'])
      },
      facebook: {
        keywords: ['facebook', 'fb', 'facebook profile', 'facebook page', 'facebook group'],
        url: 'https://www.facebook.com/search/top/?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['facebook', 'fb', 'on facebook', 'facebook profile of'])
      },
      linkedin: {
        keywords: ['linkedin', 'linkedin profile', 'linkedin job', 'linkedin company', 'professional profile'],
        url: 'https://www.linkedin.com/search/results/all/?keywords=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['linkedin', 'on linkedin', 'linkedin profile of', 'linkedin job for'])
      },
      reddit: {
        keywords: ['reddit', 'subreddit', 'r/', 'reddit post', 'reddit thread', 'reddit community'],
        url: 'https://www.reddit.com/search/?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['reddit', 'subreddit', 'on reddit', 'reddit post about'])
      },
      tiktok: {
        keywords: ['tiktok', 'tik tok', 'tiktok video', 'tiktok user', 'tiktok profile', 'tiktok account'],
        url: 'https://www.tiktok.com/search?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['tiktok', 'tik tok', 'on tiktok', 'tiktok videos of'])
      },

      // Development Platforms
      github: {
        keywords: ['github', 'git', 'repository', 'repo', 'github repo', 'github project', 'github code', 'github profile'],
        url: 'https://github.com/search?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['github', 'git', 'repository', 'repo', 'on github', 'github repo for'])
      },
      stackoverflow: {
        keywords: ['stackoverflow', 'stack overflow', 'coding question', 'programming question', 'code error', 'code problem'],
        url: 'https://stackoverflow.com/search?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['stackoverflow', 'stack overflow', 'on stackoverflow', 'coding question about'])
      },

      // E-commerce Platforms
      amazon: {
        keywords: ['amazon', 'buy', 'purchase', 'product', 'amazon product', 'amazon item', 'shopping'],
        url: 'https://www.amazon.com/s?k=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['amazon', 'buy on amazon', 'purchase on amazon', 'amazon product'])
      },
      ebay: {
        keywords: ['ebay', 'auction', 'bid', 'ebay listing', 'ebay item', 'ebay auction'],
        url: 'https://www.ebay.com/sch/i.html?_nkw=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['ebay', 'on ebay', 'ebay listing for', 'ebay auction for'])
      },
      flipkart: {
        keywords: ['flipkart', 'flipkart product', 'flipkart item', 'buy on flipkart'],
        url: 'https://www.flipkart.com/search?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['flipkart', 'on flipkart', 'flipkart product'])
      },

      // Entertainment Platforms
      spotify: {
        keywords: ['spotify', 'song', 'music', 'album', 'artist', 'playlist', 'track', 'listen'],
        url: 'https://open.spotify.com/search/',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['spotify', 'song', 'music', 'on spotify', 'listen to', 'play'])
      },
      netflix: {
        keywords: ['netflix', 'show', 'movie', 'series', 'film', 'watch', 'stream', 'netflix show', 'netflix movie'],
        url: 'https://www.netflix.com/search?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['netflix', 'on netflix', 'watch on netflix', 'netflix show', 'netflix movie'])
      },
      primevideo: {
        keywords: ['prime video', 'amazon prime', 'prime', 'prime show', 'prime movie', 'amazon prime video'],
        url: 'https://www.amazon.com/s?k=',
        urlSuffix: '&i=instant-video',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['prime video', 'amazon prime', 'prime', 'on prime', 'watch on prime'])
      },
      hotstar: {
        keywords: ['hotstar', 'jiohotstar', 'disney+ hotstar', 'hotstar show', 'hotstar movie'],
        url: 'https://www.hotstar.com/in/search?q=',
        extractQuery: (query) => this.extractQueryAfterPlatform(query, ['hotstar', 'jiohotstar', 'on hotstar', 'watch on hotstar'])
      },

      // General Web Browsing
      browse: {
        keywords: ['browse', 'go to', 'visit', 'open', 'website', 'site', 'webpage', 'web page', 'navigate to'],
        extractUrl: (query) => this.extractWebsiteUrl(query)
      },

      // General Search
      google: {
        keywords: ['google', 'search', 'find', 'look for', 'search for', 'information about', 'info on', 'details about'],
        url: 'https://www.google.com/search?q=',
        extractQuery: (query) => query // Use the full query for Google search
      }
    };

    // Common action verbs that indicate search intent
    this.actionVerbs = [
      'search', 'find', 'look', 'browse', 'explore', 'discover', 'check',
      'view', 'watch', 'see', 'show', 'display', 'get', 'fetch', 'retrieve',
      'buy', 'purchase', 'shop', 'order', 'listen', 'play', 'stream'
    ];

    // Common question words that indicate search intent
    this.questionWords = [
      'what', 'how', 'where', 'who', 'when', 'why', 'which', 'can', 'could',
      'would', 'should', 'is', 'are', 'was', 'were', 'do', 'does', 'did'
    ];
  }

  /**
   * Process a search query to determine the best search platform and extract the actual search terms
   * @param {string} query - The user's search query
   * @returns {object} - Search platform and processed query
   */
  processQuery(query) {
    if (!query || typeof query !== 'string') {
      return { platform: 'google', processedQuery: query, url: `https://www.google.com/search?q=${encodeURIComponent(query)}` };
    }

    // Normalize the query
    const normalizedQuery = query.trim().toLowerCase();

    // Check for direct URL
    if (this.isUrl(normalizedQuery)) {
      const url = this.ensureHttps(normalizedQuery);
      return { platform: 'direct', processedQuery: normalizedQuery, url };
    }

    // Check for browse intent
    if (this.hasKeywords(normalizedQuery, this.searchPatterns.browse.keywords)) {
      const url = this.searchPatterns.browse.extractUrl(normalizedQuery);
      return { platform: 'browse', processedQuery: normalizedQuery, url };
    }

    // Find the best matching platform based on keywords
    let bestMatch = { platform: 'google', score: 0 };

    for (const [platform, pattern] of Object.entries(this.searchPatterns)) {
      if (platform === 'browse') continue; // Skip browse as we already checked it

      // Check for direct platform mentions
      const keywordScore = this.calculateMatchScore(normalizedQuery, pattern.keywords);

      // Check for conversational patterns
      let conversationScore = 0;
      if (pattern.conversationPatterns) {
        conversationScore = this.calculateConversationMatchScore(normalizedQuery, pattern.conversationPatterns);
      }

      // Use the higher of the two scores
      const finalScore = Math.max(keywordScore, conversationScore);

      if (finalScore > bestMatch.score) {
        bestMatch = { platform, score: finalScore };
      }
    }

    // If no good match found, try to extract intent from conversational query
    if (bestMatch.score < 0.3) {
      const intent = this.extractIntentFromConversation(normalizedQuery);
      if (intent) {
        bestMatch = { platform: intent, score: 0.4 };
      } else {
        bestMatch.platform = 'google';
      }
    }

    // Extract the processed query
    const pattern = this.searchPatterns[bestMatch.platform];
    let processedQuery = normalizedQuery;

    // If it's a conversational query, extract the relevant parts
    if (this.isConversationalQuery(normalizedQuery)) {
      processedQuery = this.extractQueryFromConversation(normalizedQuery, bestMatch.platform);
    } else if (pattern.extractQuery) {
      processedQuery = pattern.extractQuery(normalizedQuery);
    }

    // Build the search URL
    let url = pattern.url + encodeURIComponent(processedQuery);
    if (pattern.urlSuffix) {
      url += pattern.urlSuffix;
    }

    return {
      platform: bestMatch.platform,
      processedQuery,
      url,
      isConversational: this.isConversationalQuery(normalizedQuery)
    };
  }

  /**
   * Check if a query is conversational in nature
   * @param {string} query - The query to check
   * @returns {boolean} - Whether the query is conversational
   */
  isConversationalQuery(query) {
    // Common conversational starters
    const conversationalStarters = [
      'i want', 'i need', 'i would like', 'can you', 'could you',
      'please', 'help me', 'show me', 'find me', 'i am', "i'm",
      'looking for', 'searching for', 'trying to find', 'where can i',
      'how do i', 'what is', 'who is', 'when is', 'why is'
    ];

    // Check if the query starts with any of these phrases
    for (const starter of conversationalStarters) {
      if (query.startsWith(starter)) {
        return true;
      }
    }

    // Check if the query has multiple clauses (indicated by commas, 'and', 'but', etc.)
    if (query.includes(',') ||
        query.includes(' and ') ||
        query.includes(' but ') ||
        query.includes(' so ') ||
        query.includes(' because ')) {
      return true;
    }

    // Check if the query is a complete sentence (has subject and verb)
    const words = query.split(/\s+/);
    if (words.length > 5) {
      return true;
    }

    return false;
  }

  /**
   * Calculate a match score for conversational patterns
   * @param {string} query - The normalized query
   * @param {Array} patterns - Array of conversational patterns to match against
   * @returns {number} - Match score between 0 and 1
   */
  calculateConversationMatchScore(query, patterns) {
    if (!patterns || !Array.isArray(patterns) || patterns.length === 0) {
      return 0;
    }

    let highestScore = 0;

    for (const pattern of patterns) {
      if (query.includes(pattern)) {
        // Calculate score based on pattern length and position
        const patternLength = pattern.length;
        const queryLength = query.length;
        const position = query.indexOf(pattern);

        // Patterns at the beginning get higher scores
        const positionFactor = 1 - (position / queryLength);

        // Longer patterns get higher scores
        const lengthFactor = patternLength / queryLength;

        // Calculate final score
        const score = 0.4 + (0.3 * positionFactor) + (0.3 * lengthFactor);

        if (score > highestScore) {
          highestScore = score;
        }
      }
    }

    return highestScore;
  }

  /**
   * Extract intent from a conversational query
   * @param {string} query - The conversational query
   * @returns {string|null} - The extracted intent (platform) or null if none found
   */
  extractIntentFromConversation(query) {
    // Common intent indicators
    const intentMap = {
      'video': 'youtube',
      'videos': 'youtube',
      'watch': 'youtube',
      'funny': 'youtube',
      'tutorial': 'youtube',
      'music': 'spotify',
      'song': 'spotify',
      'listen': 'spotify',
      'photo': 'instagram',
      'photos': 'instagram',
      'picture': 'instagram',
      'pictures': 'instagram',
      'image': 'instagram',
      'images': 'instagram',
      'tweet': 'twitter',
      'news': 'twitter',
      'latest': 'twitter',
      'trending': 'twitter',
      'shop': 'amazon',
      'buy': 'amazon',
      'purchase': 'amazon',
      'product': 'amazon',
      'price': 'amazon',
      'movie': 'netflix',
      'show': 'netflix',
      'series': 'netflix',
      'stream': 'netflix',
      'code': 'github',
      'repository': 'github',
      'programming': 'github',
      'developer': 'github',
      'error': 'stackoverflow',
      'problem': 'stackoverflow',
      'question': 'stackoverflow',
      'how to': 'stackoverflow'
    };

    // Check for intent indicators in the query
    for (const [indicator, platform] of Object.entries(intentMap)) {
      if (query.includes(indicator)) {
        return platform;
      }
    }

    // Check for boredom indicators (often lead to entertainment searches)
    if (query.includes('bored') ||
        query.includes('nothing to do') ||
        query.includes('entertain me') ||
        query.includes('something fun')) {
      // Default to YouTube for entertainment
      return 'youtube';
    }

    return null;
  }

  /**
   * Extract the relevant search terms from a conversational query
   * @param {string} query - The conversational query
   * @param {string} platform - The detected platform
   * @returns {string} - The extracted search terms
   */
  extractQueryFromConversation(query, platform) {
    // Remove common conversational starters
    const starters = [
      'i want to', 'i want', 'i need to', 'i need', 'i would like to', 'i would like',
      'can you', 'could you', 'please', 'help me', 'show me', 'find me',
      'i am looking for', "i'm looking for", 'looking for', 'searching for',
      'trying to find', 'where can i find', 'how do i find', 'i want to see',
      'i want to watch', 'i want to listen to', 'i want to buy', 'i want to search for',
      'i am bored', "i'm bored", 'i need something to'
    ];

    let processedQuery = query;

    // Remove starters
    for (const starter of starters) {
      if (processedQuery.startsWith(starter)) {
        processedQuery = processedQuery.substring(starter.length).trim();
        break;
      }
    }

    // Remove platform-specific terms if they exist
    if (this.searchPatterns[platform] && this.searchPatterns[platform].extractQuery) {
      processedQuery = this.searchPatterns[platform].extractQuery(processedQuery);
    }

    // Handle special cases
    if (platform === 'youtube' && processedQuery.includes('funny videos')) {
      processedQuery = processedQuery.replace('funny videos', 'funny');
    }

    if (platform === 'youtube' && processedQuery.includes('bored')) {
      processedQuery = processedQuery.replace('bored', 'entertaining videos');
    }

    // Remove filler words
    const fillerWords = [
      'just', 'really', 'very', 'quite', 'simply', 'that are', 'that is',
      'which are', 'which is', 'who are', 'who is', 'what are', 'what is'
    ];

    for (const filler of fillerWords) {
      processedQuery = processedQuery.replace(new RegExp(`\\b${filler}\\b`, 'gi'), '');
    }

    return processedQuery.trim();
  }

  /**
   * Calculate a match score between a query and a set of keywords
   * @param {string} query - The normalized query
   * @param {Array} keywords - Array of keywords to match against
   * @returns {number} - Match score between 0 and 1
   */
  calculateMatchScore(query, keywords) {
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return 0;
    }

    let highestScore = 0;

    // Check for exact matches first
    for (const keyword of keywords) {
      if (query.includes(keyword)) {
        // Calculate score based on keyword length and position
        const keywordLength = keyword.length;
        const queryLength = query.length;
        const position = query.indexOf(keyword);

        // Keywords at the beginning get higher scores
        const positionFactor = 1 - (position / queryLength);

        // Longer keywords get higher scores
        const lengthFactor = keywordLength / queryLength;

        // Calculate final score
        const score = 0.5 + (0.3 * positionFactor) + (0.2 * lengthFactor);

        if (score > highestScore) {
          highestScore = score;
        }
      }
    }

    // If no exact matches, check for partial matches
    if (highestScore === 0) {
      for (const keyword of keywords) {
        // Check if any word in the query partially matches the keyword
        const words = query.split(/\s+/);
        for (const word of words) {
          if (keyword.includes(word) || word.includes(keyword)) {
            const partialScore = 0.3 * (word.length / keyword.length);
            if (partialScore > highestScore) {
              highestScore = partialScore;
            }
          }
        }
      }
    }

    return highestScore;
  }

  /**
   * Check if a query contains any of the specified keywords
   * @param {string} query - The query to check
   * @param {Array} keywords - Array of keywords to check for
   * @returns {boolean} - Whether any keyword is present
   */
  hasKeywords(query, keywords) {
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return false;
    }

    for (const keyword of keywords) {
      if (query.includes(keyword)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Extract the actual search query after removing platform-specific keywords
   * @param {string} query - The original query
   * @param {Array} platformTerms - Terms to remove from the query
   * @returns {string} - The extracted search query
   */
  extractQueryAfterPlatform(query, platformTerms) {
    let processedQuery = query;

    // Remove platform-specific terms
    for (const term of platformTerms) {
      // Create regex patterns to match the term at different positions
      const startPattern = new RegExp(`^${term}\\s+`, 'i');
      const middlePattern = new RegExp(`\\s+${term}\\s+`, 'i');
      const endPattern = new RegExp(`\\s+${term}$`, 'i');

      // Replace all occurrences
      processedQuery = processedQuery
        .replace(startPattern, '')
        .replace(middlePattern, ' ')
        .replace(endPattern, '');
    }

    // Remove action verbs from the beginning
    for (const verb of this.actionVerbs) {
      const pattern = new RegExp(`^${verb}\\s+`, 'i');
      processedQuery = processedQuery.replace(pattern, '');
    }

    // Remove question words from the beginning
    for (const word of this.questionWords) {
      const pattern = new RegExp(`^${word}\\s+`, 'i');
      processedQuery = processedQuery.replace(pattern, '');
    }

    // Remove common filler words
    const fillerWords = ['the', 'a', 'an', 'for', 'about', 'regarding', 'concerning', 'on', 'of', 'in', 'at'];
    for (const word of fillerWords) {
      const pattern = new RegExp(`^${word}\\s+`, 'i');
      processedQuery = processedQuery.replace(pattern, '');
    }

    return processedQuery.trim();
  }

  /**
   * Extract a website URL from a browse query
   * @param {string} query - The browse query
   * @returns {string} - The extracted URL
   */
  extractWebsiteUrl(query) {
    // Remove browse keywords
    let processedQuery = query;
    const browseKeywords = this.searchPatterns.browse.keywords;

    for (const keyword of browseKeywords) {
      const pattern = new RegExp(`${keyword}\\s+`, 'i');
      processedQuery = processedQuery.replace(pattern, '');
    }

    // Check if the remaining query is a URL
    if (this.isUrl(processedQuery)) {
      return this.ensureHttps(processedQuery);
    }

    // Try to extract a domain
    const domainMatch = processedQuery.match(/([a-zA-Z0-9][-a-zA-Z0-9]*\.)+[a-zA-Z]{2,}/);
    if (domainMatch) {
      return this.ensureHttps(domainMatch[0]);
    }

    // If no domain found, try to construct a URL
    const words = processedQuery.split(/\s+/);
    if (words.length > 0) {
      // Remove common words that wouldn't be part of a domain
      const filteredWords = words.filter(word =>
        !['to', 'the', 'a', 'an', 'and', 'or', 'but', 'for', 'with', 'without', 'of', 'in', 'on', 'at'].includes(word.toLowerCase())
      );

      if (filteredWords.length > 0) {
        // Try common TLDs
        const domain = filteredWords.join('');
        return `https://www.${domain}.com`;
      }
    }

    // Default to Google search if we can't extract a URL
    return `https://www.google.com/search?q=${encodeURIComponent(processedQuery)}`;
  }

  /**
   * Check if a string is a URL
   * @param {string} str - The string to check
   * @returns {boolean} - Whether the string is a URL
   */
  isUrl(str) {
    // Simple URL check
    return /^(https?:\/\/)?([a-zA-Z0-9][-a-zA-Z0-9]*\.)+[a-zA-Z]{2,}(\/[-a-zA-Z0-9@:%_\+.~#?&//=]*)?$/.test(str);
  }

  /**
   * Ensure a URL has the https:// prefix
   * @param {string} url - The URL to check
   * @returns {string} - The URL with https:// prefix
   */
  ensureHttps(url) {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`;
    }
    return url;
  }
}

// Export the class
window.EnhancedNLPManager = EnhancedNLPManager;
