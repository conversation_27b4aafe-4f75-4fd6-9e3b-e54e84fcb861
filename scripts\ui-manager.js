'use strict';

/**
 * Manages UI interactions
 */
class UIManager {
  constructor() {
    this.statusMessage = document.getElementById('statusMessage');
    this.chatMessages = document.getElementById('chatMessages');
    this.statusTimeout = null; // For tracking status message timeout

    // Warn if critical elements are missing
    if (!this.statusMessage) {
      console.warn('Status message element not found');
    }
    if (!this.chatMessages) {
      console.warn('Chat messages element not found');
    }
  }

  /**
   * Format content for display
   * @param {string} content - The content to format
   * @returns {string} - The formatted HTML
   */
  formatContent(content) {
    if (!content) return '';

    // Escape HTML to prevent XSS
    let safeContent = this.escapeHTML(content);

    // Check for prompt blocks
    // Look for patterns like "Prompt:" or "Prompt for [something]:" followed by text
    const promptRegex = /(^|\n)(prompt:|\bprompt for [^:]+:|\bai prompt:|\bchat prompt:|\bwriting prompt:|\bprompt template:)([^\n].*?)(?=\n\n|\n[^:\s]+:|\n#|\n\*\*|$)/is;

    // Process prompt blocks first
    if (promptRegex.test(safeContent)) {
      safeContent = safeContent.replace(promptRegex, (_, prefix, promptHeader, promptContent) => {
        // Generate a unique ID for this prompt
        const promptId = 'prompt-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

        // Format the prompt header (capitalize first letter)
        const formattedHeader = promptHeader.charAt(0).toUpperCase() + promptHeader.slice(1);

        // Create the prompt block HTML
        return `${prefix}<div class="prompt-block">
          <div class="prompt-header">
            <span>${formattedHeader}</span>
            <button class="prompt-copy-btn" data-target="${promptId}">
              <i class="fas fa-copy"></i> Copy
            </button>
          </div>
          <div class="prompt-content" id="${promptId}">${promptContent.trim()}</div>
        </div>`;
      });
    }

    // Convert markdown-like syntax to HTML
    safeContent = safeContent
      // Code blocks with language specification
      .replace(/```([a-zA-Z0-9]+)\n([\s\S]+?)```/g, (_, language, code) => {
        const uniqueId = 'code-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

        return `<div class="code-container">
          <div class="code-header">
            <span class="code-language">${language}</span>
          </div>
          <pre><code id="${uniqueId}" class="language-${language}">${code}</code>
            <button class="copy-button" data-target="${uniqueId}">
              <i class="fas fa-copy"></i> Copy
            </button>
          </pre>
        </div>`;
      })
      // Code blocks without language specification
      .replace(/```([^`]+)```/g, (match, code) => {
        if (code.startsWith('javascript\n') || code.startsWith('python\n') || code.startsWith('java\n')) {
          return match; // Skip this as it was already handled by the previous regex
        }

        const uniqueId = 'code-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

        return `<div class="code-container">
          <div class="code-header">
            <span class="code-language">code</span>
          </div>
          <pre><code id="${uniqueId}">${code}</code>
            <button class="copy-button" data-target="${uniqueId}">
              <i class="fas fa-copy"></i> Copy
            </button>
          </pre>
        </div>`;
      })
      // Inline code
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      // Bold text
      .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
      // Italic text
      .replace(/\*([^*]+)\*/g, '<em>$1</em>')
      // Headings (h4 and smaller only)
      .replace(/#### ([^\n]+)/g, '<h4>$1</h4>')
      .replace(/### ([^\n]+)/g, '<h4>$1</h4>')
      // Line breaks
      .replace(/\n/g, '<br>');

    return safeContent;
  }

  /**
   * Show a status message with enhanced visual feedback
   * @param {string} message - The message to display
   * @param {boolean} isError - Whether this is an error message
   * @param {number} duration - How long to show the message in ms (default: 4000, 0 for no auto-clear)
   * @param {boolean} isLoading - Whether to show a loading animation
   * @param {string} [type] - Message type: 'info', 'success', 'warning', 'error', 'loading'
   */
  showStatus(message, isError = false, duration = 4000, isLoading = false, type = null) {
    // Check if status message element exists
    if (!this.statusMessage) {
      console.warn('Cannot show status: status message element not found');
      return;
    }

    // Performance tracking
    const startTime = performance.now();

    // Clear any existing timeout
    if (this.statusTimeout) {
      clearTimeout(this.statusTimeout);
      this.statusTimeout = null;
    }

    // Determine message type if not explicitly provided
    if (!type) {
      if (isError) {
        type = 'error';
      } else if (isLoading || message.endsWith('...')) {
        type = 'loading';
      } else if (message.toLowerCase().includes('success') || message.toLowerCase().includes('complete')) {
        type = 'success';
      } else if (message.toLowerCase().includes('warn')) {
        type = 'warning';
      } else {
        type = 'info';
      }
    }

    // Remove all status classes
    this.statusMessage.classList.remove('loading', 'error', 'success', 'warning', 'info');

    // Add appropriate class based on type
    this.statusMessage.classList.add(type);

    // Set the message content with appropriate icon
    let icon = '';
    switch (type) {
      case 'loading':
        icon = '<i class="fas fa-spinner fa-spin"></i>';
        break;
      case 'error':
        icon = '<i class="fas fa-exclamation-circle"></i>';
        break;
      case 'success':
        icon = '<i class="fas fa-check-circle"></i>';
        break;
      case 'warning':
        icon = '<i class="fas fa-exclamation-triangle"></i>';
        break;
      case 'info':
      default:
        icon = '<i class="fas fa-info-circle"></i>';
        break;
    }

    // Set the message with icon
    this.statusMessage.innerHTML = `${icon} ${message}`;

    // Add animation for better visibility
    this.statusMessage.classList.add('status-animate', 'active');
    setTimeout(() => {
      this.statusMessage.classList.remove('status-animate');
    }, 300);

    // Clear the message after the specified duration (if not 0)
    if (duration > 0 && type !== 'loading') {
      this.statusTimeout = setTimeout(() => {
        // Fade out animation
        this.statusMessage.classList.add('status-fade-out');

        // After animation completes, remove the message
        setTimeout(() => {
          this.statusMessage.textContent = '';
          this.statusMessage.classList.remove('error', 'loading', 'success', 'warning', 'info', 'active', 'status-fade-out');
          this.statusTimeout = null;
        }, 300);
      }, duration);
    }

    // Log performance
    const endTime = performance.now();
    console.log(`Status update "${type}" completed in ${Math.round(endTime - startTime)}ms`);
  }

  /**
   * Show a success status message
   * @param {string} message - The message to show
   * @param {number} [duration] - Optional timeout to hide the message after
   */
  showSuccess(message, duration = 3000) {
    this.showStatus(message, false, duration, false, 'success');
  }

  /**
   * Show a warning status message
   * @param {string} message - The message to show
   * @param {number} [duration] - Optional timeout to hide the message after
   */
  showWarning(message, duration = 5000) {
    this.showStatus(message, false, duration, false, 'warning');
  }

  /**
   * Add a message to the chat
   * @param {string} content - Message content
   * @param {string} sender - 'user' or 'ai'
   * @param {string} [className] - Optional additional class name for the message
   * @param {boolean} [isFollowUp] - Whether this is a follow-up message
   * @param {string} [messageId] - Optional message ID for tracking
   * @param {boolean} [useTypingEffect] - Whether to use typing effect for AI messages
   * @returns {HTMLElement} - The created message element
   */
  addMessageToChat(content, sender, className = '', isFollowUp = false, messageId = null, useTypingEffect = sender === 'ai') {
    // Check if chat messages element exists
    if (!this.chatMessages) {
      console.warn('Cannot add message: chat messages element not found');
      return null;
    }

    // Create message div
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    // Set message ID for tracking
    if (messageId) {
      messageDiv.dataset.messageId = messageId;
    } else {
      messageDiv.dataset.messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);
    }

    // Store original content for editing
    messageDiv.dataset.originalContent = content;

    // Add additional class if provided
    if (className) {
      messageDiv.classList.add(className);

      // Check if this is a safety filter error
      if (className === 'error-message' &&
          (content.includes('safety filter') ||
           content.includes('HARM_CATEGORY') ||
           content.includes('flagged by OpenRouter'))) {
        messageDiv.classList.add('safety-error');
      }

      // Don't use typing effect for error messages or loading messages
      if (className === 'error-message' || className === 'loading-message') {
        useTypingEffect = false;
      }
    }

    // Add follow-up class if this is a follow-up message
    if (isFollowUp) {
      messageDiv.classList.add('follow-up-message');
    }

    // Create avatar
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';

    if (sender === 'user') {
      const icon = document.createElement('i');
      icon.className = 'fas fa-user';
      avatarDiv.appendChild(icon);

      // Add edit button for user messages
      const editBtn = document.createElement('button');
      editBtn.className = 'message-edit-btn';
      editBtn.innerHTML = '<i class="fas fa-pencil-alt"></i>';
      editBtn.title = 'Edit message';
      editBtn.addEventListener('click', () => this.makeMessageEditable(messageDiv));
      messageDiv.appendChild(editBtn);

      // Don't use typing effect for user messages
      useTypingEffect = false;
    } else {
      const aiLogo = document.createElement('img');
      aiLogo.src = '../images/icon.png';
      aiLogo.alt = 'Browzy AI';
      aiLogo.className = 'infinity-logo';
      avatarDiv.appendChild(aiLogo);
    }

    // Create content div
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    // Check if this is a safety filter error to add special formatting
    if (className === 'error-message' &&
        sender === 'ai' &&
        (content.includes('safety filter') ||
         content.includes('HARM_CATEGORY') ||
         content.includes('flagged by OpenRouter'))) {

      // Add a help button for safety filter errors
      contentDiv.innerHTML = `
        <div class="error-header">
          <i class="fas fa-exclamation-triangle"></i> Safety Filter Triggered
        </div>
        <div class="error-content">${this.formatContent(content)}</div>
        <div class="safety-help-button">
          <button class="safety-help-btn"><i class="fas fa-question-circle"></i> Get Help with Safety Filters</button>
        </div>
      `;

      // Don't use typing effect for error messages
      useTypingEffect = false;
    } else if (sender === 'ai' && useTypingEffect) {
      // If it's an AI message and typing effect is enabled, start with empty content
      contentDiv.innerHTML = '';
    } else {
      contentDiv.innerHTML = this.formatContent(content);
    }

    // Assemble the message
    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);

    // Add context indicator for follow-up messages
    if (isFollowUp) {
      const contextIndicator = document.createElement('div');
      contextIndicator.className = 'context-indicator';
      contextIndicator.innerHTML = '<i class="fas fa-link"></i> Follow-up question';
      contentDiv.appendChild(contextIndicator);
    }

    // Add to chat
    this.chatMessages.appendChild(messageDiv);

    // If it's an AI message and typing effect is enabled, start the typing effect
    if (sender === 'ai' && useTypingEffect) {
      this.animateTyping(contentDiv, content);
    } else {
      // Add event listeners to any insert code buttons
      this.setupInsertCodeButtons(contentDiv);

      // Add event listener to safety help button if present
      const safetyHelpBtn = contentDiv.querySelector('.safety-help-btn');
      if (safetyHelpBtn) {
        safetyHelpBtn.addEventListener('click', () => this.showSafetyFilterHelp());
      }
    }

    // Scroll to bottom
    this.scrollToBottom();

    return messageDiv;
  }

  /**
   * Animate typing effect for AI messages
   * @param {HTMLElement} contentDiv - The content div to animate
   * @param {string} content - The full content to type
   */
  animateTyping(contentDiv, content) {
    // Format the content first to handle markdown, code blocks, etc.
    const formattedContent = this.formatContent(content);

    // Get the message element (parent of contentDiv)
    const messageElement = contentDiv.closest('.message');

    // Set initial styles to prevent layout shift
    contentDiv.style.width = '100%';
    contentDiv.style.boxSizing = 'border-box';
    contentDiv.style.position = 'relative';

    // Add typing class to the message element to show blinking cursor
    if (messageElement) {
      messageElement.classList.add('typing');
    }

    // Create a temporary div to parse the HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = formattedContent;

    // Split the content into words and HTML elements
    const contentParts = this.splitContentIntoWords(tempDiv);

    // Clear the content div initially
    contentDiv.innerHTML = '';

    // Create a span to hold the typed content
    const typedContentSpan = document.createElement('span');
    contentDiv.appendChild(typedContentSpan);

    // Add a blinking cursor element
    const cursorSpan = document.createElement('span');
    cursorSpan.className = 'typing-cursor';
    contentDiv.appendChild(cursorSpan);

    // Set up variables for the typing animation
    let currentIndex = 0;
    const typingSpeed = 100; // milliseconds per word (increased for slower animation)

    // Function to type the next word
    const typeNextWord = () => {
      if (currentIndex < contentParts.length) {
        const part = contentParts[currentIndex];

        if (part.type === 'html') {
          // For HTML elements, add them directly
          typedContentSpan.innerHTML += part.content;
        } else if (part.type === 'word') {
          // For text words, add them with a space
          typedContentSpan.innerHTML += part.content + ' ';
        }

        currentIndex++;

        // Scroll to bottom as we type
        this.scrollToBottom();

        // Schedule the next word
        setTimeout(typeNextWord, typingSpeed);
      } else {
        // Typing is complete
        finishTyping();
      }
    };

    // Function to finish typing
    const finishTyping = () => {
      // Remove the cursor
      if (cursorSpan.parentNode) {
        cursorSpan.parentNode.removeChild(cursorSpan);
      }

      // Remove typing class from the message element
      if (messageElement) {
        messageElement.classList.remove('typing');
      }

      // Reset any inline styles we added
      contentDiv.style.width = '';
      contentDiv.style.boxSizing = '';
      contentDiv.style.position = '';

      // Add event listeners to any insert code buttons
      this.setupInsertCodeButtons(contentDiv);

      // Add event listener to safety help button if present
      const safetyHelpBtn = contentDiv.querySelector('.safety-help-btn');
      if (safetyHelpBtn) {
        safetyHelpBtn.addEventListener('click', () => this.showSafetyFilterHelp());
      }

      // Scroll to bottom to ensure the full message is visible
      this.scrollToBottom();
    };

    // Start the typing animation
    setTimeout(typeNextWord, 100);

    // Scroll to bottom immediately to ensure the message is visible
    this.scrollToBottom();
  }

  /**
   * Split content into words and HTML elements for typing animation
   * @param {HTMLElement} element - The element containing the content
   * @returns {Array} - Array of objects with type ('word' or 'html') and content
   */
  splitContentIntoWords(element) {
    const result = [];

    // Function to process a node and its children
    const processNode = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        // It's a text node, split it into words
        const text = node.textContent;
        if (text.trim() !== '') {
          // Split by spaces but keep punctuation with words
          const words = text.match(/\S+|\s+/g) || [];
          words.forEach(word => {
            if (word.trim() !== '') {
              result.push({
                type: 'word',
                content: word
              });
            } else if (word.includes('\n')) {
              // Handle line breaks
              result.push({
                type: 'html',
                content: '<br>'
              });
            }
          });
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // Special handling for code blocks, pre tags, and other complex elements
        // that should be added all at once
        if (node.tagName === 'PRE' ||
            node.tagName === 'CODE' ||
            node.tagName === 'TABLE' ||
            node.classList.contains('code-block') ||
            node.classList.contains('code-container') ||
            node.classList.contains('prompt-block')) {
          result.push({
            type: 'html',
            content: node.outerHTML
          });
        } else if (node.tagName === 'BR') {
          // Handle line breaks
          result.push({
            type: 'html',
            content: '<br>'
          });
        } else {
          // For other elements, process their children
          const childNodes = Array.from(node.childNodes);

          // Add opening tag
          result.push({
            type: 'html',
            content: node.outerHTML.substring(0, node.outerHTML.indexOf('>') + 1)
          });

          // Process children
          childNodes.forEach(childNode => {
            processNode(childNode);
          });

          // Add closing tag
          result.push({
            type: 'html',
            content: '</' + node.tagName.toLowerCase() + '>'
          });
        }
      }
    };

    // Process all child nodes
    Array.from(element.childNodes).forEach(node => {
      processNode(node);
    });

    return result;
  }

  /**
   * Extract text nodes and HTML elements from a DOM element
   * @param {HTMLElement} element - The element to extract from
   * @returns {Array} - Array of objects with type ('text' or 'html') and content
   */
  extractTextAndElements(element) {
    const result = [];

    // Function to process a node and its children
    const processNode = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        // It's a text node, add it as text
        if (node.textContent.trim() !== '') {
          result.push({
            type: 'text',
            content: node.textContent
          });
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // Special handling for code blocks, pre tags, and other complex elements
        // that should be added all at once
        if (node.tagName === 'PRE' ||
            node.tagName === 'CODE' ||
            node.tagName === 'TABLE' ||
            node.classList.contains('code-block') ||
            node.classList.contains('code-container')) {
          result.push({
            type: 'html',
            content: node.outerHTML
          });
        } else {
          // For other elements, process their children
          const childNodes = Array.from(node.childNodes);

          // Add opening tag
          result.push({
            type: 'html',
            content: node.outerHTML.substring(0, node.outerHTML.indexOf('>') + 1)
          });

          // Process children
          childNodes.forEach(childNode => {
            processNode(childNode);
          });

          // Add closing tag
          result.push({
            type: 'html',
            content: '</' + node.tagName.toLowerCase() + '>'
          });
        }
      }
    };

    // Process all child nodes
    Array.from(element.childNodes).forEach(node => {
      processNode(node);
    });

    return result;
  }

  /**
   * Show help for safety filter issues
   */
  showSafetyFilterHelp() {
    // Create help message div
    const helpDiv = document.createElement('div');
    helpDiv.className = 'message ai-message safety-help';

    // Create avatar
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    const aiLogo = document.createElement('img');
    aiLogo.src = '../images/icon.png';
    aiLogo.alt = 'Browzy AI';
    aiLogo.className = 'infinity-logo';
    avatarDiv.appendChild(aiLogo);

    // Create content div
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `
      <div class="help-header">
        <i class="fas fa-info-circle"></i> Understanding Safety Filters
      </div>
      <p>AI models have built-in safety filters that block responses to certain types of content:</p>
      <ul>
        <li><strong>Harassment:</strong> Content that could be perceived as bullying, threatening, or intimidating</li>
        <li><strong>Hate Speech:</strong> Content targeting specific groups based on protected characteristics</li>
        <li><strong>Explicit Content:</strong> Requests for sexually explicit or adult content</li>
        <li><strong>Dangerous Content:</strong> Instructions for illegal activities or harmful actions</li>
      </ul>
      <p><strong>Tips to avoid triggering safety filters:</strong></p>
      <ol>
        <li>Rephrase your request in a more neutral, professional tone</li>
        <li>Break complex requests into simpler, more specific questions</li>
        <li>Avoid using sensitive terminology or potentially offensive language</li>
        <li>Try a different model (some models have different safety thresholds)</li>
        <li>Focus on educational or informational aspects rather than controversial angles</li>
      </ol>
    `;

    // Assemble the message
    helpDiv.appendChild(avatarDiv);
    helpDiv.appendChild(contentDiv);

    // Add to chat
    this.chatMessages.appendChild(helpDiv);

    // Scroll to bottom
    this.scrollToBottom();
  }

  /**
   * Make a message editable
   * @param {HTMLElement} messageDiv - The message element to make editable
   */
  makeMessageEditable(messageDiv) {
    // Only allow editing user messages
    if (!messageDiv.classList.contains('user-message')) {
      return;
    }

    // Add editing class
    messageDiv.classList.add('editing');

    // Get content div
    const contentDiv = messageDiv.querySelector('.message-content');

    // Get original content
    const originalContent = messageDiv.dataset.originalContent || contentDiv.textContent;

    // Save current HTML for restoration if needed
    const originalHtml = contentDiv.innerHTML;

    // Create edit textarea
    const textarea = document.createElement('textarea');
    textarea.className = 'message-edit-textarea';
    textarea.value = originalContent;

    // Create edit controls
    const controls = document.createElement('div');
    controls.className = 'message-edit-controls';

    // Create save button
    const saveBtn = document.createElement('button');
    saveBtn.className = 'edit-save-btn';
    saveBtn.innerHTML = '<i class="fas fa-check"></i> Save';

    // Create cancel button
    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'edit-cancel-btn';
    cancelBtn.innerHTML = '<i class="fas fa-times"></i> Cancel';

    // Add buttons to controls
    controls.appendChild(cancelBtn);
    controls.appendChild(saveBtn);

    // Clear content div and add edit elements
    contentDiv.innerHTML = '';
    contentDiv.appendChild(textarea);
    contentDiv.appendChild(controls);

    // Focus textarea
    textarea.focus();

    // Set up save button
    saveBtn.addEventListener('click', () => {
      const newContent = textarea.value.trim();

      // Don't save if empty
      if (!newContent) {
        return;
      }

      // Update message content
      contentDiv.innerHTML = this.formatContent(newContent);

      // Update original content data attribute
      messageDiv.dataset.originalContent = newContent;

      // Add edited indicator if content changed
      if (newContent !== originalContent) {
        const editedIndicator = document.createElement('div');
        editedIndicator.className = 'message-edited-indicator';
        editedIndicator.textContent = 'Edited';
        contentDiv.appendChild(editedIndicator);

        // Dispatch custom event for edited message
        const event = new CustomEvent('messageEdited', {
          detail: {
            messageId: messageDiv.dataset.messageId,
            originalContent: originalContent,
            newContent: newContent,
            messageElement: messageDiv
          }
        });
        document.dispatchEvent(event);
      }

      // Remove editing class
      messageDiv.classList.remove('editing');

      // Set up any code buttons
      this.setupInsertCodeButtons(contentDiv);
    });

    // Set up cancel button
    cancelBtn.addEventListener('click', () => {
      // Restore original content
      contentDiv.innerHTML = originalHtml;

      // Remove editing class
      messageDiv.classList.remove('editing');

      // Set up any code buttons
      this.setupInsertCodeButtons(contentDiv);
    });
  }

  /**
   * Set up event listeners for copy buttons (both code and prompt)
   * @param {HTMLElement} container - The container element to search for buttons
   */
  setupInsertCodeButtons(container) {
    console.log('Setting up copy buttons in container:', container);

    // Find all code copy buttons
    const codeCopyButtons = container.querySelectorAll('.copy-button');
    console.log('Found code copy buttons:', codeCopyButtons.length);

    // Find all prompt copy buttons
    const promptCopyButtons = container.querySelectorAll('.prompt-copy-btn');
    console.log('Found prompt copy buttons:', promptCopyButtons.length);

    // Set up code copy buttons
    codeCopyButtons.forEach(button => {
      console.log('Setting up code button:', button, 'data-target:', button.getAttribute('data-target'));

      button.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        console.log('Code copy button clicked:', button);

        try {
          // Get the target code element ID
          const codeId = button.getAttribute('data-target');
          console.log('Target code ID:', codeId);

          if (!codeId) {
            throw new Error('No target code element specified');
          }

          // Find the code element
          const codeElement = document.getElementById(codeId);
          console.log('Found code element:', codeElement);

          if (!codeElement) {
            throw new Error('Could not find code element with ID: ' + codeId);
          }

          // Get the code text
          const codeText = codeElement.textContent;
          console.log('Code to copy (first 50 chars):', codeText.substring(0, 50) + '...');

          // Show copying state
          button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Copying...';

          // Use our multi-method approach from clipboard-helper.js
          const successful = await copyTextToClipboard(codeText);

          console.log('Copy result:', successful ? 'success' : 'failed');

          if (successful) {
            // Show success state
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.classList.add('success');

            // Show status message
            this.showStatus('Code copied to clipboard!', false, 3000);

            // Reset button after 2 seconds
            setTimeout(() => {
              button.innerHTML = '<i class="fas fa-copy"></i> Copy';
              button.classList.remove('success');
            }, 2000);
          } else {
            throw new Error('All copy methods failed');
          }
        } catch (error) {
          console.error('Copy failed:', error);

          // Show error state
          button.innerHTML = '<i class="fas fa-times"></i> Failed';
          button.classList.add('error');

          // Show status message
          this.showStatus(`Error copying code: ${error.message}`, true, 3000);

          // Reset button after a delay
          setTimeout(() => {
            button.innerHTML = '<i class="fas fa-copy"></i> Copy';
            button.classList.remove('error');
          }, 2000);
        }
      });
    });

    // Set up prompt copy buttons
    promptCopyButtons.forEach(button => {
      console.log('Setting up prompt button:', button, 'data-target:', button.getAttribute('data-target'));

      button.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        console.log('Prompt copy button clicked:', button);

        try {
          // Get the target prompt element ID
          const promptId = button.getAttribute('data-target');
          console.log('Target prompt ID:', promptId);

          if (!promptId) {
            throw new Error('No target prompt element specified');
          }

          // Find the prompt element
          const promptElement = document.getElementById(promptId);
          console.log('Found prompt element:', promptElement);

          if (!promptElement) {
            throw new Error('Could not find prompt element with ID: ' + promptId);
          }

          // Get the prompt text
          const promptText = promptElement.textContent;
          console.log('Prompt to copy (first 50 chars):', promptText.substring(0, 50) + '...');

          // Show copying state
          button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Copying...';

          // Use our multi-method approach from clipboard-helper.js
          const successful = await copyTextToClipboard(promptText);

          console.log('Copy result:', successful ? 'success' : 'failed');

          if (successful) {
            // Show success state
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.classList.add('success');

            // Show status message
            this.showStatus('Prompt copied to clipboard!', false, 3000);

            // Reset button after 2 seconds
            setTimeout(() => {
              button.innerHTML = '<i class="fas fa-copy"></i> Copy';
              button.classList.remove('success');
            }, 2000);
          } else {
            throw new Error('All copy methods failed');
          }
        } catch (error) {
          console.error('Copy failed:', error);

          // Show error state
          button.innerHTML = '<i class="fas fa-times"></i> Failed';
          button.classList.add('error');

          // Show status message
          this.showStatus(`Error copying prompt: ${error.message}`, true, 3000);

          // Reset button after a delay
          setTimeout(() => {
            button.innerHTML = '<i class="fas fa-copy"></i> Copy';
            button.classList.remove('error');
          }, 2000);
        }
      });
    });
  }

  /**
   * Scroll chat to bottom
   */
  scrollToBottom() {
    if (this.chatMessages) {
      this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
  }

  /**
   * Escape HTML to prevent XSS
   * @param {string} unsafe - The unsafe string
   * @returns {string} - The escaped string
   */
  escapeHTML(unsafe) {
    return unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }



  /**
   * Show a confirmation dialog
   * @param {string} message - The message to display
   * @returns {Promise<boolean>} - Whether the user confirmed
   */
  async confirm(message) {
    return new Promise(resolve => {
      const result = window.confirm(message);
      resolve(result);
    });
  }

  /**
   * Get input from the user
   * @param {string} message - The prompt to display
   * @param {string} defaultValue - Default value
   * @returns {Promise<string|null>} - The user input or null if canceled
   */
  async prompt(message, defaultValue = '') {
    return new Promise(resolve => {
      const result = window.prompt(message, defaultValue);
      resolve(result);
    });
  }

  /**
   * Show a confirmation dialog to the user
   * @param {string} message - The message to display
   * @returns {Promise<boolean>} - True if confirmed, false if canceled
   */
  async confirm(message) {
    return new Promise(resolve => {
      const result = window.confirm(message);
      resolve(result);
    });
  }

  /**
   * Present a selection dialog with options
   * @param {string} message - The prompt to display
   * @param {string[]} options - Array of options to choose from
   * @param {string} defaultOption - Default selected option
   * @param {boolean} showUrls - Whether to show URLs in the options
   * @param {string[]} favicons - Optional array of favicon URLs
   * @returns {Promise<string|null>} - The selected option or null if canceled
   */
  async selectFromOptions(message, options, defaultOption = '', showUrls = false, favicons = []) {
    return new Promise(resolve => {
      // Create modal container
      const modal = document.createElement('div');
      modal.className = 'select-modal';

      // Create modal content
      const modalContent = document.createElement('div');
      modalContent.className = 'select-modal-content';

      // Add header
      const header = document.createElement('h3');
      header.textContent = message;
      modalContent.appendChild(header);

      // Add search input
      const searchInput = document.createElement('input');
      searchInput.type = 'text';
      searchInput.placeholder = 'Search tabs...';
      searchInput.className = 'select-search';
      modalContent.appendChild(searchInput);

      // Add options list
      const optionsList = document.createElement('div');
      optionsList.className = 'select-options';

      // Create options
      const createOptions = (filterText = '') => {
        // Clear existing options
        optionsList.innerHTML = '';

        // Filter options based on search text
        const filteredOptions = filterText
          ? options.filter(opt => opt.toLowerCase().includes(filterText.toLowerCase()))
          : options;

        if (filteredOptions.length === 0) {
          const noResults = document.createElement('div');
          noResults.className = 'no-results';
          noResults.innerHTML = '<i class="fas fa-search"></i> No matching tabs found';
          optionsList.appendChild(noResults);
          return;
        }

        // Group tabs by domain
        const domainGroups = {};

        filteredOptions.forEach((option, index) => {
          // Extract domain from option (assuming URL is in parentheses)
          let domain = 'Other';
          const urlMatch = option.match(/\((https?:\/\/[^\/]+)/i);

          if (urlMatch && urlMatch[1]) {
            try {
              const url = new URL(urlMatch[1]);
              domain = url.hostname;
            } catch (e) {
              console.error('Error parsing URL:', e);
            }
          }

          if (!domainGroups[domain]) {
            domainGroups[domain] = [];
          }

          domainGroups[domain].push({
            option,
            index,
            favicon: favicons && favicons[index] ? favicons[index] : null
          });
        });

        // Add options grouped by domain
        Object.keys(domainGroups).sort().forEach(domain => {
          // Add domain header if there's more than one domain
          if (Object.keys(domainGroups).length > 1) {
            const domainHeader = document.createElement('div');
            domainHeader.className = 'domain-group-header';
            domainHeader.innerHTML = `<i class="fas fa-globe"></i> ${domain}`;
            optionsList.appendChild(domainHeader);
          }

          // Add tabs for this domain
          domainGroups[domain].forEach(item => {
            const optionElement = document.createElement('div');
            optionElement.className = 'select-option';
            if (item.option === defaultOption) {
              optionElement.classList.add('selected');
            }

            // Add favicon if available
            const faviconImg = document.createElement('img');
            if (item.favicon) {
              faviconImg.src = item.favicon;
            } else {
              faviconImg.src = '../images/globe-icon.svg';
            }
            faviconImg.className = 'option-favicon';
            faviconImg.onerror = () => {
              // Replace with default icon if favicon fails to load
              faviconImg.src = '../images/globe-icon.svg';
            };
            optionElement.appendChild(faviconImg);

            // Create text container
            const textContainer = document.createElement('div');
            textContainer.style.flex = '1';
            textContainer.style.overflow = 'hidden';

            // Add tab title
            const textSpan = document.createElement('div');
            textSpan.className = 'option-text';

            // Extract title (remove URL part if present)
            let title = item.option;
            if (showUrls) {
              const urlIndex = title.lastIndexOf(' (');
              if (urlIndex > 0) {
                title = title.substring(0, urlIndex);
              }
            }
            textSpan.textContent = title;
            textContainer.appendChild(textSpan);

            // Add URL if needed
            if (showUrls) {
              const urlMatch = item.option.match(/\((https?:\/\/[^\)]+)\)/i);
              if (urlMatch && urlMatch[1]) {
                const urlSpan = document.createElement('div');
                urlSpan.className = 'option-url';
                urlSpan.textContent = urlMatch[1];
                textContainer.appendChild(urlSpan);
              }
            }

            optionElement.appendChild(textContainer);

            optionElement.addEventListener('click', () => {
              // Update selected option
              const allOptions = optionsList.querySelectorAll('.select-option');
              allOptions.forEach(opt => opt.classList.remove('selected'));
              optionElement.classList.add('selected');
              selectedOption = item.option;

              // Update scan button
              updateScanButton();
            });

            optionsList.appendChild(optionElement);
          });
        });
      };

      // Initialize options
      createOptions();

      // Add search functionality
      searchInput.addEventListener('input', () => {
        createOptions(searchInput.value);
        selectedOption = null;
        updateScanButton();
      });

      // Add options to modal
      modalContent.appendChild(optionsList);

      // Add buttons container
      const buttonsContainer = document.createElement('div');
      buttonsContainer.className = 'select-buttons';

      // Add scan button
      const scanButton = document.createElement('button');
      scanButton.className = 'select-scan-btn';
      scanButton.innerHTML = '<i class="fas fa-link"></i> Scan Selected Tab';
      scanButton.disabled = true; // Initially disabled until selection

      // Add cancel button
      const cancelButton = document.createElement('button');
      cancelButton.className = 'select-cancel-btn';
      cancelButton.textContent = 'Cancel';

      // Add buttons to container
      buttonsContainer.appendChild(scanButton);
      buttonsContainer.appendChild(cancelButton);

      // Add buttons container to modal
      modalContent.appendChild(buttonsContainer);

      // Add click handlers
      cancelButton.addEventListener('click', () => {
        resolve(null);
        document.body.removeChild(modal);
      });

      // Add modal to body
      modal.appendChild(modalContent);
      document.body.appendChild(modal);

      // Track selected option
      let selectedOption = defaultOption;

      // Update scan button state when an option is selected
      const updateScanButton = () => {
        if (selectedOption) {
          scanButton.disabled = false;
          scanButton.addEventListener('click', () => {
            resolve(selectedOption);
            document.body.removeChild(modal);
          }, { once: true });
        } else {
          scanButton.disabled = true;
        }
      };

      // Initialize scan button state
      updateScanButton();

      // Focus search input
      setTimeout(() => searchInput.focus(), 100);

      // Close modal if clicked outside
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          resolve(null);
          document.body.removeChild(modal);
        }
      });

      // Handle keyboard navigation
      document.addEventListener('keydown', function handleKeyDown(e) {
        if (e.key === 'Escape') {
          resolve(null);
          document.body.removeChild(modal);
          document.removeEventListener('keydown', handleKeyDown);
        } else if (e.key === 'Enter' && selectedOption) {
          resolve(selectedOption);
          document.body.removeChild(modal);
          document.removeEventListener('keydown', handleKeyDown);
        }
      });
    });
  }

  /**
   * Show loading indicator and message in chat
   * @param {string} message - The loading message to display
   * @param {boolean} showInChat - Whether to show the loading indicator in the chat (default: true)
   */
  showLoading(message, showInChat = true) {
    // Remove any existing loading indicators first
    this.removeLoadingIndicators();

    if (showInChat) {
      // Just use plain text for the loading message
      const loadingContent = `${message}...`;

      // Add to chat as AI message
      this.addMessageToChat(loadingContent, 'ai', 'loading-message');
    }

    // Update status bar with loading message
    this.showStatus(`${message}...`);
  }

  /**
   * Show loading indicator only in status bar (not in chat)
   * @param {string} message - The loading message to display
   */
  showStatusLoading(message) {
    // Just show in status bar, not in chat
    this.showLoading(message, false);

    // Add loading class to status message
    this.showStatus(message, false, 0, true);
  }

  /**
   * Remove any existing loading indicators from the chat
   */
  removeLoadingIndicators() {
    // Remove both old and new loading indicators
    const loadingIndicators = document.querySelectorAll('.loading-indicator, .loading-box');
    if (loadingIndicators.length > 0) {
      loadingIndicators.forEach(indicator => {
        const parentMessage = indicator.closest('.message');
        if (parentMessage) {
          parentMessage.remove();
        }
      });
    }

    // Also remove messages with the loading-message class
    const loadingMessages = document.querySelectorAll('.loading-message');
    if (loadingMessages.length > 0) {
      loadingMessages.forEach(message => {
        message.remove();
      });
    }
  }

  /**
   * Show a result in the chat
   * @param {string} content - The content to display
   * @param {string} title - Optional title for the result
   */
  showResult(content, title = 'Result') {
    // Remove any loading indicators
    this.removeLoadingIndicators();

    // Check if this is an error message
    const isError = title === 'Error' || title.includes('Error');

    // Format the content for errors to be more user-friendly
    let displayContent = content;
    if (isError) {
      // Remove the "Error:" prefix if it exists
      displayContent = displayContent.replace(/^Error:\s*/i, '');

      // Make the first letter uppercase
      displayContent = displayContent.charAt(0).toUpperCase() + displayContent.slice(1);

      // Add a helpful suggestion for PDF errors
      if (displayContent.includes('PDF document')) {
        displayContent += '\n\nTip: Make sure you have a PDF file open in your browser tab before using this feature.';
      }

      // Create a simple error message format
      const errorHeader = `${title}:\n\n`;
      const errorContent = errorHeader + displayContent;

      // Add as AI message
      this.addMessageToChat(errorContent, 'ai', 'error-message');

      return;
    }

    // For non-error results, format with a title
    const formattedContent = `${title}:\n\n${displayContent}`;

    // Add as AI message
    this.addMessageToChat(formattedContent, 'ai');
  }

  /**
   * Add a translation directly to the chat without the result box
   * @param {string} content - The translated content
   * @param {string} targetLanguage - The language the content was translated to
   */
  addTranslationToChat(content, targetLanguage) {
    // Remove any loading indicators
    this.removeLoadingIndicators();

    // Check if we have the enhanced translation UI available
    if (window.translateUIManager) {
      try {
        // Create an enhanced translation box
        const translationBox = window.translateUIManager.createEnhancedTranslationBox(content, targetLanguage);

        // Create a message div
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message';
        messageDiv.dataset.messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

        // Create avatar
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        const aiLogo = document.createElement('img');
        aiLogo.src = '../images/icon.png';
        aiLogo.alt = 'Browzy AI';
        aiLogo.className = 'infinity-logo';
        avatarDiv.appendChild(aiLogo);

        // Create content div
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.appendChild(translationBox);

        // Assemble the message
        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);

        // Add to chat
        this.chatMessages.appendChild(messageDiv);

        // Scroll to bottom
        this.scrollToBottom();

        // Show success status
        this.showStatus(`Translated to ${targetLanguage}`, false, 3000, false, 'success');

        return messageDiv;
      } catch (error) {
        console.error('Error using enhanced translation UI:', error);
        // Fall back to the standard implementation
      }
    }

    // Fallback to standard implementation if enhanced UI is not available or fails

    // Create a simple message with the translation
    const translationHeader = `Translating to ${targetLanguage}:\n\n`;
    const translationContent = translationHeader + content;

    // Add as AI message
    this.addMessageToChat(translationContent, 'ai');

    // Find the message we just added
    const messages = this.chatMessages.querySelectorAll('.message');
    const lastMessage = messages[messages.length - 1];

    if (lastMessage) {
      // Add a copy button to the message
      const copyBtn = document.createElement('button');
      copyBtn.className = 'message-action-btn';
      copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
      lastMessage.appendChild(copyBtn);

      // Add event listener for the copy button
      copyBtn.addEventListener('click', async () => {
        try {
          // Use our multi-method approach from clipboard-helper.js
          const successful = await copyTextToClipboard(content);

          if (successful) {
            copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            copyBtn.classList.add('success');

            // Show status message
            this.showStatus('Translation copied to clipboard!', false, 3000);

            // Reset button after 2 seconds
            setTimeout(() => {
              copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
              copyBtn.classList.remove('success');
            }, 2000);
          } else {
            throw new Error('Failed to copy translation');
          }
        } catch (error) {
          console.error('Copy failed:', error);

          // Show error state
          copyBtn.innerHTML = '<i class="fas fa-times"></i> Failed';
          copyBtn.classList.add('error');

          // Show status message
          this.showStatus(`Error copying translation: ${error.message}`, true, 3000);

          // Reset button after a delay
          setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
            copyBtn.classList.remove('error');
          }, 2000);
        }
      });
    }
  }
}
