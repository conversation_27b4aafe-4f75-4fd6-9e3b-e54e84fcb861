/* PDF Tools Enhanced UI Styles */

/* Main dialog container */
.pdf-tools-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.pdf-tools-dialog-content {
  background-color: #1a1a1a;
  background-image:
    radial-gradient(circle at 15% 85%, rgba(0, 216, 224, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(0, 166, 192, 0.08) 0%, transparent 50%),
    linear-gradient(to bottom, rgba(10, 15, 20, 0.95), rgba(5, 10, 15, 0.98));
  border-radius: 16px;
  width: 90%;
  max-width: 700px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(0, 166, 192, 0.2);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 216, 224, 0.15);
  animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 216, 224, 0.3) transparent;
}

.pdf-tools-dialog-content::-webkit-scrollbar {
  width: 6px;
}

.pdf-tools-dialog-content::-webkit-scrollbar-track {
  background: transparent;
}

.pdf-tools-dialog-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 216, 224, 0.2);
  border-radius: 6px;
}

.pdf-tools-dialog-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 216, 224, 0.4);
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Header */
.pdf-tools-dialog-header {
  background: linear-gradient(135deg, rgba(0, 166, 192, 0.9), rgba(0, 216, 224, 0.8));
  padding: 18px 24px;
  border-radius: 16px 16px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
}

.pdf-tools-dialog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  z-index: 0;
}

.pdf-tools-dialog-header h3 {
  margin: 0;
  font-size: 20px;
  display: flex;
  align-items: center;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
  letter-spacing: 0.5px;
}

.pdf-tools-dialog-header h3 i {
  margin-right: 12px;
  font-size: 22px;
}

.pdf-tools-close-btn {
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.pdf-tools-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: rotate(90deg);
}

/* Body */
.pdf-tools-dialog-body {
  padding: 24px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 216, 224, 0.3) transparent;
}

.pdf-tools-dialog-body::-webkit-scrollbar {
  width: 6px;
}

.pdf-tools-dialog-body::-webkit-scrollbar-track {
  background: transparent;
}

.pdf-tools-dialog-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 216, 224, 0.2);
  border-radius: 6px;
}

.pdf-tools-dialog-body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 216, 224, 0.4);
}

/* Sections */
.pdf-tools-section {
  margin-bottom: 30px;
  animation: fadeIn 0.5s ease;
  position: relative;
}

.pdf-tools-section::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, rgba(0, 216, 224, 0.2), transparent);
}

.pdf-tools-section:last-child::after {
  display: none;
}

.pdf-tools-section h4 {
  margin: 0 0 12px 0;
  color: var(--primary-light);
  font-size: 18px;
  display: flex;
  align-items: center;
  font-weight: 600;
  position: relative;
}

.pdf-tools-section h4 i {
  margin-right: 12px;
  color: var(--primary-color);
  font-size: 20px;
}

.pdf-tools-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 0 0 20px 0;
  padding-left: 32px;
  line-height: 1.5;
}

/* Buttons */
.pdf-tools-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding-left: 32px;
}

.pdf-tool-btn {
  background-color: rgba(10, 15, 20, 0.6);
  border: 1px solid rgba(0, 216, 224, 0.15);
  border-radius: 12px;
  padding: 16px;
  color: white;
  cursor: pointer;
  text-align: left;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.pdf-tool-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
  opacity: 0.8;
}

.pdf-tool-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25), 0 0 15px rgba(0, 216, 224, 0.1);
  border-color: rgba(0, 216, 224, 0.3);
  background-color: rgba(0, 166, 192, 0.1);
}

.pdf-tool-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.btn-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.btn-header i {
  margin-right: 12px;
  color: var(--primary-light);
  width: 20px;
  text-align: center;
  font-size: 18px;
}

.btn-header span {
  font-weight: 600;
  font-size: 16px;
}

.btn-description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  padding-left: 32px;
  line-height: 1.4;
}

/* Disabled state */
.pdf-tool-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  background-color: rgba(10, 15, 20, 0.3);
}

.pdf-tool-btn.disabled::before {
  opacity: 0.3;
}

/* Loading state */
.pdf-tool-btn.loading {
  background: linear-gradient(135deg, rgba(0, 166, 192, 0.1), rgba(72, 215, 206, 0.05));
  pointer-events: none;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 166, 192, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(0, 166, 192, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 166, 192, 0); }
}

.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-light);
  padding: 10px;
}

.btn-loading i {
  margin-right: 10px;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Footer */
.pdf-tools-dialog-footer {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 216, 224, 0.15);
  background: rgba(10, 15, 20, 0.6);
  border-radius: 0 0 16px 16px;
}

.pdf-status-message {
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  background: rgba(10, 15, 20, 0.6);
  color: rgba(255, 255, 255, 0.8);
}

.pdf-status-message i {
  margin-right: 10px;
  font-size: 16px;
}

.pdf-status-success {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 3px solid #4CAF50;
  color: #4CAF50;
}

.pdf-status-error {
  background-color: rgba(244, 67, 54, 0.1);
  border-left: 3px solid #F44336;
  color: #F44336;
}

.pdf-status-warning {
  background-color: rgba(255, 152, 0, 0.1);
  border-left: 3px solid #FF9800;
  color: #FF9800;
}

.pdf-status-info {
  background-color: rgba(0, 166, 192, 0.1);
  border-left: 3px solid var(--primary-color);
  color: var(--primary-light);
}

/* Query Input Styles */
.pdf-query-input-container {
  display: flex;
  margin-bottom: 20px;
  position: relative;
  padding-left: 32px;
}

.pdf-query-input {
  flex: 1;
  background-color: rgba(10, 15, 20, 0.6);
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: 12px;
  padding: 14px 50px 14px 16px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 15px;
  outline: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.pdf-query-input:focus {
  border-color: rgba(0, 216, 224, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 216, 224, 0.15), 0 4px 15px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.pdf-query-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.pdf-query-submit-btn {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.pdf-query-submit-btn:hover {
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.pdf-query-submit-btn:active {
  transform: translateY(-50%) scale(0.95);
}

.pdf-query-submit-btn i {
  font-size: 16px;
}

/* Example Questions */
.pdf-query-examples {
  padding-left: 32px;
  margin-bottom: 20px;
}

.pdf-query-examples p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 10px 0;
}

.pdf-query-example-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.pdf-query-example-chip {
  background-color: rgba(10, 15, 20, 0.6);
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.pdf-query-example-chip:hover {
  background-color: rgba(0, 166, 192, 0.1);
  border-color: rgba(0, 216, 224, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  color: var(--primary-light);
}

/* Query Results */
.pdf-query-result {
  background-color: rgba(10, 15, 20, 0.6);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0 0 32px;
  border-left: 3px solid var(--primary-color);
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 216, 224, 0.3) transparent;
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pdf-query-result::-webkit-scrollbar {
  width: 6px;
}

.pdf-query-result::-webkit-scrollbar-track {
  background: transparent;
}

.pdf-query-result::-webkit-scrollbar-thumb {
  background-color: rgba(0, 216, 224, 0.2);
  border-radius: 6px;
}

.pdf-query-result::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 216, 224, 0.4);
}

.pdf-query-result-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: var(--primary-light);
  font-size: 16px;
}

.pdf-query-result-loading i {
  margin-right: 12px;
  animation: spin 1.5s linear infinite;
}

.pdf-query-result-content {
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  line-height: 1.6;
}

/* Query History */
.pdf-query-history {
  margin: 20px 0 0 32px;
}

.pdf-query-history h5 {
  font-size: 16px;
  color: var(--primary-light);
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
}

.pdf-query-history h5 i {
  margin-right: 10px;
}

.pdf-query-history-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 216, 224, 0.3) transparent;
}

.pdf-query-history-items::-webkit-scrollbar {
  width: 6px;
}

.pdf-query-history-items::-webkit-scrollbar-track {
  background: transparent;
}

.pdf-query-history-items::-webkit-scrollbar-thumb {
  background-color: rgba(0, 216, 224, 0.2);
  border-radius: 6px;
}

.pdf-query-history-item {
  background-color: rgba(10, 15, 20, 0.6);
  border-radius: 10px;
  padding: 12px 16px;
  border-left: 2px solid var(--primary-color);
  transition: all 0.3s ease;
  cursor: pointer;
}

.pdf-query-history-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  background-color: rgba(0, 166, 192, 0.05);
}

.pdf-query-history-question {
  color: var(--primary-light);
  font-size: 14px;
  margin-bottom: 6px;
  font-weight: 500;
}

.pdf-query-history-answer {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
