'use strict';

/**
 * Enhanced Intent Classifier
 * A more sophisticated NLP engine for classifying search intents
 */
class EnhancedIntentClassifier {
  constructor() {
    // Initialize the classifier
    this.dataset = new SearchQueryDataset();
    this.intentPatterns = {};
    this.conversationalPatterns = [];
    this.keywordWeights = {};
    this.platformKeywords = {};
    
    // Initialize the classifier with the dataset
    this.initializeClassifier();
    
    // Debug flag
    this.debug = false;
  }
  
  /**
   * Initialize the classifier with the dataset
   */
  initializeClassifier() {
    // Get the full dataset
    const fullDataset = this.dataset.getFullDataset();
    
    // Process each intent and its examples
    for (const [intent, examples] of Object.entries(fullDataset.intents)) {
      this.intentPatterns[intent] = [];
      this.keywordWeights[intent] = {};
      this.platformKeywords[intent] = new Set();
      
      // Process each example
      for (const example of examples) {
        // Add the example to the intent patterns
        this.intentPatterns[intent].push(example.query.toLowerCase());
        
        // Extract keywords and update weights
        const keywords = this.extractKeywords(example.query);
        for (const keyword of keywords) {
          if (!this.keywordWeights[intent][keyword]) {
            this.keywordWeights[intent][keyword] = 0;
          }
          this.keywordWeights[intent][keyword] += 1;
          this.platformKeywords[intent].add(keyword);
        }
      }
    }
    
    // Process conversational patterns
    this.conversationalPatterns = fullDataset.conversationalPatterns;
    
    if (this.debug) {
      console.log('Intent patterns:', this.intentPatterns);
      console.log('Keyword weights:', this.keywordWeights);
      console.log('Conversational patterns:', this.conversationalPatterns);
    }
  }
  
  /**
   * Extract keywords from a query
   * @param {string} query - The query to extract keywords from
   * @returns {Array} Array of keywords
   */
  extractKeywords(query) {
    // Convert to lowercase
    const lowercaseQuery = query.toLowerCase();
    
    // Remove punctuation and split into words
    const words = lowercaseQuery.replace(/[^\w\s]/g, '').split(/\s+/);
    
    // Filter out stopwords
    const stopwords = new Set([
      'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 
      'about', 'of', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 
      'have', 'has', 'had', 'do', 'does', 'did', 'can', 'could', 'will', 'would', 
      'should', 'may', 'might', 'must', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
      'me', 'him', 'her', 'us', 'them'
    ]);
    
    return words.filter(word => !stopwords.has(word) && word.length > 1);
  }
  
  /**
   * Classify a query to determine its intent
   * @param {string} query - The query to classify
   * @returns {Object} The classification result with intent and confidence
   */
  classifyIntent(query) {
    // Convert to lowercase
    const lowercaseQuery = query.toLowerCase();
    
    // Initialize scores for each intent
    const scores = {};
    for (const intent in this.intentPatterns) {
      scores[intent] = 0;
    }
    
    // 1. Check for exact matches in intent patterns
    for (const intent in this.intentPatterns) {
      for (const pattern of this.intentPatterns[intent]) {
        if (lowercaseQuery.includes(pattern)) {
          scores[intent] += 10; // High score for exact matches
          if (this.debug) console.log(`Exact match for intent ${intent}: ${pattern}`);
        }
      }
    }
    
    // 2. Check for conversational patterns
    for (const { pattern, intent } of this.conversationalPatterns) {
      if (lowercaseQuery.includes(pattern)) {
        scores[intent] += 8; // High score for conversational patterns
        if (this.debug) console.log(`Conversational pattern match for intent ${intent}: ${pattern}`);
      }
    }
    
    // 3. Check for platform-specific keywords
    const keywords = this.extractKeywords(query);
    for (const intent in this.keywordWeights) {
      for (const keyword of keywords) {
        if (this.keywordWeights[intent][keyword]) {
          scores[intent] += this.keywordWeights[intent][keyword];
          if (this.debug) console.log(`Keyword match for intent ${intent}: ${keyword} (weight: ${this.keywordWeights[intent][keyword]})`);
        }
      }
    }
    
    // 4. Check for explicit platform mentions
    const platformMentions = {
      youtube: ['youtube', 'yt', 'video', 'videos', 'channel'],
      instagram: ['instagram', 'insta', 'ig', 'gram', 'photo', 'photos'],
      twitter: ['twitter', 'tweet', 'tweets', 'x.com', 'x'],
      pinterest: ['pinterest', 'pin', 'pins', 'board', 'boards'],
      github: ['github', 'git', 'repo', 'repository', 'code'],
      stackoverflow: ['stackoverflow', 'stack overflow', 'coding', 'programming', 'code'],
      amazon: ['amazon', 'buy', 'purchase', 'shop', 'shopping'],
      spotify: ['spotify', 'music', 'song', 'songs', 'playlist', 'album'],
      google: ['google', 'search', 'find', 'look up', 'lookup']
    };
    
    for (const [platform, terms] of Object.entries(platformMentions)) {
      for (const term of terms) {
        if (lowercaseQuery.includes(term)) {
          scores[platform] += 5; // Bonus for explicit platform mentions
          if (this.debug) console.log(`Platform mention for ${platform}: ${term}`);
        }
      }
    }
    
    // Find the intent with the highest score
    let maxScore = 0;
    let bestIntent = 'google'; // Default to Google
    
    for (const intent in scores) {
      if (scores[intent] > maxScore) {
        maxScore = scores[intent];
        bestIntent = intent;
      }
    }
    
    // Calculate confidence (normalize score)
    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
    const confidence = totalScore > 0 ? maxScore / totalScore : 0;
    
    if (this.debug) {
      console.log('Query:', query);
      console.log('Scores:', scores);
      console.log('Best intent:', bestIntent, 'with confidence:', confidence);
    }
    
    return {
      intent: bestIntent,
      confidence: confidence,
      scores: scores
    };
  }
  
  /**
   * Extract the search query based on the detected intent
   * @param {string} query - The original query
   * @param {string} intent - The detected intent
   * @returns {string} The extracted search query
   */
  extractSearchQuery(query, intent) {
    // Convert to lowercase
    const lowercaseQuery = query.toLowerCase();
    
    // Platform-specific terms to remove
    const platformTerms = {
      youtube: ['youtube', 'yt', 'video', 'videos', 'channel', 'watch'],
      instagram: ['instagram', 'insta', 'ig', 'gram', 'photo', 'photos'],
      twitter: ['twitter', 'tweet', 'tweets', 'x.com', 'x'],
      pinterest: ['pinterest', 'pin', 'pins', 'board', 'boards'],
      github: ['github', 'git', 'repo', 'repository', 'code'],
      stackoverflow: ['stackoverflow', 'stack overflow', 'coding', 'programming', 'code'],
      amazon: ['amazon', 'buy', 'purchase', 'shop', 'shopping'],
      spotify: ['spotify', 'music', 'song', 'songs', 'playlist', 'album'],
      google: ['google', 'search', 'find', 'look up', 'lookup']
    };
    
    // Conversational prefixes to remove
    const conversationalPrefixes = [
      'show me', 'find', 'search for', 'look up', 'lookup', 'i want to see',
      'i want to find', 'i want to search for', 'i want to look up',
      'can you show me', 'can you find', 'can you search for',
      'please show me', 'please find', 'please search for'
    ];
    
    // Connector phrases to handle
    const connectorPhrases = [
      { regex: new RegExp(`(.+?)\\s+on\\s+${intent}`, 'i'), group: 1 },
      { regex: new RegExp(`(.+?)\\s+in\\s+${intent}`, 'i'), group: 1 },
      { regex: new RegExp(`(.+?)\\s+from\\s+${intent}`, 'i'), group: 1 },
      { regex: new RegExp(`(.+?)\\s+at\\s+${intent}`, 'i'), group: 1 },
      { regex: new RegExp(`${intent}\\s+(.+)`, 'i'), group: 1 }
    ];
    
    // First, try to extract using connector phrases
    for (const { regex, group } of connectorPhrases) {
      const match = lowercaseQuery.match(regex);
      if (match && match[group]) {
        let extractedQuery = match[group].trim();
        
        // Remove conversational prefixes
        for (const prefix of conversationalPrefixes) {
          if (extractedQuery.startsWith(prefix)) {
            extractedQuery = extractedQuery.substring(prefix.length).trim();
          }
        }
        
        if (this.debug) console.log(`Extracted query using connector phrase: ${extractedQuery}`);
        return extractedQuery;
      }
    }
    
    // If no connector phrase matched, remove platform terms and conversational prefixes
    let processedQuery = lowercaseQuery;
    
    // Remove platform-specific terms
    if (platformTerms[intent]) {
      for (const term of platformTerms[intent]) {
        const regex = new RegExp(`\\b${term}\\b`, 'gi');
        processedQuery = processedQuery.replace(regex, '');
      }
    }
    
    // Remove conversational prefixes
    for (const prefix of conversationalPrefixes) {
      if (processedQuery.startsWith(prefix)) {
        processedQuery = processedQuery.substring(prefix.length);
      }
    }
    
    // Remove connector words at the beginning or end
    const connectorWords = ['on', 'in', 'at', 'from', 'for', 'about', 'with'];
    for (const word of connectorWords) {
      processedQuery = processedQuery.replace(new RegExp(`^${word}\\s+`, 'i'), '');
      processedQuery = processedQuery.replace(new RegExp(`\\s+${word}$`, 'i'), '');
    }
    
    // Clean up extra spaces
    processedQuery = processedQuery.replace(/\s+/g, ' ').trim();
    
    if (this.debug) console.log(`Processed query after removing terms: ${processedQuery}`);
    
    return processedQuery || query; // Return original query if processed query is empty
  }
  
  /**
   * Enable or disable debug mode
   * @param {boolean} enable - Whether to enable debug mode
   */
  setDebug(enable) {
    this.debug = enable;
  }
}

// Export the class
window.EnhancedIntentClassifier = EnhancedIntentClassifier;
