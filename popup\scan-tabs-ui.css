/* Scan Tabs Enhanced UI Styles */

/* Modal container */
.select-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Modal content */
.select-modal-content {
  background-color: #1a1a1a;
  background-image: 
    radial-gradient(circle at 15% 85%, rgba(0, 216, 224, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(0, 166, 192, 0.08) 0%, transparent 50%),
    linear-gradient(to bottom, rgba(10, 15, 20, 0.95), rgba(5, 10, 15, 0.98));
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(0, 166, 192, 0.2);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 216, 224, 0.15);
  animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 216, 224, 0.3) transparent;
  padding: 24px;
}

.select-modal-content::-webkit-scrollbar {
  width: 6px;
}

.select-modal-content::-webkit-scrollbar-track {
  background: transparent;
}

.select-modal-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 216, 224, 0.2);
  border-radius: 6px;
}

.select-modal-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 216, 224, 0.4);
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Header */
.select-modal-content h3 {
  margin: 0 0 20px 0;
  font-size: 22px;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  position: relative;
}

.select-modal-content h3::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300a6c0"><path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

/* Search input */
.select-search {
  width: 100%;
  padding: 14px 16px;
  margin-bottom: 20px;
  background-color: rgba(10, 15, 20, 0.6);
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.select-search:focus {
  outline: none;
  border-color: rgba(0, 216, 224, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 216, 224, 0.15), 0 4px 15px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.select-search::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Options container */
.select-options {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid rgba(0, 216, 224, 0.1);
  background-color: rgba(10, 15, 20, 0.3);
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 216, 224, 0.3) transparent;
}

.select-options::-webkit-scrollbar {
  width: 6px;
}

.select-options::-webkit-scrollbar-track {
  background: transparent;
}

.select-options::-webkit-scrollbar-thumb {
  background-color: rgba(0, 216, 224, 0.2);
  border-radius: 6px;
}

.select-options::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 216, 224, 0.4);
}

/* Domain group header */
.domain-group-header {
  padding: 10px 15px;
  background-color: rgba(0, 166, 192, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
  display: flex;
  align-items: center;
}

.domain-group-header i {
  margin-right: 8px;
  color: rgba(0, 216, 224, 0.8);
}

/* Option item */
.select-option {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.select-option:last-child {
  border-bottom: none;
}

.select-option:hover {
  background-color: rgba(0, 166, 192, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.select-option.selected {
  background-color: rgba(0, 166, 192, 0.15);
  border-left: 3px solid rgba(0, 216, 224, 0.8);
}

/* Favicon */
.option-favicon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  flex-shrink: 0;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px;
  transition: all 0.3s ease;
}

.select-option:hover .option-favicon {
  transform: scale(1.1);
}

/* Option text */
.option-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  transition: all 0.3s ease;
}

.select-option:hover .option-text {
  color: white;
}

/* Option URL */
.option-url {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* No results message */
.no-results {
  padding: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 15px;
}

/* Cancel button */
.select-cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  transition: all 0.3s ease;
  align-self: flex-end;
}

.select-cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* Scan button */
.select-scan-btn {
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-right: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.select-scan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Button container */
.select-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

/* Loading animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-indicator {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 216, 224, 0.3);
  border-top: 2px solid rgba(0, 216, 224, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}
