'use strict';

/**
 * Utility functions for searching webpage content
 */
class SearchUtils {
  /**
   * Search webpage content for a specific term
   * @param {string} content - The webpage content to search
   * @param {string} searchTerm - The term to search for
   * @returns {Object} - Search results with matches and context
   */
  static searchWebpageContent(content, searchTerm) {
    if (!content || !searchTerm) {
      return { matches: [], topicSummary: '' };
    }
    
    // Create a case-insensitive regex for the search term
    const searchRegex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    
    // Split content into paragraphs for better context
    const paragraphs = content.split(/\n\n+/);
    
    // Find matches with context
    const matches = [];
    const topics = new Set();
    
    paragraphs.forEach(paragraph => {
      if (searchRegex.test(paragraph)) {
        // Reset the regex lastIndex
        searchRegex.lastIndex = 0;
        
        // Get the context (the paragraph with the match)
        let context = paragraph.trim();
        
        // Highlight the search term in the context
        context = context.replace(searchRegex, match => `**${match}**`);
        
        // Extract potential topics from the paragraph
        const words = paragraph.split(/\s+/);
        const potentialTopics = words.filter(word => 
          word.length > 4 && 
          !['about', 'these', 'those', 'their', 'there', 'where', 'which', 'would', 'could', 'should'].includes(word.toLowerCase())
        );
        
        // Add the most frequent words as potential topics
        potentialTopics.slice(0, 3).forEach(topic => topics.add(topic));
        
        // Add the match to the results
        matches.push({
          context: context
        });
      }
    });
    
    // Create a topic summary
    const topicArray = Array.from(topics).slice(0, 5);
    const topicSummary = topicArray.join(', ');
    
    return {
      matches: matches,
      topicSummary: topicSummary || 'various topics'
    };
  }
}
