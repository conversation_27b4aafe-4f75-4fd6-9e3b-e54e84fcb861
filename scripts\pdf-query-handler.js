/**
 * PDF Query Handler
 * Handles processing of user queries about PDFs using OpenRouter API
 */
class PDFQueryHandler {
  constructor(apiManager) {
    this.apiManager = apiManager;
    this.currentPdfContent = null;
    this.currentPdfUrl = null;
    this.processingState = {
      isProcessing: false,
      progress: 0,
      error: null
    };
  }

  /**
   * Set the current PDF content
   * @param {string} content - The extracted PDF content
   * @param {string} url - The URL of the PDF
   */
  setPdfContent(content, url) {
    this.currentPdfContent = content;
    this.currentPdfUrl = url;
  }

  /**
   * Process a user query about the PDF
   * @param {string} query - The user's query
   * @param {Object} pdfInfo - Additional PDF information (metadata, structure)
   * @returns {Promise<Object>} - The response
   */
  async processQuery(query, pdfInfo = {}) {
    if (!this.currentPdfContent) {
      return {
        success: false,
        error: 'No PDF content available. Please load a PDF first.'
      };
    }

    try {
      this.processingState = {
        isProcessing: true,
        progress: 0,
        error: null
      };

      // Prepare the prompt for the AI
      const prompt = this.preparePrompt(query, pdfInfo);
      this.processingState.progress = 30;

      // Call the OpenRouter API
      const response = await this.callOpenRouterAPI(prompt);
      this.processingState.progress = 100;
      this.processingState.isProcessing = false;

      return {
        success: true,
        answer: response,
        query: query
      };
    } catch (error) {
      console.error('Error processing PDF query:', error);
      this.processingState.isProcessing = false;
      this.processingState.error = error.message;
      
      return {
        success: false,
        error: `Failed to process your query: ${error.message}`
      };
    }
  }

  /**
   * Prepare the prompt for the AI
   * @param {string} query - The user's query
   * @param {Object} pdfInfo - Additional PDF information
   * @returns {string} - The prepared prompt
   */
  preparePrompt(query, pdfInfo = {}) {
    // Extract metadata if available
    const metadata = pdfInfo.metadata || {};
    const title = metadata.title || 'Untitled PDF';
    const author = metadata.author || 'Unknown Author';
    
    // Prepare a system prompt that instructs the AI on how to respond
    const systemPrompt = `You are an AI assistant specialized in analyzing PDF documents and answering questions about them.
You have been provided with the content of a PDF document titled "${title}" by ${author}.
Your task is to answer the user's question based ONLY on the content of the PDF.
If the answer cannot be found in the PDF content, acknowledge this and don't make up information.
Format your response in a clear, concise manner. Use markdown formatting for better readability when appropriate.`;

    // Combine the system prompt, PDF content, and user query
    return `${systemPrompt}

PDF CONTENT:
${this.currentPdfContent.substring(0, 15000)}${this.currentPdfContent.length > 15000 ? '...(content truncated)' : ''}

USER QUERY: ${query}`;
  }

  /**
   * Call the OpenRouter API to process the query
   * @param {string} prompt - The prepared prompt
   * @returns {Promise<string>} - The AI response
   */
  async callOpenRouterAPI(prompt) {
    try {
      // Use the API manager to call OpenRouter
      const response = await this.apiManager.callOpenRouterAPI({
        messages: [
          {
            role: "system",
            content: "You are a helpful AI assistant specialized in analyzing PDF documents."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        model: "openai/gpt-4-turbo", // Use a capable model for document analysis
        max_tokens: 1000
      });

      // Extract and return the response text
      if (response && response.choices && response.choices.length > 0) {
        return response.choices[0].message.content;
      } else {
        throw new Error('Invalid response from OpenRouter API');
      }
    } catch (error) {
      console.error('Error calling OpenRouter API:', error);
      throw error;
    }
  }

  /**
   * Get the current processing state
   * @returns {Object} - The processing state
   */
  getProcessingState() {
    return { ...this.processingState };
  }
}

// Export the class
export default PDFQueryHandler;
