'use strict';

/**
 * Manages chat history functionality
 */
class HistoryManager {
  /**
   * Create a new History Manager
   * @param {StorageManager} storageManager - The Storage Manager instance
   * @param {ChatManager} chatManager - The Chat Manager instance
   * @param {UIManager} uiManager - The UI Manager instance
   */
  constructor(storageManager, chatManager, uiManager) {
    this.storageManager = storageManager;
    this.chatManager = chatManager;
    this.uiManager = uiManager;
    
    // DOM elements
    this.historyList = document.getElementById('historyList');
    this.clearAllHistoryBtn = document.getElementById('clearAllHistory');
    
    // Initialize event listeners
    this.initEventListeners();
  }
  
  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // Clear all history button
    if (this.clearAllHistoryBtn) {
      this.clearAllHistoryBtn.addEventListener('click', () => this.clearAllHistory());
    }
  }
  
  /**
   * Load and display chat history
   */
  async loadHistoryList() {
    try {
      // Clear current list
      if (this.historyList) {
        this.historyList.innerHTML = '';
      }
      
      // Get all sessions
      const sessions = await this.storageManager.getAllSessions();
      const sessionIds = Object.keys(sessions);
      
      if (sessionIds.length === 0) {
        // Show empty message
        this.historyList.innerHTML = '<div class="empty-history-message">No saved conversations yet</div>';
        return;
      }
      
      // Sort sessions by timestamp (newest first)
      sessionIds.sort((a, b) => {
        const timestampA = new Date(sessions[a].timestamp || 0);
        const timestampB = new Date(sessions[b].timestamp || 0);
        return timestampB - timestampA;
      });
      
      // Create history items
      sessionIds.forEach(id => {
        const session = sessions[id];
        if (!session || !session.history || session.history.length === 0) return;
        
        this.createHistoryItem(id, session);
      });
    } catch (error) {
      console.error('Error loading history:', error);
      this.uiManager.showStatus('Error loading history', true);
    }
  }
  
  /**
   * Create a history item element
   * @param {string} id - Session ID
   * @param {object} session - Session data
   */
  createHistoryItem(id, session) {
    // Create container
    const historyItem = document.createElement('div');
    historyItem.className = 'history-item';
    historyItem.dataset.id = id;
    
    // Format date
    const date = new Date(session.timestamp || Date.now());
    const formattedDate = this.formatDate(date);
    
    // Get title
    const title = session.title || 'Chat Session';
    
    // Get preview from first message
    let preview = '';
    if (session.history && session.history.length > 0) {
      const firstMsg = session.history[0];
      preview = this.stripHtml(firstMsg.content).substring(0, 100);
      if (preview.length === 100) preview += '...';
    }
    
    // Page info
    let pageInfo = '';
    if (session.pageInfo && session.pageInfo.title) {
      pageInfo = `<div class="history-item-page">${session.pageInfo.title}</div>`;
    }
    
    // Create HTML
    historyItem.innerHTML = `
      <div class="history-item-header">
        <div class="history-item-title">${this.escapeHTML(title)}</div>
        <div class="history-item-date">${formattedDate}</div>
      </div>
      <div class="history-item-preview">${this.escapeHTML(preview)}</div>
      ${pageInfo}
      <div class="history-item-actions">
        <button class="history-action-btn restore"><i class="fas fa-redo-alt"></i> Restore</button>
        <button class="history-action-btn delete"><i class="fas fa-trash-alt"></i> Delete</button>
      </div>
    `;
    
    // Add event listeners
    const restoreBtn = historyItem.querySelector('.restore');
    const deleteBtn = historyItem.querySelector('.delete');
    
    restoreBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.restoreSession(id);
    });
    
    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.deleteSession(id);
    });
    
    // Add to list
    this.historyList.appendChild(historyItem);
  }
  
  /**
   * Restore a chat session
   * @param {string} id - Session ID
   */
  async restoreSession(id) {
    try {
      const session = await this.storageManager.getSession(id);
      
      if (!session || !session.history) {
        this.uiManager.showStatus('Session not found', true);
        return;
      }
      
      // Confirm with user
      if (!confirm('This will replace your current chat. Continue?')) {
        return;
      }
      
      // Clear current chat
      this.chatManager.clearChat(false); // Don't show confirmation
      
      // Add messages from history
      session.history.forEach(msg => {
        this.chatManager.addMessageToChat(msg.content, msg.sender);
      });
      
      // Update chat history array
      this.chatManager.chatHistory = [...session.history];
      
      // Switch to chat tab
      document.getElementById('chatTab').click();
      
      this.uiManager.showStatus('Chat restored successfully');
    } catch (error) {
      console.error('Error restoring session:', error);
      this.uiManager.showStatus('Error restoring chat', true);
    }
  }
  
  /**
   * Delete a chat session
   * @param {string} id - Session ID
   */
  async deleteSession(id) {
    try {
      if (!confirm('Are you sure you want to delete this conversation?')) {
        return;
      }
      
      await this.storageManager.deleteSession(id);
      
      // Remove from UI
      const historyItem = this.historyList.querySelector(`[data-id="${id}"]`);
      if (historyItem) {
        historyItem.remove();
      }
      
      // Check if list is empty
      if (this.historyList.children.length === 0) {
        this.historyList.innerHTML = '<div class="empty-history-message">No saved conversations yet</div>';
      }
      
      this.uiManager.showStatus('Conversation deleted');
    } catch (error) {
      console.error('Error deleting session:', error);
      this.uiManager.showStatus('Error deleting conversation', true);
    }
  }
  
  /**
   * Clear all history
   */
  async clearAllHistory() {
    try {
      if (!confirm('Are you sure you want to delete all saved conversations? This cannot be undone.')) {
        return;
      }
      
      // Get all sessions
      const sessions = await this.storageManager.getAllSessions();
      const sessionIds = Object.keys(sessions);
      
      // Delete each session
      for (const id of sessionIds) {
        await this.storageManager.deleteSession(id);
      }
      
      // Update UI
      this.historyList.innerHTML = '<div class="empty-history-message">No saved conversations yet</div>';
      
      this.uiManager.showStatus('All conversations deleted');
    } catch (error) {
      console.error('Error clearing history:', error);
      this.uiManager.showStatus('Error clearing history', true);
    }
  }
  
  /**
   * Format a date for display
   * @param {Date} date - The date to format
   * @returns {string} - Formatted date string
   */
  formatDate(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    // Today: Show time
    if (diffDay < 1) {
      if (diffHour < 1) {
        if (diffMin < 1) {
          return 'Just now';
        }
        return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
      }
      return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
    }
    
    // Yesterday: Show "Yesterday"
    if (diffDay === 1) {
      return 'Yesterday';
    }
    
    // Within a week: Show day of week
    if (diffDay < 7) {
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      return days[date.getDay()];
    }
    
    // Older: Show date
    return date.toLocaleDateString();
  }
  
  /**
   * Strip HTML tags from a string
   * @param {string} html - HTML string
   * @returns {string} - Plain text
   */
  stripHtml(html) {
    if (!html) return '';
    
    // Create a temporary element
    const temp = document.createElement('div');
    temp.innerHTML = html;
    
    // Get text content
    return temp.textContent || temp.innerText || '';
  }
  
  /**
   * Escape HTML to prevent XSS
   * @param {string} unsafe - The unsafe string
   * @returns {string} - The escaped string
   */
  escapeHTML(unsafe) {
    if (!unsafe) return '';
    return unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
}
