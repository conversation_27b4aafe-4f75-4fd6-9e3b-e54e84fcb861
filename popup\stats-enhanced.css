/* Enhanced Stats UI - Focused Improvements */

/* Stats Header */
.stats-header {
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-header h2 {
  font-size: 1.5rem;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.stats-header h2 i {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.1);
  padding: 0.75rem;
  border-radius: 50%;
  font-size: 1.25rem;
}

.stats-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  margin: 0;
}

/* Summary Cards */
.stats-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.summary-card {
  background: rgba(30, 30, 30, 0.6);
  border-radius: 0.75rem;
  padding: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.summary-card:nth-child(1)::before {
  background: linear-gradient(90deg, #FF6B3C, #FF8F6C);
}

.summary-card:nth-child(2)::before {
  background: linear-gradient(90deg, #1EB980, #4DD8A5);
}

.summary-card:nth-child(3)::before {
  background: linear-gradient(90deg, #FFCF44, #FFE07D);
}

.summary-icon {
  margin-bottom: 1rem;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.summary-card:nth-child(1) .summary-icon {
  background: rgba(255, 107, 60, 0.1);
  color: #FF6B3C;
}

.summary-card:nth-child(2) .summary-icon {
  background: rgba(30, 185, 128, 0.1);
  color: #1EB980;
}

.summary-card:nth-child(3) .summary-icon {
  background: rgba(255, 207, 68, 0.1);
  color: #FFCF44;
}

.summary-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summary-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.summary-card:nth-child(1) .summary-value {
  color: #FF6B3C;
}

.summary-card:nth-child(2) .summary-value {
  color: #1EB980;
}

.summary-card:nth-child(3) .summary-value {
  color: #FFCF44;
}

.summary-subtext {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Usage Graph */
.usage-graph {
  background: rgba(30, 30, 30, 0.6);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.usage-graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}

.usage-graph-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.usage-graph-title i {
  color: #1EB980;
  font-size: 1rem;
}

.usage-graph-controls {
  display: flex;
  gap: 0.5rem;
}

.graph-period-selector {
  background: rgba(30, 185, 128, 0.1);
  border: 1px solid rgba(30, 185, 128, 0.2);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
}

.graph-period-selector:hover {
  background: rgba(30, 185, 128, 0.2);
}

.graph-period-selector.active {
  background: rgba(30, 185, 128, 0.3);
  font-weight: 600;
}

.graph-container {
  height: 200px;
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.graph-container::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 2rem;
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.03) 0%,
    rgba(255, 255, 255, 0.03) 25%,
    rgba(255, 255, 255, 0.02) 25%,
    rgba(255, 255, 255, 0.02) 50%,
    rgba(255, 255, 255, 0.01) 50%,
    rgba(255, 255, 255, 0.01) 75%,
    transparent 75%);
  pointer-events: none;
  z-index: 0;
}

.graph-bar-group {
  flex: 1;
  display: flex;
  gap: 2px;
  align-items: flex-end;
  position: relative;
  min-width: 2rem;
  height: 100%;
}

.graph-bar {
  flex: 1;
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: all 0.3s ease;
  z-index: 1;
}

.graph-bar-openai {
  background: linear-gradient(to top, #FF6B3C, #FF8F6C);
}

.graph-bar-gemini {
  background: linear-gradient(to top, #1EB980, #4DD8A5);
}

.graph-bar-openrouter {
  background: linear-gradient(to top, #FFCF44, #FFE07D);
}



.graph-bar-group:hover {
  transform: translateY(-5px);
}

.graph-bar-tooltip {
  position: absolute;
  top: -2.5rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(10, 10, 10, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  color: #fff;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 10;
}

.graph-bar-group:hover .graph-bar-tooltip {
  opacity: 1;
}

.graph-bar-label {
  position: absolute;
  bottom: -1.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  white-space: nowrap;
}

.graph-legend {
  display: flex;
  justify-content: center;
  gap: 1.25rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.legend-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 0.25rem;
}

.legend-openai {
  background: linear-gradient(90deg, #FF6B3C, #FF8F6C);
}

.legend-gemini {
  background: linear-gradient(90deg, #1EB980, #4DD8A5);
}

.legend-openrouter {
  background: linear-gradient(90deg, #FFCF44, #FFE07D);
}



/* Provider Stats */
.stats-section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  color: #fff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title i {
  color: #FF6B3C;
  font-size: 1rem;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.stats-card {
  background: rgba(30, 30, 30, 0.6);
  border-radius: 0.75rem;
  padding: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  bottom: 0;
}

.openrouter.stats-card::before {
  background: linear-gradient(to bottom, #FFCF44, #FFE07D);
}



.gemini.stats-card::before {
  background: linear-gradient(to bottom, #1EB980, #4DD8A5);
}

.openai.stats-card::before {
  background: linear-gradient(to bottom, #FF6B3C, #FF8F6C);
}

.provider-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.provider-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.openrouter .provider-icon {
  color: #FFCF44;
  background: rgba(255, 207, 68, 0.1);
}



.gemini .provider-icon {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.1);
}

.openai .provider-icon {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.1);
}

.provider-header h4 {
  margin: 0;
  font-size: 1.125rem;
  color: #fff;
  font-weight: 600;
}

.stats-metrics {
  display: flex;
  justify-content: space-around;
  background: rgba(10, 10, 10, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

.metric {
  text-align: center;
  padding: 0.5rem 0.75rem;
  flex: 1;
  position: relative;
}

.metric:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.metric-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fff;
}

.openrouter .metric-value {
  color: #FFCF44;
}



.gemini .metric-value {
  color: #1EB980;
}

.openai .metric-value {
  color: #FF6B3C;
}

/* Model Usage */
.models-section {
  margin-bottom: 1.5rem;
}

.models-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.models-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.models-title i {
  color: #FFCF44;
  font-size: 1rem;
}

.models-controls {
  display: flex;
  gap: 0.5rem;
}

.models-filter {
  background: rgba(255, 207, 68, 0.1);
  border: 1px solid rgba(255, 207, 68, 0.2);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
}

.models-filter:hover {
  background: rgba(255, 207, 68, 0.2);
}

.models-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
}

.model-column {
  background: rgba(30, 30, 30, 0.6);
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.model-column:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.model-column-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(10, 10, 10, 0.3);
  position: relative;
}

.model-column-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.model-column-header.openai::before {
  background: linear-gradient(90deg, #FF6B3C, #FF8F6C);
}

.model-column-header.gemini::before {
  background: linear-gradient(90deg, #1EB980, #4DD8A5);
}



.model-column-header.openrouter::before {
  background: linear-gradient(90deg, #FFCF44, #FFE07D);
}

.model-column-header.anthropic::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.model-column-header i {
  font-size: 1rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.model-column-header.openai i {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.1);
}

.model-column-header.gemini i {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.1);
}



.model-column-header.openrouter i {
  color: #FFCF44;
  background: rgba(255, 207, 68, 0.1);
}

.model-column-header.anthropic i {
  color: #9C27B0;
  background: rgba(156, 39, 176, 0.1);
}

.model-column-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
}

.model-column-header .model-count {
  margin-left: auto;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

.model-list {
  padding: 0.5rem 0;
  max-height: 200px;
  overflow-y: auto;
}

.model-list::-webkit-scrollbar {
  width: 4px;
}

.model-list::-webkit-scrollbar-track {
  background: rgba(10, 10, 10, 0.2);
}

.model-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.model-item {
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
  transition: background 0.2s ease;
}

.model-item:last-child {
  border-bottom: none;
}

.model-item:hover {
  background: rgba(255, 255, 255, 0.03);
}

/* Model usage indicators */
.model-item.has-usage {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
}

.model-item.has-usage::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
}

.model-item.low-usage::before {
  background: linear-gradient(to bottom, #4DD8A5, #1EB980);
}

.model-item.medium-usage::before {
  background: linear-gradient(to bottom, #FFE07D, #FFCF44);
}

.model-item.high-usage::before {
  background: linear-gradient(to bottom, #FF9D7D, #FF6B3C);
}

.model-count.has-usage {
  background: rgba(30, 185, 128, 0.2);
  color: #1EB980;
}

.model-name {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.model-usage {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  min-width: 1.5rem;
  text-align: center;
}

.openai .model-usage {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.1);
}

.gemini .model-usage {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.1);
}



.openrouter .model-usage {
  color: #FFCF44;
  background: rgba(255, 207, 68, 0.1);
}

.anthropic .model-usage {
  color: #9C27B0;
  background: rgba(156, 39, 176, 0.1);
}

/* Cost Estimation */
.cost-estimation {
  background: rgba(30, 30, 30, 0.6);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.cost-estimation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #FF6B3C, #1EB980);
}

.cost-header {
  margin-bottom: 1.25rem;
}

.cost-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.cost-title i {
  color: #FF6B3C;
  font-size: 1rem;
}

.cost-disclaimer {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.cost-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1rem;
}

.cost-card {
  background: rgba(10, 10, 10, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.03);
  text-align: center;
  transition: transform 0.2s ease;
}

.cost-card:hover {
  transform: translateY(-3px);
}

.cost-provider {
  font-size: 0.875rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.cost-provider i {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 0.75rem;
}

.cost-provider.openai i {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.1);
}

.cost-provider.gemini i {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.1);
}

.cost-provider.anthropic i {
  color: #9C27B0;
  background: rgba(156, 39, 176, 0.1);
}



.cost-value {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.cost-provider.openai + .cost-value {
  color: #FF6B3C;
}

.cost-provider.gemini + .cost-value {
  color: #1EB980;
}

.cost-provider.anthropic + .cost-value {
  color: #9C27B0;
}



.cost-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.total-cost {
  margin-top: 1.25rem;
  background: rgba(10, 10, 10, 0.4);
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.total-cost-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
}

.total-cost-value {
  font-size: 1.75rem;
  font-weight: 700;
  background: linear-gradient(90deg, #FF6B3C, #1EB980);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Reset Button */
.reset-stats {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.reset-btn {
  background: rgba(239, 68, 68, 0.1);
  color: #fff;
  border: 1px solid rgba(239, 68, 68, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reset-btn i {
  color: #ef4444;
}

.reset-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-2px);
}

.reset-btn:active {
  transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .stats-summary {
    grid-template-columns: 1fr;
  }

  .cost-summary {
    grid-template-columns: 1fr;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .models-container {
    grid-template-columns: 1fr;
  }

  .usage-graph-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .models-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .stats-summary {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .models-container {
    grid-template-columns: repeat(2, 1fr);
  }
}
