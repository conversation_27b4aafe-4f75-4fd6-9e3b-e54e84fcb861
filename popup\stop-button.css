/* Stop Button Styling */
.stop-button-container {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
  display: none;
  animation: fadeIn 0.3s ease-out;
}

.stop-button {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  background-color: rgba(255, 59, 48, 0.9);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.stop-button:hover {
  background-color: rgba(255, 59, 48, 1);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.stop-button:active {
  transform: scale(0.95);
}

.stop-button i {
  font-size: 22px;
}

.stop-button-container.visible {
  display: flex;
  animation: fadeIn 0.3s ease-out;
}

/* Pulse animation for the stop button */
.stop-button.pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 59, 48, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 59, 48, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 59, 48, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stop-button-container {
    bottom: 70px;
    right: 15px;
  }

  .stop-button {
    width: 42px;
    height: 42px;
  }
}

/* Sidebar mode adjustments */
.sidebar-mode .stop-button-container {
  bottom: 70px;
  right: 15px;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .stop-button {
    background-color: rgba(255, 69, 58, 0.9);
  }

  .stop-button:hover {
    background-color: rgba(255, 69, 58, 1);
  }
}
