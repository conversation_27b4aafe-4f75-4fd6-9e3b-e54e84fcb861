<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Add strict Content Security Policy -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://*.openai.com https://openrouter.ai https://*.openrouter.ai https://*.googleapis.com https://*.google.com https://*.anthropic.com https://*.mistral.ai https://*.deepseek.com https://*.cohere.com http://localhost:5000 http://localhost:5173">
  <!-- Disable all client hints features -->
  <meta http-equiv="Permissions-Policy" content="interest-cohort=(), ch-dpr=(), ch-viewport-width=(), ch-viewport-height=(), ch-width=(), ch-height=(), ch-device-memory=(), ch-ua=(), ch-ua-arch=(), ch-ua-platform=(), ch-ua-model=(), ch-ua-mobile=(), ch-ua-full-version=(), ch-ua-platform-version=(), ch-prefers-color-scheme=(), ch-downlink=(), ch-ect=(), ch-rtt=(), ch-save-data=(), ch-ua-bitness=(), ch-ua-wow64=(), clipboard-read=(), clipboard-write=()">
  <title>Authentication Callback</title>
  <style>
    body {
      font-family: 'Montserrat', sans-serif;
      background-color: #121212;
      color: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    
    .container {
      max-width: 600px;
      padding: 30px;
      background-color: #2D2D2D;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    
    h1 {
      color: #00b4d8;
      margin-bottom: 20px;
    }
    
    p {
      margin-bottom: 20px;
      line-height: 1.6;
    }
    
    .spinner {
      border: 4px solid rgba(0, 180, 216, 0.3);
      border-radius: 50%;
      border-top: 4px solid #00b4d8;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .success {
      color: #4CAF50;
    }
    
    .error {
      color: #F44336;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Authentication</h1>
    <div id="status">
      <p>Processing authentication...</p>
      <div class="spinner"></div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const statusElement = document.getElementById('status');
      
      // Function to parse URL parameters
      function getUrlParams() {
        const params = {};
        const queryString = window.location.search.substring(1);
        const hashString = window.location.hash.substring(1);
        
        // Parse query parameters
        if (queryString) {
          const pairs = queryString.split('&');
          for (let i = 0; i < pairs.length; i++) {
            const pair = pairs[i].split('=');
            params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
          }
        }
        
        // Parse hash parameters
        if (hashString) {
          const pairs = hashString.split('&');
          for (let i = 0; i < pairs.length; i++) {
            const pair = pairs[i].split('=');
            params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
          }
        }
        
        return params;
      }
      
      // Process the authentication callback
      function processCallback() {
        const params = getUrlParams();
        
        // Check for Trello token
        if (params.token) {
          // Send the token to the extension
          chrome.runtime.sendMessage({
            action: 'trello_auth_callback',
            token: params.token
          });
          
          statusElement.innerHTML = `
            <p class="success">Authentication successful!</p>
            <p>You can now close this window and return to the extension.</p>
          `;
          
          return;
        }
        
        // Check for Google code
        if (params.code) {
          // Send the code to the extension
          chrome.runtime.sendMessage({
            action: 'google_auth_callback',
            code: params.code
          });
          
          statusElement.innerHTML = `
            <p class="success">Authentication successful!</p>
            <p>You can now close this window and return to the extension.</p>
          `;
          
          return;
        }
        
        // Check for errors
        if (params.error) {
          // Send the error to the extension
          chrome.runtime.sendMessage({
            action: 'auth_error',
            error: params.error
          });
          
          statusElement.innerHTML = `
            <p class="error">Authentication failed: ${params.error}</p>
            <p>Please close this window and try again.</p>
          `;
          
          return;
        }
        
        // No recognized parameters
        statusElement.innerHTML = `
          <p class="error">Authentication failed: No valid authentication parameters found.</p>
          <p>Please close this window and try again.</p>
        `;
        
        // Send a generic error to the extension
        chrome.runtime.sendMessage({
          action: 'auth_error',
          error: 'No valid authentication parameters found'
        });
      }
      
      // Process the callback after a short delay
      setTimeout(processCallback, 500);
    });
  </script>
</body>
</html>
