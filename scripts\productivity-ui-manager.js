'use strict';

/**
 * Productivity UI Manager class for handling the message-based UI for productivity tools
 */
class ProductivityUIManager {
  /**
   * Initialize the Productivity UI Manager
   * @param {FeatureManager} featureManager - The feature manager instance
   * @param {UIManager} uiManager - The UI manager instance
   * @param {ProductivityIntegrations} productivityIntegrations - The productivity integrations instance
   */
  constructor(featureManager, uiManager, productivityIntegrations) {
    this.featureManager = featureManager;
    this.uiManager = uiManager;
    this.productivityIntegrations = productivityIntegrations;
    
    // Initialize event listeners
    this.initEventListeners();
  }
  
  /**
   * Initialize event listeners for chat interactions
   */
  initEventListeners() {
    // Add event listener for chat link clicks
    document.getElementById('chatMessages').addEventListener('click', (event) => {
      // Check if the clicked element is a link
      if (event.target.tagName === 'A') {
        const action = event.target.getAttribute('href');
        
        // Handle productivity tool actions
        if (action && action.startsWith('#productivity-')) {
          event.preventDefault();
          this.handleProductivityAction(action);
        }
      }
    });
  }
  
  /**
   * Handle productivity tool actions from chat links
   * @param {string} action - The action to handle
   */
  async handleProductivityAction(action) {
    try {
      // Connection actions
      if (action === '#productivity-connect-notion') {
        await this.featureManager.connectProductivityTool('notion');
      } else if (action === '#productivity-connect-trello') {
        await this.featureManager.connectProductivityTool('trello');
      } else if (action === '#productivity-connect-google') {
        await this.featureManager.connectProductivityTool('google');
      }
      
      // Disconnect actions
      else if (action === '#productivity-disconnect-notion') {
        await this.featureManager.disconnectProductivityTool('notion');
      } else if (action === '#productivity-disconnect-trello') {
        await this.featureManager.disconnectProductivityTool('trello');
      } else if (action === '#productivity-disconnect-google') {
        await this.featureManager.disconnectProductivityTool('google');
      }
      
      // Creation option menus
      else if (action === '#productivity-create-notion') {
        this.showNotionCreationOptions();
      } else if (action === '#productivity-create-trello') {
        this.showTrelloCreationOptions();
      } else if (action === '#productivity-create-google') {
        this.showGoogleCreationOptions();
      }
      
      // Template option menus
      else if (action === '#productivity-templates-notion') {
        this.showNotionTemplateOptions();
      } else if (action === '#productivity-templates-trello') {
        this.showTrelloTemplateOptions();
      } else if (action === '#productivity-templates-google') {
        this.showGoogleTemplateOptions();
      }
      
      // Notion creation actions
      else if (action === '#productivity-notion-page') {
        this.showNotionPageCreationOptions();
      } else if (action === '#productivity-notion-database') {
        await this.featureManager.createProductivityItem('notion', 'database');
      } else if (action === '#productivity-notion-page-current') {
        await this.featureManager.createProductivityItem('notion', 'page', { source: 'current' });
      } else if (action === '#productivity-notion-page-ai') {
        this.promptForAIContent('notion', 'page');
      }
      
      // Trello creation actions
      else if (action === '#productivity-trello-card') {
        this.showTrelloCardCreationOptions();
      } else if (action === '#productivity-trello-board') {
        await this.featureManager.createProductivityItem('trello', 'board');
      } else if (action === '#productivity-trello-card-current') {
        await this.featureManager.createProductivityItem('trello', 'card', { source: 'current' });
      } else if (action === '#productivity-trello-card-ai') {
        this.promptForAIContent('trello', 'card');
      }
      
      // Google creation actions
      else if (action === '#productivity-google-document') {
        this.showGoogleDocumentCreationOptions();
      } else if (action === '#productivity-google-spreadsheet') {
        await this.featureManager.createProductivityItem('google', 'spreadsheet');
      } else if (action === '#productivity-google-event') {
        await this.featureManager.createProductivityItem('google', 'event');
      } else if (action === '#productivity-google-document-current') {
        await this.featureManager.createProductivityItem('google', 'document', { source: 'current' });
      } else if (action === '#productivity-google-document-ai') {
        this.promptForAIContent('google', 'document');
      }
      
      // View items actions
      else if (action === '#productivity-view-notion') {
        this.showNotionViewOptions();
      } else if (action === '#productivity-view-trello') {
        this.showTrelloViewOptions();
      } else if (action === '#productivity-view-google') {
        this.showGoogleViewOptions();
      } else if (action === '#productivity-notion-view-pages') {
        await this.featureManager.getProductivityItems('notion', 'pages');
      } else if (action === '#productivity-notion-view-databases') {
        await this.featureManager.getProductivityItems('notion', 'databases');
      } else if (action === '#productivity-trello-view-boards') {
        await this.featureManager.getProductivityItems('trello', 'boards');
      } else if (action === '#productivity-trello-view-cards') {
        await this.featureManager.getProductivityItems('trello', 'cards');
      } else if (action === '#productivity-google-view-documents') {
        await this.featureManager.getProductivityItems('google', 'documents');
      } else if (action === '#productivity-google-view-spreadsheets') {
        await this.featureManager.getProductivityItems('google', 'spreadsheets');
      } else if (action === '#productivity-google-view-events') {
        await this.featureManager.getProductivityItems('google', 'events');
      }
      
      // Default action - show main menu
      else {
        this.showProductivityToolsOptions();
      }
    } catch (error) {
      console.error('Error handling productivity action:', error);
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
    }
  }
  
  /**
   * Show the main productivity tools options
   */
  async showProductivityToolsOptions() {
    try {
      // Get authentication status
      let authStatus = { notion: false, trello: false, google: false };
      
      if (this.productivityIntegrations) {
        authStatus = this.productivityIntegrations.getAuthStatus();
      } else if (this.featureManager.productivityIntegrations) {
        authStatus = this.featureManager.productivityIntegrations.getAuthStatus();
      }
      
      // Create connection status message
      const connectedPlatforms = Object.entries(authStatus)
        .filter(([_, isConnected]) => isConnected)
        .map(([platform, _]) => platform);
      
      let connectionStatus = '';
      if (connectedPlatforms.length > 0) {
        connectionStatus = `\n\n**Connected platforms:** ${connectedPlatforms.join(', ')}`;
      } else {
        connectionStatus = '\n\n**No platforms connected yet.**';
      }
      
      // Create the message
      const message = `# Productivity Tools${connectionStatus}

What would you like to do?

## Connect or Disconnect
${authStatus.notion 
  ? '- [Disconnect from Notion](#productivity-disconnect-notion)' 
  : '- [Connect to Notion](#productivity-connect-notion)'}
${authStatus.trello 
  ? '- [Disconnect from Trello](#productivity-disconnect-trello)' 
  : '- [Connect to Trello](#productivity-connect-trello)'}
${authStatus.google 
  ? '- [Disconnect from Google](#productivity-disconnect-google)' 
  : '- [Connect to Google](#productivity-connect-google)'}

## Create Content
${authStatus.notion ? '- [Create in Notion](#productivity-create-notion)' : '- Connect to Notion first'}
${authStatus.trello ? '- [Create in Trello](#productivity-create-trello)' : '- Connect to Trello first'}
${authStatus.google ? '- [Create in Google](#productivity-create-google)' : '- Connect to Google first'}

## View Content
${authStatus.notion ? '- [View Notion content](#productivity-view-notion)' : '- Connect to Notion first'}
${authStatus.trello ? '- [View Trello content](#productivity-view-trello)' : '- Connect to Trello first'}
${authStatus.google ? '- [View Google content](#productivity-view-google)' : '- Connect to Google first'}

## Use Templates
${authStatus.notion ? '- [Use Notion templates](#productivity-templates-notion)' : '- Connect to Notion first'}
${authStatus.trello ? '- [Use Trello templates](#productivity-templates-trello)' : '- Connect to Trello first'}
${authStatus.google ? '- [Use Google templates](#productivity-templates-google)' : '- Connect to Google first'}`;

      // Add the message to the chat
      this.uiManager.addMessageToChat(message, 'ai');
    } catch (error) {
      console.error('Error showing productivity tools options:', error);
      this.uiManager.addMessageToChat(`Error showing productivity tools options: ${error.message}`, 'ai', 'error-message');
    }
  }
  
  /**
   * Show Notion creation options
   */
  showNotionCreationOptions() {
    const message = `# Create in Notion

What would you like to create?

- [Create a Page](#productivity-notion-page)
- [Create a Database](#productivity-notion-database)

Or [go back to Productivity Tools](#productivity-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Notion page creation options
   */
  showNotionPageCreationOptions() {
    const message = `# Create a Notion Page

How would you like to create the page?

- [Create from current page content](#productivity-notion-page-current)
- [Create from AI-generated content](#productivity-notion-page-ai)

Or [go back to Notion options](#productivity-create-notion)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Trello creation options
   */
  showTrelloCreationOptions() {
    const message = `# Create in Trello

What would you like to create?

- [Create a Card](#productivity-trello-card)
- [Create a Board](#productivity-trello-board)

Or [go back to Productivity Tools](#productivity-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Trello card creation options
   */
  showTrelloCardCreationOptions() {
    const message = `# Create a Trello Card

How would you like to create the card?

- [Create from current page content](#productivity-trello-card-current)
- [Create from AI-generated content](#productivity-trello-card-ai)

Or [go back to Trello options](#productivity-create-trello)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Google creation options
   */
  showGoogleCreationOptions() {
    const message = `# Create in Google Workspace

What would you like to create?

- [Create a Document](#productivity-google-document)
- [Create a Spreadsheet](#productivity-google-spreadsheet)
- [Create a Calendar Event](#productivity-google-event)

Or [go back to Productivity Tools](#productivity-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Google document creation options
   */
  showGoogleDocumentCreationOptions() {
    const message = `# Create a Google Document

How would you like to create the document?

- [Create from current page content](#productivity-google-document-current)
- [Create from AI-generated content](#productivity-google-document-ai)

Or [go back to Google options](#productivity-create-google)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Notion template options
   */
  async showNotionTemplateOptions() {
    try {
      // Get available templates
      const templates = await this.featureManager.productivityIntegrations.getTemplates('notion');
      
      let templateLinks = '';
      if (templates.length > 0) {
        templateLinks = templates.map(template => 
          `- [${template.name}](#productivity-notion-template-${template.name.toLowerCase().replace(/\s+/g, '-')})`
        ).join('\n');
      } else {
        templateLinks = '- No templates available';
      }
      
      const message = `# Notion Templates

Available templates:

${templateLinks}

Or [go back to Productivity Tools](#productivity-main)`;

      this.uiManager.addMessageToChat(message, 'ai');
    } catch (error) {
      console.error('Error showing Notion templates:', error);
      this.uiManager.addMessageToChat(`Error showing Notion templates: ${error.message}`, 'ai', 'error-message');
    }
  }
  
  /**
   * Show Trello template options
   */
  async showTrelloTemplateOptions() {
    try {
      // Get available templates
      const templates = await this.featureManager.productivityIntegrations.getTemplates('trello');
      
      let templateLinks = '';
      if (templates.length > 0) {
        templateLinks = templates.map(template => 
          `- [${template.name}](#productivity-trello-template-${template.name.toLowerCase().replace(/\s+/g, '-')})`
        ).join('\n');
      } else {
        templateLinks = '- No templates available';
      }
      
      const message = `# Trello Templates

Available templates:

${templateLinks}

Or [go back to Productivity Tools](#productivity-main)`;

      this.uiManager.addMessageToChat(message, 'ai');
    } catch (error) {
      console.error('Error showing Trello templates:', error);
      this.uiManager.addMessageToChat(`Error showing Trello templates: ${error.message}`, 'ai', 'error-message');
    }
  }
  
  /**
   * Show Google template options
   */
  async showGoogleTemplateOptions() {
    try {
      // Get available templates
      const templates = await this.featureManager.productivityIntegrations.getTemplates('google');
      
      let templateLinks = '';
      if (templates.length > 0) {
        templateLinks = templates.map(template => 
          `- [${template.name}](#productivity-google-template-${template.name.toLowerCase().replace(/\s+/g, '-')})`
        ).join('\n');
      } else {
        templateLinks = '- No templates available';
      }
      
      const message = `# Google Templates

Available templates:

${templateLinks}

Or [go back to Productivity Tools](#productivity-main)`;

      this.uiManager.addMessageToChat(message, 'ai');
    } catch (error) {
      console.error('Error showing Google templates:', error);
      this.uiManager.addMessageToChat(`Error showing Google templates: ${error.message}`, 'ai', 'error-message');
    }
  }
  
  /**
   * Show Notion view options
   */
  showNotionViewOptions() {
    const message = `# View Notion Content

What would you like to view?

- [View Pages](#productivity-notion-view-pages)
- [View Databases](#productivity-notion-view-databases)

Or [go back to Productivity Tools](#productivity-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Trello view options
   */
  showTrelloViewOptions() {
    const message = `# View Trello Content

What would you like to view?

- [View Boards](#productivity-trello-view-boards)
- [View Cards](#productivity-trello-view-cards)

Or [go back to Productivity Tools](#productivity-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show Google view options
   */
  showGoogleViewOptions() {
    const message = `# View Google Content

What would you like to view?

- [View Documents](#productivity-google-view-documents)
- [View Spreadsheets](#productivity-google-view-spreadsheets)
- [View Calendar Events](#productivity-google-view-events)

Or [go back to Productivity Tools](#productivity-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Prompt for AI-generated content
   * @param {string} platform - The platform to create content for
   * @param {string} itemType - The type of item to create
   */
  async promptForAIContent(platform, itemType) {
    try {
      // Prompt for content description
      const prompt = await this.uiManager.prompt(`What kind of ${itemType} would you like to create in ${platform}?`);
      
      if (!prompt) {
        return; // User cancelled
      }
      
      // Show a message that content generation is starting
      this.uiManager.addMessageToChat(
        `Generating ${itemType} content for ${platform} based on: "${prompt}"...`,
        'ai'
      );
      
      // Create the item with AI-generated content
      await this.featureManager.productivityIntegrations.createItemFromAI(platform, itemType, prompt);
      
      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully created ${itemType} in ${platform} with AI-generated content.`,
        'ai'
      );
    } catch (error) {
      console.error(`Error creating ${itemType} with AI in ${platform}:`, error);
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
    }
  }
}
