/* Enhanced Coming Soon Popup Styles */
.coming-soon-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s cubic-bezier(0.16, 1, 0.3, 1), visibility 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.coming-soon-popup[style*="display: flex"] {
  opacity: 1;
  visibility: visible;
  animation: popupFadeIn 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.coming-soon-wrapper {
  width: 90%;
  max-width: 520px;
  background-color: #1a1a1a;
  background-image:
    radial-gradient(circle at 15% 85%, rgba(0, 216, 224, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(0, 166, 192, 0.12) 0%, transparent 50%),
    linear-gradient(to bottom, rgba(10, 15, 20, 0.95), rgba(5, 10, 15, 0.98));
  border-radius: 20px;
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.5),
    0 0 25px rgba(0, 166, 192, 0.25),
    inset 0 0 0 1px rgba(0, 216, 224, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
}

/* Add subtle animated particles in the background */
.coming-soon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(0, 216, 224, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(0, 166, 192, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 50% 50%, rgba(0, 216, 224, 0.03) 0%, transparent 30%);
  opacity: 0.8;
  z-index: 0;
  animation: backgroundShift 15s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
  0% { background-position: 0% 0%; }
  100% { background-position: 100% 100%; }
}

.coming-soon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 26px;
  background: linear-gradient(135deg, rgba(0, 166, 192, 0.95), rgba(0, 216, 224, 0.85));
  border-bottom: 1px solid rgba(0, 216, 224, 0.2);
  position: relative;
  overflow: hidden;
}

.coming-soon-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), transparent);
  z-index: 0;
}

/* Add subtle animated light effect */
.coming-soon-header::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: lightSweep 8s ease-in-out infinite;
  z-index: 1;
  opacity: 0.5;
}

@keyframes lightSweep {
  0% { transform: rotate(30deg) translateX(-100%); }
  100% { transform: rotate(30deg) translateX(100%); }
}

.coming-soon-title {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 2;
}

.coming-soon-logo {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.9);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.coming-soon-logo:hover {
  transform: scale(1.08) rotate(5deg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 0 4px rgba(255, 255, 255, 0.2);
}

/* Add glow effect to logo */
.coming-soon-logo::after {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(0, 216, 224, 0.4) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.coming-soon-logo:hover::after {
  opacity: 1;
}

.coming-soon-title-text {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.coming-soon-title-text h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  letter-spacing: 0.5px;
}

.coming-soon-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 3px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.coming-soon-close {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.25);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.coming-soon-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 1);
  transform: rotate(90deg) scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.coming-soon-body {
  padding: 35px 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
}

.coming-soon-icon {
  font-size: 70px;
  color: #00a6c0;
  margin-bottom: 25px;
  animation: iconFloat 3s ease-in-out infinite;
  position: relative;
  text-shadow: 0 2px 10px rgba(0, 166, 192, 0.5);
}

/* Add glow effect to icon */
.coming-soon-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(0, 216, 224, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  animation: glowPulse 2s ease-in-out infinite;
}

@keyframes iconFloat {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

@keyframes glowPulse {
  0% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2); }
  100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
}

.coming-soon-message {
  color: white;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 18px;
  background: linear-gradient(to right, #ffffff, #48d7ce);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent; /* Fallback */
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
}

/* Add subtle underline to message */
.coming-soon-message::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, #00a6c0, #48d7ce);
  border-radius: 3px;
}

.coming-soon-description {
  color: rgba(255, 255, 255, 0.85);
  font-size: 16px;
  line-height: 1.7;
  margin-bottom: 25px;
  max-width: 420px;
  position: relative;
}

/* Feature highlights section */
.feature-highlights {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
  width: 100%;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: rgba(0, 166, 192, 0.1);
  padding: 15px;
  border-radius: 12px;
  width: 110px;
  border: 1px solid rgba(0, 216, 224, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, #00a6c0, #48d7ce);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  background: rgba(0, 166, 192, 0.15);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.feature-item:hover::before {
  opacity: 1;
}

.feature-item i {
  font-size: 24px;
  color: #00a6c0;
  margin-bottom: 5px;
  transition: transform 0.3s ease;
}

.feature-item:hover i {
  transform: scale(1.2);
}

.feature-item span {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-align: center;
}

.coming-soon-btn {
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow:
    0 6px 15px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(0, 216, 224, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
}

/* Add shine effect to button */
.coming-soon-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.7s ease;
}

.coming-soon-btn:hover {
  transform: translateY(-3px) scale(1.03);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 0 2px rgba(0, 216, 224, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.coming-soon-btn:hover::before {
  left: 100%;
}

.coming-soon-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow:
    0 3px 10px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(0, 216, 224, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.coming-soon-btn i {
  font-size: 18px;
  animation: bellShake 5s ease-in-out infinite;
  transform-origin: top center;
}

@keyframes bellShake {
  0%, 50%, 100% { transform: rotate(0); }
  85%, 95% { transform: rotate(15deg); }
  90% { transform: rotate(-15deg); }
}

/* Animation for dialog opening */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    box-shadow:
      0 5px 20px rgba(0, 0, 0, 0.3),
      0 0 15px rgba(0, 166, 192, 0.15);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    box-shadow:
      0 15px 40px rgba(0, 0, 0, 0.5),
      0 0 25px rgba(0, 166, 192, 0.25);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .coming-soon-wrapper {
    width: 95%;
    max-width: 450px;
  }

  .coming-soon-header {
    padding: 15px 20px;
  }

  .coming-soon-body {
    padding: 25px 20px;
  }

  .coming-soon-message {
    font-size: 24px;
  }

  .coming-soon-description {
    font-size: 15px;
    line-height: 1.6;
  }

  .coming-soon-btn {
    padding: 12px 24px;
    font-size: 15px;
  }

  /* Responsive feature highlights */
  .feature-highlights {
    flex-wrap: wrap;
    gap: 15px;
  }

  .feature-item {
    width: 100px;
    padding: 12px;
  }

  .feature-item i {
    font-size: 22px;
  }

  .feature-item span {
    font-size: 13px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .coming-soon-wrapper {
    width: 95%;
    max-width: 350px;
  }

  .coming-soon-icon {
    font-size: 60px;
    margin-bottom: 20px;
  }

  .coming-soon-message {
    font-size: 22px;
  }

  .feature-highlights {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .feature-item {
    width: 80%;
    max-width: 200px;
    flex-direction: row;
    justify-content: flex-start;
    padding: 10px 15px;
    gap: 15px;
  }

  .feature-item i {
    font-size: 20px;
    margin-bottom: 0;
  }
}
