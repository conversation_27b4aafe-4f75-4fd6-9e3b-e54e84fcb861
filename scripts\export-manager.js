/**
 * Export Manager for BlowAI
 * Handles exporting chat conversations to various formats
 */
class ExportManager {
  /**
   * Create a new Export Manager
   * @param {ChatManager} chatManager - The Chat Manager instance
   * @param {UIManager} uiManager - The UI Manager instance
   */
  constructor(chatManager, uiManager) {
    this.chatManager = chatManager;
    this.uiManager = uiManager;
  }

  /**
   * Export the current chat to a text file
   */
  exportChatToText() {
    try {
      // Get all message elements from the chat
      const messageElements = document.querySelectorAll('.message');
      
      if (messageElements.length === 0) {
        this.uiManager.showStatus('No messages to export', true, 3000);
        return;
      }

      // Create a text representation of the chat
      let chatText = "# BlowAI Chat Export\n";
      chatText += `Date: ${new Date().toLocaleString()}\n\n`;

      // Process each message
      messageElements.forEach(messageEl => {
        // Determine if it's a user or AI message
        const isUserMessage = messageEl.classList.contains('user-message');
        const sender = isUserMessage ? 'User' : 'AI';
        
        // Get the message content
        const contentEl = messageEl.querySelector('.message-content');
        if (!contentEl) return;
        
        // Extract text content, handling code blocks specially
        let messageText = this.extractTextFromMessageContent(contentEl);
        
        // Add to the chat text
        chatText += `## ${sender}:\n${messageText}\n\n`;
      });

      // Create a blob and download link
      const blob = new Blob([chatText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      
      // Create a download link and trigger it
      const a = document.createElement('a');
      a.href = url;
      a.download = `blowai-chat-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
      
      this.uiManager.showStatus('Chat exported successfully!', false, 3000);
    } catch (error) {
      console.error('Error exporting chat:', error);
      this.uiManager.showStatus(`Error exporting chat: ${error.message}`, true, 3000);
    }
  }

  /**
   * Extract text content from a message, handling code blocks specially
   * @param {HTMLElement} contentEl - The message content element
   * @returns {string} - The extracted text
   */
  extractTextFromMessageContent(contentEl) {
    let text = '';
    
    // Process each child node
    Array.from(contentEl.childNodes).forEach(node => {
      // Handle different node types
      if (node.nodeType === Node.TEXT_NODE) {
        // Plain text node
        text += node.textContent;
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // Element node
        if (node.tagName === 'PRE') {
          // Code block
          const codeEl = node.querySelector('code');
          if (codeEl) {
            const language = node.closest('.code-container')?.querySelector('.code-language')?.textContent || 'code';
            text += `\n\`\`\`${language}\n${codeEl.textContent}\n\`\`\`\n`;
          } else {
            text += `\n\`\`\`\n${node.textContent}\n\`\`\`\n`;
          }
        } else if (node.tagName === 'CODE') {
          // Inline code
          text += `\`${node.textContent}\``;
        } else if (node.tagName === 'P' || node.tagName === 'DIV') {
          // Paragraph or div
          const nodeText = this.extractTextFromMessageContent(node);
          text += nodeText + (nodeText.endsWith('\n') ? '' : '\n');
        } else if (node.tagName === 'BR') {
          // Line break
          text += '\n';
        } else if (node.tagName === 'UL' || node.tagName === 'OL') {
          // Lists
          text += '\n' + this.extractTextFromMessageContent(node) + '\n';
        } else if (node.tagName === 'LI') {
          // List item
          text += '- ' + this.extractTextFromMessageContent(node) + '\n';
        } else if (node.tagName === 'STRONG' || node.tagName === 'B') {
          // Bold text
          text += `**${node.textContent}**`;
        } else if (node.tagName === 'EM' || node.tagName === 'I') {
          // Italic text
          text += `*${node.textContent}*`;
        } else if (node.tagName === 'H1' || node.tagName === 'H2' || 
                  node.tagName === 'H3' || node.tagName === 'H4') {
          // Headings
          const level = node.tagName.charAt(1);
          const hashes = '#'.repeat(parseInt(level));
          text += `\n${hashes} ${node.textContent}\n`;
        } else if (node.tagName === 'A') {
          // Links
          text += `[${node.textContent}](${node.href})`;
        } else if (node.tagName === 'IMG') {
          // Images
          text += `![${node.alt || 'Image'}](${node.src})`;
        } else if (node.tagName === 'TABLE') {
          // Tables - simplified as they're complex to represent in plain text
          text += '\n[Table content]\n';
        } else if (node.tagName === 'BUTTON' || 
                  node.classList.contains('copy-button') || 
                  node.classList.contains('brave-copy-btn')) {
          // Skip buttons
          return;
        } else {
          // Other elements - recursively extract text
          text += this.extractTextFromMessageContent(node);
        }
      }
    });
    
    return text;
  }
}
