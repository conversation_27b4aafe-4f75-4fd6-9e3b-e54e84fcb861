'use strict';

/**
 * Destress Mode class for providing stress relief techniques
 */
class DestressMode {
  /**
   * Initialize the Destress Mode
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(uiManager) {
    this.uiManager = uiManager;
    this.techniques = [
      {
        title: "Deep Breathing Exercise",
        description: "Take a deep breath in through your nose for 4 seconds, hold for 2 seconds, then exhale through your mouth for 6 seconds. Repeat 5 times.",
        icon: "fa-wind",
        duration: "1 minute",
        type: "breathing"
      },
      {
        title: "Quick Meditation",
        description: "Close your eyes. Focus on your breathing. Notice how your body feels. Let thoughts come and go without judgment. Just be present for a moment.",
        icon: "fa-om",
        duration: "2 minutes",
        type: "meditation"
      },
      {
        title: "Progressive Muscle Relaxation",
        description: "Tense each muscle group for 5 seconds, then relax for 10 seconds. Start with your feet and work up to your face.",
        icon: "fa-dumbbell",
        duration: "3 minutes",
        type: "physical"
      },
      {
        title: "Visualization",
        description: "Close your eyes and imagine a peaceful place. It could be a beach, forest, or anywhere you feel calm. Engage all your senses in this mental image.",
        icon: "fa-image",
        duration: "2 minutes",
        type: "meditation"
      },
      {
        title: "5-4-3-2-1 Grounding Technique",
        description: "Name 5 things you can see, 4 things you can touch, 3 things you can hear, 2 things you can smell, and 1 thing you can taste.",
        icon: "fa-hand-paper",
        duration: "1 minute",
        type: "mindfulness"
      },
      {
        title: "Quick Stretch",
        description: "Stand up and stretch your arms overhead. Gently twist your torso from side to side. Roll your shoulders backward and forward.",
        icon: "fa-running",
        duration: "1 minute",
        type: "physical"
      },
      {
        title: "Positive Affirmations",
        description: "Repeat these phrases: 'I am calm', 'I can handle this', 'This feeling will pass', 'I am in control of my reactions'.",
        icon: "fa-comment-dots",
        duration: "1 minute",
        type: "mindfulness"
      },
      {
        title: "Gratitude Practice",
        description: "Think of three things you're grateful for right now. They can be simple things like a warm drink, comfortable chair, or kind message.",
        icon: "fa-heart",
        duration: "1 minute",
        type: "mindfulness"
      },
      {
        title: "Quick Mindful Walk",
        description: "If possible, take a short walk. Pay attention to each step, how your feet feel touching the ground, and the sensation of movement.",
        icon: "fa-walking",
        duration: "3 minutes",
        type: "physical"
      },
      {
        title: "Desk Yoga",
        description: "Sitting in your chair, gently roll your neck, stretch your arms overhead, and twist your torso. Focus on your breathing as you move.",
        icon: "fa-pray",
        duration: "2 minutes",
        type: "physical"
      },
      {
        title: "Box Breathing",
        description: "Inhale for 4 counts, hold for 4 counts, exhale for 4 counts, hold for 4 counts. Visualize tracing a square as you breathe. Repeat 5 times.",
        icon: "fa-square",
        duration: "2 minutes",
        type: "breathing"
      },
      {
        title: "Body Scan Meditation",
        description: "Starting from your toes, bring awareness to each part of your body moving upward. Notice any tension and consciously relax each area.",
        icon: "fa-user",
        duration: "3 minutes",
        type: "meditation"
      },
      {
        title: "4-7-8 Breathing Technique",
        description: "Inhale quietly through your nose for 4 seconds, hold your breath for 7 seconds, then exhale completely through your mouth for 8 seconds.",
        icon: "fa-lungs",
        duration: "2 minutes",
        type: "breathing"
      },
      {
        title: "Mindful Hand Massage",
        description: "Apply lotion or oil to one hand and massage it with the other, paying attention to sensations. Then switch hands.",
        icon: "fa-hands",
        duration: "2 minutes",
        type: "physical"
      },
      {
        title: "Loving-Kindness Meditation",
        description: "Silently repeat: 'May I be happy. May I be healthy. May I be safe. May I live with ease.' Then extend these wishes to others.",
        icon: "fa-heart",
        duration: "3 minutes",
        type: "meditation"
      },
      {
        title: "Alternate Nostril Breathing",
        description: "Use your thumb to close your right nostril, inhale through left. Close left with ring finger, exhale through right. Repeat, alternating sides.",
        icon: "fa-wind",
        duration: "2 minutes",
        type: "breathing"
      },
      {
        title: "Mindful Listening",
        description: "Close your eyes and focus on the sounds around you. Notice distant sounds, nearby sounds, and the quality of silence between sounds.",
        icon: "fa-headphones",
        duration: "2 minutes",
        type: "mindfulness"
      },
      {
        title: "Tension Release Exercise",
        description: "Clench your fists tightly, raise your shoulders to your ears, and scrunch your face. Hold for 5 seconds, then release completely and relax.",
        icon: "fa-fist-raised",
        duration: "1 minute",
        type: "physical"
      },
      {
        title: "Mindful Eating",
        description: "Take a small piece of food. Examine it closely, smell it, feel its texture. Take a small bite and notice all the flavors and sensations.",
        icon: "fa-apple-alt",
        duration: "2 minutes",
        type: "mindfulness"
      },
      {
        title: "Visualization: Blue Light",
        description: "Imagine a healing blue light entering the top of your head with each breath, flowing through your body and washing away tension.",
        icon: "fa-lightbulb",
        duration: "3 minutes",
        type: "meditation"
      }
    ];

    this.recommendations = {
      music: [
        {
          title: "Relaxing Lo-fi Beats",
          description: "Calm, instrumental lo-fi hip hop beats perfect for relaxation and focus.",
          icon: "fa-music",
          platform: "spotify",
          query: "lofi beats relaxation"
        },
        {
          title: "Nature Sounds",
          description: "Peaceful sounds of rain, ocean waves, and forest ambience to help you relax.",
          icon: "fa-leaf",
          platform: "spotify",
          query: "nature sounds relaxation"
        },
        {
          title: "Classical Piano",
          description: "Soothing piano compositions by composers like Chopin, Debussy, and Einaudi.",
          icon: "fa-music",
          platform: "spotify",
          query: "relaxing piano classical"
        },
        {
          title: "Ambient Soundscapes",
          description: "Immersive ambient music that creates a peaceful atmosphere for relaxation.",
          icon: "fa-volume-up",
          platform: "spotify",
          query: "ambient soundscapes relaxation"
        },
        {
          title: "Meditation Music",
          description: "Specially designed music to accompany meditation and mindfulness practices.",
          icon: "fa-om",
          platform: "spotify",
          query: "meditation music mindfulness"
        },
        {
          title: "Acoustic Guitar",
          description: "Gentle acoustic guitar melodies that soothe the mind and calm the nerves.",
          icon: "fa-guitar",
          platform: "spotify",
          query: "acoustic guitar relaxation"
        },
        {
          title: "Yoga Music",
          description: "Harmonious sounds designed to accompany yoga practice and deep breathing.",
          icon: "fa-pray",
          platform: "spotify",
          query: "yoga music relaxation"
        },
        {
          title: "Binaural Beats",
          description: "Sound therapy using specific frequencies to promote relaxation and focus.",
          icon: "fa-wave-square",
          platform: "spotify",
          query: "binaural beats relaxation"
        },
        {
          title: "Japanese Zen Music",
          description: "Traditional Japanese music with flutes and nature sounds for peaceful meditation.",
          icon: "fa-spa",
          platform: "spotify",
          query: "japanese zen music relaxation"
        },
        {
          title: "Tibetan Singing Bowls",
          description: "Resonant sounds of singing bowls that promote deep relaxation and healing.",
          icon: "fa-bell",
          platform: "spotify",
          query: "tibetan singing bowls meditation"
        }
      ],
      youtube: [
        {
          title: "Guided Meditation Videos",
          description: "Follow along with expert-led meditation sessions to calm your mind.",
          icon: "fa-youtube",
          platform: "youtube",
          query: "guided meditation for stress relief"
        },
        {
          title: "Nature Documentaries",
          description: "Immerse yourself in beautiful natural landscapes and wildlife.",
          icon: "fa-tree",
          platform: "youtube",
          query: "relaxing nature documentary"
        },
        {
          title: "ASMR Videos",
          description: "Experience the calming tingles and relaxation of ASMR content.",
          icon: "fa-headphones",
          platform: "youtube",
          query: "relaxing ASMR"
        },
        {
          title: "Yoga Sessions",
          description: "Follow along with gentle yoga routines designed for stress relief.",
          icon: "fa-pray",
          platform: "youtube",
          query: "gentle yoga for stress relief"
        },
        {
          title: "Relaxing Music Visualizations",
          description: "Beautiful visual patterns that move in sync with calming music.",
          icon: "fa-wave-square",
          platform: "youtube",
          query: "relaxing music visualization"
        },
        {
          title: "Breathing Exercises",
          description: "Guided breathing techniques to reduce stress and anxiety quickly.",
          icon: "fa-wind",
          platform: "youtube",
          query: "breathing exercises for stress relief"
        },
        {
          title: "Virtual Nature Walks",
          description: "Take a virtual walk through beautiful forests, beaches, and mountains.",
          icon: "fa-mountain",
          platform: "youtube",
          query: "virtual nature walk 4k"
        },
        {
          title: "Relaxing Aquarium Videos",
          description: "Peaceful underwater scenes with fish and gentle water sounds.",
          icon: "fa-fish",
          platform: "youtube",
          query: "relaxing aquarium video"
        },
        {
          title: "Mindfulness Talks",
          description: "Insightful talks on mindfulness and stress management from experts.",
          icon: "fa-brain",
          platform: "youtube",
          query: "mindfulness talks stress relief"
        },
        {
          title: "Fireplace Videos",
          description: "Cozy fireplace scenes with crackling sounds for relaxation.",
          icon: "fa-fire",
          platform: "youtube",
          query: "relaxing fireplace video"
        }
      ],
      book: [
        {
          title: "The Power of Now by Eckhart Tolle",
          description: "A guide to spiritual enlightenment focused on living in the present moment.",
          icon: "fa-book",
          platform: "search",
          query: "The Power of Now Eckhart Tolle book"
        },
        {
          title: "Why Zebras Don't Get Ulcers by Robert Sapolsky",
          description: "An exploration of how prolonged stress affects your body and what you can do about it.",
          icon: "fa-book",
          platform: "search",
          query: "Why Zebras Don't Get Ulcers Robert Sapolsky book"
        },
        {
          title: "10% Happier by Dan Harris",
          description: "A skeptic's journey to discovering the benefits of meditation and mindfulness.",
          icon: "fa-book",
          platform: "search",
          query: "10% Happier Dan Harris book"
        },
        {
          title: "Breath by James Nestor",
          description: "A fascinating exploration of how proper breathing can transform your health and mind.",
          icon: "fa-book",
          platform: "search",
          query: "Breath James Nestor book"
        },
        {
          title: "Atomic Habits by James Clear",
          description: "Build good habits and break bad ones with this practical guide to habit formation.",
          icon: "fa-book",
          platform: "search",
          query: "Atomic Habits James Clear book"
        },
        {
          title: "Mindfulness in Plain English by Bhante Gunaratana",
          description: "A clear, straightforward guide to mindfulness meditation practice.",
          icon: "fa-book",
          platform: "search",
          query: "Mindfulness in Plain English Bhante Gunaratana book"
        },
        {
          title: "When Things Fall Apart by Pema Chödrön",
          description: "Heart advice for difficult times from a Buddhist perspective.",
          icon: "fa-book",
          platform: "search",
          query: "When Things Fall Apart Pema Chodron book"
        },
        {
          title: "Digital Minimalism by Cal Newport",
          description: "A philosophy for technology use that can help reduce digital stress.",
          icon: "fa-book",
          platform: "search",
          query: "Digital Minimalism Cal Newport book"
        },
        {
          title: "Burnout by Emily Nagoski and Amelia Nagoski",
          description: "The secret to unlocking the stress cycle and finding balance.",
          icon: "fa-book",
          platform: "search",
          query: "Burnout Emily Nagoski Amelia Nagoski book"
        },
        {
          title: "The Happiness Trap by Russ Harris",
          description: "Stop struggling, start living using Acceptance and Commitment Therapy techniques.",
          icon: "fa-book",
          platform: "search",
          query: "The Happiness Trap Russ Harris book"
        }
      ]
    };
  }

  /**
   * Get a random stress relief technique
   * @returns {Object} - A random stress relief technique
   */
  getRandomTechnique() {
    const randomIndex = Math.floor(Math.random() * this.techniques.length);
    return this.techniques[randomIndex];
  }

  /**
   * Get a random recommendation from a specific category
   * @param {string} category - The category to get a recommendation from (music, youtube, book)
   * @returns {Object} - A random recommendation
   */
  getRandomRecommendation(category) {
    if (!this.recommendations[category]) {
      return {
        title: "Relaxing Music",
        description: "Listen to some relaxing music to help you destress.",
        icon: "fa-music",
        platform: "spotify",
        query: "relaxing music"
      };
    }

    const categoryRecommendations = this.recommendations[category];
    const randomIndex = Math.floor(Math.random() * categoryRecommendations.length);
    return categoryRecommendations[randomIndex];
  }

  /**
   * Open a URL in a new tab
   * @param {string} url - The URL to open
   */
  openUrl(url) {
    try {
      chrome.tabs.create({ url });
    } catch (error) {
      console.error('Error opening URL:', error);
    }
  }

  /**
   * Show a destress mode dialog with a random technique and recommendations
   */
  showDestressDialog() {
    try {
      // Get a random technique
      const technique = this.getRandomTechnique();

      // Get random recommendations
      const musicRec = this.getRandomRecommendation('music');
      const youtubeRec = this.getRandomRecommendation('youtube');
      const bookRec = this.getRandomRecommendation('book');

      // Create dialog element if it doesn't exist
      const dialogId = 'destressDialog';
      let dialog = document.getElementById(dialogId);

      if (!dialog) {
        dialog = document.createElement('div');
        dialog.id = dialogId;
        dialog.className = 'destress-dialog';
        document.body.appendChild(dialog);
      }

      // Get techniques by type for guided exercises
      const breathingTechniques = this.techniques.filter(t => t.type === 'breathing');
      const meditationTechniques = this.techniques.filter(t => t.type === 'meditation');
      const randomBreathingTechnique = breathingTechniques[Math.floor(Math.random() * breathingTechniques.length)];
      const randomMeditationTechnique = meditationTechniques[Math.floor(Math.random() * meditationTechniques.length)];

      // Set dialog content
      dialog.innerHTML = `
        <div class="destress-content">
          <div class="destress-header">
            <h3><i class="fas fa-peace"></i> Destress Mode</h3>
            <button class="destress-close-btn"><i class="fas fa-times"></i></button>
          </div>
          <div class="destress-body">
            <div class="destress-tabs">
              <button class="destress-tab active" data-tab="techniques">Techniques</button>
              <button class="destress-tab" data-tab="guided">Guided Exercises</button>
              <button class="destress-tab" data-tab="recommendations">Recommendations</button>
            </div>

            <div class="destress-tab-content active" id="techniques-tab">
              <div class="technique-card ${technique.type}-card">
                <div class="technique-icon">
                  <i class="fas ${technique.icon}"></i>
                </div>
                <div class="technique-badge">${technique.type}</div>
                <h4>${technique.title}</h4>
                <p class="technique-duration"><i class="far fa-clock"></i> ${technique.duration}</p>
                <p class="technique-description">${technique.description}</p>
                <div class="technique-filter">
                  <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="breathing">Breathing</button>
                    <button class="filter-btn" data-filter="meditation">Meditation</button>
                    <button class="filter-btn" data-filter="physical">Physical</button>
                    <button class="filter-btn" data-filter="mindfulness">Mindfulness</button>
                  </div>
                </div>
              </div>
              <div class="destress-actions">
                <button id="nextTechniqueBtn" class="next-technique-btn">
                  <i class="fas fa-sync-alt"></i> Try Another Technique
                </button>
                <button id="startTimerBtn" class="timer-btn">
                  <i class="fas fa-stopwatch"></i> Start Timer
                </button>
              </div>
            </div>

            <div class="destress-tab-content" id="guided-tab">
              <h4 class="guided-title">Guided Exercises</h4>
              <p class="guided-subtitle">Follow these step-by-step exercises</p>

              <div class="guided-exercises">
                <div class="guided-exercise-card breathing-exercise">
                  <div class="exercise-header">
                    <div class="exercise-icon">
                      <i class="fas fa-wind"></i>
                    </div>
                    <h4>Breathing Exercise</h4>
                  </div>
                  <div class="exercise-content">
                    <div class="breathing-animation">
                      <div class="breathing-circle">
                        <div class="breathing-instruction">Breathe In</div>
                        <div class="breathing-count">4</div>
                      </div>
                    </div>
                    <p class="exercise-description">${randomBreathingTechnique.description}</p>
                    <div class="exercise-controls">
                      <button id="startBreathingBtn" class="exercise-btn">
                        <i class="fas fa-play"></i> Start Exercise
                      </button>
                    </div>
                  </div>
                </div>

                <div class="guided-exercise-card meditation-exercise">
                  <div class="exercise-header">
                    <div class="exercise-icon">
                      <i class="fas fa-om"></i>
                    </div>
                    <h4>Meditation Exercise</h4>
                  </div>
                  <div class="exercise-content">
                    <div class="meditation-progress">
                      <div class="progress-bar">
                        <div class="progress-fill"></div>
                      </div>
                      <div class="progress-time">0:00</div>
                    </div>
                    <p class="exercise-description">${randomMeditationTechnique.description}</p>
                    <div class="exercise-controls">
                      <button id="startMeditationBtn" class="exercise-btn">
                        <i class="fas fa-play"></i> Start 2-Min Meditation
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="destress-tab-content" id="recommendations-tab">
              <h4 class="recommendations-title">Recommendations to Help You Relax</h4>
              <p class="recommendations-subtitle">Click any recommendation to open it in a new tab</p>

              <div class="recommendations-container">
                <div class="recommendation-card" id="music-rec">
                  <div class="recommendation-icon">
                    <i class="fas ${musicRec.icon}"></i>
                  </div>
                  <div class="recommendation-content">
                    <h5>${musicRec.title}</h5>
                    <p>${musicRec.description}</p>
                    <span class="recommendation-platform"><i class="fab fa-spotify"></i> Listen on Spotify</span>
                  </div>
                </div>

                <div class="recommendation-card" id="youtube-rec">
                  <div class="recommendation-icon">
                    <i class="fas ${youtubeRec.icon}"></i>
                  </div>
                  <div class="recommendation-content">
                    <h5>${youtubeRec.title}</h5>
                    <p>${youtubeRec.description}</p>
                    <span class="recommendation-platform"><i class="fab fa-youtube"></i> Watch on YouTube</span>
                  </div>
                </div>

                <div class="recommendation-card" id="book-rec">
                  <div class="recommendation-icon">
                    <i class="fas ${bookRec.icon}"></i>
                  </div>
                  <div class="recommendation-content">
                    <h5>${bookRec.title}</h5>
                    <p>${bookRec.description}</p>
                    <span class="recommendation-platform"><i class="fas fa-book"></i> Find this Book</span>
                  </div>
                </div>
              </div>

              <div class="destress-actions">
                <button id="moreRecommendationsBtn" class="next-technique-btn">
                  <i class="fas fa-sync-alt"></i> More Recommendations
                </button>
              </div>
            </div>
          </div>
        </div>
      `;

      // Show the dialog
      dialog.style.display = 'flex';

      // Add event listeners
      const closeBtn = dialog.querySelector('.destress-close-btn');
      closeBtn.addEventListener('click', () => {
        dialog.style.display = 'none';
      });

      // Next technique button
      const nextBtn = dialog.querySelector('#nextTechniqueBtn');
      nextBtn.addEventListener('click', () => {
        this.showDestressDialog(); // Show a new random technique
      });

      // More recommendations button
      const moreRecommendationsBtn = dialog.querySelector('#moreRecommendationsBtn');
      if (moreRecommendationsBtn) {
        moreRecommendationsBtn.addEventListener('click', () => {
          this.showDestressDialog(); // Show new random recommendations
        });
      }

      // Tab switching
      const tabs = dialog.querySelectorAll('.destress-tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove('active'));

          // Add active class to clicked tab
          tab.classList.add('active');

          // Hide all tab content
          const tabContents = dialog.querySelectorAll('.destress-tab-content');
          tabContents.forEach(content => content.classList.remove('active'));

          // Show the selected tab content
          const tabName = tab.getAttribute('data-tab');
          const tabContent = dialog.querySelector(`#${tabName}-tab`);
          if (tabContent) {
            tabContent.classList.add('active');
          }
        });
      });

      // Filter buttons for techniques
      const filterButtons = dialog.querySelectorAll('.filter-btn');
      filterButtons.forEach(btn => {
        btn.addEventListener('click', () => {
          // Remove active class from all filter buttons
          filterButtons.forEach(b => b.classList.remove('active'));

          // Add active class to clicked button
          btn.classList.add('active');

          // Get filter value
          const filter = btn.getAttribute('data-filter');

          // Filter techniques and show a new one
          if (filter === 'all') {
            this.showDestressDialog();
          } else {
            const filteredTechniques = this.techniques.filter(t => t.type === filter);
            if (filteredTechniques.length > 0) {
              const randomTechnique = filteredTechniques[Math.floor(Math.random() * filteredTechniques.length)];

              // Update technique card
              const techniqueCard = dialog.querySelector('.technique-card');
              techniqueCard.className = `technique-card ${randomTechnique.type}-card`;
              techniqueCard.querySelector('.technique-icon i').className = `fas ${randomTechnique.icon}`;
              techniqueCard.querySelector('.technique-badge').textContent = randomTechnique.type;
              techniqueCard.querySelector('h4').textContent = randomTechnique.title;
              techniqueCard.querySelector('.technique-duration').innerHTML = `<i class="far fa-clock"></i> ${randomTechnique.duration}`;
              techniqueCard.querySelector('.technique-description').textContent = randomTechnique.description;
            }
          }
        });
      });

      // Timer button
      const timerBtn = dialog.querySelector('#startTimerBtn');
      if (timerBtn) {
        timerBtn.addEventListener('click', () => {
          const durationText = technique.duration;
          const minutes = parseInt(durationText.match(/\d+/)[0]);
          let seconds = minutes * 60;

          // Create timer element if it doesn't exist
          let timerElement = dialog.querySelector('.technique-timer');
          if (!timerElement) {
            timerElement = document.createElement('div');
            timerElement.className = 'technique-timer';
            const techniqueCard = dialog.querySelector('.technique-card');
            techniqueCard.appendChild(timerElement);
          }

          // Update timer button
          timerBtn.innerHTML = '<i class="fas fa-stopwatch"></i> Reset Timer';

          // Start timer
          const timerInterval = setInterval(() => {
            seconds--;
            const displayMinutes = Math.floor(seconds / 60);
            const displaySeconds = seconds % 60;
            timerElement.textContent = `${displayMinutes}:${displaySeconds < 10 ? '0' : ''}${displaySeconds}`;

            if (seconds <= 0) {
              clearInterval(timerInterval);
              timerElement.textContent = 'Done!';
              timerElement.classList.add('timer-done');

              // Play a gentle sound to indicate completion
              const audio = new Audio('https://assets.mixkit.co/sfx/preview/mixkit-software-interface-remove-2576.mp3');
              audio.volume = 0.3;
              audio.play().catch(e => console.log('Audio play failed:', e));
            }
          }, 1000);
        });
      }

      // Breathing exercise
      const startBreathingBtn = dialog.querySelector('#startBreathingBtn');
      if (startBreathingBtn) {
        startBreathingBtn.addEventListener('click', () => {
          const breathingCircle = dialog.querySelector('.breathing-circle');
          const breathingInstruction = dialog.querySelector('.breathing-instruction');
          const breathingCount = dialog.querySelector('.breathing-count');

          startBreathingBtn.disabled = true;
          startBreathingBtn.textContent = 'Exercise in progress...';

          // Breathing cycle: inhale (4s), hold (2s), exhale (6s), repeat 5 times
          let phase = 'inhale';
          let count = 4;
          let cycle = 0;

          breathingCircle.classList.add('inhale');

          const breathingInterval = setInterval(() => {
            count--;
            breathingCount.textContent = count;

            if (count === 0) {
              if (phase === 'inhale') {
                phase = 'hold';
                count = 2;
                breathingInstruction.textContent = 'Hold';
                breathingCircle.classList.remove('inhale');
                breathingCircle.classList.add('hold');
              } else if (phase === 'hold') {
                phase = 'exhale';
                count = 6;
                breathingInstruction.textContent = 'Breathe Out';
                breathingCircle.classList.remove('hold');
                breathingCircle.classList.add('exhale');
              } else if (phase === 'exhale') {
                cycle++;
                if (cycle >= 5) {
                  clearInterval(breathingInterval);
                  breathingInstruction.textContent = 'Complete';
                  breathingCount.textContent = '✓';
                  breathingCircle.classList.remove('exhale');
                  breathingCircle.classList.add('complete');

                  startBreathingBtn.disabled = false;
                  startBreathingBtn.innerHTML = '<i class="fas fa-redo"></i> Restart Exercise';
                } else {
                  phase = 'inhale';
                  count = 4;
                  breathingInstruction.textContent = 'Breathe In';
                  breathingCircle.classList.remove('exhale');
                  breathingCircle.classList.add('inhale');
                }
              }
            }
          }, 1000);
        });
      }

      // Meditation exercise
      const startMeditationBtn = dialog.querySelector('#startMeditationBtn');
      if (startMeditationBtn) {
        startMeditationBtn.addEventListener('click', () => {
          const progressFill = dialog.querySelector('.progress-fill');
          const progressTime = dialog.querySelector('.progress-time');
          const meditationCard = dialog.querySelector('.meditation-exercise');

          startMeditationBtn.disabled = true;
          startMeditationBtn.innerHTML = '<i class="fas fa-om"></i> Meditation in progress...';

          // Add active class to meditation card
          meditationCard.classList.add('active-meditation');

          // Play gentle bell sound to start meditation
          try {
            const startSound = new Audio('https://assets.mixkit.co/sfx/preview/mixkit-bell-meditation-sound-2003.mp3');
            startSound.volume = 0.3;
            startSound.play().catch(e => console.log('Audio play failed:', e));
          } catch (error) {
            console.log('Could not play meditation start sound:', error);
          }

          // 2-minute meditation
          let seconds = 0;
          const totalSeconds = 120;

          const meditationInterval = setInterval(() => {
            seconds++;
            const percent = (seconds / totalSeconds) * 100;
            progressFill.style.width = `${percent}%`;

            const displayMinutes = Math.floor(seconds / 60);
            const displaySeconds = seconds % 60;
            progressTime.textContent = `${displayMinutes}:${displaySeconds < 10 ? '0' : ''}${displaySeconds}`;

            // Add pulsing effect at certain intervals
            if (seconds % 10 === 0) {
              progressFill.classList.add('pulse');
              setTimeout(() => {
                progressFill.classList.remove('pulse');
              }, 1000);
            }

            if (seconds >= totalSeconds) {
              clearInterval(meditationInterval);
              progressTime.textContent = 'Complete';
              meditationCard.classList.remove('active-meditation');
              meditationCard.classList.add('completed-meditation');

              // Play completion sound
              try {
                const endSound = new Audio('https://assets.mixkit.co/sfx/preview/mixkit-meditation-bell-sound-2004.mp3');
                endSound.volume = 0.3;
                endSound.play().catch(e => console.log('Audio play failed:', e));
              } catch (error) {
                console.log('Could not play meditation end sound:', error);
              }

              startMeditationBtn.disabled = false;
              startMeditationBtn.innerHTML = '<i class="fas fa-redo"></i> Restart Meditation';
            }
          }, 1000);
        });
      }

      // Music recommendation card
      const musicRecCard = dialog.querySelector('#music-rec');
      if (musicRecCard) {
        musicRecCard.addEventListener('click', () => {
          const url = `https://open.spotify.com/search/${encodeURIComponent(musicRec.query)}`;
          this.openUrl(url);
          dialog.style.display = 'none';
        });
      }

      // YouTube recommendation card
      const youtubeRecCard = dialog.querySelector('#youtube-rec');
      if (youtubeRecCard) {
        youtubeRecCard.addEventListener('click', () => {
          const url = `https://www.youtube.com/results?search_query=${encodeURIComponent(youtubeRec.query)}`;
          this.openUrl(url);
          dialog.style.display = 'none';
        });
      }

      // Book recommendation card
      const bookRecCard = dialog.querySelector('#book-rec');
      if (bookRecCard) {
        bookRecCard.addEventListener('click', () => {
          const url = `https://www.google.com/search?q=${encodeURIComponent(bookRec.query)}`;
          this.openUrl(url);
          dialog.style.display = 'none';
        });
      }

      // Close when clicking outside
      dialog.addEventListener('click', (event) => {
        if (event.target === dialog) {
          dialog.style.display = 'none';
        }
      });
    } catch (error) {
      console.error('Error showing destress dialog:', error);
      this.uiManager.showStatus('Error showing destress mode. Please try again.', true, 3000);
    }
  }
}
