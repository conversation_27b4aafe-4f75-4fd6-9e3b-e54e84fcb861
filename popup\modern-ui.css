/* Modern UI Overlay for Chat Interface */
:root {
  /* Modern UI theme using Turquoise color scheme */
  --modern-bg: #283b48; /* Medium Teal/Blue */
  --modern-text: #FFFFFF; /* Pure White */
  --modern-border-radius: 20px;
  --modern-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  --modern-gradient: linear-gradient(135deg, #00a6c0, #48d7ce); /* Turquoise Gradient */
  --modern-backdrop-blur: blur(15px);
  --modern-glass-bg: rgba(40, 59, 72, 0.7); /* Medium Teal/Blue with transparency */
  --modern-glass-border: 1px solid rgba(242, 244, 247, 0.1); /* Fog Gray with transparency */
  --modern-input-bg: rgba(40, 59, 72, 0.8); /* Medium Teal/Blue with transparency */
  --modern-blue-gradient: linear-gradient(135deg, #283b48, #1a2a36); /* Teal/Blue Gradient */
}

/* Floating Chat Container */
.floating-chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  max-height: 500px;
  background-color: rgba(20, 21, 23, 0.9); /* Coal Black with transparency */
  border-radius: var(--modern-border-radius);
  box-shadow: var(--modern-shadow);
  backdrop-filter: var(--modern-backdrop-blur);
  -webkit-backdrop-filter: var(--modern-backdrop-blur);
  border: var(--modern-glass-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 9999;
  transition: all 0.3s ease;
  background-image: linear-gradient(135deg,
    rgba(255, 79, 24, 0.05) 0%, /* Radiant Orange */
    rgba(242, 244, 247, 0.05) 100%); /* Fog Gray */
}

/* Chat Header */
.floating-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: rgba(20, 21, 23, 0.95); /* Coal Black with transparency */
  border-bottom: var(--modern-glass-border);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.floating-chat-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--modern-text);
  font-weight: 500;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, sans-serif;
}

.floating-chat-title img {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background-color: #4361ee;
}

.floating-chat-subtitle {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 2px;
  font-weight: 400;
}

.floating-chat-close {
  background-color: rgba(220, 53, 69, 0.8);
  color: var(--modern-text);
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(220, 53, 69, 0.3);
}

.floating-chat-close:hover {
  background-color: rgba(220, 53, 69, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.5);
}

/* Chat Messages */
.floating-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: rgba(20, 21, 23, 0.5); /* Coal Black with transparency */
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 350px;
}

/* Floating Chat Message */
.floating-chat-message {
  padding: 12px 16px;
  border-radius: 12px;
  background-color: rgba(30, 31, 34, 0.7); /* Lighter Coal Black with transparency */
  color: #FFFFFF; /* Pure White */
  font-size: 14px;
  line-height: 1.5;
  max-width: 85%;
  align-self: flex-start;
  backdrop-filter: var(--modern-backdrop-blur);
  -webkit-backdrop-filter: var(--modern-backdrop-blur);
  border: var(--modern-glass-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: floatIn 0.3s ease-out forwards;
  font-family: var(--font-primary);
  letter-spacing: 0.2px;
}

/* Enhanced search results styling */
.search-result {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-platform {
  font-weight: 600;
  color: #FF4F18; /* Radiant Orange */
  display: flex;
  align-items: center;
  gap: 6px;
}

.search-platform i {
  font-size: 16px;
}

.search-query, .search-understanding {
  font-size: 14px;
  color: #FFFFFF; /* Pure White */
  line-height: 1.4;
}

.search-understanding {
  background-color: rgba(255, 79, 24, 0.1); /* Radiant Orange with transparency */
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 2px solid #FF4F18; /* Radiant Orange */
}

.search-understanding strong {
  color: #FF4F18; /* Radiant Orange */
  font-weight: 600;
}

/* Confidence indicators */
.confidence {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  font-weight: normal;
  opacity: 0.8;
  display: inline-flex;
  align-items: center;
}

.confidence.high {
  background-color: rgba(72, 215, 206, 0.2);
  color: var(--primary-light);
  border: 1px solid rgba(72, 215, 206, 0.3);
}

.confidence.medium {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.confidence.low {
  background-color: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.search-action {
  margin-top: 5px;
}

.search-action a {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: rgba(255, 79, 24, 0.2); /* Radiant Orange with transparency */
  padding: 6px 12px;
  border-radius: 16px;
  transition: all 0.2s ease;
  color: #FFFFFF; /* Pure White */
  text-decoration: none;
}

.search-action a:hover {
  background-color: rgba(255, 79, 24, 0.4); /* Radiant Orange with more opacity */
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 79, 24, 0.3); /* Radiant Orange shadow */
}

/* User message in floating chat */
.floating-chat-message.user {
  background: var(--modern-gradient); /* Voldemort to lighter Voldemort gradient */
  align-self: flex-end;
  border-top-right-radius: 4px;
  color: #FFFFFF; /* Pure White for better contrast */
}

/* Chat Input Area */
.floating-chat-input-container {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: rgba(20, 21, 23, 0.8); /* Coal Black with transparency */
  border-top: var(--modern-glass-border);
}

.floating-chat-input {
  flex: 1;
  background-color: var(--modern-input-bg);
  border: var(--modern-glass-border);
  border-radius: 15px;
  padding: 10px 15px;
  color: var(--modern-text);
  font-size: 14px;
  outline: none;
  resize: none;
  min-height: 20px;
  max-height: 100px;
  font-family: var(--font-primary);
}

.floating-chat-input::placeholder {
  color: rgba(255, 255, 255, 0.5); /* White with transparency */
}

/* Chat Controls */
.floating-chat-controls {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
  background-color: rgba(20, 21, 23, 0.8); /* Coal Black with transparency */
  border-top: var(--modern-glass-border);
  position: relative;
}

.floating-chat-control-button {
  background-color: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 14px;
}

.floating-chat-control-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Floating Actions Dropdown */
.floating-actions-dropdown-menu {
  position: absolute;
  bottom: 45px;
  right: 15px;
  background-color: rgba(20, 21, 23, 0.95); /* Coal Black with transparency */
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(242, 244, 247, 0.1); /* Fog Gray with transparency */
  width: 280px;
  z-index: 10000;
  overflow: hidden;
  display: none;
}

.floating-actions-dropdown-menu.show {
  display: block;
  animation: fadeIn 0.2s ease-out forwards;
}

.floating-actions-dropdown-btn.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
}

.floating-actions-dropdown-btn.has-active {
  color: #FF4F18; /* Radiant Orange */
}

.floating-actions-dropdown-btn.highlight-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 79, 24, 0.4); /* Radiant Orange */
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 79, 24, 0); /* Radiant Orange */
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 79, 24, 0); /* Radiant Orange */
  }
}

/* Up Button (Expand) */
.floating-chat-up-button {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background-color: #FFFFFF; /* Pure White */
  color: #141517; /* Coal Black */
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: all 0.2s ease;
  font-size: 16px;
}

.floating-chat-up-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  background-color: #FF4F18; /* Radiant Orange */
  color: #FFFFFF; /* Pure White */
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .floating-chat-container {
    width: 90%;
    right: 5%;
    bottom: 10px;
  }
}

/* Animation for chat appearance */
@keyframes floatIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.floating-chat-container {
  animation: floatIn 0.3s ease-out forwards;
}

/* Background gradient for the page */
body.modern-ui-active {
  background: linear-gradient(135deg, #141517 0%, #141517 100%); /* Coal Black */
  transition: background 0.5s ease;
}
