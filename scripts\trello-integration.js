'use strict';

/**
 * Trello Integration class for connecting with Trello API
 */
class TrelloIntegration {
  /**
   * Initialize the Trello Integration
   * @param {StorageManager} storageManager - The storage manager instance
   */
  constructor(storageManager) {
    this.storageManager = storageManager;
    this.apiKey = null;
    this.token = null;
    this.apiBaseUrl = 'https://api.trello.com/1';
    this.authWindowId = null;

    // Trello API key for the extension
    this.appKey = ''; // You need to set your own Trello API key in the settings

    // Initialize
    this.init();
  }

  /**
   * Initialize the integration
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // Load credentials from storage
      const credentials = await this.storageManager.get('trello_credentials');
      if (credentials) {
        const parsed = JSON.parse(credentials);
        this.apiKey = parsed.apiKey;
        this.token = parsed.token;
        console.log('Trello credentials loaded from storage');
      }
    } catch (error) {
      console.error('Error initializing Trello integration:', error);
    }
  }

  /**
   * Check if authenticated with Trello
   * @returns {Promise<boolean>} - Whether authenticated
   */
  async isAuthenticated() {
    try {
      if (!this.apiKey || !this.token) {
        return false;
      }

      // Verify the credentials by making a test request
      const response = await this.makeRequest('/members/me');

      return response && response.id ? true : false;
    } catch (error) {
      console.error('Error checking Trello authentication:', error);
      return false;
    }
  }

  /**
   * Authenticate with Trello
   * @returns {Promise<boolean>} - Whether authentication was successful
   */
  async authenticate() {
    try {
      // For Trello, we need to use OAuth
      const authUrl = `https://trello.com/1/authorize?expiration=never&name=SoulAI&scope=read,write&response_type=token&key=${this.appKey}&callback_method=fragment&return_url=${encodeURIComponent(chrome.runtime.getURL('popup/auth-callback.html'))}`;

      // Open the auth window
      const authWindow = await chrome.windows.create({
        url: authUrl,
        type: 'popup',
        width: 800,
        height: 600
      });

      this.authWindowId = authWindow.id;

      // Wait for the auth callback
      return new Promise((resolve, reject) => {
        const handleAuthCallback = (message, sender, sendResponse) => {
          if (message.action === 'trello_auth_callback' && message.token) {
            // Save the credentials
            this.apiKey = this.appKey;
            this.token = message.token;

            // Save to storage
            this.storageManager.set('trello_credentials', JSON.stringify({
              apiKey: this.apiKey,
              token: this.token
            }));

            // Close the auth window
            if (this.authWindowId) {
              chrome.windows.remove(this.authWindowId);
              this.authWindowId = null;
            }

            // Remove the listener
            chrome.runtime.onMessage.removeListener(handleAuthCallback);

            resolve(true);
          } else if (message.action === 'trello_auth_error') {
            // Close the auth window
            if (this.authWindowId) {
              chrome.windows.remove(this.authWindowId);
              this.authWindowId = null;
            }

            // Remove the listener
            chrome.runtime.onMessage.removeListener(handleAuthCallback);

            reject(new Error(message.error || 'Authentication failed'));
          }
        };

        // Listen for the auth callback
        chrome.runtime.onMessage.addListener(handleAuthCallback);

        // Set a timeout
        setTimeout(() => {
          // Remove the listener
          chrome.runtime.onMessage.removeListener(handleAuthCallback);

          // Close the auth window
          if (this.authWindowId) {
            chrome.windows.remove(this.authWindowId);
            this.authWindowId = null;
          }

          reject(new Error('Authentication timed out'));
        }, 300000); // 5 minutes
      });
    } catch (error) {
      console.error('Error authenticating with Trello:', error);
      throw error;
    }
  }

  /**
   * Disconnect from Trello
   * @returns {Promise<boolean>} - Whether disconnection was successful
   */
  async disconnect() {
    try {
      // Clear the credentials
      this.apiKey = null;
      this.token = null;

      // Remove from storage
      await this.storageManager.remove('trello_credentials');

      return true;
    } catch (error) {
      console.error('Error disconnecting from Trello:', error);
      throw error;
    }
  }

  /**
   * Make a request to the Trello API
   * @param {string} endpoint - The API endpoint
   * @param {string} method - The HTTP method
   * @param {Object} data - The request data
   * @returns {Promise<Object>} - The response data
   */
  async makeRequest(endpoint, method = 'GET', data = null) {
    try {
      if (!this.apiKey || !this.token) {
        throw new Error('Not authenticated with Trello');
      }

      // Add auth params to the endpoint
      const authParams = `key=${this.apiKey}&token=${this.token}`;
      const url = `${this.apiBaseUrl}${endpoint}${endpoint.includes('?') ? '&' : '?'}${authParams}`;

      const options = {
        method: method,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`Trello API error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error making Trello API request:', error);
      throw error;
    }
  }

  /**
   * Create a new item in Trello
   * @param {string} itemType - The type of item to create (board, list, card)
   * @param {Object} content - The content of the item
   * @param {Object} options - Additional options for the item
   * @returns {Promise<Object>} - The created item
   */
  async createItem(itemType, content, options = {}) {
    try {
      switch (itemType.toLowerCase()) {
        case 'board':
          return await this.createBoard(content, options);
        case 'list':
          return await this.createList(content, options);
        case 'card':
          return await this.createCard(content, options);
        default:
          throw new Error(`Unknown item type: ${itemType}`);
      }
    } catch (error) {
      console.error(`Error creating ${itemType} in Trello:`, error);
      throw error;
    }
  }

  /**
   * Create a new board in Trello
   * @param {Object} content - The content of the board
   * @param {Object} options - Additional options for the board
   * @returns {Promise<Object>} - The created board
   */
  async createBoard(content, options = {}) {
    try {
      // Prepare the board data
      const boardData = {
        name: content.name || 'Untitled Board',
        desc: content.desc || '',
        defaultLists: content.defaultLists !== undefined ? content.defaultLists : true,
        prefs: options.prefs || {}
      };

      // Create the board
      const response = await this.makeRequest('/boards', 'POST', boardData);

      return response;
    } catch (error) {
      console.error('Error creating board in Trello:', error);
      throw error;
    }
  }

  /**
   * Create a new list in Trello
   * @param {Object} content - The content of the list
   * @param {Object} options - Additional options for the list
   * @returns {Promise<Object>} - The created list
   */
  async createList(content, options = {}) {
    try {
      // Check if board ID is provided
      if (!options.idBoard) {
        throw new Error('Board ID is required to create a list');
      }

      // Prepare the list data
      const listData = {
        name: content.name || 'Untitled List',
        idBoard: options.idBoard,
        pos: options.pos || 'bottom'
      };

      // Create the list
      const response = await this.makeRequest('/lists', 'POST', listData);

      return response;
    } catch (error) {
      console.error('Error creating list in Trello:', error);
      throw error;
    }
  }

  /**
   * Create a new card in Trello
   * @param {Object} content - The content of the card
   * @param {Object} options - Additional options for the card
   * @returns {Promise<Object>} - The created card
   */
  async createCard(content, options = {}) {
    try {
      // Check if list ID is provided
      if (!options.idList) {
        throw new Error('List ID is required to create a card');
      }

      // Prepare the card data
      const cardData = {
        name: content.name || 'Untitled Card',
        desc: content.desc || '',
        idList: options.idList,
        pos: options.pos || 'bottom',
        due: options.due || null,
        dueComplete: options.dueComplete || false,
        idMembers: options.idMembers || [],
        idLabels: options.idLabels || []
      };

      // Create the card
      const response = await this.makeRequest('/cards', 'POST', cardData);

      return response;
    } catch (error) {
      console.error('Error creating card in Trello:', error);
      throw error;
    }
  }

  /**
   * Get items from Trello
   * @param {string} itemType - The type of items to get (boards, lists, cards)
   * @param {Object} options - Options for filtering and sorting items
   * @returns {Promise<Array>} - The retrieved items
   */
  async getItems(itemType, options = {}) {
    try {
      switch (itemType.toLowerCase()) {
        case 'boards':
          return await this.getBoards(options);
        case 'lists':
          return await this.getLists(options);
        case 'cards':
          return await this.getCards(options);
        default:
          throw new Error(`Unknown item type: ${itemType}`);
      }
    } catch (error) {
      console.error(`Error getting ${itemType} from Trello:`, error);
      throw error;
    }
  }

  /**
   * Get boards from Trello
   * @param {Object} options - Options for filtering and sorting boards
   * @returns {Promise<Array>} - The retrieved boards
   */
  async getBoards(options = {}) {
    try {
      // Get the user's boards
      const response = await this.makeRequest('/members/me/boards');

      // Filter if needed
      if (options.filter) {
        return response.filter(board => {
          // Apply filters
          if (options.filter.name && !board.name.toLowerCase().includes(options.filter.name.toLowerCase())) {
            return false;
          }

          return true;
        });
      }

      return response;
    } catch (error) {
      console.error('Error getting boards from Trello:', error);
      throw error;
    }
  }

  /**
   * Get lists from Trello
   * @param {Object} options - Options for filtering and sorting lists
   * @returns {Promise<Array>} - The retrieved lists
   */
  async getLists(options = {}) {
    try {
      // Check if board ID is provided
      if (!options.idBoard) {
        throw new Error('Board ID is required to get lists');
      }

      // Get the lists for the board
      const response = await this.makeRequest(`/boards/${options.idBoard}/lists`);

      // Filter if needed
      if (options.filter) {
        return response.filter(list => {
          // Apply filters
          if (options.filter.name && !list.name.toLowerCase().includes(options.filter.name.toLowerCase())) {
            return false;
          }

          return true;
        });
      }

      return response;
    } catch (error) {
      console.error('Error getting lists from Trello:', error);
      throw error;
    }
  }

  /**
   * Get cards from Trello
   * @param {Object} options - Options for filtering and sorting cards
   * @returns {Promise<Array>} - The retrieved cards
   */
  async getCards(options = {}) {
    try {
      let endpoint;

      // Check if list ID or board ID is provided
      if (options.idList) {
        endpoint = `/lists/${options.idList}/cards`;
      } else if (options.idBoard) {
        endpoint = `/boards/${options.idBoard}/cards`;
      } else {
        throw new Error('List ID or board ID is required to get cards');
      }

      // Get the cards
      const response = await this.makeRequest(endpoint);

      // Filter if needed
      if (options.filter) {
        return response.filter(card => {
          // Apply filters
          if (options.filter.name && !card.name.toLowerCase().includes(options.filter.name.toLowerCase())) {
            return false;
          }

          if (options.filter.due === 'overdue' && card.due) {
            const dueDate = new Date(card.due);
            if (dueDate > new Date()) {
              return false;
            }
          }

          return true;
        });
      }

      return response;
    } catch (error) {
      console.error('Error getting cards from Trello:', error);
      throw error;
    }
  }
}
