'use strict';

/**
 * Proxy Service for handling web requests
 * This service helps bypass CORS restrictions and handle authentication
 */
class ProxyService {
  /**
   * Initialize the proxy service
   */
  constructor() {
    // Available proxy servers (add more as needed)
    this.proxyServers = [
      'https://corsproxy.io/?',
      'https://cors-anywhere.herokuapp.com/',
      'https://api.allorigins.win/raw?url='
    ];
    
    // Current proxy index
    this.currentProxyIndex = 0;
    
    // Sites that need special handling
    this.specialSites = {
      'instagram.com': this.handleInstagram,
      'facebook.com': this.handleFacebook,
      'twitter.com': this.handleTwitter,
      'x.com': this.handleTwitter,
      'linkedin.com': this.handleLinkedIn
    };
  }
  
  /**
   * Get the current proxy URL
   * @returns {string} - The proxy URL
   */
  getCurrentProxy() {
    return this.proxyServers[this.currentProxyIndex];
  }
  
  /**
   * Rotate to the next proxy server
   * @returns {string} - The next proxy URL
   */
  rotateProxy() {
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxyServers.length;
    return this.getCurrentProxy();
  }
  
  /**
   * Check if a URL needs special handling
   * @param {string} url - The URL to check
   * @returns {boolean} - Whether the URL needs special handling
   */
  needsSpecialHandling(url) {
    try {
      const hostname = new URL(url).hostname;
      return Object.keys(this.specialSites).some(site => hostname.includes(site));
    } catch (error) {
      console.error('Error checking if URL needs special handling:', error);
      return false;
    }
  }
  
  /**
   * Get the special handler for a URL
   * @param {string} url - The URL to get the handler for
   * @returns {Function|null} - The special handler function or null
   */
  getSpecialHandler(url) {
    try {
      const hostname = new URL(url).hostname;
      for (const site in this.specialSites) {
        if (hostname.includes(site)) {
          return this.specialSites[site];
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting special handler:', error);
      return null;
    }
  }
  
  /**
   * Fetch a URL through the proxy
   * @param {string} url - The URL to fetch
   * @param {Object} options - Fetch options
   * @returns {Promise<Object>} - The response
   */
  async fetchWithProxy(url, options = {}) {
    // Check if the URL needs special handling
    if (this.needsSpecialHandling(url)) {
      const handler = this.getSpecialHandler(url);
      if (handler) {
        return await handler.call(this, url, options);
      }
    }
    
    // Try each proxy server until one works
    for (let i = 0; i < this.proxyServers.length; i++) {
      try {
        const proxyUrl = this.getCurrentProxy() + encodeURIComponent(url);
        
        // Add default headers
        const headers = {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Referer': 'https://www.google.com/',
          ...options.headers
        };
        
        // Perform the fetch
        const response = await fetch(proxyUrl, {
          ...options,
          headers
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // Get the response text
        const text = await response.text();
        
        return {
          success: true,
          content: text,
          status: response.status,
          headers: Object.fromEntries(response.headers.entries())
        };
      } catch (error) {
        console.error(`Error with proxy ${this.getCurrentProxy()}:`, error);
        
        // Rotate to the next proxy
        this.rotateProxy();
        
        // If we've tried all proxies, throw the error
        if (i === this.proxyServers.length - 1) {
          throw error;
        }
      }
    }
  }
  
  /**
   * Special handler for Instagram
   * @param {string} url - The Instagram URL
   * @param {Object} options - Fetch options
   * @returns {Promise<Object>} - The response
   */
  async handleInstagram(url, options = {}) {
    try {
      // For Instagram, we need to use a different approach
      // First, try to get the public page
      const proxyUrl = this.getCurrentProxy() + encodeURIComponent(url);
      
      // Add Instagram-specific headers
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://www.instagram.com/',
        ...options.headers
      };
      
      // Perform the fetch
      const response = await fetch(proxyUrl, {
        ...options,
        headers
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Get the response text
      const text = await response.text();
      
      // Extract the shared data from the page
      const sharedDataMatch = text.match(/<script type="text\/javascript">window\._sharedData = (.+?);<\/script>/);
      let enhancedContent = text;
      
      if (sharedDataMatch && sharedDataMatch[1]) {
        try {
          const sharedData = JSON.parse(sharedDataMatch[1]);
          
          // Extract useful information from the shared data
          if (sharedData.entry_data && sharedData.entry_data.ProfilePage) {
            const profileData = sharedData.entry_data.ProfilePage[0].graphql.user;
            
            // Create a more accessible version of the profile data
            const profileSummary = `
              <div id="instagram-profile-summary">
                <h1>${profileData.full_name || profileData.username}</h1>
                <p>@${profileData.username}</p>
                <p>${profileData.biography || ''}</p>
                <p>Posts: ${profileData.edge_owner_to_timeline_media.count}</p>
                <p>Followers: ${profileData.edge_followed_by.count}</p>
                <p>Following: ${profileData.edge_follow.count}</p>
              </div>
            `;
            
            // Add the profile summary to the content
            enhancedContent = profileSummary + enhancedContent;
          }
          
          // Extract posts if available
          if (sharedData.entry_data && 
              sharedData.entry_data.ProfilePage && 
              sharedData.entry_data.ProfilePage[0].graphql.user.edge_owner_to_timeline_media) {
            
            const posts = sharedData.entry_data.ProfilePage[0].graphql.user.edge_owner_to_timeline_media.edges;
            
            if (posts && posts.length > 0) {
              let postsHtml = '<div id="instagram-posts-summary"><h2>Recent Posts</h2>';
              
              posts.forEach(post => {
                const node = post.node;
                postsHtml += `
                  <div class="instagram-post">
                    <p>Likes: ${node.edge_liked_by.count}</p>
                    <p>Comments: ${node.edge_media_to_comment.count}</p>
                    <p>${node.edge_media_to_caption.edges[0]?.node.text || 'No caption'}</p>
                  </div>
                `;
              });
              
              postsHtml += '</div>';
              
              // Add the posts summary to the content
              enhancedContent = postsHtml + enhancedContent;
            }
          }
        } catch (parseError) {
          console.error('Error parsing Instagram shared data:', parseError);
        }
      }
      
      return {
        success: true,
        content: enhancedContent,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };
    } catch (error) {
      console.error('Error handling Instagram:', error);
      
      // Fallback to regular proxy fetch
      return await this.fetchWithProxy(url, options);
    }
  }
  
  /**
   * Special handler for Facebook
   * @param {string} url - The Facebook URL
   * @param {Object} options - Fetch options
   * @returns {Promise<Object>} - The response
   */
  async handleFacebook(url, options = {}) {
    // Similar approach to Instagram but customized for Facebook
    try {
      const proxyUrl = this.getCurrentProxy() + encodeURIComponent(url);
      
      // Add Facebook-specific headers
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://www.facebook.com/',
        ...options.headers
      };
      
      // Perform the fetch
      const response = await fetch(proxyUrl, {
        ...options,
        headers
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Get the response text
      const text = await response.text();
      
      return {
        success: true,
        content: text,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };
    } catch (error) {
      console.error('Error handling Facebook:', error);
      
      // Fallback to regular proxy fetch
      return await this.fetchWithProxy(url, options);
    }
  }
  
  /**
   * Special handler for Twitter/X
   * @param {string} url - The Twitter URL
   * @param {Object} options - Fetch options
   * @returns {Promise<Object>} - The response
   */
  async handleTwitter(url, options = {}) {
    try {
      const proxyUrl = this.getCurrentProxy() + encodeURIComponent(url);
      
      // Add Twitter-specific headers
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://twitter.com/',
        ...options.headers
      };
      
      // Perform the fetch
      const response = await fetch(proxyUrl, {
        ...options,
        headers
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Get the response text
      const text = await response.text();
      
      return {
        success: true,
        content: text,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };
    } catch (error) {
      console.error('Error handling Twitter:', error);
      
      // Fallback to regular proxy fetch
      return await this.fetchWithProxy(url, options);
    }
  }
  
  /**
   * Special handler for LinkedIn
   * @param {string} url - The LinkedIn URL
   * @param {Object} options - Fetch options
   * @returns {Promise<Object>} - The response
   */
  async handleLinkedIn(url, options = {}) {
    try {
      const proxyUrl = this.getCurrentProxy() + encodeURIComponent(url);
      
      // Add LinkedIn-specific headers
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://www.linkedin.com/',
        ...options.headers
      };
      
      // Perform the fetch
      const response = await fetch(proxyUrl, {
        ...options,
        headers
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      // Get the response text
      const text = await response.text();
      
      return {
        success: true,
        content: text,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };
    } catch (error) {
      console.error('Error handling LinkedIn:', error);
      
      // Fallback to regular proxy fetch
      return await this.fetchWithProxy(url, options);
    }
  }
}

// Export the ProxyService
export default ProxyService;
