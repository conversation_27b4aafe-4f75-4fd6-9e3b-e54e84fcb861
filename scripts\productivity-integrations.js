'use strict';

/**
 * Productivity Integrations class for connecting with popular productivity tools
 */
class ProductivityIntegrations {
  /**
   * Initialize the Productivity Integrations
   * @param {APIManager} apiManager - The API manager instance
   * @param {UIManager} uiManager - The UI manager instance
   * @param {StorageManager} storageManager - The storage manager instance
   */
  constructor(apiManager, uiManager, storageManager) {
    this.apiManager = apiManager;
    this.uiManager = uiManager;
    this.storageManager = storageManager;

    // Platform integration instances
    this.notionIntegration = null;
    this.trelloIntegration = null;
    this.googleIntegration = null;

    // Authentication states
    this.authStates = {
      notion: false,
      trello: false,
      google: false
    };

    // Initialize integrations
    this.initIntegrations();
  }

  /**
   * Initialize all integrations
   * @returns {Promise<void>}
   */
  async initIntegrations() {
    try {
      // Load authentication states from storage
      const authData = await this.storageManager.get('productivity_auth_states');
      if (authData) {
        this.authStates = JSON.parse(authData) || this.authStates;
      }

      // Initialize platform-specific integrations with error handling
      try {
        if (typeof NotionIntegration !== 'undefined') {
          this.notionIntegration = new NotionIntegration(this.storageManager);
          // Check if token is valid and update auth state
          this.authStates.notion = await this.notionIntegration.isAuthenticated();
        } else {
          console.warn('NotionIntegration class is not defined');
          this.authStates.notion = false;
        }
      } catch (error) {
        console.error('Error initializing Notion integration:', error);
        this.authStates.notion = false;
      }

      try {
        if (typeof TrelloIntegration !== 'undefined') {
          this.trelloIntegration = new TrelloIntegration(this.storageManager);
          // Check if token is valid and update auth state
          this.authStates.trello = await this.trelloIntegration.isAuthenticated();
        } else {
          console.warn('TrelloIntegration class is not defined');
          this.authStates.trello = false;
        }
      } catch (error) {
        console.error('Error initializing Trello integration:', error);
        this.authStates.trello = false;
      }

      try {
        if (typeof GoogleIntegration !== 'undefined') {
          this.googleIntegration = new GoogleIntegration(this.storageManager);
          // Check if token is valid and update auth state
          this.authStates.google = await this.googleIntegration.isAuthenticated();
        } else {
          console.warn('GoogleIntegration class is not defined');
          this.authStates.google = false;
        }
      } catch (error) {
        console.error('Error initializing Google integration:', error);
        this.authStates.google = false;
      }

      // Save updated auth states
      await this.storageManager.set('productivity_auth_states', JSON.stringify(this.authStates));

      console.log('Productivity integrations initialized:', this.authStates);
    } catch (error) {
      console.error('Error initializing productivity integrations:', error);
    }
  }

  /**
   * Get the authentication status for all platforms
   * @returns {Object} - Authentication status for each platform
   */
  getAuthStatus() {
    return this.authStates;
  }

  /**
   * Authenticate with a specific platform
   * @param {string} platform - The platform to authenticate with (notion, trello, google)
   * @returns {Promise<boolean>} - Whether authentication was successful
   */
  async authenticate(platform) {
    try {
      let success = false;

      switch (platform.toLowerCase()) {
        case 'notion':
          success = await this.notionIntegration.authenticate();
          break;
        case 'trello':
          success = await this.trelloIntegration.authenticate();
          break;
        case 'google':
          success = await this.googleIntegration.authenticate();
          break;
        default:
          throw new Error(`Unknown platform: ${platform}`);
      }

      // Update auth state
      this.authStates[platform.toLowerCase()] = success;
      await this.storageManager.set('productivity_auth_states', JSON.stringify(this.authStates));

      return success;
    } catch (error) {
      console.error(`Error authenticating with ${platform}:`, error);
      throw new Error(`Failed to authenticate with ${platform}: ${error.message}`);
    }
  }

  /**
   * Disconnect from a specific platform
   * @param {string} platform - The platform to disconnect from (notion, trello, google)
   * @returns {Promise<boolean>} - Whether disconnection was successful
   */
  async disconnect(platform) {
    try {
      let success = false;

      switch (platform.toLowerCase()) {
        case 'notion':
          success = await this.notionIntegration.disconnect();
          break;
        case 'trello':
          success = await this.trelloIntegration.disconnect();
          break;
        case 'google':
          success = await this.googleIntegration.disconnect();
          break;
        default:
          throw new Error(`Unknown platform: ${platform}`);
      }

      // Update auth state
      this.authStates[platform.toLowerCase()] = false;
      await this.storageManager.set('productivity_auth_states', JSON.stringify(this.authStates));

      return success;
    } catch (error) {
      console.error(`Error disconnecting from ${platform}:`, error);
      throw new Error(`Failed to disconnect from ${platform}: ${error.message}`);
    }
  }

  /**
   * Create a new item in a productivity platform
   * @param {string} platform - The platform to create the item in (notion, trello, google)
   * @param {string} itemType - The type of item to create (page, card, document, etc.)
   * @param {Object} content - The content of the item
   * @param {Object} options - Additional options for the item
   * @returns {Promise<Object>} - The created item
   */
  async createItem(platform, itemType, content, options = {}) {
    try {
      // Check if authenticated
      if (!this.authStates[platform.toLowerCase()]) {
        throw new Error(`Not authenticated with ${platform}. Please connect first.`);
      }

      let result;

      switch (platform.toLowerCase()) {
        case 'notion':
          result = await this.notionIntegration.createItem(itemType, content, options);
          break;
        case 'trello':
          result = await this.trelloIntegration.createItem(itemType, content, options);
          break;
        case 'google':
          result = await this.googleIntegration.createItem(itemType, content, options);
          break;
        default:
          throw new Error(`Unknown platform: ${platform}`);
      }

      return result;
    } catch (error) {
      console.error(`Error creating item in ${platform}:`, error);
      throw new Error(`Failed to create item in ${platform}: ${error.message}`);
    }
  }

  /**
   * Get items from a productivity platform
   * @param {string} platform - The platform to get items from (notion, trello, google)
   * @param {string} itemType - The type of items to get (pages, cards, documents, etc.)
   * @param {Object} options - Options for filtering and sorting items
   * @returns {Promise<Array>} - The retrieved items
   */
  async getItems(platform, itemType, options = {}) {
    try {
      // Check if authenticated
      if (!this.authStates[platform.toLowerCase()]) {
        throw new Error(`Not authenticated with ${platform}. Please connect first.`);
      }

      let result;

      switch (platform.toLowerCase()) {
        case 'notion':
          result = await this.notionIntegration.getItems(itemType, options);
          break;
        case 'trello':
          result = await this.trelloIntegration.getItems(itemType, options);
          break;
        case 'google':
          result = await this.googleIntegration.getItems(itemType, options);
          break;
        default:
          throw new Error(`Unknown platform: ${platform}`);
      }

      return result;
    } catch (error) {
      console.error(`Error getting items from ${platform}:`, error);
      throw new Error(`Failed to get items from ${platform}: ${error.message}`);
    }
  }

  /**
   * Create a new item from the current page content
   * @param {string} platform - The platform to create the item in (notion, trello, google)
   * @param {string} itemType - The type of item to create (page, card, document, etc.)
   * @param {Object} options - Additional options for the item
   * @returns {Promise<Object>} - The created item
   */
  async createItemFromPage(platform, itemType, options = {}) {
    try {
      // Get the current page content
      const pageContent = await this.apiManager.getPageContent();

      if (!pageContent) {
        throw new Error('Could not get current page content');
      }

      // Prepare content based on platform and item type
      let content;

      switch (platform.toLowerCase()) {
        case 'notion':
          content = this.prepareNotionContent(pageContent, itemType);
          break;
        case 'trello':
          content = this.prepareTrelloContent(pageContent, itemType);
          break;
        case 'google':
          content = this.prepareGoogleContent(pageContent, itemType);
          break;
        default:
          throw new Error(`Unknown platform: ${platform}`);
      }

      // Create the item
      return await this.createItem(platform, itemType, content, options);
    } catch (error) {
      console.error(`Error creating item from page in ${platform}:`, error);
      throw new Error(`Failed to create item from page in ${platform}: ${error.message}`);
    }
  }

  /**
   * Prepare content for Notion based on page content
   * @param {Object} pageContent - The page content
   * @param {string} itemType - The type of item to create
   * @returns {Object} - The prepared content
   */
  prepareNotionContent(pageContent, itemType) {
    switch (itemType.toLowerCase()) {
      case 'page':
        return {
          title: pageContent.title || 'Untitled Page',
          content: pageContent.content || '',
          url: pageContent.url,
          timestamp: new Date().toISOString()
        };
      case 'database':
        return {
          title: pageContent.title || 'Untitled Database',
          url: pageContent.url,
          properties: {
            Title: {
              title: {}
            },
            URL: {
              url: {}
            },
            Content: {
              rich_text: {}
            },
            Date: {
              date: {}
            }
          }
        };
      default:
        return {
          title: pageContent.title || 'Untitled',
          content: pageContent.content || '',
          url: pageContent.url
        };
    }
  }

  /**
   * Prepare content for Trello based on page content
   * @param {Object} pageContent - The page content
   * @param {string} itemType - The type of item to create
   * @returns {Object} - The prepared content
   */
  prepareTrelloContent(pageContent, itemType) {
    switch (itemType.toLowerCase()) {
      case 'card':
        return {
          name: pageContent.title || 'Untitled Card',
          desc: `${pageContent.content ? pageContent.content.substring(0, 500) : ''}\n\nSource: ${pageContent.url}`,
          url: pageContent.url
        };
      case 'board':
        return {
          name: pageContent.title || 'Untitled Board',
          desc: `Board created from ${pageContent.url}`,
          defaultLists: true
        };
      default:
        return {
          name: pageContent.title || 'Untitled',
          desc: pageContent.content || '',
          url: pageContent.url
        };
    }
  }

  /**
   * Prepare content for Google Workspace based on page content
   * @param {Object} pageContent - The page content
   * @param {string} itemType - The type of item to create
   * @returns {Object} - The prepared content
   */
  prepareGoogleContent(pageContent, itemType) {
    switch (itemType.toLowerCase()) {
      case 'document':
        return {
          title: pageContent.title || 'Untitled Document',
          content: pageContent.content || '',
          sourceUrl: pageContent.url
        };
      case 'spreadsheet':
        return {
          title: pageContent.title || 'Untitled Spreadsheet',
          sourceUrl: pageContent.url
        };
      case 'event':
        return {
          summary: pageContent.title || 'Untitled Event',
          description: `${pageContent.content ? pageContent.content.substring(0, 500) : ''}\n\nSource: ${pageContent.url}`,
          start: {
            dateTime: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
          },
          end: {
            dateTime: new Date(Date.now() + 90000000).toISOString(), // Tomorrow + 1 hour
            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
          }
        };
      default:
        return {
          title: pageContent.title || 'Untitled',
          content: pageContent.content || '',
          sourceUrl: pageContent.url
        };
    }
  }

  /**
   * Create a new item from AI-generated content
   * @param {string} platform - The platform to create the item in (notion, trello, google)
   * @param {string} itemType - The type of item to create (page, card, document, etc.)
   * @param {string} prompt - The prompt to generate content from
   * @param {Object} options - Additional options for the item
   * @returns {Promise<Object>} - The created item
   */
  async createItemFromAI(platform, itemType, prompt, options = {}) {
    try {
      // Generate content using AI
      const response = await this.apiManager.sendRequest(
        `Please create content for a ${itemType} in ${platform} based on the following prompt: ${prompt}`,
        {
          systemPrompt: `You are an expert at creating content for productivity tools.
          Create well-structured, professional content for a ${itemType} in ${platform}.
          Focus on clarity, organization, and usefulness.
          Return only the content without any explanations or formatting instructions.`
        }
      );

      if (!response || !response.text) {
        throw new Error('Failed to generate content with AI');
      }

      // Prepare content based on platform and item type
      let content;

      switch (platform.toLowerCase()) {
        case 'notion':
          content = {
            title: options.title || this.extractTitle(response.text, 'Untitled Notion Page'),
            content: response.text
          };
          break;
        case 'trello':
          content = {
            name: options.title || this.extractTitle(response.text, 'Untitled Trello Card'),
            desc: response.text
          };
          break;
        case 'google':
          content = {
            title: options.title || this.extractTitle(response.text, 'Untitled Google Document'),
            content: response.text
          };
          break;
        default:
          throw new Error(`Unknown platform: ${platform}`);
      }

      // Create the item
      return await this.createItem(platform, itemType, content, options);
    } catch (error) {
      console.error(`Error creating item from AI in ${platform}:`, error);
      throw new Error(`Failed to create item from AI in ${platform}: ${error.message}`);
    }
  }

  /**
   * Extract a title from content
   * @param {string} content - The content to extract a title from
   * @param {string} defaultTitle - The default title if none is found
   * @returns {string} - The extracted title
   */
  extractTitle(content, defaultTitle = 'Untitled') {
    if (!content) return defaultTitle;

    // Try to extract a title from the first line
    const lines = content.split('\n');
    if (lines.length > 0) {
      const firstLine = lines[0].trim();

      // Check if the first line is a markdown heading
      if (firstLine.startsWith('# ')) {
        return firstLine.substring(2).trim();
      }

      // Otherwise, just use the first line if it's not too long
      if (firstLine.length > 0 && firstLine.length <= 100) {
        return firstLine;
      }
    }

    return defaultTitle;
  }

  /**
   * Apply a template to create a new item
   * @param {string} platform - The platform to create the item in (notion, trello, google)
   * @param {string} templateName - The name of the template to apply
   * @param {Object} variables - Variables to replace in the template
   * @returns {Promise<Object>} - The created item
   */
  async applyTemplate(platform, templateName, variables = {}) {
    try {
      // Get the template
      const templates = await this.getTemplates(platform);
      const template = templates.find(t => t.name === templateName);

      if (!template) {
        throw new Error(`Template not found: ${templateName}`);
      }

      // Replace variables in the template
      let content = JSON.parse(JSON.stringify(template.content)); // Deep clone

      // Replace variables in strings
      const replaceVariables = (obj) => {
        if (typeof obj === 'string') {
          return Object.entries(variables).reduce((str, [key, value]) => {
            return str.replace(new RegExp(`{{${key}}}`, 'g'), value);
          }, obj);
        } else if (Array.isArray(obj)) {
          return obj.map(item => replaceVariables(item));
        } else if (obj !== null && typeof obj === 'object') {
          const result = {};
          for (const [key, value] of Object.entries(obj)) {
            result[key] = replaceVariables(value);
          }
          return result;
        }
        return obj;
      };

      content = replaceVariables(content);

      // Create the item
      return await this.createItem(platform, template.itemType, content, template.options);
    } catch (error) {
      console.error(`Error applying template in ${platform}:`, error);
      throw new Error(`Failed to apply template in ${platform}: ${error.message}`);
    }
  }

  /**
   * Get available templates for a platform
   * @param {string} platform - The platform to get templates for (notion, trello, google)
   * @returns {Promise<Array>} - The available templates
   */
  async getTemplates(platform) {
    try {
      // Get templates from storage
      const templatesData = await this.storageManager.get(`${platform.toLowerCase()}_templates`);
      const templates = templatesData ? JSON.parse(templatesData) : [];

      // If no templates are available, return default templates
      if (!templates || templates.length === 0) {
        return this.getDefaultTemplates(platform);
      }

      return templates;
    } catch (error) {
      console.error(`Error getting templates for ${platform}:`, error);
      return this.getDefaultTemplates(platform);
    }
  }

  /**
   * Get default templates for a platform
   * @param {string} platform - The platform to get default templates for
   * @returns {Array} - The default templates
   */
  getDefaultTemplates(platform) {
    switch (platform.toLowerCase()) {
      case 'notion':
        return [
          {
            name: 'Meeting Notes',
            description: 'Template for meeting notes',
            itemType: 'page',
            content: {
              title: 'Meeting Notes: {{title}}',
              content: `# Meeting Notes: {{title}}\n\n**Date:** {{date}}\n**Participants:** {{participants}}\n\n## Agenda\n\n- \n\n## Discussion\n\n- \n\n## Action Items\n\n- [ ] \n\n## Next Steps\n\n- `
            },
            options: {}
          },
          {
            name: 'Project Plan',
            description: 'Template for project planning',
            itemType: 'page',
            content: {
              title: 'Project Plan: {{title}}',
              content: `# Project Plan: {{title}}\n\n**Start Date:** {{startDate}}\n**End Date:** {{endDate}}\n**Owner:** {{owner}}\n\n## Objectives\n\n- \n\n## Deliverables\n\n- \n\n## Timeline\n\n- \n\n## Resources\n\n- \n\n## Risks\n\n- `
            },
            options: {}
          }
        ];
      case 'trello':
        return [
          {
            name: 'Task List',
            description: 'Template for a task list',
            itemType: 'board',
            content: {
              name: '{{title}} Tasks',
              desc: 'Task board for {{title}}',
              defaultLists: true
            },
            options: {}
          },
          {
            name: 'Bug Report',
            description: 'Template for bug reporting',
            itemType: 'card',
            content: {
              name: 'Bug: {{title}}',
              desc: `**Description**\n{{description}}\n\n**Steps to Reproduce**\n1. \n\n**Expected Behavior**\n\n**Actual Behavior**\n\n**Environment**\n- Browser: \n- OS: \n\n**Additional Information**\n`
            },
            options: {}
          }
        ];
      case 'google':
        return [
          {
            name: 'Weekly Report',
            description: 'Template for weekly reports',
            itemType: 'document',
            content: {
              title: 'Weekly Report: {{title}}',
              content: `# Weekly Report: {{title}}\n\n**Week:** {{week}}\n**Prepared by:** {{author}}\n\n## Accomplishments\n\n- \n\n## Challenges\n\n- \n\n## Next Week's Goals\n\n- \n\n## Resources Needed\n\n- `
            },
            options: {}
          },
          {
            name: 'Event Planning',
            description: 'Template for event planning',
            itemType: 'spreadsheet',
            content: {
              title: 'Event Planning: {{title}}'
            },
            options: {}
          }
        ];
      default:
        return [];
    }
  }

  /**
   * Save a template
   * @param {string} platform - The platform to save the template for
   * @param {Object} template - The template to save
   * @returns {Promise<boolean>} - Whether the template was saved successfully
   */
  async saveTemplate(platform, template) {
    try {
      if (!template.name || !template.itemType || !template.content) {
        throw new Error('Invalid template format');
      }

      // Get existing templates
      const templates = await this.getTemplates(platform);

      // Check if template with the same name already exists
      const existingIndex = templates.findIndex(t => t.name === template.name);

      if (existingIndex >= 0) {
        // Update existing template
        templates[existingIndex] = template;
      } else {
        // Add new template
        templates.push(template);
      }

      // Save templates to storage
      await this.storageManager.set(`${platform.toLowerCase()}_templates`, JSON.stringify(templates));

      return true;
    } catch (error) {
      console.error(`Error saving template for ${platform}:`, error);
      throw new Error(`Failed to save template for ${platform}: ${error.message}`);
    }
  }

  /**
   * Delete a template
   * @param {string} platform - The platform to delete the template from
   * @param {string} templateName - The name of the template to delete
   * @returns {Promise<boolean>} - Whether the template was deleted successfully
   */
  async deleteTemplate(platform, templateName) {
    try {
      // Get existing templates
      const templates = await this.getTemplates(platform);

      // Filter out the template to delete
      const filteredTemplates = templates.filter(t => t.name !== templateName);

      if (filteredTemplates.length === templates.length) {
        throw new Error(`Template not found: ${templateName}`);
      }

      // Save filtered templates to storage
      await this.storageManager.set(`${platform.toLowerCase()}_templates`, JSON.stringify(filteredTemplates));

      return true;
    } catch (error) {
      console.error(`Error deleting template for ${platform}:`, error);
      throw new Error(`Failed to delete template for ${platform}: ${error.message}`);
    }
  }
}
