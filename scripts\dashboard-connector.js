/**
 * Dashboard Connector
 * Handles connection between the extension and the BrowzyAI dashboard
 */
class DashboardConnector {
  /**
   * Initialize the Dashboard Connector
   */
  constructor() {
    this.storageManager = new StorageManager();
    this.isConnected = false;
    this.connectionToken = null;
    this.userData = null;
    this.userPlan = null;
    // Use browzyai.com URLs for production    this.dashboardUrl = 'https://browzyai.com'; // Base URL for the dashboard
    this.apiUrl = 'https://api.unexpected-glynis-legendsumeet-c06bc78a.koyeb.app'
    this.connectionListeners = [];
    this.chatHistoryListeners = [];
    this.chatHistoryQueue = []; // Queue for chat messages to be sent
    this.isSendingChatHistory = false; // Flag to prevent multiple sends
    this.lastHistorySyncTime = 0; // Last time chat history was synced

    // Load connection data from storage
    this.loadConnectionData();
  }

  /**
   * Load connection data from storage
   */
  async loadConnectionData() {
    try {
      // Load token from storage using chrome.storage.local directly
      const result = await chrome.storage.local.get([
        'dashboard_connection_token',
        'dashboard_user_data',
        'dashboard_user_plan',
        'dashboard_last_sync'
      ]);

      if (result.dashboard_connection_token) {
        this.connectionToken = result.dashboard_connection_token;

        // Check if we have user data
        if (result.dashboard_user_data) {
          this.userData = result.dashboard_user_data;
          this.isConnected = true;

          // Load user plan if available
          if (result.dashboard_user_plan) {
            this.userPlan = result.dashboard_user_plan;
          }

          // Load last sync time if available
          if (result.dashboard_last_sync) {
            this.lastHistorySyncTime = result.dashboard_last_sync;
          }

          // Notify listeners
          this.notifyConnectionListeners();

          // Fetch user plan from server
          this.fetchUserPlan();
        }
      }
    } catch (error) {
      console.error('Error loading connection data:', error);
    }
  }

  /**
   * Save connection data to storage
   */
  async saveConnectionData() {
    try {
      // Create an object to store data
      const dataToSave = {
        dashboard_connection_token: this.connectionToken,
        dashboard_last_sync: this.lastHistorySyncTime
      };

      // Add user data if available
      if (this.userData) {
        dataToSave.dashboard_user_data = this.userData;
      }

      // Add user plan if available
      if (this.userPlan) {
        dataToSave.dashboard_user_plan = this.userPlan;
      }

      // Save to storage using chrome.storage.local directly
      await chrome.storage.local.set(dataToSave);
    } catch (error) {
      console.error('Error saving connection data:', error);
    }
  }

  /**
   * Validate a token with the server
   * @param {string} token - The token to validate
   * @returns {Promise<Object>} - The validation result
   */
  async validateToken(token) {
    try {
      console.log('Validating token with server:', token);

      // Get browser and system information
      const browserInfo = this.getBrowserInfo();
      const osInfo = this.getOSInfo();
      const deviceType = this.getDeviceType();
      const extensionVersion = chrome.runtime.getManifest().version;

      // Generate device fingerprint
      let deviceFingerprint = '';
      try {
        if (window.DeviceFingerprint) {
          deviceFingerprint = await window.DeviceFingerprint.generate();
          console.log('Generated device fingerprint:', deviceFingerprint);
        } else {
          console.warn('DeviceFingerprint not available, using fallback');
          // Simple fallback fingerprint
          const components = [
            navigator.userAgent,
            navigator.language,
            screen.colorDepth,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            navigator.platform
          ];
          const fingerprintStr = components.join('###');
          let hash = 0;
          for (let i = 0; i < fingerprintStr.length; i++) {
            hash = ((hash << 5) - hash) + fingerprintStr.charCodeAt(i);
            hash |= 0;
          }
          deviceFingerprint = Math.abs(hash).toString(16);
        }
      } catch (error) {
        console.error('Error generating device fingerprint:', error);
      }

      // Validate token with the server
      const response = await fetch(`${this.apiUrl}/api/extension/token/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': chrome.runtime.getURL('/')
        },
        body: JSON.stringify({
          token: token || this.connectionToken,
          extensionVersion,
          browserInfo,
          osInfo,
          deviceType,
          deviceFingerprint
        }),
        mode: 'cors',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Token validation failed (HTTP error):', errorData);
        return {
          success: false,
          error: errorData.error || 'Failed to validate token'
        };
      }

      // Parse response
      const data = await response.json();

      if (!data.success) {
        console.error('Token validation failed (API error):', data.error);
        return {
          success: false,
          error: data.error || 'Failed to validate token'
        };
      }

      // Return success with user data
      console.log('Token validation successful, user data:', {
        userId: data.userId,
        username: data.username,
        connectionId: data.connectionId,
        lastUsed: data.lastUsed
      });

      // Add more detailed logging for username
      console.log('Username from server:', data.username);

      // If we received a lastUsed timestamp, update the UI
      if (data.lastUsed) {
        console.log('Token was just used at:', data.lastUsed);
        // Notify listeners about the connection update
        this.notifyConnectionListeners('updated', {
          lastUsed: data.lastUsed
        });
      }

      return {
        success: true,
        userData: {
          userId: data.userId,
          username: data.username,
          connectionId: data.connectionId,
          lastUsed: data.lastUsed
        }
      };
    } catch (error) {
      console.error('Error validating token:', error);
      return {
        success: false,
        error: error.message || 'An error occurred while validating the token'
      };
    }
  }

  /**
   * Connect to the dashboard using a token
   * @param {string} token - The connection token
   * @returns {Promise<boolean>} - Whether the connection was successful
   */
  async connectWithToken(token) {
    try {
      // Validate token format (9 characters alphanumeric)
      if (!token || !/^[a-zA-Z0-9]{9}$/.test(token)) {
        throw new Error('Invalid token format. Token must be 9 characters long and contain only letters and numbers.');
      }

      // Store token
      this.connectionToken = token;

      // Notify listeners that we're connecting
      this.notifyConnectionListeners('connecting');

      // Get browser and system information
      const browserInfo = this.getBrowserInfo();
      const osInfo = this.getOSInfo();
      const deviceType = this.getDeviceType();
      const extensionVersion = chrome.runtime.getManifest().version;

      console.log('Browser info:', browserInfo);
      console.log('OS info:', osInfo);
      console.log('Device type:', deviceType);
      console.log('Extension version:', extensionVersion);

      // Validate the token
      const validationResult = await this.validateToken(token);

      if (!validationResult.success) {
        throw new Error(validationResult.error || 'Failed to validate token');
      }

      // Store user data
      this.userData = validationResult.userData;
      console.log('Setting user data:', this.userData);

      // Log username specifically to help with debugging
      console.log('Username being stored:', this.userData.username);

      // Set connected state
      this.isConnected = true;

      // Save connection data
      await this.saveConnectionData();

      // Fetch user plan
      await this.fetchUserPlan();

      // Notify listeners
      this.notifyConnectionListeners();

      // Update the user profile UI
      this.updateUserProfileUI();

      return true;
    } catch (error) {
      console.error('Error connecting to dashboard:', error);

      // Clear connection data
      this.connectionToken = null;
      this.userData = null;
      this.userPlan = null;
      this.isConnected = false;

      // Notify listeners
      this.notifyConnectionListeners('error', error.message);

      return false;
    }
  }

  /**
   * Fetch user plan from the server
   * @returns {Promise<boolean>} - Whether the plan was fetched successfully
   */
  async fetchUserPlan() {
    if (!this.isConnected || !this.connectionToken) {
      return false;
    }

    try {
      // Fetch user plan from server
      const response = await fetch(`${this.apiUrl}/api/extension/user/plan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': chrome.runtime.getURL('/')
        },
        body: JSON.stringify({ token: this.connectionToken }),
        mode: 'cors',
        credentials: 'include'
      });

      if (!response.ok) {
        console.error('Error fetching user plan:', response.statusText);
        return false;
      }

      // Parse response
      const data = await response.json();

      if (!data.success) {
        console.error('Error fetching user plan:', data.error);
        return false;
      }

      // Store user plan
      this.userPlan = data.plan;

      // Also fetch usage data
      try {
        const usageResponse = await fetch(`${this.apiUrl}/api/usage/summary`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.connectionToken}`,
            'Origin': chrome.runtime.getURL('/')
          },
          mode: 'cors',
          credentials: 'include'
        });

        if (usageResponse.ok) {
          const usageData = await usageResponse.json();

          if (usageData.success && usageData.summary) {
            // Add usage data to the user plan
            this.userPlan.requestsUsed = usageData.summary.daily.total;
            console.log(`Updated user plan with usage data: ${this.userPlan.requestsUsed} requests used`);
          }
        }
      } catch (usageError) {
        console.error('Error fetching usage data:', usageError);
        // Continue even if usage data fetch fails
      }

      // Save connection data
      await this.saveConnectionData();

      // Notify listeners with updated data
      this.notifyConnectionListeners();

      // Update the user profile UI
      this.updateUserProfileUI();

      return true;
    } catch (error) {
      console.error('Error fetching user plan:', error);
      return false;
    }
  }

  /**
   * Disconnect from the dashboard
   */
  async disconnect() {
    try {
      // Store user data before clearing
      const userId = this.userData?.userId;
      const connectionId = this.userData?.connectionId;

      // If we have user data, notify the server about disconnection
      if (userId) {
        try {
          console.log('Notifying server about disconnection for user:', userId);

          // Send disconnection to server
          const response = await fetch(`${this.apiUrl}/api/extension/disconnect`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Origin': chrome.runtime.getURL('/')
            },
            body: JSON.stringify({
              userId,
              connectionId
            }),
            mode: 'cors',
            credentials: 'include'
          });

          if (!response.ok) {
            console.error('Error notifying server about disconnection:', response.statusText);
          } else {
            console.log('Server notified about disconnection successfully');
          }
        } catch (notifyError) {
          console.error('Error notifying server about disconnection:', notifyError);
          // Continue with disconnection even if notification fails
        }
      }

      // Clear connection data
      this.connectionToken = null;
      this.userData = null;
      this.userPlan = null;
      this.isConnected = false;

      // Clear storage using chrome.storage.local directly
      await chrome.storage.local.remove([
        'dashboard_connection_token',
        'dashboard_user_data',
        'dashboard_user_plan',
        'dashboard_last_sync'
      ]);

      // Notify listeners
      this.notifyConnectionListeners();

      return true;
    } catch (error) {
      console.error('Error disconnecting from dashboard:', error);
      return false;
    }
  }

  /**
   * Send chat history to the server
   * @param {Array} messages - The chat messages to send
   * @returns {Promise<boolean>} - Whether the messages were sent successfully
   */
  async sendChatHistory(messages) {
    if (!this.isConnected || !this.connectionToken || !messages || !messages.length) {
      console.log('Cannot send chat history: Not connected or no messages');
      return false;
    }

    // Check usage limit for free users
    const hasReachedLimit = await this.checkUsageLimit();
    if (hasReachedLimit) {
      console.error('Usage limit reached, cannot send more messages');
      return false;
    }

    // Format messages to ensure they have all required fields
    const formattedMessages = messages.map(msg => ({
      id: msg.id || `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: msg.content || msg.query || '',
      sender: msg.sender || 'user',
      timestamp: msg.timestamp || new Date().toISOString(),
      url: msg.url || window.location.href
    }));

    // Add messages to queue
    this.chatHistoryQueue.push(...formattedMessages);

    // If already sending, return
    if (this.isSendingChatHistory) {
      console.log('Already sending chat history, added to queue');
      return true;
    }

    try {
      this.isSendingChatHistory = true;
      console.log(`Processing chat history queue with ${this.chatHistoryQueue.length} messages`);

      // Process queue in batches of 10
      while (this.chatHistoryQueue.length > 0) {
        const batch = this.chatHistoryQueue.splice(0, 10);
        console.log(`Sending batch of ${batch.length} messages to server`);

        // Get device fingerprint
        let deviceFingerprint = '';
        try {
          if (window.DeviceFingerprint) {
            deviceFingerprint = await window.DeviceFingerprint.generate();
          }
        } catch (error) {
          console.error('Error generating device fingerprint:', error);
        }

        // Send batch to server
        const response = await fetch(`${this.apiUrl}/api/extension/chat/history`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Origin': chrome.runtime.getURL('/')
          },
          body: JSON.stringify({
            token: this.connectionToken,
            messages: batch,
            deviceFingerprint
          }),
          mode: 'cors',
          credentials: 'include'
        });

        if (!response.ok) {
          console.error('Error sending chat history:', response.statusText);

          // Put messages back in queue
          this.chatHistoryQueue.unshift(...batch);

          // Stop processing
          break;
        }

        // Parse response
        const data = await response.json();
        if (data.success) {
          console.log(`Successfully sent ${batch.length} messages to server`);

          // Note: We no longer increment usage count here
          // Usage count is now handled by the sendChatMessage method
          // This ensures we only count messages that are explicitly sent by the user
          // and not duplicate counts when syncing history

          // Notify chat history listeners about the new messages
          this.notifyChatHistoryListeners(batch, true);

          // Update last sync time
          this.lastHistorySyncTime = Date.now();
          await this.saveConnectionData();
        } else {
          console.error('Server reported error sending chat history:', data.error);

          // Put messages back in queue
          this.chatHistoryQueue.unshift(...batch);

          // Stop processing
          break;
        }
      }

      return true;
    } catch (error) {
      console.error('Error sending chat history:', error);
      return false;
    } finally {
      this.isSendingChatHistory = false;
    }
  }

  /**
   * Get user plan information
   * @returns {Object|null} - The user plan
   */
  getUserPlan() {
    return this.userPlan;
  }

  /**
   * Get browser information
   * @returns {Object} - Browser name and version
   */
  getBrowserInfo() {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';

    // Chrome
    if (userAgent.indexOf('Chrome') > -1) {
      browserName = 'Chrome';
      const chromeVersion = userAgent.match(/Chrome\/(\d+\.\d+)/);
      if (chromeVersion) {
        browserVersion = chromeVersion[1];
      }
    }
    // Firefox
    else if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox';
      const firefoxVersion = userAgent.match(/Firefox\/(\d+\.\d+)/);
      if (firefoxVersion) {
        browserVersion = firefoxVersion[1];
      }
    }
    // Safari
    else if (userAgent.indexOf('Safari') > -1) {
      browserName = 'Safari';
      const safariVersion = userAgent.match(/Version\/(\d+\.\d+)/);
      if (safariVersion) {
        browserVersion = safariVersion[1];
      }
    }
    // Edge
    else if (userAgent.indexOf('Edg') > -1) {
      browserName = 'Edge';
      const edgeVersion = userAgent.match(/Edg\/(\d+\.\d+)/);
      if (edgeVersion) {
        browserVersion = edgeVersion[1];
      }
    }
    // Opera
    else if (userAgent.indexOf('OPR') > -1) {
      browserName = 'Opera';
      const operaVersion = userAgent.match(/OPR\/(\d+\.\d+)/);
      if (operaVersion) {
        browserVersion = operaVersion[1];
      }
    }

    return { name: browserName, version: browserVersion };
  }

  /**
   * Get operating system information
   * @returns {Object} - OS name and version
   */
  getOSInfo() {
    const userAgent = navigator.userAgent;
    let osName = 'Unknown';
    let osVersion = 'Unknown';

    // Windows
    if (userAgent.indexOf('Windows') > -1) {
      osName = 'Windows';
      const windowsVersion = userAgent.match(/Windows NT (\d+\.\d+)/);
      if (windowsVersion) {
        const versionMap = {
          '10.0': '10',
          '6.3': '8.1',
          '6.2': '8',
          '6.1': '7',
          '6.0': 'Vista',
          '5.1': 'XP'
        };
        osVersion = versionMap[windowsVersion[1]] || windowsVersion[1];
      }
    }
    // macOS
    else if (userAgent.indexOf('Mac') > -1) {
      osName = 'macOS';
      const macVersion = userAgent.match(/Mac OS X (\d+[._]\d+)/);
      if (macVersion) {
        osVersion = macVersion[1].replace('_', '.');
      }
    }
    // Linux
    else if (userAgent.indexOf('Linux') > -1) {
      osName = 'Linux';
      if (userAgent.indexOf('Android') > -1) {
        osName = 'Android';
        const androidVersion = userAgent.match(/Android (\d+\.\d+)/);
        if (androidVersion) {
          osVersion = androidVersion[1];
        }
      }
    }
    // iOS
    else if (/(iPhone|iPad|iPod)/.test(userAgent)) {
      osName = 'iOS';
      const iosVersion = userAgent.match(/OS (\d+_\d+)/);
      if (iosVersion) {
        osVersion = iosVersion[1].replace('_', '.');
      }
    }

    return { name: osName, version: osVersion };
  }

  /**
   * Get device type
   * @returns {string} - Device type (desktop, mobile, tablet)
   */
  getDeviceType() {
    const userAgent = navigator.userAgent;

    if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(userAgent)) {
      return 'tablet';
    }

    if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(userAgent)) {
      return 'mobile';
    }

    return 'desktop';
  }

  /**
   * Fetch with CORS handling through background script
   * @param {string} url - The URL to fetch
   * @param {Object} options - Fetch options
   * @returns {Promise<Response>} - The fetch response
   */
  async fetchWithCORS(url, options = {}) {
    try {
      // Add default headers
      options.headers = {
        'Content-Type': 'application/json',
        'Origin': chrome.runtime.getURL('/'),
        ...options.headers
      };

      // Add authorization if we have a token
      if (this.connectionToken) {
        options.headers['Authorization'] = `Bearer ${this.connectionToken}`;
      }      // Send request through background script
      const response = await chrome.runtime.sendMessage({
        action: 'fetchWithCORS',
        url,
        options
      });

      // Handle error responses from the background script
      if (!response || response.error) {
        throw new Error(response?.error || 'No response from background script');
      }

      if (!response.success || !response.data) {
        throw new Error('Invalid response format from background script');
      }

      return response.data;
    } catch (error) {
      console.error('Error in fetchWithCORS:', error);
      throw error;
    }
  }

  /**
   * Test connection to the server
   * @returns {Promise<boolean>} - Whether the connection was successful
   */  async testConnection() {
    try {
      console.log('Testing connection to server:', this.apiUrl);
      
      const response = await this.fetchWithCORS(`${this.apiUrl}/test`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain',
          'Content-Type': 'application/json'
        },
        mode: 'cors'
      });

      // Handle the response based on its type
      if (typeof response === 'string') {
        // If it's a string response containing 'ok', consider it successful
        if (response.toLowerCase().includes('ok')) {
          console.log('Server returned OK response');
          return true;
        }
        // Try to parse it as JSON
        try {
          const data = JSON.parse(response);
          if (data && (data.success || data.status === 'ok' || data.healthy === true)) {
            console.log('Server health check successful');
            return true;
          }
        } catch (e) {
          console.warn('Response is not JSON:', response);
        }
      } else if (typeof response === 'object') {
        // Handle object response
        if (response && (response.success || response.status === 'ok' || response.healthy === true)) {
          console.log('Server health check successful');
          return true;
        }
      }

      console.error('Server health check failed:', data.error || 'Unknown error');
      return false;
    } catch (error) {
      console.error('Error testing connection:', error);
      return false;
    }
  }

  /**
   * Check if a feature is available in the current plan
   * @param {string} feature - The feature to check
   * @returns {boolean} - Whether the feature is available
   */
  hasFeature(feature) {
    // All features are available to all users
    return true;
  }

  /**
   * Get the maximum number of requests per day
   * @returns {number} - The maximum number of requests per day (-1 for unlimited)
   */
  getMaxRequestsPerDay() {
    return -1; // Unlimited for all users
  }

  /**
   * Get the total number of requests used
   * @returns {number} - The total number of requests used
   */
  getTotalRequestsUsed() {
    if (!this.isConnected || !this.userPlan) {
      return 0;
    }

    return this.userPlan.requestsUsed || 0;
  }

  /**
   * Increment the usage count
   * @param {number} count - Number of requests to add (default: 1)
   * @returns {Promise<boolean>} - Whether the operation was successful
   */
  async incrementUsageCount(count = 1) {
    try {
      if (!this.isConnected || !this.userPlan) {
        console.log('Not connected to dashboard, cannot increment usage count');
        return false;
      }

      // Increment the local usage counter
      const oldCount = this.userPlan.requestsUsed || 0;
      this.userPlan.requestsUsed = oldCount + count;
      console.log(`Incremented usage count: ${oldCount} -> ${this.userPlan.requestsUsed}`);

      // Save the updated usage count
      await this.saveConnectionData();

      // Update the UI
      this.updateUserProfileUI();

      // Notify the server about the usage increment
      if (this.connectionToken) {
        try {
          console.log(`Notifying server about usage increment: +${count}`);
          const response = await fetch(`${this.apiUrl}/api/usage/increment`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.connectionToken}`,
              'Origin': chrome.runtime.getURL('/')
            },
            body: JSON.stringify({
              token: this.connectionToken,
              count: count
            }),
            mode: 'cors',
            credentials: 'include'
          });

          if (!response.ok) {
            console.error('Error notifying server about usage increment:', response.status, response.statusText);
            return true; // Still return true as we updated locally
          }

          const data = await response.json();
          console.log('Server usage increment response:', data);

          // Update local usage data if provided by server
          if (data.success && data.requestsUsed !== undefined) {
            this.userPlan.requestsUsed = data.requestsUsed;
            console.log(`Updated usage count from server: ${this.userPlan.requestsUsed}`);

            // Save the updated usage count
            await this.saveConnectionData();

            // Update the UI
            this.updateUserProfileUI();
          }

          return true;
        } catch (error) {
          console.error('Error notifying server about usage increment:', error);
          return true; // Still return true as we updated locally
        }
      }

      return true;
    } catch (error) {
      console.error('Error incrementing usage count:', error);
      return false;
    }
  }

  /**
   * Check if the user has reached their usage limit
   * @returns {Promise<boolean>} - Whether the user has reached their limit
   */
  async checkUsageLimit() {
    // All users have unlimited usage
    console.log('No usage limits applied - all features are free');
    return false;
  }

  /**
   * Check usage limit based on local data only
   * @returns {boolean} - Whether the user has reached their limit
   */
  checkLocalUsageLimit() {
    // All users have unlimited usage
    console.log('No local usage limits applied - all features are free');
    return false;
  }

  /**
   * Fetch chat history from the server
   * @param {number} limit - Maximum number of messages to fetch
   * @returns {Promise<Array>} - The chat history
   */
  async fetchChatHistory(limit = 100) {
    if (!this.isConnected || !this.connectionToken) {
      return [];
    }

    try {
      // Get chat history from server
      const response = await fetch(`${this.apiUrl}/api/extension/chat/history?token=${this.connectionToken}&limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.connectionToken}`,
          'Origin': chrome.runtime.getURL('/')
        },
        mode: 'cors',
        credentials: 'include'
      });

      if (!response.ok) {
        console.error('Error fetching chat history:', response.statusText);
        return [];
      }

      // Parse response
      const data = await response.json();

      if (!data.success) {
        console.error('Error fetching chat history:', data.error);
        return [];
      }

      // Notify chat history listeners
      if (data.messages && data.messages.length > 0) {
        this.notifyChatHistoryListeners(data.messages);
      }

      return data.messages || [];
    } catch (error) {
      console.error('Error fetching chat history:', error);
      return [];
    }
  }

  /**
   * Send a chat message to the dashboard
   * @param {Object} message - The chat message
   * @returns {Promise<boolean>} - Whether the message was sent
   */
  async sendChatMessage(message) {
    if (!this.isConnected || !this.connectionToken) {
      console.log('Not connected to dashboard, cannot send chat message');
      return false;
    }

    try {
      // Generate a unique ID if not provided
      const messageId = message.id || `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const timestamp = message.timestamp || new Date().toISOString();
      const url = message.url || window.location.href;

      // Format the user message
      const userMessage = {
        id: messageId,
        content: message.query || message.content,
        sender: 'user',
        timestamp: timestamp,
        url: url
      };

      // Format the AI response message if available
      let messages = [userMessage];
      let hasAiResponse = false;

      if (message.response) {
        const aiMessage = {
          id: `resp_${messageId}`,
          content: message.response,
          sender: 'ai',
          timestamp: new Date(new Date(timestamp).getTime() + 1000).toISOString(), // 1 second after user message
          url: url
        };

        messages.push(aiMessage);
        hasAiResponse = true;
      }

      // Send both messages to chat history
      const result = await this.sendChatHistory(messages);

      // If we have an AI response, increment the usage count
      // We only count AI responses as usage, not user messages
      if (result && hasAiResponse) {
        console.log('Incrementing usage count for AI response');
        await this.incrementUsageCount(1);
      }

      return result;
    } catch (error) {
      console.error('Error sending chat message:', error);
      return false;
    }
  }

  /**
   * Add a connection listener
   * @param {Function} listener - The listener function
   */
  addConnectionListener(listener) {
    if (typeof listener === 'function' && !this.connectionListeners.includes(listener)) {
      this.connectionListeners.push(listener);

      // Notify the new listener of the current state
      try {
        listener({
          status: this.isConnected ? 'connected' : 'disconnected',
          userData: this.userData
        });
      } catch (error) {
        console.error('Error notifying connection listener:', error);
      }
    }
  }

  /**
   * Remove a connection listener
   * @param {Function} listener - The listener function
   */
  removeConnectionListener(listener) {
    const index = this.connectionListeners.indexOf(listener);
    if (index !== -1) {
      this.connectionListeners.splice(index, 1);
    }
  }

  /**
   * Notify all connection listeners
   * @param {string} status - The connection status
   * @param {Object|string} extraData - Extra data or error message
   */
  notifyConnectionListeners(status = null, extraData = null) {
    // Determine if extraData is an error message or additional data
    let error = null;
    let additionalData = {};

    if (extraData) {
      if (typeof extraData === 'string') {
        error = extraData;
      } else if (typeof extraData === 'object') {
        additionalData = extraData;
      }
    }

    const data = {
      status: status || (this.isConnected ? 'connected' : 'disconnected'),
      userData: this.userData,
      userPlan: this.userPlan,
      error,
      ...additionalData
    };

    console.log('Notifying connection listeners with status:', status, 'and data:', data);

    this.connectionListeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Error in connection listener:', error);
      }
    });

    // Update the user profile UI in the settings
    this.updateUserProfileUI();
  }

  /**
   * Update the user profile UI in the settings section
   */
  updateUserProfileUI() {
    try {
      // Get the profile elements
      const profileUsername = document.getElementById('profileUsername');
      const profilePlan = document.getElementById('profilePlan');
      const profilePlanFeatures = document.getElementById('profilePlanFeatures');
      const profileUsageLimit = document.getElementById('profileUsageLimit');

      // If any of the elements don't exist, return
      if (!profileUsername || !profilePlan || !profilePlanFeatures || !profileUsageLimit) {
        console.log('Profile elements not found in the DOM');
        return;
      }

      console.log('Updating user profile UI with data:', {
        isConnected: this.isConnected,
        userData: this.userData,
        userPlan: this.userPlan
      });

      if (this.isConnected && this.userData) {
        // Always display the actual username from the server
        if (this.userData.username) {
          profileUsername.textContent = this.userData.username;
          console.log('Setting username to:', this.userData.username);
        } else {
          profileUsername.textContent = 'Connected User';
          console.log('No username found, using "Connected User"');
        }

        // Add additional debug logging to help troubleshoot username issues
        console.log('Full userData object:', JSON.stringify(this.userData));

        // Update the plan information if available
        if (this.userPlan) {
          // Set the plan name - all plans are now "Premium Plan"
          profilePlan.textContent = "Premium Plan";
          console.log('Setting plan name to: Premium Plan');

          // Set the plan features - all features are available
          const featuresText = 'Unlimited requests with all premium features';
          profilePlanFeatures.textContent = featuresText;
          console.log('Setting plan features to:', featuresText);

          // Set the usage limit - no limits for any user
          const requestsUsed = this.userPlan.requestsUsed || 0;
          const usageLimitText = `Usage: ${requestsUsed} requests (unlimited)`;
          profileUsageLimit.textContent = usageLimitText;
          console.log('Setting usage limit to:', usageLimitText);
        } else {
          // Default values if plan is not available
          profilePlan.textContent = 'Premium Plan';
          profilePlanFeatures.textContent = 'Unlimited requests with all premium features';
          profileUsageLimit.textContent = 'Usage: Unlimited requests';
          console.log('No plan found, using default premium values');
        }
      } else {
        // Not connected, show default values
        profileUsername.textContent = 'Not Connected';
        profilePlan.textContent = 'Premium Plan';
        profilePlanFeatures.textContent = 'All premium features available';
        profileUsageLimit.textContent = 'Usage: Unlimited requests';
        console.log('Not connected, showing premium default values');
      }
    } catch (error) {
      console.error('Error updating user profile UI:', error);
    }
  }

  /**
   * Add a chat history listener
   * @param {Function} listener - The listener function
   */
  addChatHistoryListener(listener) {
    if (typeof listener === 'function' && !this.chatHistoryListeners.includes(listener)) {
      this.chatHistoryListeners.push(listener);
    }
  }

  /**
   * Remove a chat history listener
   * @param {Function} listener - The listener function
   */
  removeChatHistoryListener(listener) {
    const index = this.chatHistoryListeners.indexOf(listener);
    if (index !== -1) {
      this.chatHistoryListeners.splice(index, 1);
    }
  }

  /**
   * Notify all chat history listeners
   * @param {Array} history - The chat history
   * @param {boolean} isNewMessage - Whether this is a new message
   */
  notifyChatHistoryListeners(history, isNewMessage = false) {
    this.chatHistoryListeners.forEach(listener => {
      try {
        listener(history, isNewMessage);
      } catch (error) {
        console.error('Error in chat history listener:', error);
      }
    });
  }

  /**
   * Check if the dashboard is connected
   * @returns {boolean} - Whether the dashboard is connected
   */
  isConnectedToDashboard() {
    return this.isConnected;
  }

  /**
   * Get the current user data
   * @returns {Object|null} - The user data
   */
  getUserData() {
    return this.userData;
  }
}

// Create a global instance
const dashboardConnector = new DashboardConnector();
