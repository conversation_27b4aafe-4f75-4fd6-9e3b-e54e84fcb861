'use strict';

console.log('YouTube content script loaded');

// Function to extract YouTube-specific content
function extractYouTubeContent() {
  const youtubeData = {
    videoTitle: '',
    videoDescription: '',
    channelName: '',
    viewCount: '',
    uploadDate: '',
    likes: '',
    comments: [],
    relatedVideos: [],
    isShort: window.location.href.includes('/shorts/')
  };

  try {
    // Video title
    const titleElement = document.querySelector('h1.title, h1.ytd-video-primary-info-renderer, #title h1, #video-title');
    if (titleElement && titleElement.innerText) {
      youtubeData.videoTitle = titleElement.innerText.trim();
    }

    // Video description
    const descriptionElement = document.querySelector('#description, #description-text, .ytd-video-secondary-info-renderer #description');
    if (descriptionElement && descriptionElement.innerText) {
      youtubeData.videoDescription = descriptionElement.innerText.trim();
    }

    // Channel name
    const channelElement = document.querySelector('#channel-name, #owner-name a, .ytd-channel-name a, #upload-info a');
    if (channelElement && channelElement.innerText) {
      youtubeData.channelName = channelElement.innerText.trim();
    }

    // View count
    const viewCountElement = document.querySelector('.view-count, #count .ytd-video-view-count-renderer, #info-text .ytd-video-view-count-renderer');
    if (viewCountElement && viewCountElement.innerText) {
      youtubeData.viewCount = viewCountElement.innerText.trim();
    }

    // Upload date
    const dateElement = document.querySelector('#info-strings yt-formatted-string, #upload-info .ytd-video-primary-info-renderer, #metadata-line span:nth-child(2)');
    if (dateElement && dateElement.innerText) {
      youtubeData.uploadDate = dateElement.innerText.trim();
    }

    // Likes
    const likesElement = document.querySelector('#top-level-buttons-computed yt-formatted-string, .like-button-renderer-like-button .yt-simple-endpoint span');
    if (likesElement && likesElement.innerText) {
      youtubeData.likes = likesElement.innerText.trim();
    }

    // Comments (top few)
    const commentElements = document.querySelectorAll('ytd-comment-renderer #content-text, #comments #content-text');
    if (commentElements && commentElements.length > 0) {
      commentElements.forEach((comment, index) => {
        if (index < 5 && comment && comment.innerText) { // Limit to 5 comments
          youtubeData.comments.push(comment.innerText.trim());
        }
      });
    }

    // Related videos (titles)
    const relatedElements = document.querySelectorAll('#related #video-title, .ytd-compact-video-renderer #video-title');
    if (relatedElements && relatedElements.length > 0) {
      relatedElements.forEach((video, index) => {
        if (index < 5 && video && video.innerText) { // Limit to 5 related videos
          youtubeData.relatedVideos.push(video.innerText.trim());
        }
      });
    }

    return youtubeData;
  } catch (error) {
    console.error('Error extracting YouTube content:', error);
    return youtubeData;
  }
}

// Function to extract page content
function extractPageContent() {
  try {
    // Get the page title
    const title = document.title || 'YouTube Video';

    // Get the main content
    let mainContent = '';
    const contentElements = document.querySelectorAll('article, main, .content, #content');

    if (contentElements.length > 0) {
      mainContent = contentElements[0].innerText || '';
    } else {
      try {
        const clonedBody = document.body.cloneNode(true);
        const scriptsAndStyles = clonedBody.querySelectorAll('script, style, noscript, svg, img');
        scriptsAndStyles.forEach(el => {
          try { el.remove(); } catch (e) { /* ignore removal errors */ }
        });
        mainContent = clonedBody.innerText || '';
      } catch (bodyError) {
        console.error('Error cloning body:', bodyError);
        mainContent = document.body ? (document.body.innerText || '') : '';
      }
    }

    // Trim and normalize whitespace
    mainContent = mainContent.replace(/\s+/g, ' ').trim();

    // Extract YouTube-specific data
    const youtubeData = extractYouTubeContent();

    // Compile the page data
    const pageData = {
      url: window.location.href,
      title,
      content: mainContent || 'No content available',
      youtube: youtubeData,
      timestamp: new Date().toISOString()
    };

    return pageData;
  } catch (error) {
    console.error('Error extracting page content:', error);
    return {
      url: window.location.href || '',
      title: document.title || 'YouTube Video',
      content: 'Content extraction failed. Please refresh the page.',
      youtube: extractYouTubeContent(),
      timestamp: new Date().toISOString()
    };
  }
}

// Listen for messages from popup or background script
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  try {
    if (!message || !message.action) {
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }

    // Simple ping to check if content script is loaded
    if (message.action === 'ping') {
      sendResponse({ success: true, status: 'youtube_content_script_active' });
      return true;
    }

    if (message.action === 'extractPageContent') {
      try {
        // Extract content and send it to the background script for caching
        const content = extractPageContent();
        chrome.runtime.sendMessage({
          action: 'cachePageContent',
          content
        }).catch(err => console.error('Error caching content:', err));

        sendResponse({ success: true, content: content });
      } catch (error) {
        console.error('Error in extractPageContent handler:', error);
        sendResponse({
          success: false,
          error: 'Content extraction failed',
          content: {
            url: window.location.href || '',
            title: document.title || 'YouTube Video',
            content: 'Content extraction failed. Please refresh the page.',
            youtube: extractYouTubeContent(),
            timestamp: new Date().toISOString()
          }
        });
      }
      return true;
    }

    if (message.action === 'getSelectedText') {
      try {
        sendResponse({ success: true, text: window.getSelection().toString().trim() });
      } catch (error) {
        console.error('Error getting selected text:', error);
        sendResponse({ success: false, text: '' });
      }
      return true;
    }

    // If we get here, it's an unknown action
    sendResponse({ success: false, error: 'Unknown action' });
    return true;
  } catch (error) {
    console.error('Critical error in message listener:', error);
    sendResponse({ success: false, error: 'Internal extension error' });
    return true;
  }
});

// Extract and cache the page content on initial load
window.addEventListener('load', () => {
  try {
    const content = extractPageContent();
    chrome.runtime.sendMessage({
      action: 'cachePageContent',
      content
    }).catch(error => {
      console.error('Error caching page content on load:', error);
    });
  } catch (error) {
    console.error('Error extracting page content on load:', error);
  }
});

// Also register a DOMContentLoaded event as a backup
document.addEventListener('DOMContentLoaded', () => {
  // Wait a short time to ensure the page is fully rendered
  setTimeout(() => {
    try {
      const content = extractPageContent();
      chrome.runtime.sendMessage({
        action: 'cachePageContent',
        content
      }).catch(error => {
        console.error('Error caching page content on DOMContentLoaded:', error);
      });
    } catch (error) {
      console.error('Error extracting page content on DOMContentLoaded:', error);
    }
  }, 500);
});

console.log('YouTube content script initialization complete');
