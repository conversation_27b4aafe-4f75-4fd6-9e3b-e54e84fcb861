/**
 * Clipboard helper functions for SoulAI extension
 */

// Create a global ClipboardHelper object
window.ClipboardHelper = {
  copyText: copyTextToClipboard,
  addCopyButtonsToCodeBlocks: addCopyButtonsToCodeBlocks
};

/**
 * Copy text to clipboard using multiple methods for maximum compatibility
 * @param {string} text - The text to copy to clipboard
 * @returns {Promise<boolean>} - Whether the copy was successful
 */
function copyTextToClipboard(text) {
  return new Promise((resolve, reject) => {
    // Try multiple methods in sequence for maximum compatibility
    tryClipboardAPI(text)
      .then(success => {
        if (success) {
          console.log('Clipboard API method succeeded');
          resolve(true);
        } else {
          return tryExecCommand(text);
        }
      })
      .then(success => {
        if (success) {
          console.log('execCommand method succeeded');
          resolve(true);
        } else {
          return tryInputSelection(text);
        }
      })
      .then(success => {
        if (success) {
          console.log('Input selection method succeeded');
          resolve(true);
        } else {
          console.warn('All clipboard methods failed');
          resolve(false);
        }
      })
      .catch(err => {
        console.error('Co<PERSON> failed with error:', err);
        reject(err);
      });
  });
}

/**
 * Try to copy using the modern Clipboard API
 * @param {string} text - The text to copy
 * @returns {Promise<boolean>} - Whether the copy was successful
 */
function tryClipboardAPI(text) {
  return new Promise(resolve => {
    try {
      if (!navigator.clipboard) {
        console.log('Clipboard API not available');
        return resolve(false);
      }

      navigator.clipboard.writeText(text)
        .then(() => {
          console.log('Clipboard API success');
          resolve(true);
        })
        .catch(err => {
          console.log('Clipboard API failed:', err);
          resolve(false);
        });
    } catch (err) {
      console.log('Clipboard API error:', err);
      resolve(false);
    }
  });
}

/**
 * Try to copy using document.execCommand
 * @param {string} text - The text to copy
 * @returns {Promise<boolean>} - Whether the copy was successful
 */
function tryExecCommand(text) {
  return new Promise(resolve => {
    try {
      // Create a temporary textarea element
      const textarea = document.createElement('textarea');

      // Set its value to the text we want to copy
      textarea.value = text;

      // Make it part of the visible viewport to ensure it works in Brave
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.top = '10px';
      textarea.style.left = '10px';
      textarea.style.width = '2em';
      textarea.style.height = '2em';
      textarea.style.padding = 0;
      textarea.style.border = 'none';
      textarea.style.outline = 'none';
      textarea.style.boxShadow = 'none';
      textarea.style.background = 'transparent';

      // Add it to the document
      document.body.appendChild(textarea);

      // Select the text
      textarea.focus();
      textarea.select();

      try {
        // Execute the copy command
        const successful = document.execCommand('copy');
        console.log('execCommand result:', successful ? 'success' : 'failed');

        // Remove the textarea
        document.body.removeChild(textarea);

        resolve(successful);
      } catch (err) {
        // Remove the textarea
        document.body.removeChild(textarea);
        console.log('execCommand error:', err);
        resolve(false);
      }
    } catch (err) {
      console.log('textarea setup error:', err);
      resolve(false);
    }
  });
}

/**
 * Try to copy using input selection method
 * @param {string} text - The text to copy
 * @returns {Promise<boolean>} - Whether the copy was successful
 */
function tryInputSelection(text) {
  return new Promise(resolve => {
    try {
      // Create a temporary input element (sometimes works better than textarea)
      const input = document.createElement('input');
      input.setAttribute('readonly', '');
      input.setAttribute('value', text);
      input.style.position = 'fixed';
      input.style.opacity = '1'; // Make it visible to ensure it works in Brave
      input.style.top = '20px';
      input.style.left = '20px';
      input.style.width = '100px';
      input.style.zIndex = '9999';

      document.body.appendChild(input);

      // Select the text
      input.focus();
      input.select();
      input.setSelectionRange(0, text.length);

      // Try to copy
      try {
        const successful = document.execCommand('copy');
        console.log('Input selection method result:', successful ? 'success' : 'failed');

        // Remove after a short delay to ensure the copy completes
        setTimeout(() => {
          document.body.removeChild(input);
        }, 100);

        resolve(successful);
      } catch (err) {
        document.body.removeChild(input);
        console.log('Input selection method error:', err);
        resolve(false);
      }
    } catch (err) {
      console.log('Input setup error:', err);
      resolve(false);
    }
  });
}

/**
 * Add copy buttons to all code blocks in the container
 * @param {HTMLElement} container - The container element to search for code blocks
 */
function addCopyButtonsToCodeBlocks(container) {
  // Find all code blocks
  const codeBlocks = container.querySelectorAll('pre code');

  // Add a copy button to each code block
  codeBlocks.forEach((codeBlock, index) => {
    // Create a unique ID for this code block if it doesn't have one
    if (!codeBlock.id) {
      codeBlock.id = 'code-block-' + index;
    }

    // Get the parent pre element
    const preElement = codeBlock.parentElement;

    // Check if we already added a copy button to this code block
    if (preElement.querySelector('.copy-button')) {
      return;
    }

    // Create the copy button
    const copyButton = document.createElement('button');
    copyButton.className = 'copy-btn';
    copyButton.innerHTML = '<i class="fas fa-copy"></i>Copy';
    copyButton.setAttribute('data-target', codeBlock.id);

    // Add the button to the pre element
    preElement.style.position = 'relative';
    preElement.appendChild(copyButton);

    // Add click event listener to the button
    copyButton.addEventListener('click', function() {
      // Get the code text
      const codeText = codeBlock.textContent;

      // Copy the text to clipboard
      copyTextToClipboard(codeText)
        .then(successful => {
          if (successful) {
            // Show success state
            copyButton.classList.add('success');
            copyButton.innerHTML = '<i class="fas fa-check"></i>Copied!';

            // Reset after 2 seconds
            setTimeout(() => {
              copyButton.classList.remove('success');
              copyButton.innerHTML = '<i class="fas fa-copy"></i>Copy';
            }, 2000);
          } else {
            // Show error state
            copyButton.classList.add('error');
            copyButton.innerHTML = '<i class="fas fa-times"></i>Failed';

            // Reset after 2 seconds
            setTimeout(() => {
              copyButton.classList.remove('error');
              copyButton.innerHTML = '<i class="fas fa-copy"></i>Copy';
            }, 2000);
          }
        })
        .catch(err => {
          console.error('Copy failed:', err);

          // Show error state
          copyButton.classList.add('error');
          copyButton.innerHTML = '<i class="fas fa-times"></i>Failed';

          // Reset after 2 seconds
          setTimeout(() => {
            copyButton.classList.remove('error');
            copyButton.innerHTML = '<i class="fas fa-copy"></i>Copy';
          }, 2000);
        });
    });
  });
}
