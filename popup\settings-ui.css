/* Modern Settings UI Styles */

/* Settings Header */
.settings-header {
  margin-bottom: 15px;
  position: relative;
}

.settings-header h2 {
  font-size: 1.3rem;
  margin-bottom: 5px;
  color: var(--text-color);
  position: relative;
  padding-bottom: 8px;
  font-family: var(--font-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-header h2 i {
  color: var(--primary-color);
}

.settings-header h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: 3px;
}

.settings-description {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-left: 28px;
  opacity: 0.8;
}

/* API Key Cards */
.api-key-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.api-key-card {
  background-color: rgba(17, 31, 39, 0.7); /* Midnight Carbon with transparency */
  border-radius: 12px;
  border: 1px solid rgba(123, 179, 144, 0.08); /* <PERSON> Veil with transparency */
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.api-key-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(123, 179, 144, 0.3); /* Sage Veil with transparency */
}

.api-key-card-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: rgba(10, 18, 24, 0.7); /* Darker Midnight Carbon with transparency */
  border-bottom: 1px solid rgba(123, 179, 144, 0.05); /* Sage Veil with transparency */
}

.api-key-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 1rem;
  color: white;
}



.api-key-icon.openrouter {
  background: linear-gradient(135deg, #7BB390, #BDDFAB); /* Sage Veil to Pastel Pistachio */
}

.api-key-icon.openai {
  background: linear-gradient(135deg, #BDDFAB, #F2F1DC); /* Pastel Pistachio to Hazy Grove */
}

.api-key-icon.gemini {
  background: linear-gradient(135deg, #111F27, #7BB390); /* Midnight Carbon to Sage Veil */
}

.api-key-title {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-color);
  flex: 1;
}

.api-key-status {
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 5px;
}

.api-key-status i {
  font-size: 0.7rem;
}

.api-key-status.set {
  color: #2ecc71;
}

.api-key-status.not-set {
  color: #e74c3c;
}

.api-key-input {
  padding: 15px;
  position: relative;
}

.api-key-input input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid rgba(123, 179, 144, 0.1); /* Sage Veil with transparency */
  border-radius: 8px;
  background-color: rgba(17, 31, 39, 0.6); /* Midnight Carbon with transparency */
  color: var(--text-color);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.api-key-input input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(123, 179, 144, 0.2); /* Sage Veil with transparency */
}

.api-key-input .toggle-visibility {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.api-key-input .toggle-visibility:hover {
  color: var(--primary-color);
  opacity: 1;
}

.api-key-description {
  padding: 0 15px 15px;
  font-size: 0.85rem;
  color: var(--text-light);
  line-height: 1.5;
}

.api-key-link {
  display: inline-block;
  margin-top: 8px;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.api-key-link:hover {
  text-decoration: underline;
  color: var(--primary-light);
}

.api-key-link i {
  font-size: 0.7rem;
  margin-left: 3px;
}

.api-key-warning {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 3px solid #e74c3c;
}

.api-key-warning i {
  color: #e74c3c;
  font-size: 1.1rem;
}

.api-key-warning span {
  color: var(--text-color);
  font-size: 0.9rem;
}

/* User Profile Section */
.user-profile-section {
  margin-bottom: 20px;
  display: block !important;
}

.profile-card {
  background: linear-gradient(135deg, #283b48, #1a2a36);
  border-radius: 12px;
  padding: 20px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(0, 166, 192, 0.2);
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0.5;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
}

.profile-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0, 166, 192, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
  border: 1px solid rgba(0, 166, 192, 0.3);
}

.profile-avatar i {
  font-size: 28px;
  color: white;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  color: white;
}

.profile-plan {
  font-size: 14px;
  opacity: 0.9;
  color: #48d7ce;
  font-weight: 500;
  text-transform: capitalize;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  z-index: 1;
  background: rgba(0, 0, 0, 0.2);
  padding: 12px;
  border-radius: 8px;
  margin-top: 5px;
}

.profile-detail {
  display: flex;
  align-items: center;
  font-size: 14px;
  opacity: 0.9;
  color: white;
}

.profile-detail i {
  margin-right: 10px;
  width: 16px;
  text-align: center;
  color: #00a6c0;
}

/* Settings Card */
.settings-card {
  background-color: rgba(17, 31, 39, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(123, 179, 144, 0.08);
  overflow: hidden;
  padding: 20px;
  margin-bottom: 20px;
}

.settings-input-container {
  margin-bottom: 15px;
}

.settings-input-container label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-input-container label i {
  color: var(--primary-color);
}
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.step-content {
  font-size: 0.9rem;
  color: var(--text-color);
}

.step-content a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s ease;
}

.step-content a:hover {
  text-decoration: underline;
  color: var(--primary-light);
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.setting-card {
  background-color: rgba(17, 31, 39, 0.7); /* Midnight Carbon with transparency */
  border-radius: 12px;
  border: 1px solid rgba(123, 179, 144, 0.08); /* Sage Veil with transparency */
  overflow: hidden;
  transition: all 0.3s ease;
}

.setting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(123, 179, 144, 0.3); /* Sage Veil with transparency */
}

.setting-card-header {
  padding: 12px 15px;
  background-color: rgba(10, 18, 24, 0.7); /* Darker Midnight Carbon with transparency */
  border-bottom: 1px solid rgba(123, 179, 144, 0.05); /* Sage Veil with transparency */
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-card-header i {
  color: var(--primary-color);
}

.setting-card-content {
  padding: 15px;
}

.setting-card-content input[type="number"] {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid rgba(123, 179, 144, 0.1); /* Sage Veil with transparency */
  border-radius: 8px;
  background-color: rgba(17, 31, 39, 0.6); /* Midnight Carbon with transparency */
  color: var(--text-color);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.setting-card-content input[type="number"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(123, 179, 144, 0.2); /* Sage Veil with transparency */
}

.setting-hint {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 8px;
  opacity: 0.8;
}

.temperature-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.temperature-container input[type="range"] {
  flex: 1;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 3px;
  background: linear-gradient(to right, #7BB390, #FC5A50); /* Sage Veil to Algerian Coral */
  outline: none;
}

.temperature-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #F2F1DC; /* Hazy Grove */
  cursor: pointer;
  border: 2px solid var(--primary-color);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.temperature-container span {
  font-size: 0.9rem;
  color: var(--text-color);
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.temperature-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--text-light);
  margin-bottom: 8px;
}

/* Toggle Settings */
.toggle-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.toggle-setting {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 12px 15px;
  background-color: rgba(17, 31, 39, 0.7); /* Midnight Carbon with transparency */
  border-radius: 12px;
  border: 1px solid rgba(123, 179, 144, 0.08); /* Sage Veil with transparency */
  transition: all 0.3s ease;
}

.toggle-setting:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(123, 179, 144, 0.3); /* Sage Veil with transparency */
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 46px;
  height: 24px;
  flex-shrink: 0;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(17, 31, 39, 0.6); /* Midnight Carbon with transparency */
  transition: .4s;
  border-radius: 34px;
  border: 1px solid rgba(123, 179, 144, 0.1); /* Sage Veil with transparency */
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 2px;
  background-color: #F2F1DC; /* Hazy Grove */
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(22px);
}

.toggle-content {
  flex: 1;
}

.toggle-label {
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--text-color);
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-label i {
  color: var(--primary-color);
}

.toggle-description {
  font-size: 0.8rem;
  color: var(--text-light);
  line-height: 1.4;
}

/* Save Button */
.save-btn {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #7BB390, #111F27); /* Sage Veil to Midnight Carbon */
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
}

.save-btn:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.save-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .api-key-cards {
    grid-template-columns: 1fr;
  }
}
