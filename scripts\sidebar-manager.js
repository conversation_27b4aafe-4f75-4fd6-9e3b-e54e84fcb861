'use strict';

/**
 * Manages the sidebar functionality using Chrome's Side Panel API
 */
class SidebarManager {
  constructor() {
    this.sidebarEnabled = false;
    this.isInSidebarMode = false;

    // Initialize
    this.init();
  }

  /**
   * Initialize the sidebar manager
   */
  async init() {
    try {
      // Check if we're running in Chrome or Brave
      const isChrome = navigator.userAgent.includes('Chrome') && !navigator.userAgent.includes('Brave');
      const isBrave = navigator.userAgent.includes('Brave');

      console.log('Browser detection:', { isChrome, isBrave });

      // Load sidebar settings
      const settings = await chrome.storage.local.get(['sidebarEnabled', 'usingSidePanelFallback', 'isBraveBrowser', 'usingSidePanel']);
      this.sidebarEnabled = settings.sidebarEnabled || false;
      this.usingSidePanelFallback = settings.usingSidePanelFallback || false;
      this.isBraveBrowser = settings.isBraveBrowser || isBrave;
      this.usingSidePanel = settings.usingSidePanel || false;

      // Check if we're currently in sidebar mode
      this.checkSidebarMode();

      // Initialize event listeners
      this.initEventListeners();

      // For Chrome, ensure we're using the Side Panel API
      if (isChrome && !this.usingSidePanelFallback) {
        console.log('Running in Chrome, configuring Side Panel API');

        // Check if the Side Panel API is supported
        const response = await chrome.runtime.sendMessage({
          action: 'configureSidePanel',
          enabled: true // Always enable in Chrome
        });

        // Update settings based on the response
        if (response && response.success) {
          if (response.supported === false) {
            console.warn('Side Panel API not supported in Chrome. Using fallback mode.');
            this.usingSidePanelFallback = true;
            await chrome.storage.local.set({
              usingSidePanelFallback: true,
              sidebarEnabled: true
            });
          } else {
            // Side Panel API is supported in Chrome
            await chrome.storage.local.set({
              usingSidePanelFallback: false,
              sidebarEnabled: true,
              usingSidePanel: true
            });
          }
        }
      }
      // For Brave, use the custom sidebar implementation
      else if (isBrave) {
        console.log('Running in Brave, using custom sidebar implementation');
        // No changes needed for Brave as it's already working
      }

      console.log('SidebarManager initialized:', {
        sidebarEnabled: this.sidebarEnabled,
        isInSidebarMode: this.isInSidebarMode,
        usingSidePanelFallback: this.usingSidePanelFallback,
        isBraveBrowser: this.isBraveBrowser,
        usingSidePanel: this.usingSidePanel
      });
    } catch (error) {
      console.error('Error initializing SidebarManager:', error);
    }
  }

  /**
   * Check if we're currently in sidebar mode
   */
  checkSidebarMode() {
    // Check URL parameters for sidebar=true
    const urlParams = new URLSearchParams(window.location.search);
    this.isInSidebarMode = urlParams.get('sidebar') === 'true';

    // Apply sidebar mode class to body if needed
    if (this.isInSidebarMode) {
      document.body.classList.add('sidebar-mode');

      // Play the sidebar animation when in sidebar mode
      this.playSidebarAnimation();
    }
  }

  /**
   * Play the enhanced sidebar animation sequence using GSAP
   */
  playSidebarAnimation() {
    // Get the animation overlay elements
    const animationOverlay = document.getElementById('animationOverlay');
    const logo = animationOverlay.querySelector('.animated-logo');
    const welcomeText = animationOverlay.querySelector('.welcome-text');
    const loadingIndicator = animationOverlay.querySelector('.loading-indicator-animation');
    const container = document.querySelector('.container');
    const particles = animationOverlay.querySelectorAll('.particle');
    const glowEffect = animationOverlay.querySelector('.glow-effect');
    const dots = animationOverlay.querySelectorAll('.dot');

    // Create a GSAP timeline for the animation sequence
    const tl = gsap.timeline({
      onComplete: () => {
        // Hide the overlay when animation is complete
        gsap.to(animationOverlay, {
          opacity: 0,
          duration: 0.8,
          ease: "power2.inOut",
          onComplete: () => {
            animationOverlay.style.display = 'none';
          }
        });
      }
    });

    // Set initial states
    gsap.set(container, { opacity: 0 });
    gsap.set(animationOverlay, { opacity: 1, display: 'flex' });
    gsap.set(particles, { opacity: 0, scale: 0 });
    gsap.set(glowEffect, { opacity: 0, scale: 0.5 });
    gsap.set(dots, { opacity: 0, y: 10 });

    // Particles animation setup
    particles.forEach((particle, index) => {
      // Random positions for particles
      const randomX = gsap.utils.random(-100, 100);
      const randomY = gsap.utils.random(-100, 100);
      const randomDelay = index * 0.2;

      // Add particle animations to timeline
      tl.to(particle, {
        opacity: gsap.utils.random(0.3, 0.7),
        scale: 1,
        duration: 1,
        delay: randomDelay,
        ease: "power2.out"
      }, 0.5);

      // Add floating animation
      gsap.to(particle, {
        x: randomX,
        y: randomY,
        duration: gsap.utils.random(15, 30),
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });
    });

    // Enhanced animation sequence
    tl.to(glowEffect, { opacity: 0.8, scale: 1, duration: 1.5, ease: "power2.out" }, 0)
      .fromTo(logo,
        { opacity: 0, scale: 0.5, rotation: -10 },
        { opacity: 1, scale: 1, rotation: 0, duration: 1.2, ease: "back.out(1.7)" },
        0.3
      )
      .fromTo(logo,
        { y: 0 },
        { y: -15, duration: 0.8, repeat: 1, yoyo: true, ease: "power1.inOut" },
        "+=0.2"
      )
      .fromTo(welcomeText,
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" },
        "-=0.5"
      )
      .fromTo(loadingIndicator,
        { opacity: 0, y: 10 },
        { opacity: 1, y: 0, duration: 0.5, ease: "power1.out" },
        "-=0.3"
      )
      .to(dots, {
        opacity: 1,
        y: 0,
        stagger: 0.15,
        duration: 0.4,
        ease: "power1.inOut",
        onComplete: () => {
          // Add pulsing animation to dots
          gsap.to(dots, {
            scale: 1.3,
            duration: 0.6,
            stagger: 0.15,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut"
          });
        }
      }, "-=0.2")
      .to(container, { opacity: 1, duration: 0.8, ease: "power2.inOut" }, "+=1");
  }

  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // Listen for sidebar toggle button clicks
    const toggleBtn = document.getElementById('sidebarToggleBtn');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggleSidebar());
    }

    // Listen for the second sidebar toggle button clicks
    const toggleBtn2 = document.getElementById('sidebarToggleBtn2');
    if (toggleBtn2) {
      toggleBtn2.addEventListener('click', () => this.toggleSidebar());
    }

    // Listen for close sidebar button clicks
    const closeSidebarBtn = document.getElementById('closeSidebarBtn');
    if (closeSidebarBtn) {
      // Add direct window close functionality
      closeSidebarBtn.addEventListener('click', (e) => {
        console.log('Close sidebar button clicked');
        e.preventDefault();
        e.stopPropagation();

        // For sidebar mode, just close the window directly
        if (this.isInSidebarMode) {
          console.log('In sidebar mode, closing window directly');
          window.close();
          return;
        }

        // Otherwise use the regular close method
        this.closeSidebar();
      });
    }
  }

  /**
   * Save sidebar settings
   */
  async saveSidebarSettings() {
    try {
      await chrome.storage.local.set({ sidebarEnabled: this.sidebarEnabled });
      console.log('Sidebar settings saved:', { sidebarEnabled: this.sidebarEnabled });

      // Configure the side panel behavior based on the setting
      const response = await chrome.runtime.sendMessage({
        action: 'configureSidePanel',
        enabled: this.sidebarEnabled
      });

      // Check if the Side Panel API is supported
      if (response && response.success && response.supported === false) {
        console.warn('Side Panel API not supported in this browser. Using fallback mode.');
        // Store the information that we're using fallback mode
        await chrome.storage.local.set({ usingSidePanelFallback: true });
      } else {
        // Clear the fallback flag if the API is supported
        await chrome.storage.local.set({ usingSidePanelFallback: false });
      }
    } catch (error) {
      console.error('Error saving sidebar settings:', error);
    }
  }

  /**
   * Close the sidebar
   */
  async closeSidebar() {
    try {
      console.log('Closing sidebar');

      // Check if we're running in Chrome or Brave
      const isChrome = navigator.userAgent.includes('Chrome') && !navigator.userAgent.includes('Brave');
      const isBrave = navigator.userAgent.includes('Brave');

      console.log('Browser detection in closeSidebar:', { isChrome, isBrave });

      // First, try to send a message to the parent window if we're in an iframe
      // This is for the Brave custom sidebar implementation
      if (window.parent && window.parent !== window) {
        try {
          console.log('Attempting to notify parent window to close sidebar');
          window.parent.postMessage({
            source: 'happy-ai-popup',
            action: 'closeSidebar'
          }, '*');

          // Wait a moment to see if the parent handles it
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.error('Error sending message to parent window:', error);
        }
      }

      // For Chrome, use the Side Panel API to close the panel
      if (isChrome) {
        try {
          console.log('Attempting to close Chrome Side Panel');
          const response = await chrome.runtime.sendMessage({ action: 'closeSidePanel' });
          console.log('Chrome Side Panel close response:', response);

          // If we're in the sidebar mode (popup with sidebar=true parameter)
          if (this.isInSidebarMode) {
            console.log('In sidebar mode, closing window');
            window.close();
          }
        } catch (error) {
          console.error('Error closing Chrome Side Panel:', error);
          // Fallback: try to close the window anyway
          if (this.isInSidebarMode) {
            console.log('Fallback: closing window');
            window.close();
          }
        }
      }
      // For Brave, send a message to close the custom sidebar
      else if (isBrave) {
        try {
          console.log('Attempting to close Brave sidebar');

          // First try: Send a message to the background script
          const bgResponse = await chrome.runtime.sendMessage({ action: 'closeSidePanel' });
          console.log('Background script response:', bgResponse);

          // If that didn't work or we're in sidebar mode, try direct approach
          if (!bgResponse?.success || this.isInSidebarMode) {
            // Get the current tab
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs && tabs.length > 0) {
              const tab = tabs[0];

              // Skip unsupported URLs
              if (!tab.url.startsWith('chrome://') &&
                  !tab.url.startsWith('brave://') &&
                  !tab.url.startsWith('about:') &&
                  !tab.url.startsWith('chrome-extension://') &&
                  !tab.url.startsWith('brave-extension://')) {

                // Send message to content script to hide the sidebar with better error handling
                try {
                  // Check if the URL is a protected URL that doesn't allow content script injection
                  const isProtectedUrl = tab.url.startsWith('chrome://') ||
                                        tab.url.startsWith('brave://') ||
                                        tab.url.startsWith('edge://') ||
                                        tab.url.startsWith('about:') ||
                                        tab.url.startsWith('chrome-extension://') ||
                                        tab.url.startsWith('devtools://');

                  if (isProtectedUrl) {
                    console.log('Protected URL detected, skipping script injection:', tab.url);
                    // For protected URLs, just return without trying to inject scripts
                    return;
                  }

                  // For regular URLs, try to inject the content script if it's not already there
                  await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content/brave-sidebar.js']
                  });
                  console.log('Injected Brave sidebar script before hiding');

                  // Wait a moment for the script to initialize
                  await new Promise(resolve => setTimeout(resolve, 300));

                  // Now try to hide the sidebar
                  await chrome.tabs.sendMessage(tab.id, { action: 'hideBraveSidebar' });
                  console.log('Brave sidebar hidden via content script');
                } catch (contentError) {
                  console.error('Error sending message to content script:', contentError);
                  // Don't throw, just log the error and continue
                  console.log('Continuing despite error - UI will proceed');
                }
              }
            }
          }

          // If we're in the sidebar mode (popup with sidebar=true parameter)
          if (this.isInSidebarMode) {
            console.log('In sidebar mode, closing window');
            // Add a small delay to ensure messages are processed
            setTimeout(() => window.close(), 200);
          }
        } catch (error) {
          console.error('Error hiding Brave sidebar:', error);
          // Fallback: try to close the window anyway
          if (this.isInSidebarMode) {
            console.log('Fallback: closing window');
            window.close();
          }
        }
      }
      // For other browsers or unknown cases, just close the window
      else {
        console.log('Unknown browser, closing window');
        if (this.isInSidebarMode) {
          window.close();
        }
      }

      // Update sidebar state in storage
      if (this.isInSidebarMode) {
        try {
          await chrome.storage.local.set({ usingSidePanel: false });
        } catch (storageError) {
          console.error('Error updating sidebar state in storage:', storageError);
        }
      }
    } catch (error) {
      console.error('Error closing sidebar:', error);
      // Final fallback: try to close the window anyway
      if (this.isInSidebarMode) {
        console.log('Final fallback: closing window');
        window.close();
      }
    }
  }

  /**
   * Toggle sidebar mode
   */
  async toggleSidebar() {
    try {
      if (this.isInSidebarMode) {
        // We're already in the sidebar, close it
        console.log('Already in sidebar mode, closing');
        this.closeSidebar();
      } else {
        // Check if we're running in Chrome or Brave
        const isChrome = navigator.userAgent.includes('Chrome') && !navigator.userAgent.includes('Brave');
        const isBrave = navigator.userAgent.includes('Brave');

        // Get the latest settings
        const settings = await chrome.storage.local.get(['usingSidePanelFallback', 'isBraveBrowser', 'usingSidePanel']);
        const usingSidePanelFallback = settings.usingSidePanelFallback || false;
        const isBraveBrowser = settings.isBraveBrowser || isBrave;

        console.log('Toggle sidebar with settings:', {
          isChrome,
          isBrave,
          usingSidePanelFallback,
          isBraveBrowser
        });

        // For Chrome, always try to use the Side Panel API first
        if (isChrome && !usingSidePanelFallback) {
          console.log('Using Chrome Side Panel API');

          // We're in the popup, try to open the side panel
          const response = await chrome.runtime.sendMessage({
            action: 'openSidePanel'
          });

          console.log('Side Panel API response:', response);

          // If we got a fallback response, the Side Panel API is not supported
          if (response && response.fallback) {
            console.log('Chrome Side Panel API not supported, using fallback mode');
            // Store the information that we're using fallback mode for future use
            await chrome.storage.local.set({
              usingSidePanelFallback: true,
              usingSidePanel: false
            });

            // Open the popup in a new tab with sidebar=true parameter
            chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
          }
        }
        // For Brave or when Side Panel API is not supported, use the fallback
        else {
          if (isBraveBrowser) {
            console.log('Using Brave custom sidebar implementation');
          } else {
            console.log('Using Side Panel fallback mode (opening in new tab)');
          }

          // Try to open the side panel first (for Brave)
          const response = await chrome.runtime.sendMessage({
            action: 'openSidePanel'
          });

          // If we got a fallback response or if we're using the fallback mode
          if ((response && response.fallback) || usingSidePanelFallback) {
            // Open the popup in a new tab with sidebar=true parameter
            chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
          }
        }

        // Close the popup after opening the side panel or new tab
        setTimeout(() => window.close(), 300);
      }
    } catch (error) {
      console.error('Error toggling sidebar:', error);
    }
  }
}
