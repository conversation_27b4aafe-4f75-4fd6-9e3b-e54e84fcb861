# Dashboard Dockerfile for BrowzyAI
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    bash

# Development stage
FROM base AS development

# Install development tools
RUN npm install -g @vitejs/plugin-react vite

# Copy package files
COPY dashboard/package*.json ./dashboard/
WORKDIR /app/dashboard

# Install dependencies
RUN npm install

# Copy dashboard source
COPY dashboard/ .

# Set environment variables
ENV NODE_ENV=development
ENV VITE_PORT=5173
ENV VITE_HOST=0.0.0.0

# Expose port
EXPOSE 5173 24678

# Start development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]

# Build stage
FROM base AS build

# Copy package files
COPY dashboard/package*.json ./dashboard/
WORKDIR /app/dashboard

# Install dependencies
RUN npm ci --only=production

# Copy dashboard source
COPY dashboard/ .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Copy built assets from build stage
COPY --from=build /app/dashboard/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create non-root user
RUN addgroup -g 1001 -S nginx
RUN adduser -S browzyai -u 1001

# Set proper permissions
RUN chown -R browzyai:nginx /usr/share/nginx/html
RUN chown -R browzyai:nginx /var/cache/nginx
RUN chown -R browzyai:nginx /var/log/nginx
RUN chown -R browzyai:nginx /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R browzyai:nginx /var/run/nginx.pid

# Switch to non-root user
USER browzyai

# Expose port
EXPOSE 5173

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5173 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
