/* Modern Stats UI - New Design */
:root {
  --stats-primary: #6366f1;
  --stats-secondary: #8b5cf6;
  --stats-success: #10b981;
  --stats-warning: #f59e0b;
  --stats-danger: #ef4444;
  --stats-info: #3b82f6;
  --stats-dark: #1f2937;
  --stats-light: #f3f4f6;
  --stats-bg: #111827;
  --stats-card-bg: #1f2937;
  --stats-border: #374151;
  --stats-text: #f9fafb;
  --stats-text-secondary: #9ca3af;
  --stats-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --stats-gradient: linear-gradient(135deg, var(--stats-primary), var(--stats-secondary));
}

/* Stats Container */
.stats-container {
  padding: 1rem;
  color: var(--stats-text);
}

/* Stats Header */
.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--stats-border);
}

.stats-header-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stats-header-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.stats-header-title i {
  font-size: 1.25rem;
  color: var(--stats-primary);
  background: rgba(99, 102, 241, 0.1);
  padding: 0.75rem;
  border-radius: 50%;
}

.stats-header-actions {
  display: flex;
  gap: 0.75rem;
}

.stats-time-filter {
  background: var(--stats-card-bg);
  border: 1px solid var(--stats-border);
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: var(--stats-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.stats-time-filter:hover {
  border-color: var(--stats-primary);
}

/* Stats Overview Cards */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stats-card {
  background: var(--stats-card-bg);
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: var(--stats-shadow);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--stats-border);
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.stats-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--stats-gradient);
}

.stats-card-primary::after {
  background: linear-gradient(135deg, var(--stats-primary), var(--stats-secondary));
}

.stats-card-success::after {
  background: linear-gradient(135deg, var(--stats-success), #34d399);
}

.stats-card-warning::after {
  background: linear-gradient(135deg, var(--stats-warning), #fbbf24);
}

.stats-card-danger::after {
  background: linear-gradient(135deg, var(--stats-danger), #f87171);
}

.stats-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stats-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--stats-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stats-card-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.stats-card-primary .stats-card-icon {
  background: rgba(99, 102, 241, 0.1);
  color: var(--stats-primary);
}

.stats-card-success .stats-card-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--stats-success);
}

.stats-card-warning .stats-card-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--stats-warning);
}

.stats-card-danger .stats-card-icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--stats-danger);
}

.stats-card-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-card-primary .stats-card-value {
  color: var(--stats-primary);
}

.stats-card-success .stats-card-value {
  color: var(--stats-success);
}

.stats-card-warning .stats-card-value {
  color: var(--stats-warning);
}

.stats-card-danger .stats-card-value {
  color: var(--stats-danger);
}

.stats-card-subtitle {
  font-size: 0.875rem;
  color: var(--stats-text-secondary);
}

.stats-card-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  font-size: 0.875rem;
}

.stats-trend-up {
  color: var(--stats-success);
}

.stats-trend-down {
  color: var(--stats-danger);
}

/* Usage Chart Section */
.stats-chart-section {
  background: var(--stats-card-bg);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--stats-shadow);
  border: 1px solid var(--stats-border);
}

.stats-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stats-chart-title {
  font-size: 1.125rem;
  font-weight: 600;
}

.stats-chart-filters {
  display: flex;
  gap: 0.5rem;
}

.stats-chart-filter {
  background: var(--stats-bg);
  border: 1px solid var(--stats-border);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.stats-chart-filter.active {
  background: var(--stats-primary);
  border-color: var(--stats-primary);
  color: white;
}

.stats-chart-container {
  height: 300px;
  position: relative;
}

.stats-chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
}

.stats-chart-grid-line {
  height: 1px;
  background: rgba(255, 255, 255, 0.05);
  width: 100%;
}

.stats-chart-bars {
  display: flex;
  align-items: flex-end;
  height: 250px;
  gap: 1rem;
  padding-top: 1rem;
  position: relative;
}

.stats-chart-bar-group {
  flex: 1;
  display: flex;
  gap: 2px;
  height: 100%;
  align-items: flex-end;
  position: relative;
}

.stats-chart-bar {
  flex: 1;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
}

.stats-chart-bar-primary {
  background: linear-gradient(to top, var(--stats-primary), var(--stats-secondary));
}

.stats-chart-bar-success {
  background: linear-gradient(to top, var(--stats-success), #34d399);
}

.stats-chart-bar-warning {
  background: linear-gradient(to top, var(--stats-warning), #fbbf24);
}

.stats-chart-bar-danger {
  background: linear-gradient(to top, var(--stats-danger), #f87171);
}

.stats-chart-bar-group:hover {
  transform: translateY(-5px);
}

.stats-chart-bar-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: var(--stats-text-secondary);
  white-space: nowrap;
}

.stats-chart-legend {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2rem;
}

.stats-chart-legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--stats-text-secondary);
}

.stats-chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

.stats-chart-legend-primary {
  background: var(--stats-primary);
}

.stats-chart-legend-success {
  background: var(--stats-success);
}

.stats-chart-legend-warning {
  background: var(--stats-warning);
}

.stats-chart-legend-danger {
  background: var(--stats-danger);
}

/* Provider Stats Section */
.stats-providers {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stats-provider-card {
  background: var(--stats-card-bg);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: var(--stats-shadow);
  border: 1px solid var(--stats-border);
  position: relative;
}

.stats-provider-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 4px 4px 0 0;
}

.stats-provider-openai::before {
  background: linear-gradient(90deg, #FF6B3C, #FF8F6C);
}

.stats-provider-gemini::before {
  background: linear-gradient(90deg, #1EB980, #4DD8A5);
}

.stats-provider-anthropic::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}



.stats-provider-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stats-provider-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stats-provider-openai .stats-provider-icon {
  background: rgba(255, 107, 60, 0.1);
  color: #FF6B3C;
}

.stats-provider-gemini .stats-provider-icon {
  background: rgba(30, 185, 128, 0.1);
  color: #1EB980;
}

.stats-provider-anthropic .stats-provider-icon {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
}



.stats-provider-info {
  flex: 1;
}

.stats-provider-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.stats-provider-models {
  font-size: 0.875rem;
  color: var(--stats-text-secondary);
}

.stats-provider-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stats-provider-metric {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
}

.stats-provider-metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stats-provider-openai .stats-provider-metric-value {
  color: #FF6B3C;
}

.stats-provider-gemini .stats-provider-metric-value {
  color: #1EB980;
}

.stats-provider-anthropic .stats-provider-metric-value {
  color: #9C27B0;
}



.stats-provider-metric-label {
  font-size: 0.75rem;
  color: var(--stats-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Models Section */
.stats-models-section {
  margin-bottom: 2rem;
}

.stats-models-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stats-models-title {
  font-size: 1.125rem;
  font-weight: 600;
}

.stats-models-filters {
  display: flex;
  gap: 0.5rem;
}

.stats-models-filter {
  background: var(--stats-card-bg);
  border: 1px solid var(--stats-border);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.stats-models-filter:hover {
  border-color: var(--stats-primary);
}

.stats-models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.stats-model-card {
  background: var(--stats-card-bg);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: var(--stats-shadow);
  border: 1px solid var(--stats-border);
  transition: transform 0.2s ease;
}

.stats-model-card:hover {
  transform: translateY(-5px);
}

.stats-model-header {
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid var(--stats-border);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stats-model-header-openai {
  border-left: 4px solid #FF6B3C;
}

.stats-model-header-gemini {
  border-left: 4px solid #1EB980;
}

.stats-model-header-anthropic {
  border-left: 4px solid #9C27B0;
}



.stats-model-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.stats-model-header-openai .stats-model-icon {
  background: rgba(255, 107, 60, 0.1);
  color: #FF6B3C;
}

.stats-model-header-gemini .stats-model-icon {
  background: rgba(30, 185, 128, 0.1);
  color: #1EB980;
}

.stats-model-header-anthropic .stats-model-icon {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
}



.stats-model-title {
  font-weight: 600;
}

.stats-model-list {
  padding: 0.5rem 0;
  max-height: 250px;
  overflow-y: auto;
}

.stats-model-list::-webkit-scrollbar {
  width: 4px;
}

.stats-model-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.stats-model-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.stats-model-item {
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: background 0.2s ease;
}

.stats-model-item:hover {
  background: rgba(255, 255, 255, 0.03);
}

.stats-model-item:last-child {
  border-bottom: none;
}

.stats-model-name {
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.stats-model-usage {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  min-width: 2rem;
  text-align: center;
}

.stats-model-header-openai .stats-model-usage {
  background: rgba(255, 107, 60, 0.1);
  color: #FF6B3C;
}

.stats-model-header-gemini .stats-model-usage {
  background: rgba(30, 185, 128, 0.1);
  color: #1EB980;
}

.stats-model-header-anthropic .stats-model-usage {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
}



/* Cost Estimation Section */
.stats-cost-section {
  background: var(--stats-card-bg);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--stats-shadow);
  border: 1px solid var(--stats-border);
}

.stats-cost-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stats-cost-title {
  font-size: 1.125rem;
  font-weight: 600;
}

.stats-cost-disclaimer {
  font-size: 0.75rem;
  color: var(--stats-text-secondary);
  margin-top: 0.25rem;
}

.stats-cost-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.stats-cost-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
  padding: 1.25rem;
  text-align: center;
}

.stats-cost-provider {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.stats-cost-provider-icon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.stats-cost-openai .stats-cost-provider-icon {
  background: rgba(255, 107, 60, 0.1);
  color: #FF6B3C;
}

.stats-cost-gemini .stats-cost-provider-icon {
  background: rgba(30, 185, 128, 0.1);
  color: #1EB980;
}

.stats-cost-anthropic .stats-cost-provider-icon {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
}



.stats-cost-provider-name {
  font-size: 0.875rem;
  font-weight: 600;
}

.stats-cost-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-cost-openai .stats-cost-value {
  color: #FF6B3C;
}

.stats-cost-gemini .stats-cost-value {
  color: #1EB980;
}

.stats-cost-anthropic .stats-cost-value {
  color: #9C27B0;
}



.stats-cost-label {
  font-size: 0.75rem;
  color: var(--stats-text-secondary);
}

.stats-total-cost {
  margin-top: 1.5rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.5rem;
  padding: 1.25rem;
  text-align: center;
}

.stats-total-cost-label {
  font-size: 0.875rem;
  color: var(--stats-text-secondary);
  margin-bottom: 0.5rem;
}

.stats-total-cost-value {
  font-size: 2rem;
  font-weight: 700;
  background: var(--stats-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Reset Button */
.stats-reset {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.stats-reset-btn {
  background: rgba(239, 68, 68, 0.1);
  color: var(--stats-text);
  border: 1px solid rgba(239, 68, 68, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stats-reset-btn i {
  color: var(--stats-danger);
}

.stats-reset-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-2px);
}

.stats-reset-btn:active {
  transform: translateY(0);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .stats-overview,
  .stats-providers,
  .stats-models-grid,
  .stats-cost-grid {
    grid-template-columns: 1fr;
  }

  .stats-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .stats-chart-header,
  .stats-models-header,
  .stats-cost-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}
