// API request handler for background script

// Handle API requests through background script to avoid CORS issues
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'fetchWithCORS') {
    handleAPIRequest(request.url, request.options)
      .then(response => sendResponse(response))
      .catch(error => sendResponse({ error: error.message }));
    return true; // Will respond asynchronously
  }
});

/**
 * Handle API requests with proper CORS headers
 * @param {string} url - The URL to fetch
 * @param {Object} options - The fetch options
 * @returns {Promise<Object>} - The response data
 */
async function handleAPIRequest(url, options = {}) {
  try {
    // Add any necessary headers for API requests
    const headers = {
      ...options.headers,
      'Accept': 'application/json',
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${response.statusText}\n${errorText}`);
    }

    // Parse JSON response
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}
