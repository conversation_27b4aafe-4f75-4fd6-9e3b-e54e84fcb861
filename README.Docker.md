# BrowzyAI Docker Setup

This document provides comprehensive instructions for running BrowzyAI using Docker.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Git

## Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/Arkit-k/Browzy.git
   cd Browzy
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Start the development environment**
   ```bash
   npm run docker:dev
   ```

4. **Access the services**
   - Dashboard: http://localhost:5173
   - API: http://localhost:5000
   - Mailhog (email testing): http://localhost:8025
   - PostgreSQL: localhost:5432
   - Redis: localhost:6379

## Available Commands

### Development
```bash
# Start development environment
npm run docker:dev

# Start production environment
npm run docker:up

# Stop all services
npm run docker:down

# View logs
npm run docker:logs

# Rebuild containers
npm run docker:build
```

### Manual Docker Commands
```bash
# Development environment
docker-compose -f docker-compose.dev.yml up -d

# Production environment
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f [service-name]

# Rebuild specific service
docker-compose build [service-name]
```

## Services Overview

### 1. BrowzyAI API (`browzyai-api`)
- **Port**: 5000
- **Purpose**: Backend API for the Chrome extension
- **Health Check**: http://localhost:5000/health

### 2. BrowzyAI Dashboard (`browzyai-dashboard`)
- **Port**: 5173
- **Purpose**: Web dashboard for managing the extension
- **Technology**: React/Vite

### 3. PostgreSQL Database (`postgres`)
- **Port**: 5432
- **Database**: browzyai
- **User**: browzyai
- **Password**: browzyai_password

### 4. Redis Cache (`redis`)
- **Port**: 6379
- **Purpose**: Caching and session storage

### 5. Mailhog (`mailhog`) - Development Only
- **SMTP Port**: 1025
- **Web UI**: http://localhost:8025
- **Purpose**: Email testing in development

## Environment Configuration

### Development (.env.docker)
- Hot reloading enabled
- Debug logging
- Mailhog for email testing
- Rate limiting disabled

### Production (.env)
- Optimized for performance
- Security headers enabled
- Rate limiting enabled
- Compressed assets

## File Structure

```
├── Dockerfile                 # Main application Dockerfile
├── Dockerfile.dashboard       # Dashboard-specific Dockerfile
├── docker-compose.yml         # Production configuration
├── docker-compose.dev.yml     # Development configuration
├── nginx.conf                 # Nginx configuration for dashboard
├── .dockerignore             # Docker ignore file
├── .env.example              # Environment variables template
├── .env.docker               # Docker-specific environment
└── README.Docker.md          # This file
```

## Development Workflow

1. **Start the development environment**
   ```bash
   npm run docker:dev
   ```

2. **Make changes to your code**
   - Extension files are watched and auto-reload
   - Dashboard has hot module replacement
   - API restarts on file changes

3. **View logs**
   ```bash
   npm run docker:logs
   ```

4. **Access services**
   - Test the extension by loading it in Chrome
   - Use the dashboard at http://localhost:5173
   - Check emails in Mailhog at http://localhost:8025

## Production Deployment

1. **Set up production environment**
   ```bash
   cp .env.example .env
   # Configure production values
   ```

2. **Build and start production containers**
   ```bash
   npm run docker:build
   npm run docker:up
   ```

3. **Verify deployment**
   ```bash
   curl http://localhost:5000/health
   curl http://localhost:5173/health
   ```

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   lsof -i :5000
   lsof -i :5173
   
   # Stop conflicting services or change ports in docker-compose files
   ```

2. **Permission issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

3. **Container won't start**
   ```bash
   # Check logs
   docker-compose logs [service-name]
   
   # Rebuild container
   docker-compose build [service-name]
   ```

4. **Database connection issues**
   ```bash
   # Check if PostgreSQL is running
   docker-compose ps postgres
   
   # Connect to database
   docker-compose exec postgres psql -U browzyai -d browzyai
   ```

### Useful Commands

```bash
# Enter a running container
docker-compose exec [service-name] sh

# View container resource usage
docker stats

# Clean up unused containers and images
docker system prune -a

# Reset everything (WARNING: This will delete all data)
docker-compose down -v
docker system prune -a
```

## Security Considerations

1. **Change default passwords** in production
2. **Use environment variables** for sensitive data
3. **Enable HTTPS** in production
4. **Regularly update** Docker images
5. **Limit container resources** if needed

## Performance Optimization

1. **Use multi-stage builds** (already implemented)
2. **Enable caching** for static assets
3. **Use .dockerignore** to reduce build context
4. **Monitor resource usage** with `docker stats`

## Contributing

When contributing to the Docker setup:

1. Test changes in both development and production modes
2. Update documentation if adding new services
3. Ensure environment variables are properly documented
4. Test on different platforms (Linux, macOS, Windows)

## Support

For Docker-related issues:
1. Check the logs: `npm run docker:logs`
2. Verify your Docker installation
3. Ensure all required ports are available
4. Check the GitHub issues for similar problems
