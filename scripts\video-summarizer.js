'use strict';

/**
 * Video Summarizer
 * A lightweight tool to summarize YouTube videos using their captions
 */
class VideoSummarizer {
  constructor() {
    this.isProcessing = false;
    this.currentVideoId = null;
    this.transcript = null;
    this.summary = null;

    // Configuration
    this.config = {
      minCaptionLength: 500, // Minimum length of captions to attempt summarization
      maxSummaryLength: 1000, // Maximum length of summary in characters
      summaryRatio: 0.3, // Target ratio of summary to original text
      keyPointCount: 5, // Number of key points to extract
    };

    // Initialize the UI elements
    this.initUI();
  }

  /**
   * Initialize UI elements for the summarizer
   */
  initUI() {
    // Create the summarizer button
    this.createSummarizerButton();

    // Create the summary panel (hidden initially)
    this.createSummaryPanel();
  }

  /**
   * Create the summarizer button to be added to YouTube pages
   */
  createSummarizerButton() {
    this.summarizerButton = document.createElement('button');
    this.summarizerButton.className = 'browzy-video-summarizer-btn';
    this.summarizerButton.innerHTML = '<i class="fas fa-list-alt"></i> Summarize Video';
    this.summarizerButton.title = 'Get a summary of this video';
    this.summarizerButton.style.display = 'none'; // Hidden by default

    // Add click event listener
    this.summarizerButton.addEventListener('click', () => {
      this.toggleSummaryPanel();
    });

    // Add to document body to be positioned later
    document.body.appendChild(this.summarizerButton);
  }

  /**
   * Create the summary panel to display results
   */
  createSummaryPanel() {
    this.summaryPanel = document.createElement('div');
    this.summaryPanel.className = 'browzy-summary-panel';
    this.summaryPanel.style.display = 'none'; // Hidden by default

    // Create panel header
    const panelHeader = document.createElement('div');
    panelHeader.className = 'browzy-summary-header';

    const panelTitle = document.createElement('h3');
    panelTitle.textContent = 'Video Summary';

    const closeButton = document.createElement('button');
    closeButton.className = 'browzy-summary-close';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', () => {
      this.summaryPanel.style.display = 'none';
    });

    panelHeader.appendChild(panelTitle);
    panelHeader.appendChild(closeButton);

    // Create panel content
    this.summaryContent = document.createElement('div');
    this.summaryContent.className = 'browzy-summary-content';

    // Create loading indicator
    this.loadingIndicator = document.createElement('div');
    this.loadingIndicator.className = 'browzy-summary-loading';
    this.loadingIndicator.innerHTML = '<div class="spinner"></div><p>Generating summary...</p>';
    this.loadingIndicator.style.display = 'none';

    // Create error message
    this.errorMessage = document.createElement('div');
    this.errorMessage.className = 'browzy-summary-error';
    this.errorMessage.style.display = 'none';

    // Assemble panel
    this.summaryPanel.appendChild(panelHeader);
    this.summaryPanel.appendChild(this.summaryContent);
    this.summaryPanel.appendChild(this.loadingIndicator);
    this.summaryPanel.appendChild(this.errorMessage);

    // Add to document body
    document.body.appendChild(this.summaryPanel);
  }

  /**
   * Toggle the summary panel visibility
   */
  toggleSummaryPanel() {
    if (this.summaryPanel.style.display === 'none') {
      // Show the panel
      this.summaryPanel.style.display = 'block';

      // Get the current video ID
      const videoId = this.getCurrentVideoId();

      // If video ID changed or we don't have a summary yet, generate one
      if (videoId !== this.currentVideoId || !this.summary) {
        this.currentVideoId = videoId;
        this.generateSummary();
      }
    } else {
      // Hide the panel
      this.summaryPanel.style.display = 'none';
    }
  }

  /**
   * Get the current YouTube video ID from the URL
   * @returns {string|null} The video ID or null if not found
   */
  getCurrentVideoId() {
    const url = window.location.href;
    const match = url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
    return match ? match[1] : null;
  }

  /**
   * Check if the current page is a YouTube video page
   * @returns {boolean} True if on a YouTube video page
   */
  isYouTubeVideoPage() {
    return window.location.hostname.includes('youtube.com') &&
           window.location.pathname.includes('/watch') &&
           this.getCurrentVideoId() !== null;
  }

  /**
   * Initialize the summarizer on YouTube pages
   */
  init() {
    // Check if we're on a YouTube video page
    if (this.isYouTubeVideoPage()) {
      console.log('Video Summarizer: YouTube video page detected');

      // Show the summarizer button
      this.positionSummarizerButton();
      this.summarizerButton.style.display = 'block';

      // Add styles
      this.addStyles();
    } else {
      // Hide the button on non-YouTube pages
      this.summarizerButton.style.display = 'none';
    }
  }

  /**
   * Position the summarizer button on the YouTube page
   */
  positionSummarizerButton() {
    // Try to find YouTube's menu container under the video
    const menuContainer = document.querySelector('#menu-container') ||
                          document.querySelector('#top-level-buttons-computed');

    if (menuContainer) {
      // Position next to like/dislike buttons
      menuContainer.appendChild(this.summarizerButton);
    } else {
      // Fallback: Fixed position in the corner
      this.summarizerButton.style.position = 'fixed';
      this.summarizerButton.style.bottom = '20px';
      this.summarizerButton.style.right = '20px';
      this.summarizerButton.style.zIndex = '9999';
    }
  }

  /**
   * Add CSS styles for the summarizer UI
   */
  addStyles() {
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      .browzy-video-summarizer-btn {
        background-color: #00a6c0;
        color: white;
        border: none;
        border-radius: 18px;
        padding: 8px 16px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-left: 8px;
        transition: background-color 0.2s;
      }

      .browzy-video-summarizer-btn:hover {
        background-color: #48d7ce;
      }

      .browzy-video-summarizer-btn i {
        margin-right: 6px;
      }

      .browzy-summary-panel {
        position: fixed;
        top: 80px;
        right: 20px;
        width: 350px;
        max-height: 70vh;
        background-color: #283b48;
        color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }

      .browzy-summary-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: #00a6c0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .browzy-summary-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }

      .browzy-summary-close {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        padding: 0;
        line-height: 1;
      }

      .browzy-summary-content {
        padding: 16px;
        overflow-y: auto;
        flex: 1;
      }

      .browzy-summary-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 24px;
      }

      .browzy-summary-loading .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #00a6c0;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 16px;
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }

      .browzy-summary-error {
        padding: 16px;
        color: #ff6b6b;
        text-align: center;
      }

      .browzy-key-points {
        margin-top: 16px;
      }

      .browzy-key-points h4 {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
      }

      .browzy-key-points ul {
        margin: 0;
        padding-left: 20px;
      }

      .browzy-key-points li {
        margin-bottom: 8px;
      }

      .browzy-timestamp {
        display: inline-block;
        background-color: rgba(0, 166, 192, 0.2);
        padding: 2px 6px;
        border-radius: 4px;
        margin-right: 6px;
        font-size: 12px;
        cursor: pointer;
      }

      .browzy-timestamp:hover {
        background-color: rgba(0, 166, 192, 0.4);
      }
    `;

    document.head.appendChild(styleEl);
  }

  /**
   * Generate a summary for the current video
   */
  async generateSummary() {
    if (this.isProcessing) return;

    this.isProcessing = true;
    this.showLoading(true);
    this.showError(false);
    this.summaryContent.innerHTML = '';

    try {
      // Step 1: Extract the transcript
      const transcript = await this.extractTranscript();

      if (!transcript || transcript.length < this.config.minCaptionLength) {
        throw new Error('Could not find a transcript for this video or transcript is too short.');
      }

      this.transcript = transcript;

      // Step 2: Generate the summary
      const summary = await this.summarizeTranscript(transcript);
      this.summary = summary;

      // Step 3: Display the summary
      this.displaySummary(summary);

    } catch (error) {
      console.error('Video Summarizer Error:', error);
      this.showError(true, error.message);
    } finally {
      this.isProcessing = false;
      this.showLoading(false);
    }
  }

  /**
   * Extract the transcript from the YouTube video
   * @returns {Promise<string>} The transcript text
   */
  async extractTranscript() {
    const videoId = this.getCurrentVideoId();

    if (!videoId) {
      throw new Error('Could not determine video ID');
    }

    console.log('Video Summarizer: Extracting transcript for video ID:', videoId);

    try {
      // First try using the content script's extraction function
      if (typeof window.extractYouTubeTranscript === 'function') {
        console.log('Video Summarizer: Using content script extraction method');
        const transcript = await window.extractYouTubeTranscript();

        if (transcript && transcript.length > 0) {
          console.log('Video Summarizer: Successfully extracted transcript via content script');
          return transcript;
        }
      }

      // If that fails, try using the background script
      console.log('Video Summarizer: Trying background script extraction method');
      return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
          { action: 'extractTranscript', videoId },
          response => {
            if (chrome.runtime.lastError) {
              console.error('Video Summarizer: Error sending message to background script:', chrome.runtime.lastError);
              reject(new Error('Failed to communicate with background script'));
              return;
            }

            if (response && response.success) {
              console.log('Video Summarizer: Successfully extracted transcript via background script');
              resolve(response.transcript);
            } else {
              console.error('Video Summarizer: Background script extraction failed:', response?.error || 'Unknown error');
              reject(new Error(response?.error || 'Failed to extract transcript'));
            }
          }
        );
      });
    } catch (error) {
      console.error('Video Summarizer: All extraction methods failed:', error);

      // Last resort: Try to find transcript on the page
      console.log('Video Summarizer: Attempting to find transcript on page');

      // Look for the transcript button
      const transcriptButton = Array.from(document.querySelectorAll('button'))
        .find(button => button.textContent.toLowerCase().includes('transcript'));

      if (transcriptButton) {
        // Click the transcript button to open the panel
        transcriptButton.click();

        // Wait for the transcript panel to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Extract text from the transcript panel
        const transcriptItems = document.querySelectorAll('yt-formatted-string.segment-text');

        if (transcriptItems && transcriptItems.length > 0) {
          let transcript = '';

          transcriptItems.forEach(item => {
            transcript += item.textContent + ' ';
          });

          // Clean up the transcript
          transcript = transcript.replace(/\s+/g, ' ').trim();

          if (transcript.length > 0) {
            console.log('Video Summarizer: Successfully extracted transcript from page');
            return transcript;
          }
        }
      }

      // If all methods fail, throw an error
      throw new Error('Could not extract transcript. This video may not have captions available.');
    }
  }

  /**
   * Summarize the transcript using NLP techniques
   * @param {string} transcript - The video transcript
   * @returns {Promise<object>} The summary object with text and key points
   */
  async summarizeTranscript(transcript) {
    console.log('Video Summarizer: Summarizing transcript of length:', transcript.length);

    try {
      // First try using the background script for summarization
      return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
          { action: 'summarizeTranscript', transcript },
          response => {
            if (chrome.runtime.lastError) {
              console.error('Video Summarizer: Error sending message to background script:', chrome.runtime.lastError);
              reject(new Error('Failed to communicate with background script'));
              return;
            }

            if (response && response.success) {
              console.log('Video Summarizer: Successfully summarized transcript via background script');
              resolve(response.summary);
            } else {
              console.error('Video Summarizer: Background script summarization failed:', response?.error || 'Unknown error');
              reject(new Error(response?.error || 'Failed to summarize transcript'));
            }
          }
        );
      });
    } catch (error) {
      console.error('Video Summarizer: Background summarization failed:', error);

      // Fallback: Simple client-side summarization
      console.log('Video Summarizer: Falling back to simple client-side summarization');

      // Split the transcript into sentences
      const sentences = this.splitIntoSentences(transcript);

      if (sentences.length === 0) {
        throw new Error('Could not parse transcript into sentences');
      }

      // For a very simple summarization, just take the first sentence and a few distributed throughout
      const summaryLength = Math.min(5, Math.max(3, Math.floor(sentences.length * 0.1))); // 10% of sentences or 3-5

      // Take the first sentence, which often contains the main topic
      let summaryText = sentences[0] + ' ';

      // Take a few sentences distributed throughout the transcript
      const step = Math.floor(sentences.length / (summaryLength - 1));
      for (let i = 1; i < summaryLength; i++) {
        const index = Math.min(i * step, sentences.length - 1);
        summaryText += sentences[index] + ' ';
      }

      // Extract key points (just take a few important-looking sentences)
      const keyPoints = [];

      // Look for sentences that might contain key information
      for (const sentence of sentences) {
        // Look for sentences that might indicate important points
        if (
          sentence.includes('important') ||
          sentence.includes('key') ||
          sentence.includes('main') ||
          sentence.includes('significant') ||
          sentence.includes('essential') ||
          sentence.includes('crucial') ||
          sentence.length > 100 // Longer sentences often contain more information
        ) {
          keyPoints.push(sentence);
          if (keyPoints.length >= 5) break;
        }
      }

      // If we couldn't find enough key points, just take some evenly distributed sentences
      if (keyPoints.length < 3) {
        const step = Math.floor(sentences.length / 5);
        for (let i = 0; keyPoints.length < 5 && i < 5; i++) {
          const index = Math.min(i * step, sentences.length - 1);
          if (!keyPoints.includes(sentences[index])) {
            keyPoints.push(sentences[index]);
          }
        }
      }

      return {
        summary: summaryText.trim(),
        keyPoints: keyPoints.slice(0, 5) // Limit to 5 key points
      };
    }
  }

  /**
   * Split text into sentences
   * @param {string} text - The text to split
   * @returns {string[]} Array of sentences
   */
  splitIntoSentences(text) {
    // Simple sentence splitting - in a production environment, you would use a more sophisticated approach
    return text
      .replace(/([.!?])\s+/g, '$1|')
      .split('|')
      .filter(sentence => sentence.trim().length > 10); // Filter out very short sentences
  }

  /**
   * Display the summary in the panel
   * @param {object} summary - The summary object
   */
  displaySummary(summary) {
    this.summaryContent.innerHTML = '';

    // Add the main summary
    const summaryText = document.createElement('p');
    summaryText.textContent = summary.summary;
    this.summaryContent.appendChild(summaryText);

    // Add key points
    if (summary.keyPoints && summary.keyPoints.length > 0) {
      const keyPointsSection = document.createElement('div');
      keyPointsSection.className = 'browzy-key-points';

      const keyPointsTitle = document.createElement('h4');
      keyPointsTitle.textContent = 'Key Points';
      keyPointsSection.appendChild(keyPointsTitle);

      const keyPointsList = document.createElement('ul');
      summary.keyPoints.forEach(point => {
        const listItem = document.createElement('li');
        listItem.textContent = point;
        keyPointsList.appendChild(listItem);
      });

      keyPointsSection.appendChild(keyPointsList);
      this.summaryContent.appendChild(keyPointsSection);
    }
  }

  /**
   * Show or hide the loading indicator
   * @param {boolean} show - Whether to show the loading indicator
   */
  showLoading(show) {
    this.loadingIndicator.style.display = show ? 'flex' : 'none';
  }

  /**
   * Show or hide the error message
   * @param {boolean} show - Whether to show the error message
   * @param {string} message - The error message to display
   */
  showError(show, message = 'An error occurred while generating the summary.') {
    this.errorMessage.style.display = show ? 'block' : 'none';
    if (show) {
      this.errorMessage.textContent = message;
    }
  }
}

// Export the class
window.VideoSummarizer = VideoSummarizer;
