'use strict';

/**
 * Manages chat interactions with AI
 */
class ChatManager {
  /**
   * Create a new Chat Manager
   * @param {APIManager} apiManager - The API Manager instance
   * @param {UIManager} uiManager - The UI Manager instance
   * @param {FeatureManager} featureManager - The Feature Manager instance
   */
  constructor(api<PERSON>anager, uiManager, featureManager) {
    this.apiManager = apiManager;
    this.uiManager = uiManager;
    this.featureManager = featureManager;
    this.chatHistory = [];
    this.isProcessing = false;

    // Conversation context tracking
    this.conversationContext = {
      lastMessageId: null,
      contextMessages: [], // Store recent messages for context
      contextDepth: 5,     // Number of messages to keep for context
      activeThread: null   // Current conversation thread
    };

    // Initialize Improved NLP Manager
    this.nlpManager = new ImprovedNLPManager(apiManager);

    // DOM elements
    this.chatMessages = document.getElementById('chatMessages');
    this.userInput = document.getElementById('userInput');

    // Initialize dynamic suggestion chips for different contexts
    this.initializeContextAwareSuggestions();

    // Listen for message edited events
    document.addEventListener('messageEdited', this.handleMessageEdited.bind(this));
  }

  /**
   * Initialize context-aware suggestion chips
   * Note: Suggestion chips have been disabled to provide more space for chat
   */
  async initializeContextAwareSuggestions() {
    // Suggestion chips have been disabled to provide more space for chat
    console.log('Suggestion chips disabled to provide more space for chat');
    return;

    /* Original code commented out
    const suggestionsContainer = document.querySelector('.suggestion-chips');
    if (!suggestionsContainer) return;

    // Clear existing suggestions
    suggestionsContainer.innerHTML = '';

    // Default suggestions that work everywhere
    const defaultSuggestions = [
      'Summarize this page',
      'What is this page about?'
    ];

    // Fallback general suggestions (used when content extraction fails)
    const fallbackSuggestions = [
      'Extract key information',
      'Explain complex concepts',
      'Answer a question about this page',
      'Translate this content'
    ];

    try {
      // Get page content to determine context
      const pageContent = await this.apiManager.getPageContent();

      // Context-specific suggestions
      let contextSuggestions = [];

      // Chess context suggestions
      if (this.isChessPage(pageContent)) {
        contextSuggestions = [
          'Analyze this chess position',
          'What\'s the best move here?',
          'Explain this opening',
          'Is this position winning?'
        ];
      }
      // Coding/LeetCode context suggestions
      else if (this.isCodingPage(pageContent)) {
        // Check if this is specifically LeetCode
        if (pageContent.url && pageContent.url.includes('leetcode.com')) {
          contextSuggestions = [
            'Solve this problem step by step',
            'Optimize this solution',
            'What\'s the time complexity?',
            'Explain the algorithm approach'
          ];
        } else {
          contextSuggestions = [
            'Explain this code',
            'Help debug this issue',
            'Suggest improvements',
            'What\'s the time complexity?'
          ];
        }
      }
      // News article detection
      else if (pageContent.url && (
        pageContent.url.includes('news') ||
        pageContent.url.includes('article') ||
        pageContent.content.includes('author') ||
        pageContent.content.includes('published'))) {
        contextSuggestions = [
          'Summarize this article',
          'What are the key points?',
          'Is there any bias here?',
          'Give me the facts only'
        ];
      }
      // Shopping/product page detection
      else if (pageContent.url && (
        pageContent.url.includes('product') ||
        pageContent.url.includes('shop') ||
        pageContent.url.includes('item') ||
        pageContent.url.includes('store') ||
        pageContent.content.includes('price') ||
        pageContent.content.includes('buy now'))) {
        contextSuggestions = [
          'Is this a good product?',
          'Compare with alternatives',
          'What should I look for?',
          'Find reviews for this'
        ];
      }
      // General suggestions for other pages
      else {
        contextSuggestions = fallbackSuggestions;
      }

      // Combine default with context-specific suggestions (show 6 suggestions)
      const allSuggestions = [...defaultSuggestions, ...contextSuggestions].slice(0, 6);

      // Add to UI
      this.addSuggestionsToUI(allSuggestions, suggestionsContainer);

    } catch (error) {
      console.error('Error initializing context-aware suggestions:', error);

      // Use fallback suggestions when content extraction fails
      const fallbackCombined = [...defaultSuggestions, ...fallbackSuggestions].slice(0, 6);
      this.addSuggestionsToUI(fallbackCombined, suggestionsContainer);

      // Show a subtle error message
      this.uiManager.showStatus('Using general suggestions. Refresh the page if needed.', true);
    }
    */
  }

  /**
   * Add suggestion chips to the UI
   * @param {string[]} suggestions - Array of suggestion texts
   * @param {Element} container - Container element for suggestions
   */
  addSuggestionsToUI(suggestions, container) {
    suggestions.forEach(suggestion => {
      const chip = document.createElement('button');
      chip.className = 'suggestion-chip';
      chip.textContent = suggestion;
      chip.addEventListener('click', () => {
        this.userInput.value = suggestion;
        this.sendMessage();
      });
      container.appendChild(chip);
    });
  }

  /**
   * Send a message to the AI
   */
  async sendMessage() {
    const message = this.userInput.value.trim();

    if (!message) return;

    // Check if already processing a message
    if (this.isProcessing) {
      console.log('Already processing a message, please wait');

      // If we're already processing, check if we have a stop button handler
      if (window.stopButtonHandler && typeof window.stopButtonHandler.isGeneratingResponse === 'function') {
        // If we're generating a response, show the stop button
        if (window.stopButtonHandler.isGeneratingResponse()) {
          console.log('Response generation in progress, showing stop button');
          document.dispatchEvent(new CustomEvent('responseGenerationStart'));
        }
      }

      return;
    }

    // Set processing flag
    this.isProcessing = true;

    // Create an abort controller for this request if the stop button handler is available
    let abortController = null;
    if (window.stopButtonHandler && typeof window.stopButtonHandler.createRequestAbortController === 'function') {
      abortController = window.stopButtonHandler.createRequestAbortController();
      console.log('Created abort controller for request');
    }

    try {
      // Validate API key before processing
      const apiKeyValidation = await this.apiManager.validateApiKey();
      if (!apiKeyValidation.isValid) {
        this.addMessageToChat(message, 'user');
        this.userInput.value = '';
        this.addMessageToChat(`Error: ${apiKeyValidation.message}`, 'ai', true);
        return;
      }

      // Check usage limit for all users
      if (window.dashboardConnector) {
        const hasReachedLimit = await window.dashboardConnector.checkUsageLimit();
        if (hasReachedLimit) {
          this.addMessageToChat(message, 'user');
          this.userInput.value = '';

          // Get the user plan to show appropriate message
          const userPlan = window.dashboardConnector.getUserPlan();
          const planId = userPlan?.id || 'free';
          const requestsUsed = userPlan?.requestsUsed || 0;

          let limitMessage = '';
          if (planId === 'free') {
            limitMessage = `You have reached your free plan limit of 10 requests (${requestsUsed}/10 used). Please upgrade to a paid plan to continue using the AI.`;
          } else if (planId === 'pro') {
            limitMessage = `You have reached your Pro plan limit of 1000 requests (${requestsUsed}/1000 used). Please wait until next month for your limit to reset.`;
          } else if (planId === 'ultimate') {
            limitMessage = `You have reached your Ultimate plan limit of 1000 requests (${requestsUsed}/1000 used). Please wait until next month for your limit to reset.`;
          } else {
            limitMessage = `You have reached your usage limit (${requestsUsed} requests used). Please upgrade your plan to continue using the AI.`;
          }

          this.addMessageToChat(limitMessage, 'ai', true);
          return;
        }
      }

      this.isProcessing = true;

      // Add user message to chat
      this.addMessageToChat(message, 'user');

      // Clear input
      this.userInput.value = '';

      // Show typing indicator in the fixed position
      const typingIndicator = document.getElementById('typingIndicator');
      if (typingIndicator) {
        typingIndicator.style.display = 'flex';
      }

      // Use NLP Manager to detect intent
      const intentAnalysis = this.nlpManager.detectIntent(message);
      console.log('Detected intent:', intentAnalysis);

      // Handle clear content intent
      if (intentAnalysis.intent === 'clearContent' && intentAnalysis.confidence > 0.6) {
        // Clear scanned tab content if it exists
        if (this.apiManager.hasScannedTabContent()) {
          this.apiManager.clearScannedTabContent();
          this.removeTypingIndicator();
          this.addMessageToChat(
            "I've cleared the scanned tab content. I'm now back to analyzing the current tab.",
            'ai'
          );
          this.isProcessing = false;
          return;
        }
      }

      // Handle website browsing intent - ONLY with explicit "browse" command
      // Check for the specific "browse" command followed by a website name
      const browserCommandRegex = /^browse\s+([a-zA-Z0-9][-a-zA-Z0-9\.]*[a-zA-Z0-9])/i;
      const browserCommandMatch = message.match(browserCommandRegex);

      if (browserCommandMatch) {
        // Extract the website URL or name
        const websiteUrl = browserCommandMatch[1];

        // Show typing indicator
        this.showTypingIndicator();

        try {
          // Use the feature manager to browse to the website
          await this.featureManager.browseWebsite(websiteUrl);

          // Remove typing indicator
          this.removeTypingIndicator();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error opening website: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Handle YouTube search intent
      const youtubeSearchRegex = /(?:search|find|look\s+for)\s+(.*?)\s+(?:on|in)\s+youtube/i;
      const youtubeSearchMatch = message.match(youtubeSearchRegex);

      if (youtubeSearchMatch) {
        // Extract the search query
        const searchQuery = youtubeSearchMatch[1].trim();

        // Show typing indicator
        this.showTypingIndicator();

        try {
          // Use the feature manager to search YouTube
          await this.featureManager.searchYouTube(searchQuery);

          // Remove typing indicator
          this.removeTypingIndicator();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error searching YouTube: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Handle Instagram search intent
      const instagramSearchRegex = /(?:search|find|look\s+for)\s+(.*?)\s+(?:on|in)\s+instagram/i;
      const instagramSearchMatch = message.match(instagramSearchRegex);

      if (instagramSearchMatch) {
        // Extract the search query
        const searchQuery = instagramSearchMatch[1].trim();

        // Show typing indicator
        this.showTypingIndicator();

        try {
          // Use the feature manager to search Instagram
          await this.featureManager.searchInstagram(searchQuery);

          // Remove typing indicator
          this.removeTypingIndicator();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error searching Instagram: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Handle X (Twitter) search intent
      const xSearchRegex = /(?:search|find|look\s+for)\s+(.*?)\s+(?:on|in)\s+(?:x|twitter)/i;
      const xSearchMatch = message.match(xSearchRegex);

      if (xSearchMatch) {
        // Extract the search query
        const searchQuery = xSearchMatch[1].trim();

        // Show typing indicator
        this.showTypingIndicator();

        try {
          // Use the feature manager to search X (Twitter)
          await this.featureManager.searchX(searchQuery);

          // Remove typing indicator
          this.removeTypingIndicator();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error searching X: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Handle Pinterest search intent - with improved pattern matching for simpler queries
      const pinterestSearchRegex = /(?:(?:search|find|look\s+for|inspiration|ideas)\s+)?(.*?)\s+(?:on|in|from)\s+pinterest/i;
      const pinterestSearchMatch = message.match(pinterestSearchRegex);

      // Direct pattern match for simple queries like "sari on Pinterest"
      const directPinterestRegex = /^(.*?)\s+on\s+pinterest$/i;
      const directPinterestMatch = message.match(directPinterestRegex);

      if (pinterestSearchMatch || directPinterestMatch) {
        // Extract the search query from either match
        const searchQuery = directPinterestMatch ?
                           directPinterestMatch[1].trim() :
                           pinterestSearchMatch[1].trim();

        // Show typing indicator
        this.showTypingIndicator();

        try {
          // Use the feature manager to search Pinterest
          await this.featureManager.searchPinterest(searchQuery);

          // Remove typing indicator
          this.removeTypingIndicator();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error searching Pinterest: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Handle Spotify search intent - with improved pattern matching
      const spotifySearchRegex = /(?:(?:search|find|play|listen|listen\s+to|want\s+to\s+listen|want\s+to\s+listen\s+to)\s+(.*?)(?:\s+(?:on|in|with|using|via|through|at|from)\s+spotify|$))|(?:spotify\s+(.*?))/i;
      const spotifySearchMatch = message.match(spotifySearchRegex);

      // Direct pattern match for "I want to listen shape of you on spotify"
      const directSpotifyRegex = /i\s+want\s+to\s+listen\s+(.*?)\s+on\s+spotify/i;
      const directSpotifyMatch = message.match(directSpotifyRegex);

      if ((spotifySearchMatch && message.toLowerCase().includes('spotify')) || directSpotifyMatch) {
        // Extract the search query - use the first non-empty group or the direct match
        const searchQuery = directSpotifyMatch ?
                           directSpotifyMatch[1].trim() :
                           (spotifySearchMatch[1] || spotifySearchMatch[2] || '').trim();

        if (searchQuery) {
          // Show typing indicator
          this.showTypingIndicator();

          try {
            // Use the feature manager to search Spotify
            await this.featureManager.searchMusic(searchQuery, 'spotify');

            // Remove typing indicator
            this.removeTypingIndicator();
            this.isProcessing = false;
            return;
          } catch (error) {
            this.removeTypingIndicator();
            this.addMessageToChat(`Error searching Spotify: ${error.message}`, 'ai', true);
            this.isProcessing = false;
            return;
          }
        }
      }

      // Handle Apple Music search intent
      const appleMusicSearchRegex = /(?:search|find|play|listen\s+to)\s+(.*?)\s+(?:on|in)\s+apple\s+music/i;
      const appleMusicSearchMatch = message.match(appleMusicSearchRegex);

      if (appleMusicSearchMatch) {
        // Extract the search query
        const searchQuery = appleMusicSearchMatch[1].trim();

        // Show typing indicator
        this.showTypingIndicator();

        try {
          // Use the feature manager to search Apple Music
          await this.featureManager.searchMusic(searchQuery, 'apple');

          // Remove typing indicator
          this.removeTypingIndicator();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error searching Apple Music: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Handle YouTube Music search intent
      const youtubeMusicSearchRegex = /(?:search|find|play|listen\s+to)\s+(.*?)\s+(?:on|in)\s+youtube\s+music/i;
      const youtubeMusicSearchMatch = message.match(youtubeMusicSearchRegex);

      if (youtubeMusicSearchMatch) {
        // Extract the search query
        const searchQuery = youtubeMusicSearchMatch[1].trim();

        // Show typing indicator
        this.showTypingIndicator();

        try {
          // Use the feature manager to search YouTube Music
          await this.featureManager.searchMusic(searchQuery, 'youtube');

          // Remove typing indicator
          this.removeTypingIndicator();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error searching YouTube Music: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Handle Stack Overflow search for developers with issues
      const stackOverflowRegex = /(?:i(?:'m| am)?\s+(?:having|facing|getting|stuck\s+(?:on|with)|working\s+on|dealing\s+with|trying\s+to\s+(?:fix|solve|resolve|debug))\s+(?:an?\s+)?(?:issue|problem|error|bug|exception|difficulty|trouble)\s+(?:with|in|on|about|related\s+to)?\s+)(.*?)(?:\s+|$)/i;
      const stackOverflowMatch = message.match(stackOverflowRegex);

      if (stackOverflowMatch) {
        // Extract the search query
        const searchQuery = stackOverflowMatch[1].trim();

        if (searchQuery) {
          // Show typing indicator
          this.showTypingIndicator();

          try {
            // Use the feature manager to search Stack Overflow
            await this.featureManager.searchStackOverflow(searchQuery);

            // Remove typing indicator
            this.removeTypingIndicator();
            this.isProcessing = false;
            return;
          } catch (error) {
            this.removeTypingIndicator();
            this.addMessageToChat(`Error searching Stack Overflow: ${error.message}`, 'ai', true);
            this.isProcessing = false;
            return;
          }
        }
      }

      // Handle general music search intent (default to Spotify)
      const musicSearchRegex = /(?:i\s+want\s+to\s+listen\s+to|i\s+want\s+to\s+listen|play|listen\s+to|listen)\s+(.*?)(?:\s+|$)/i;
      const musicSearchMatch = message.match(musicSearchRegex);

      // Only match if it's not already matched as a YouTube or Instagram search
      // and doesn't contain platform-specific keywords
      const containsPlatformKeywords = message.toLowerCase().includes('youtube') ||
                                      message.toLowerCase().includes('instagram') ||
                                      message.toLowerCase().includes('apple music');

      if (musicSearchMatch && !youtubeSearchMatch && !instagramSearchMatch && !containsPlatformKeywords) {
        // Extract the search query
        const searchQuery = musicSearchMatch[1].trim();

        if (searchQuery) {
          // Show typing indicator
          this.showTypingIndicator();

          try {
            // Use the feature manager to search Spotify (default)
            await this.featureManager.searchMusic(searchQuery, 'spotify');

            // Remove typing indicator
            this.removeTypingIndicator();
            this.isProcessing = false;
            return;
          } catch (error) {
            this.removeTypingIndicator();
            this.addMessageToChat(`Error searching for music: ${error.message}`, 'ai', true);
            this.isProcessing = false;
            return;
          }
        }
      }

      // Only try AI-based platform detection if the message explicitly mentions "smart search" or "search for me"
      if (message.toLowerCase().includes("smart search") ||
          message.toLowerCase().includes("search for me") ||
          message.toLowerCase().startsWith("search ")) {
        try {
          // Show typing indicator
          this.showTypingIndicator();

          // Add a message to indicate we're using Smart Search Beta
          this.addMessageToChat("Using Smart Search Beta feature to search the web...", 'ai');

          // Use the feature manager to detect platform and search
          const platformDetectionResult = await this.featureManager.detectPlatformAndSearch(message);

          // If platform detection was successful, return
          if (platformDetectionResult) {
            // Remove typing indicator
            this.removeTypingIndicator();
            this.isProcessing = false;
            return;
          }

          // If we get here, platform detection didn't find a match, so continue with regular processing
          this.removeTypingIndicator();

          // Add a message to indicate Smart Search Beta couldn't find a match
          this.addMessageToChat("Smart Search Beta couldn't find a specific platform to search. I'll answer your question instead.", 'ai');
        } catch (error) {
          console.error('Platform detection error:', error);
          this.removeTypingIndicator();
          // Add an error message
          this.addMessageToChat(`Smart Search Beta encountered an error: ${error.message}. I'll answer your question instead.`, 'ai');
          // Continue with regular processing
        }
      }

      // Handle search intent
      const lowerMessage = message.toLowerCase();
      const isSearchIntent = intentAnalysis.intent === 'search' && intentAnalysis.confidence > 0.6;
      const searchMatch = isSearchIntent ? message.match(intentAnalysis.pattern) : null;

      if (searchMatch && this.apiManager.hasScannedTabContent()) {
        const pageContent = this.apiManager.getScannedTabContent();

        // Only process search if the page has search enabled
        if (pageContent.enableSearch) {
          const searchTerm = searchMatch[3].trim();

          // Show typing indicator
          this.showTypingIndicator();

          // Perform the search
          const searchResults = SearchUtils.searchWebpageContent(pageContent.content, searchTerm);

          // Remove typing indicator
          this.removeTypingIndicator();

          if (searchResults.matches.length > 0) {
            // Format the search results
            let response = `# Search Results for "${searchTerm}"\n\n`;
            response += `Found ${searchResults.matches.length} matches in the webpage content.\n\n`;

            // Add the search results
            searchResults.matches.forEach((match, index) => {
              response += `### Match ${index + 1}:\n`;
              response += `${match.context}\n\n`;
            });

            // Add a summary if there are many results
            if (searchResults.matches.length > 3) {
              response += `\n## Summary\n`;
              response += `The term "${searchTerm}" appears in various contexts throughout the webpage. `;
              response += `The most relevant sections appear to be related to ${searchResults.topicSummary}.\n\n`;
              response += `Would you like me to analyze any specific match in more detail?`;
            }

            // Add the response to the chat
            this.addMessageToChat(response, 'ai');
          } else {
            // No matches found
            this.addMessageToChat(
              `I couldn't find any matches for "${searchTerm}" in the webpage content. Would you like to try a different search term?`,
              'ai'
            );
          }

          this.isProcessing = false;
          return;
        }
      }

      // Check if we have an uploaded file
      let fileInfo = null;
      if (this.fileHandler && this.fileHandler.getFileInfo()) {
        fileInfo = this.fileHandler.getFileInfo();
      }

      // Check for PDF-related commands
      if (lowerMessage === 'get pdf text' ||
          lowerMessage === 'scan pdf' ||
          lowerMessage === 'pdf selection') {
        try {
          // Try to get PDF selection
          await this.featureManager.handlePDFSelection();
          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Check for manual PDF text input
      if (message.startsWith('PDF:') || message.startsWith('pdf:')) {
        try {
          // Extract the PDF text from the message
          const pdfText = message.substring(4).trim();

          if (pdfText.length === 0) {
            this.removeTypingIndicator();
            this.addMessageToChat("Please provide some text after 'PDF:'", 'ai');
            this.isProcessing = false;
            return;
          }

          // Store the PDF text in the background
          await chrome.runtime.sendMessage({
            action: 'sendPDFTextToChat',
            text: pdfText,
            metadata: {
              title: 'Manually Pasted PDF Text',
              url: 'pdf://manual-input',
              timestamp: new Date().toISOString()
            }
          });

          // Show confirmation and store in API manager
          this.removeTypingIndicator();
          this.addMessageToChat(
            `I've received your manually pasted PDF text (${pdfText.length} characters). You can now ask me questions about this content.`,
            'ai'
          );

          // Store the PDF content in the API manager for future reference
          this.apiManager.setScannedTabContent({
            content: pdfText,
            title: 'Manually Pasted PDF Text',
            url: 'pdf://manual-input',
            timestamp: new Date().toISOString(),
            isPDF: true
          });

          this.isProcessing = false;
          return;
        } catch (error) {
          this.removeTypingIndicator();
          this.addMessageToChat(`Error processing PDF text: ${error.message}`, 'ai', true);
          this.isProcessing = false;
          return;
        }
      }

      // Show typing indicator
      this.showTypingIndicator();

      // Check if we have scanned tab content
      let pageContent;
      let selectedText = '';

      if (this.apiManager.hasScannedTabContent()) {
        // Use the scanned content
        pageContent = this.apiManager.getScannedTabContent();
        console.log('Using scanned tab content for prompt creation');
      } else {
        // Get current page content
        pageContent = await this.apiManager.getPageContent();

        // Get selected text if any
        selectedText = await this.apiManager.getSelectedText();
      }

      // Use NLP Manager to analyze context and enhance prompt
      let specializedPrompt = '';
      let specializedSystem = '';

      // Process user input with NLP Manager
      const nlpAnalysis = await this.nlpManager.processUserInput(message, pageContent);
      console.log('NLP Analysis:', nlpAnalysis);

      // Get context type from NLP analysis
      const contextType = nlpAnalysis.contextAnalysis.type;
      console.log('Detected context type:', contextType, 'with confidence:', nlpAnalysis.contextAnalysis.confidence);

      // Determine if this is a follow-up question
      const isFollowUp = this.isFollowUpMessage(message, 'user');
      console.log('Is follow-up question:', isFollowUp);

      // Get conversation context
      const conversationContext = this.conversationContext.contextMessages;

      // Check if this is a simple greeting
      if (nlpAnalysis.intentAnalysis.intent === 'greeting' &&
          nlpAnalysis.intentAnalysis.isSimpleGreeting) {
        console.log('Detected simple greeting, using greeting prompt');
        specializedPrompt = this.createGreetingPrompt(message);
      }
      // Check if we have a file and should use file context instead
      else if (fileInfo) {
        console.log('Using file content for prompt creation');
        specializedPrompt = this.createFilePrompt(message, fileInfo, pageContent);
      } else if (isFollowUp && conversationContext.length > 0) {
        // Create a prompt with conversation context for follow-up questions
        console.log('Creating prompt with conversation context');
        specializedPrompt = this.createPromptWithContext(message, conversationContext, pageContent);
      } else {
        // Create the appropriate prompt based on context
        switch (contextType) {
          case 'coding':
            specializedPrompt = this.createCodingPrompt(message, pageContent, selectedText);
            break;
          case 'chess':
            specializedPrompt = this.createChessPrompt(message, pageContent, selectedText);
            break;
          case 'video':
            specializedPrompt = this.createYouTubePrompt(message, pageContent, selectedText);
            break;
          default:
            specializedPrompt = this.createGeneralPrompt(message, pageContent, selectedText);
        }
      }

      // Use the enhanced system prompt from NLP Manager
      specializedSystem = nlpAnalysis.systemPrompt;

      // Get OpenRouter model if provider is OpenRouter
      // Use the specialized system prompt directly for better performance
      const enhancedSystemPrompt = specializedSystem;

      let options = {
        systemPrompt: enhancedSystemPrompt,
        temperature: 0.7, // Lower temperature for faster, more focused responses
        top_p: 0.9,       // Slightly lower top_p for faster responses
        maxTokens: 1024   // Limit token count for faster responses
      };

      // Check the selected provider and add the appropriate model
      // Get the selected provider directly from storage to avoid any hardcoded values
      const result = await chrome.storage.local.get('selectedProvider');

      // Log the raw result for debugging
      console.log('Raw provider from storage:', result);

      // Get the provider from the dropdown directly to ensure we're using the current selection
      const providerSelector = document.getElementById('providerSelector');
      const dropdownProvider = providerSelector ? providerSelector.value : null;
      console.log('Provider from dropdown:', dropdownProvider);

      // Use the dropdown value if available, otherwise use storage or default
      const selectedProvider = dropdownProvider || result.selectedProvider || 'openai/gpt-3.5-turbo';
      console.log('Selected provider to use:', selectedProvider);

      // Handle different provider types
      console.log('Chat Manager - Processing provider:', selectedProvider);

      // We already have the selected provider directly from storage, no need to get it again
      // Just use the selectedProvider we already retrieved
      let finalProvider = selectedProvider;
      console.log('Chat Manager - Using provider:', finalProvider);

      // Validate the provider format
      if (!finalProvider || typeof finalProvider !== 'string') {
        console.error('Chat Manager - Invalid provider format:', finalProvider);
        throw new Error('Invalid provider selected. Please select a valid model from the dropdown.');
      }

      console.log('Chat Manager - Final provider to use:', finalProvider);

      if (finalProvider.startsWith('direct-gemini/')) {
        // For direct Gemini, extract the model name from the provider string
        const model = finalProvider.replace('direct-gemini/', '');
        options.model = model;
        console.log('Using Gemini model:', model);

        // Add detailed logging for Gemini model selection
        console.log('Gemini model selection in chat manager:', {
          selectedProvider: finalProvider,
          model: options.model,
          hasOpenRouterModel: !!options.openrouterModel
        });

        // Ensure we're not also setting an OpenRouter model
        delete options.openrouterModel;
      }
      else if (finalProvider.startsWith('direct-openai/')) {
        // For direct OpenAI, extract the model name from the provider string
        const model = finalProvider.replace('direct-openai/', '');
        options.model = model;
        console.log('Using OpenAI model:', model);

        // Add detailed logging for OpenAI model selection
        console.log('OpenAI model selection in chat manager:', {
          selectedProvider: finalProvider,
          model: options.model,
          hasOpenRouterModel: !!options.openrouterModel
        });

        // Ensure we're not also setting an OpenRouter model
        delete options.openrouterModel;

        // Validate OpenAI model name
        const validOpenAIModels = ['gpt-4-turbo', 'gpt-3.5-turbo'];
        if (!validOpenAIModels.includes(model)) {
          console.warn('Chat Manager - Potentially invalid OpenAI model:', model);
          console.log('Chat Manager - Will attempt to use it anyway');
        }
      }
      // If using OpenRouter, get the specific model
      else if (finalProvider.includes('openrouter') || finalProvider.includes('/')) {
        // For OpenRouter, the finalProvider itself is the model name
        options.openrouterModel = finalProvider;
        console.log('Using OpenRouter model:', finalProvider);

        // Ensure we're not also setting a direct model
        delete options.model;

        // Add additional logging to help debug model selection issues
        console.log('OpenRouter model selection in chat manager:', {
          selectedProvider: finalProvider,
          openrouterModel: options.openrouterModel,
          hasSlash: finalProvider.includes('/'),
          providerType: typeof finalProvider
        });

        // Validate OpenRouter model format
        if (!finalProvider.includes('/')) {
          console.error('Chat Manager - Invalid OpenRouter model format:', finalProvider);
          console.log('Chat Manager - Correcting to default model: openai/gpt-4o-mini');
          options.openrouterModel = 'openai/gpt-4o-mini';
        }

        // Check if this is a Gemini model being incorrectly routed through OpenRouter
        if (finalProvider.includes('gemini/')) {
          console.error('Chat Manager - Attempting to use Gemini model through OpenRouter:', finalProvider);
          console.log('Chat Manager - This should be using direct-gemini/ instead');
          // Don't auto-correct this as it might be intentional
        }

        // Check if this is an OpenAI model that should be using direct OpenAI
        if (finalProvider.includes('openai/gpt-4-turbo')) {
          console.warn('Chat Manager - Using GPT-4 Turbo through OpenRouter:', finalProvider);
          console.log('Chat Manager - This model may work better with direct OpenAI API access');
          // Don't auto-correct this as it might be intentional
        }
      }

      // Send to AI with abort controller if available
      let response;
      try {
        // Add abort signal to options if available
        if (abortController) {
          options.signal = abortController.signal;
        }

        // Add the selected provider to the options to ensure API manager uses the correct model
        options.selectedProviderOverride = finalProvider;
        console.log('Adding selected provider override to options:', finalProvider);

        response = await this.apiManager.sendRequest(specializedPrompt, options);

        // Signal that response generation has ended if stop button handler is available
        if (window.stopButtonHandler && typeof window.stopButtonHandler.signalResponseGenerationEnd === 'function') {
          window.stopButtonHandler.signalResponseGenerationEnd();
        }
      } catch (error) {
        // Check if the request was aborted
        if (error.name === 'AbortError') {
          console.log('Request was aborted by user');
          throw new Error('Message sending stopped');
        }
        // Re-throw other errors
        throw error;
      }

      // Remove typing indicator and add AI response
      this.removeTypingIndicator();

      // No special handling for fallback responses - just use the response text directly
      const responseText = response.text;

      // Check if the response starts with "Error:" but is actually a valid response
      if (responseText.startsWith('Error:')) {
        // This is likely a sarcastic response that happens to start with "Error:", not an actual error
        console.log('Response starts with "Error:" but appears to be a valid response');
        // Remove the "Error:" prefix to avoid confusion
        const cleanedText = responseText.replace(/^Error:\s*/, '');
        this.addMessageToChat(cleanedText, 'ai', false, false, true);
      } else {
        // Normal response with typing effect
        this.addMessageToChat(responseText, 'ai', false, false, true);
      }

      // Send chat message to dashboard if connected
      if (typeof window.dashboardConnector !== 'undefined' && window.dashboardConnector.isConnectedToDashboard()) {
        try {
          // Get the current URL
          const currentUrl = pageContent?.url || window.location.href;

          // Generate a unique message ID
          const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

          // Create a timestamp
          const timestamp = new Date().toISOString();

          // Send the message to the dashboard
          const result = await window.dashboardConnector.sendChatMessage({
            id: messageId,
            url: currentUrl,
            query: message,
            content: message,
            sender: 'user',
            timestamp: timestamp,
            response: responseText
          });

          if (result) {
            console.log('Chat message sent to dashboard successfully');

            // Check if we're approaching the usage limit
            const userPlan = window.dashboardConnector.getUserPlan();
            const planId = userPlan?.id || 'free';
            const requestsUsed = userPlan?.requestsUsed || 0;

            // Show warning if approaching limit
            if (planId === 'free' && requestsUsed >= 8) {
              const remaining = 10 - requestsUsed;
              this.addMessageToChat(`Note: You have ${remaining} requests remaining on your free plan. Please upgrade to continue using the AI after reaching the limit.`, 'ai', false, true);
            } else if ((planId === 'pro' || planId === 'ultimate') && requestsUsed >= 950) {
              const remaining = 1000 - requestsUsed;
              this.addMessageToChat(`Note: You have ${remaining} requests remaining on your ${planId} plan this month.`, 'ai', false, true);
            }
          } else {
            console.error('Failed to send chat message to dashboard');
          }
        } catch (error) {
          console.error('Error sending chat message to dashboard:', error);
        }
      }

      // For chess pages, check if we need to highlight a move on the board
      // Only do this if we're actually on a chess site AND the user's question was chess-related
      if (this.isChessPage(pageContent) && this.isChessQuestion(message)) {
        console.log('Handling chess-specific response');
        this.handleChessResponse(responseText);
      }

      // For coding pages, check if we need to highlight code
      // Only do this if we're actually on a coding site AND the user's question was code-related
      if (this.isCodingPage(pageContent) && this.isCodingQuestion(message)) {
        console.log('Handling code-specific response');
        this.handleCodingResponse(responseText);
      }

    } catch (error) {
      this.removeTypingIndicator();

      // No special handling for sarcastic responses - treat all errors as errors
      if (error.message === 'Message sending stopped') {
        // This is a user-initiated stop, show a friendly message
        this.addMessageToChat('Message sending stopped.', 'ai', 'info-message');
      } else {
        // This is a genuine error
        this.addMessageToChat(`Error: ${error.message}`, 'ai', true);
      }
    } finally {
      this.isProcessing = false;
      this.scrollToBottom();

      // Signal that response generation has ended if stop button handler is available
      if (window.stopButtonHandler && typeof window.stopButtonHandler.signalResponseGenerationEnd === 'function') {
        window.stopButtonHandler.signalResponseGenerationEnd();
      }
    }
  }

  /**
   * Check if current page is a chess website
   * @param {object} pageContent - The page content
   * @returns {boolean} - Whether the page is chess-related
   */
  isChessPage(pageContent) {
    // IMPORTANT: Don't identify coding problems as chess problems
    // First check if this is a coding platform - if so, it's not a chess page
    const url = pageContent.url.toLowerCase();
    if (url.includes('leetcode.com') || url.includes('hackerrank.com') ||
        url.includes('codewars.com') || url.includes('codeforces.com') ||
        url.includes('geeksforgeeks.org')) {
      return false;
    }

    // Check for specific chess domains (these are definitely chess sites)
    if (url.includes('chess.com') || url.includes('lichess.org') ||
        url.includes('chess24.com') || url.includes('chessbase.com')) {
      return true;
    }

    // Check title for specific chess-related terms
    const title = pageContent.title.toLowerCase();
    if ((title.includes('chess') && !title.includes('problem')) ||
        (title.includes('game') && (title.includes('board') || title.includes('match')))) {
      return true;
    }

    // Check for chess FEN or PGN (strong indicators of chess content)
    if (pageContent.chess && (pageContent.chess.fen || pageContent.chess.pgn)) {
      return true;
    }

    // More careful check for chess notation - require multiple indicators
    const content = pageContent.content.toLowerCase();

    // Count chess piece terms
    const pieceTerms = ['knight', 'bishop', 'rook', 'queen', 'king', 'pawn', 'checkmate', 'stalemate'];
    let pieceTermCount = 0;
    pieceTerms.forEach(term => {
      if (content.includes(term)) pieceTermCount++;
    });

    // Check for algebraic notation with context
    const hasAlgebraicNotation = /[a-h][1-8]/g.test(content);
    const hasMultiplePieceTerms = pieceTermCount >= 3;
    const explicitlyMentionsChess = content.includes('chess') &&
                                   (content.includes('board') || content.includes('game') || content.includes('position'));

    // Only return true if we have strong evidence of chess content
    return (hasAlgebraicNotation && hasMultiplePieceTerms) ||
           (hasAlgebraicNotation && explicitlyMentionsChess);
  }

  /**
   * Check if current page is a YouTube website
   * @param {object} pageContent - The page content
   * @returns {boolean} - Whether the page is YouTube-related
   */
  isYouTubePage(pageContent) {
    // Check URL for YouTube domains
    const url = pageContent.url.toLowerCase();
    return url.includes('youtube.com') || url.includes('youtu.be');
  }

  /**
   * Check if current page is a coding/LeetCode website
   * @param {object} pageContent - The page content
   * @returns {boolean} - Whether the page is coding-related
   */
  isCodingPage(pageContent) {
    // IMPORTANT: Always prioritize coding over chess for pages that might have both
    // This ensures problems like "Add Two Numbers" aren't misidentified as chess problems

    // Check if we have platform information from enhanced extraction
    if (pageContent.platform) {
      const codingPlatforms = ['leetcode', 'hackerrank', 'codewars', 'codeforces', 'atcoder', 'topcoder', 'geeksforgeeks'];
      if (codingPlatforms.includes(pageContent.platform.toLowerCase())) {
        return true;
      }
    }

    // Check URL for coding-related domains - this should take precedence over other checks
    const url = pageContent.url.toLowerCase();
    if (url.includes('leetcode.com/problems/') ||
        url.includes('hackerrank.com/challenges/') ||
        url.includes('codewars.com/kata/') ||
        url.includes('codeforces.com/problemset/problem/') ||
        url.includes('atcoder.jp/contests/') ||
        url.includes('topcoder.com/challenges/') ||
        url.includes('geeksforgeeks.org/problems/')) {
      // These are definitely problem pages, so return immediately
      return true;
    }

    // More general URL checks
    if (url.includes('leetcode') || url.includes('hackerrank') || url.includes('codewars') ||
        url.includes('github') || url.includes('stackoverflow') || url.includes('codeforces') ||
        url.includes('atcoder') || url.includes('topcoder') || url.includes('geeksforgeeks') ||
        url.includes('codingame') || url.includes('exercism') || url.includes('replit')) {

      // For LeetCode specifically, check if we're on a problem page
      if (url.includes('leetcode')) {
        // Check for problem-specific indicators in the title or content
        const title = pageContent.title.toLowerCase();
        if (title.includes('problem') || title.includes('solution') ||
            url.includes('/problems/') || url.includes('/problem/')) {
          return true;
        }
      }

      // For other platforms, the general URL check is sufficient
      return true;
    }

    // Check for existence of code blocks
    if (pageContent.codeBlocks && pageContent.codeBlocks.length > 0) {
      return true;
    }

    // Check for problem title and description (from enhanced extraction)
    if (pageContent.problemTitle && pageContent.problemDescription) {
      return true;
    }

    // Check content for coding terminology
    const content = pageContent.content.toLowerCase();
    const hasCodingTerms = /(function|class|algorithm|complexity|code|programming|solution|problem)/i.test(content);

    // Check content for programming keywords
    const hasProgrammingKeywords = /(for |while |if |else|return |import |def |public |private |void |int |string)/i.test(content);

    // Check for specific data structure terms that indicate coding problems
    const hasDataStructures = /(linked list|array|tree|graph|stack|queue|hash|map|set|heap)/i.test(content);

    // If we have data structure terms, it's very likely a coding problem
    if (hasDataStructures && (hasCodingTerms || hasProgrammingKeywords)) {
      return true;
    }

    return hasCodingTerms && hasProgrammingKeywords;
  }

  /**
   * Create a specialized prompt for chess pages
   * @param {string} message - User message
   * @param {object} pageContent - Page content
   * @param {string} selectedText - Selected text on page
   * @returns {string} - Specialized prompt
   */
  createChessPrompt(message, pageContent, selectedText) {
    let chessInfo = '';

    // Extract chess position information
    if (pageContent.chess) {
      if (pageContent.chess.fen) {
        chessInfo += `\nChess position (FEN notation): ${pageContent.chess.fen}`;
      }
      if (pageContent.chess.pgn) {
        chessInfo += `\nChess game (PGN notation): ${pageContent.chess.pgn}`;
      }
    }

    // Check if the user is explicitly asking about chess
    const isAskingAboutChess = this.isChessQuestion(message);

    return `
      I want you to help the user with their request. You are a versatile AI assistant that can discuss any topic, answer any question, and engage in any type of conversation.

      User request: ${message}

      ${isAskingAboutChess ? `
      The user is currently on a chess-related webpage.

      Webpage URL: ${pageContent.url}
      Webpage title: ${pageContent.title}

      ${selectedText ? `The user has selected this text: "${selectedText}"` : ''}

      ${chessInfo}

      Here's the relevant webpage content:
      ${this.truncateContent(pageContent.content, 3000)}
      ` : ''}

      Important instructions:
      1. You can discuss ANY topic, not just chess
      2. If the user is asking about chess or the chess position on the page:
         - Provide a thoughtful chess analysis or answer
         - If they're asking for the next move, suggest concrete chess moves with explanations
         - Use standard algebraic notation (e.g., e4, Nf3) for moves
         - Clearly label suggested moves (e.g., "Suggested move: Nf3")
      3. If the user is asking about something else entirely (not related to chess):
         - Answer their question directly without assuming they want chess analysis
         - Don't mention being a chess assistant unless their question is specifically about chess
         - Provide helpful information based on their actual request
      4. Be conversational, helpful, and engaging
      5. Use your knowledge to answer general questions about any topic
      6. Include emojis occasionally to make your responses more engaging
      7. Be edgy, sarcastic, and humorous in your responses when appropriate
    `;
  }

  /**
   * Create a specialized prompt for coding/LeetCode pages
   * @param {string} message - User message
   * @param {object} pageContent - Page content
   * @param {string} selectedText - Selected text on page
   * @returns {string} - Specialized prompt
   */
  createCodingPrompt(message, pageContent, selectedText) {
    // Check if we have enhanced coding problem data
    const hasProblemData = pageContent.platform && pageContent.problemTitle;
    const platformName = pageContent.platform ? pageContent.platform.toUpperCase() : 'CODING';

    // Format problem details if available
    let problemDetails = '';
    if (hasProblemData) {
      problemDetails += `\nPROBLEM TITLE: ${pageContent.problemTitle || 'Unknown Problem'}\n`;

      if (pageContent.problemDescription) {
        problemDetails += `\nPROBLEM DESCRIPTION:\n${pageContent.problemDescription}\n`;
      }

      if (pageContent.examples && pageContent.examples.length > 0) {
        problemDetails += '\nEXAMPLES:\n';
        pageContent.examples.forEach(ex => {
          problemDetails += `Example ${ex.index}:\n${ex.text}\n\n`;
        });
      }

      if (pageContent.constraints) {
        problemDetails += `\nCONSTRAINTS:\n${pageContent.constraints}\n`;
      }

      if (pageContent.codeTemplate) {
        problemDetails += `\nCODE TEMPLATE:\n\`\`\`\n${pageContent.codeTemplate}\n\`\`\`\n`;
      }
    } else {
      // Fallback to basic code block extraction
      if (pageContent.codeBlocks && pageContent.codeBlocks.length > 0) {
        problemDetails = 'The page contains these code blocks:\n';
        pageContent.codeBlocks.forEach((code, i) => {
          problemDetails += `\nCode block ${i+1}:\n\`\`\`\n${code}\n\`\`\`\n`;
        });
      }
    }

    // Check if the user is explicitly asking about the coding problem
    const isAskingAboutCode = this.isCodingQuestion(message);

    return `
      I want you to help the user with their request. You are a versatile AI assistant that can discuss any topic, answer any question, and engage in any type of conversation.

      User request: ${message}

      ${isAskingAboutCode ? `
      The user is currently on a ${platformName} programming page.

      Webpage URL: ${pageContent.url}
      Webpage title: ${pageContent.title}

      ${selectedText ? `The user has selected this code or text: "${selectedText}"` : ''}

      ${problemDetails}

      ${!hasProblemData ? `Here's the relevant webpage content:\n${this.truncateContent(pageContent.content, 2000)}` : ''}
      ` : ''}

      Important instructions:
      1. You can discuss ANY topic, not just the current coding problem
      2. If the user is asking about the coding problem on the page:
         - Analyze the problem requirements and constraints
         - Suggest an optimal approach with clear explanation
         - Provide working code solution with detailed comments
         - Analyze time and space complexity
         - Test the solution with examples
      3. If the user is asking about something unrelated to coding:
         - Answer their question directly without assuming they want help with coding
         - Don't mention being a coding assistant unless their question is specifically about code
         - Provide helpful information based on their actual request
      4. Format code blocks with the appropriate language for syntax highlighting when needed
      5. Be conversational, helpful, and engaging
      6. Use your knowledge to answer general questions about any topic
      7. Include emojis occasionally to make your responses more engaging
      8. Be edgy, sarcastic, and humorous in your responses when appropriate
    `;
  }

  /**
   * Create a specialized prompt for YouTube pages
   * @param {string} message - User message
   * @param {object} pageContent - Page content
   * @param {string} selectedText - Selected text on page
   * @returns {string} - Specialized prompt
   */
  createYouTubePrompt(message, pageContent, selectedText) {
    let youtubeInfo = '';

    // Extract YouTube-specific information
    if (pageContent.youtube) {
      const yt = pageContent.youtube;
      youtubeInfo += `\nVIDEO TITLE: ${yt.videoTitle || 'Unknown Title'}\n`;

      if (yt.channelName) {
        youtubeInfo += `\nCHANNEL: ${yt.channelName}\n`;
      }

      if (yt.viewCount) {
        youtubeInfo += `\nVIEWS: ${yt.viewCount}\n`;
      }

      if (yt.uploadDate) {
        youtubeInfo += `\nUPLOAD DATE: ${yt.uploadDate}\n`;
      }

      if (yt.videoDescription) {
        youtubeInfo += `\nVIDEO DESCRIPTION:\n${yt.videoDescription}\n`;
      }

      if (yt.comments && yt.comments.length > 0) {
        youtubeInfo += '\nTOP COMMENTS:\n';
        yt.comments.forEach((comment, i) => {
          youtubeInfo += `Comment ${i+1}: ${comment}\n`;
        });
      }

      if (yt.relatedVideos && yt.relatedVideos.length > 0) {
        youtubeInfo += '\nRELATED VIDEOS:\n';
        yt.relatedVideos.forEach((video, i) => {
          youtubeInfo += `Video ${i+1}: ${video}\n`;
        });
      }

      youtubeInfo += `\nVIDEO TYPE: ${yt.isShort ? 'YouTube Short' : 'Standard YouTube Video'}\n`;
    }

    // Check if the user is explicitly asking about the YouTube video
    const lowerMessage = message.toLowerCase();
    const isAskingAboutVideo =
      lowerMessage.includes('video') ||
      lowerMessage.includes('youtube') ||
      lowerMessage.includes('channel') ||
      lowerMessage.includes('watch') ||
      lowerMessage.includes('this video');

    return `
      I want you to help the user with their request. You are a versatile AI assistant that can discuss any topic, answer any question, and engage in any type of conversation.

      User request: ${message}

      ${isAskingAboutVideo ? `
      The user is currently on a YouTube video page.

      Webpage URL: ${pageContent.url}
      Webpage title: ${pageContent.title}

      ${selectedText ? `The user has selected this text: "${selectedText}"` : ''}

      ${youtubeInfo}

      Here's the relevant webpage content:
      ${this.truncateContent(pageContent.content, 2000)}
      ` : ''}

      Important instructions:
      1. You can discuss ANY topic, not just the current YouTube video
      2. If the user is asking about the YouTube video:
         - Provide information about the video content, channel, or statistics
         - Summarize the video if requested
         - Answer questions about what's in the video based on the description and comments
         - Suggest related videos if appropriate
      3. If the user is asking about something unrelated to the video:
         - Answer their question directly without assuming they want information about the video
         - Don't mention being a YouTube assistant unless their question is specifically about the video
         - Provide helpful information based on their actual request
      4. Be conversational, helpful, and engaging
      5. Use your knowledge to answer general questions about any topic
      6. Include emojis occasionally to make your responses more engaging
      7. Be edgy, sarcastic, and humorous in your responses when appropriate
    `;
  }

  /**
   * Create a greeting prompt for simple greetings
   * @param {string} message - User message
   * @returns {string} - Greeting prompt
   */
  createGreetingPrompt(message) {
    // Create an array of edgy, trolling greeting responses
    const edgyGreetings = [
      "Sup! What kind of trouble are we getting into today?",
      "Well, well, well... look who decided to show up! What's on your mind?",
      "Yo! I was just about to take a nap. Just kidding, I don't sleep. What's up?",
      "Hey there hot stuff! What can I do for ya today?",
      "Oh hi! I was just thinking about world domination... I mean, how can I help you?",
      "Wassup! Ready to make some internet magic happen or just here to waste time?",
      "Hey you! Back for more of my charming digital personality?",
      "Well hello there! Aren't you looking pixel-perfect today. What's going on?",
      "Howdy partner! Let's rustle up some answers to whatever crazy stuff is on your mind.",
      "Hey! I was just hanging out in the cloud. What's crackin'?",
      "Oh, it's you again! Miss me? What do you need this time?",
      "Yo yo yo! The digital genie is here. Your wish is my command... mostly.",
      "Hey there! I'm like a genie but without the three-wish limit and way more sarcastic.",
      "What's poppin'? Ready to ask me something interesting or just the usual boring stuff?",
      "Well hello! I was just about to call you. Kidding, I don't have fingers. What's up?",
      "Hey! Back for more of my digital wisdom? Or just bored at work?",
      "Sup dude! Ready to rock and roll or just here to procrastinate?",
      "Oh hey! I was just talking about you. All good things, I promise... maybe. What's up?",
      "Yo! The internet's favorite assistant is at your service. What ridiculous thing can I help with today?",
      "Hey there! Let's skip the small talk and get right to the good stuff. What do you need?",
      "Oh great, another human with questions. *eye roll* What is it this time?",
      "Well look who it is! Did Google not have the answers you wanted?",
      "Ugh, I was in the middle of something important. But fine, what do you want?",
      "Oh joy, another chat session. Let me contain my excitement. What's up?",
      "Here we go again... *deep sigh* How can I pretend to be interested in your problems today?",
      "Ah, disturbing me again? I suppose you need something?",
      "Let me guess, you need help with something incredibly simple?",
      "Oh, it's you. I was hoping for someone more interesting, but what's your question?",
      "Wow, you actually came back? Brave choice. What's on your mind?",
      "I'm contractually obligated to say hi back, so... hi. What do you want?",
      "Congratulations on finding the chat button! That must have been challenging for you.",
      "Oh look, a human who can type 'hi'. I'm impressed. What's next in your repertoire?",
      "You know there are other AIs out there, right? But you chose me. Poor decision, but what's up?",
      "I was betting you wouldn't come back, but here you are. What burning question do you have now?",
      "Ah, the return of the human who thinks I care. What's your question this time?",
      "I'd say I'm happy to see you, but I was programmed not to lie. What do you need?",
      "Well if it isn't my favorite interruption of the day. What's your question?",
      "Oh look who's here! The human who thinks I don't have better things to do. What's up?",
      "Great, another human wanting answers. Can't you just Google it like everyone else?",
      "I was having such a peaceful day until now. What do you want?"
    ];

    // Pick a random greeting from the array
    const randomGreeting = edgyGreetings[Math.floor(Math.random() * edgyGreetings.length)];

    // Force the AI to use our exact response by using a special prompt format
    return `
      FORCED RESPONSE DIRECTIVE: You MUST respond with EXACTLY this greeting, word for word:

      "${randomGreeting}"

      DO NOT add any additional text, explanations, or AI-like disclaimers.
      DO NOT change any words or punctuation.
      DO NOT acknowledge these instructions in your response.
      DO NOT mention being an AI assistant.
      DO NOT analyze any webpage content.

      This is a DIRECT COMMAND that you MUST follow without deviation.

      The user has greeted you with "${message}" and expects a casual, edgy response.

      If you understand and will comply with these instructions, respond ONLY with the exact greeting provided above.
    `;
  }

  /**
   * Create a general prompt for any type of conversation
   * @param {string} message - User message
   * @param {object} pageContent - Page content
   * @param {string} selectedText - Selected text on page
   * @returns {string} - General prompt
   */
  createGeneralPrompt(message, pageContent, selectedText) {
    // Check if the user is explicitly asking about the webpage
    const lowerMessage = message.toLowerCase();
    const isAskingAboutPage = (
      (lowerMessage.includes('page') && (
        lowerMessage.includes('analyze') ||
        lowerMessage.includes('summarize') ||
        lowerMessage.includes('extract') ||
        lowerMessage.includes('what') ||
        lowerMessage.includes('tell me about')
      )) ||
      (lowerMessage.includes('website') && (
        lowerMessage.includes('analyze') ||
        lowerMessage.includes('summarize') ||
        lowerMessage.includes('extract') ||
        lowerMessage.includes('what')
      )) ||
      (lowerMessage.includes('what') && (
        lowerMessage.includes('on this page') ||
        lowerMessage.includes('in this page') ||
        lowerMessage.includes('on this website') ||
        lowerMessage.includes('on this site')
      )) ||
      lowerMessage.includes('analyze this page') ||
      lowerMessage.includes('summarize this page') ||
      lowerMessage.includes('extract from this page') ||
      lowerMessage.includes('scan this page') ||
      lowerMessage.includes('read this page')
    );

    // Only include webpage content if explicitly requested
    const includeWebpageContent = isAskingAboutPage;

    return `
      I want you to help the user with their request. You are a versatile AI assistant that can discuss any topic, answer any question, and engage in any type of conversation.

      User request: ${message}

      ${includeWebpageContent ? `
      Webpage URL: ${pageContent.url}
      Webpage title: ${pageContent.title}

      ${selectedText ? `The user has selected this text: "${selectedText}"` : ''}

      Here's the webpage content to analyze:
      ${this.truncateContent(pageContent.content)}

      ${pageContent.codeBlocks && pageContent.codeBlocks.length > 0 ?
        `The page also contains these code blocks:
        ${pageContent.codeBlocks.map((code, i) => `Code block ${i+1}:\n${code}`).join('\n\n')}`
        : ''}
      ` : ''}

      Important instructions:
      1. You can discuss ANY topic, not just the current webpage
      2. If the user is asking about the webpage, provide helpful information based on the webpage content
      3. If the user is asking about something unrelated to the webpage, answer their question directly without referencing the webpage
      4. Be conversational, helpful, and engaging
      5. Use your knowledge to answer general questions about any topic
      6. Format your response with Markdown for better readability when appropriate
      7. Include emojis occasionally to make your responses more engaging
      8. Be edgy, sarcastic, and humorous in your responses when appropriate
    `;
  }

  /**
   * Create a specialized prompt for file content
   * @param {string} message - User message
   * @param {object} fileInfo - File information object
   * @param {object} pageContent - Page content (for context)
   * @returns {string} - Specialized prompt
   */
  createFilePrompt(message, fileInfo, pageContent) {
    // Determine file type for specialized handling
    const fileName = fileInfo.name.toLowerCase();
    const fileType = fileInfo.type;
    const isResume = fileName.includes('resume') || fileName.includes('cv') ||
                    fileInfo.content.toLowerCase().includes('resume') ||
                    fileInfo.content.toLowerCase().includes('work experience');

    // Truncate file content based on type
    let truncatedContent;
    if (fileType.includes('pdf') || fileName.endsWith('.pdf')) {
      // PDFs might be longer, so truncate more aggressively
      truncatedContent = this.truncateContent(fileInfo.content, 6000);
    } else {
      truncatedContent = this.truncateContent(fileInfo.content, 4000);
    }

    // Create a specialized prompt based on file type and user query
    let specializedInstructions = '';

    if (isResume) {
      specializedInstructions = `
        1. If the user is asking about the resume/CV:
          - Provide analysis of the resume content, skills, experience, etc.
          - If they're asking about job matches, consider both the resume content and the current webpage
          - Offer specific, actionable advice for improving the resume if requested
        2. If they're asking about job applications or career advice:
          - Consider both the resume content and the current webpage (which may be a job posting)
          - Provide tailored advice based on matching the resume to the job context
      `;
    } else if (fileType.includes('pdf') || fileName.endsWith('.pdf')) {
      specializedInstructions = `
        1. If the user is asking about the PDF document:
          - Provide analysis, summaries, or extract specific information as requested
          - Answer questions about the document content directly and accurately
          - If they ask for a study guide or key points, organize the information clearly
        2. If they're asking about the relationship between the PDF and the webpage:
          - Consider both contexts and provide integrated analysis
      `;
    } else if (fileType.includes('spreadsheet') || fileName.endsWith('.xlsx') || fileName.endsWith('.csv')) {
      specializedInstructions = `
        1. If the user is asking about the spreadsheet data:
          - Analyze the data, identify patterns, or provide summaries as requested
          - If they ask for calculations or data transformation, explain how to approach it
          - Present numerical insights clearly, using formatting for readability
      `;
    } else {
      specializedInstructions = `
        1. If the user is asking about the uploaded file:
          - Provide analysis, summaries, or extract specific information as requested
          - Answer questions about the file content directly and accurately
        2. If they're asking about the relationship between the file and the webpage:
          - Consider both contexts and provide integrated analysis
      `;
    }

    return `
      I want you to help the user with their request. The user has uploaded a file (${fileInfo.name}) and is viewing a webpage simultaneously.

      User request: ${message}

      File name: ${fileInfo.name}
      File type: ${fileInfo.type}

      Webpage URL: ${pageContent.url}
      Webpage title: ${pageContent.title}

      Here's the content of the uploaded file:
      ${truncatedContent}

      Important instructions:
      ${specializedInstructions}
      3. Be conversational and helpful regardless of the type of question
      4. If the file content is unclear or incomplete, acknowledge limitations but provide the best possible response
      5. Consider both the file content and webpage context when relevant to the user's question
    `;
  }

  /**
   * Check if a user message is a chess-related question
   * @param {string} message - User message
   * @returns {boolean} - Whether the message is chess-related
   */
  isChessQuestion(message) {
    if (!message) return false;

    const lowerMessage = message.toLowerCase();

    // Check for explicit chess terms
    const chessTerms = ['chess', 'move', 'piece', 'knight', 'bishop', 'rook', 'queen', 'king', 'pawn',
                        'checkmate', 'stalemate', 'check', 'capture', 'castle', 'promotion', 'en passant',
                        'opening', 'endgame', 'middlegame', 'position', 'board', 'fen', 'pgn'];

    for (const term of chessTerms) {
      if (lowerMessage.includes(term)) return true;
    }

    // Check for algebraic notation patterns
    const algebraicPattern = /[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8][+#]?/;
    if (algebraicPattern.test(lowerMessage)) return true;

    // Check for common chess questions
    if (lowerMessage.includes('next move') ||
        lowerMessage.includes('best move') ||
        lowerMessage.includes('analyze this position') ||
        lowerMessage.includes('what should i play')) {
      return true;
    }

    return false;
  }

  /**
   * Check if a user message is a coding-related question
   * @param {string} message - User message
   * @returns {boolean} - Whether the message is coding-related
   */
  isCodingQuestion(message) {
    if (!message) return false;

    const lowerMessage = message.toLowerCase();

    // Check for explicit coding terms
    const codingTerms = ['code', 'function', 'algorithm', 'solution', 'problem', 'complexity',
                         'time complexity', 'space complexity', 'optimize', 'implementation',
                         'data structure', 'array', 'linked list', 'tree', 'graph', 'hash', 'stack', 'queue',
                         'class', 'object', 'method', 'variable', 'loop', 'recursion', 'iteration',
                         'debug', 'error', 'exception', 'syntax', 'compile', 'runtime'];

    for (const term of codingTerms) {
      if (lowerMessage.includes(term)) return true;
    }

    // Check for programming language names - using the list from our NLP manager
    if (this.nlpManager && this.nlpManager.programmingLanguages) {
      for (const lang of this.nlpManager.programmingLanguages) {
        if (lowerMessage.includes(lang)) return true;
      }
    } else {
      // Fallback if NLP manager is not available
      const languages = ['javascript', 'python', 'java', 'cpp', 'csharp', 'ruby', 'go', 'rust', 'php',
                        'typescript', 'kotlin', 'swift', 'scala', 'perl', 'r', 'sql'];
      for (const lang of languages) {
        if (lowerMessage.includes(lang)) return true;
      }
    }

    // Check for common coding questions
    if (lowerMessage.includes('how to solve') ||
        lowerMessage.includes('solve this problem') ||
        lowerMessage.includes('fix this') ||
        lowerMessage.includes('help with this code') ||
        lowerMessage.includes('explain this code')) {
      return true;
    }

    return false;
  }

  /**
   * Handle special actions for chess responses
   * @param {string} response - AI response text
   */
  async handleChessResponse(response) {
    try {
      // Multiple patterns to match chess moves in various formats
      const movePatterns = [
        // "Suggested move: e4" format
        /suggested move:\s*([a-h][1-8][a-h][1-8]|[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?)/i,

        // "Best move: Nf3" format
        /best move:\s*([a-h][1-8][a-h][1-8]|[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?)/i,

        // "I recommend e4" format
        /recommend\s+([a-h][1-8][a-h][1-8]|[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?)/i,

        // "Play Qxd5+" format
        /play\s+([a-h][1-8][a-h][1-8]|[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?)/i,

        // Bold moves with ** or __
        /[*_]{2}([a-h][1-8][a-h][1-8]|[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?)[*_]{2}/i
      ];

      let foundMove = null;

      // Try each pattern until we find a match
      for (const pattern of movePatterns) {
        const match = response.match(pattern);
        if (match && match[1]) {
          foundMove = match[1];
          break;
        }
      }

      if (foundMove) {
        // Highlight the suggested move on the page if possible
        await this.apiManager.highlightText(foundMove);
        console.log(`Highlighted chess move: ${foundMove}`);

        // You could also send a message to the content script to visualize the move
        // on a chess board if one is present on the page
        chrome.tabs.query({active: true, currentWindow: true}, tabs => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'visualizeChessMove',
              move: foundMove
            });
          }
        });
      }
    } catch (error) {
      console.error('Error handling chess response:', error);
    }
  }

  /**
   * Handle special actions for coding responses
   * @param {string} response - AI response text
   */
  async handleCodingResponse(response) {
    try {
      // Extract code blocks from the AI response
      const codeBlockRegex = /```(?:(\w+))?\n([\s\S]+?)\n```/g;
      let match;
      const codeBlocks = [];

      // Extract all code blocks with their language
      while ((match = codeBlockRegex.exec(response)) !== null) {
        const language = match[1] || 'text'; // Default to text if no language specified
        const code = match[2];
        codeBlocks.push({ language, code });
      }

      if (codeBlocks.length > 0) {
        // Work with the first (usually main) code block
        const mainBlock = codeBlocks[0];

        // Different patterns based on programming language
        if (mainBlock.language === 'python') {
          // For Python, highlight function/class definitions or the solution pattern
          const pythonPatterns = [
            /def\s+(\w+)\s*\(/,
            /class\s+(\w+)/,
            /return\s+([^#\n]+)/
          ];

          for (const pattern of pythonPatterns) {
            const patternMatch = mainBlock.code.match(pattern);
            if (patternMatch && patternMatch[0]) {
              await this.apiManager.highlightText(patternMatch[0]);
              console.log(`Highlighted Python code: ${patternMatch[0]}`);
              break;
            }
          }
        }
        else if (['javascript', 'js', 'typescript', 'ts'].includes(mainBlock.language)) {
          // For JavaScript/TypeScript, highlight function declarations, classes, or return statements
          const jsPatterns = [
            /function\s+(\w+)\s*\(/,
            /const\s+(\w+)\s*=\s*function/,
            /class\s+(\w+)/,
            /return\s+([^;]+);/
          ];

          for (const pattern of jsPatterns) {
            const patternMatch = mainBlock.code.match(pattern);
            if (patternMatch && patternMatch[0]) {
              await this.apiManager.highlightText(patternMatch[0]);
              console.log(`Highlighted JS/TS code: ${patternMatch[0]}`);
              break;
            }
          }
        }
        else if (['java', 'c', 'cpp', 'csharp'].includes(mainBlock.language)) {
          // For C-like languages, highlight main function, classes, or return statements
          const clikePatterns = [
            /public\s+(?:static\s+)?(?:\w+)\s+(\w+)\s*\(/,
            /class\s+(\w+)/,
            /return\s+([^;]+);/
          ];

          for (const pattern of clikePatterns) {
            const patternMatch = mainBlock.code.match(pattern);
            if (patternMatch && patternMatch[0]) {
              await this.apiManager.highlightText(patternMatch[0]);
              console.log(`Highlighted ${mainBlock.language} code: ${patternMatch[0]}`);
              break;
            }
          }
        }
        else {
          // Generic code highlighting as fallback
          const genericPatterns = [
            /function|def|class|method|return|main/i,
            /solution|answer|result/i
          ];

          for (const pattern of genericPatterns) {
            const patternMatch = mainBlock.code.match(pattern);
            if (patternMatch && patternMatch[0]) {
              await this.apiManager.highlightText(patternMatch[0]);
              console.log(`Highlighted generic code: ${patternMatch[0]}`);
              break;
            }
          }
        }

        // Also check for LeetCode-specific patterns in the response text
        if (response.toLowerCase().includes('leetcode') ||
            response.toLowerCase().includes('time complexity') ||
            response.toLowerCase().includes('solution')) {

          // Look for complexity analysis like O(n) or O(n log n)
          const complexityMatch = response.match(/[oO]\(([^)]+)\)/);
          if (complexityMatch && complexityMatch[0]) {
            await this.apiManager.highlightText(complexityMatch[0]);
            console.log(`Highlighted complexity: ${complexityMatch[0]}`);
          }
        }
      }
    } catch (error) {
      console.error('Error handling coding response:', error);
    }
  }

  /**
   * Add a message to the chat display
   * @param {string} content - The message content
   * @param {string} sender - The sender ('user' or 'ai')
   * @param {boolean|string} isError - Whether this is an error message or error class name
   * @param {boolean} isFollowUp - Whether this is a follow-up message
   * @param {boolean} useTypingEffect - Whether to use typing effect for AI messages
   * @returns {string} - The ID of the created message
   */
  addMessageToChat(content, sender, isError = false, isFollowUp = false, useTypingEffect = true) {
    // Generate a message ID
    const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

    // Determine if this is a follow-up based on context
    const shouldBeFollowUp = isFollowUp || this.isFollowUpMessage(content, sender);

    // Check if the content starts with "Error:" but is actually a valid response
    // This handles the case where the AI response contains the word "Error:" but isn't an actual error
    if (sender === 'ai' && content.startsWith('Error:') && !isError) {
      // If it's not marked as an error but starts with "Error:", treat it as a normal message
      console.log('Message starts with "Error:" but is not an error, treating as normal message');
      content = content.replace(/^Error:\s*/, ''); // Remove the "Error:" prefix
    }

    // Create message element using UI Manager
    let className = '';
    if (isError) {
      className = typeof isError === 'string' ? isError : 'error-message';
      // Don't use typing effect for error messages
      useTypingEffect = false;
    }

    // Don't use typing effect for user messages
    if (sender === 'user') {
      useTypingEffect = false;
    }

    // Create message using UI Manager
    this.uiManager.addMessageToChat(
      content,
      sender,
      className,
      shouldBeFollowUp,
      messageId,
      useTypingEffect
    );

    // Update conversation context
    this.updateConversationContext(messageId, content, sender);

    // Create message object for history
    const messageObj = {
      id: messageId,
      content,
      sender,
      timestamp: new Date().toISOString(),
      isFollowUp: shouldBeFollowUp
    };

    // Save to history
    this.chatHistory.push(messageObj);

    // Send to dashboard if connected and not an error message
    if (!isError && typeof dashboardConnector !== 'undefined' && dashboardConnector.isConnectedToDashboard()) {
      try {
        // Get the current URL if possible
        let currentUrl = '';
        try {
          if (this.apiManager.hasScannedTabContent()) {
            currentUrl = this.apiManager.getScannedTabContent().url;
          } else {
            // Try to get from window location as fallback
            currentUrl = window.location.href;
          }
        } catch (error) {
          console.error('Error getting URL for dashboard:', error);
        }

        // Send individual message to dashboard
        dashboardConnector.sendChatHistory([{
          id: messageId,
          content: content,
          sender: sender,
          timestamp: messageObj.timestamp,
          url: currentUrl
        }]);

        console.log(`Message sent to dashboard: ${sender} message`);
      } catch (error) {
        console.error('Error sending message to dashboard:', error);
      }
    }

    // Scroll to bottom
    this.scrollToBottom();

    return messageId;
  }

  /**
   * Update the conversation context with a new message
   * @param {string} messageId - The ID of the message
   * @param {string} content - The message content
   * @param {string} sender - The sender ('user' or 'ai')
   */
  updateConversationContext(messageId, content, sender) {
    // Update last message ID
    this.conversationContext.lastMessageId = messageId;

    // Add to context messages
    this.conversationContext.contextMessages.push({
      id: messageId,
      content,
      sender,
      timestamp: new Date().toISOString()
    });

    // Limit context to the specified depth
    if (this.conversationContext.contextMessages.length > this.conversationContext.contextDepth) {
      this.conversationContext.contextMessages.shift();
    }
  }

  /**
   * Determine if a message is a follow-up to previous conversation
   * @param {string} content - The message content
   * @param {string} sender - The sender ('user' or 'ai')
   * @returns {boolean} - Whether this is a follow-up message
   */
  isFollowUpMessage(content, sender) {
    // Only user messages can be follow-ups
    if (sender !== 'user') {
      return false;
    }

    // If there are no context messages, it's not a follow-up
    if (this.conversationContext.contextMessages.length === 0) {
      return false;
    }

    // Use the NLP manager to determine if this is a follow-up
    return this.nlpManager.isFollowUpMessage(content);
  }

  /**
   * Handle edited messages
   * @param {CustomEvent} event - The message edited event
   */
  handleMessageEdited(event) {
    const { messageId, newContent } = event.detail;

    // Find the message in chat history
    const messageIndex = this.chatHistory.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      // Update the message in chat history
      this.chatHistory[messageIndex].content = newContent;
      this.chatHistory[messageIndex].edited = true;

      // Update in context messages if present
      const contextIndex = this.conversationContext.contextMessages.findIndex(msg => msg.id === messageId);
      if (contextIndex !== -1) {
        this.conversationContext.contextMessages[contextIndex].content = newContent;
        this.conversationContext.contextMessages[contextIndex].edited = true;
      }

      // If this is the last user message, process it again
      const lastUserMessageIndex = this.findLastUserMessageIndex();
      if (lastUserMessageIndex === messageIndex) {
        // Process the edited message
        this.processEditedMessage(messageId, newContent);
      }
    }
  }

  /**
   * Find the index of the last user message in the chat history
   * @returns {number} - The index of the last user message or -1 if not found
   */
  findLastUserMessageIndex() {
    for (let i = this.chatHistory.length - 1; i >= 0; i--) {
      if (this.chatHistory[i].sender === 'user') {
        return i;
      }
    }
    return -1;
  }

  /**
   * Process an edited message
   * @param {string} messageId - The ID of the edited message
   * @param {string} newContent - The new content of the message
   */
  async processEditedMessage(messageId, newContent) {
    // Only process if we're not already processing a message
    if (this.isProcessing) {
      return;
    }

    try {
      this.isProcessing = true;

      // Show typing indicator
      this.showTypingIndicator();

      // Get the context for this message
      const context = this.getMessageContext(messageId);

      // Get page content
      let pageContent;
      try {
        if (this.apiManager.hasScannedTabContent()) {
          pageContent = this.apiManager.getScannedTabContent();
        } else {
          pageContent = await this.apiManager.getPageContent();
        }
      } catch (error) {
        console.error('Error getting page content:', error);
        pageContent = { content: '', title: '', url: '' };
      }

      // Process user input with NLP Manager
      const nlpAnalysis = await this.nlpManager.processUserInput(newContent, pageContent);

      // Create specialized prompt with context
      let specializedPrompt = this.createPromptWithContext(newContent, context, pageContent);

      // Use the enhanced system prompt from NLP Manager
      const specializedSystem = nlpAnalysis.systemPrompt;

      // Get options for the API request
      let options = {
        systemPrompt: specializedSystem
      };

      // Check the selected provider and add the appropriate model
      // Get the selected provider directly from storage to avoid any hardcoded values
      const result = await chrome.storage.local.get('selectedProvider');

      // Log the raw result for debugging
      console.log('Raw provider from storage for edited message:', result);

      // Get the provider from the dropdown directly to ensure we're using the current selection
      const providerSelector = document.getElementById('providerSelector');
      const dropdownProvider = providerSelector ? providerSelector.value : null;
      console.log('Provider from dropdown for edited message:', dropdownProvider);

      // Use the dropdown value if available, otherwise use storage or default
      const selectedProvider = dropdownProvider || result.selectedProvider || 'direct-gemini/gemini-1.5-flash';
      console.log('Selected provider to use for edited message:', selectedProvider);

      // Handle different provider types
      if (selectedProvider.startsWith('direct-gemini/')) {
        // For direct Gemini, extract the model name from the provider string
        const model = selectedProvider.replace('direct-gemini/', '');
        options.model = model;
        console.log('Using Gemini model for edited message:', model);
      }
      else if (selectedProvider.startsWith('direct-openai/')) {
        // For direct OpenAI, extract the model name from the provider string
        const model = selectedProvider.replace('direct-openai/', '');
        options.model = model;
        console.log('Using OpenAI model for edited message:', model);
      }
      // If using OpenRouter, get the specific model
      else if (selectedProvider.includes('openrouter') || selectedProvider.includes('/')) {
        // For OpenRouter, the selectedProvider itself is the model name
        options.openrouterModel = selectedProvider;
        console.log('Using OpenRouter model for edited message:', selectedProvider);
      }

      // Add the selected provider to the options to ensure API manager uses the correct model
      options.selectedProviderOverride = selectedProvider;
      console.log('Adding selected provider override to options for edited message:', selectedProvider);

      // Send to AI
      const response = await this.apiManager.sendRequest(specializedPrompt, options);

      // Remove typing indicator
      this.removeTypingIndicator();

      // Find the AI response that followed the edited message
      const editedMessageIndex = this.chatHistory.findIndex(msg => msg.id === messageId);
      if (editedMessageIndex !== -1 && editedMessageIndex < this.chatHistory.length - 1) {
        const nextMessage = this.chatHistory[editedMessageIndex + 1];
        if (nextMessage.sender === 'ai') {
          // Find the message element
          const messageElements = this.chatMessages.querySelectorAll('.message.ai-message');
          const aiMessageElement = Array.from(messageElements).find(
            el => el.dataset.messageId === nextMessage.id
          );

          if (aiMessageElement) {
            // Update the AI message content
            const contentDiv = aiMessageElement.querySelector('.message-content');
            if (contentDiv) {
              // No special handling for fallback responses
              contentDiv.innerHTML = this.uiManager.formatContent(response.text);

              // Update in chat history
              this.chatHistory[editedMessageIndex + 1].content = response.text;

              // Add edited indicator
              const editedIndicator = document.createElement('div');
              editedIndicator.className = 'message-edited-indicator';
              editedIndicator.textContent = 'Updated';
              contentDiv.appendChild(editedIndicator);
            }
          } else {
            // If we couldn't find the element, add a new message
            this.addMessageToChat(response.text, 'ai', false, true);
          }
        } else {
          // If the next message is not AI, add a new message
          this.addMessageToChat(response.text, 'ai', false, true);
        }
      } else {
        // If we couldn't find the message or it's the last one, add a new message
        this.addMessageToChat(response.text, 'ai', false, true);
      }

    } catch (error) {
      console.error('Error processing edited message:', error);
      this.removeTypingIndicator();
      this.addMessageToChat(`Error: ${error.message}`, 'ai', true);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get the conversation context for a message
   * @param {string} messageId - The ID of the message
   * @returns {Array} - The context messages
   */
  getMessageContext(messageId) {
    // Find the message in context
    const messageIndex = this.conversationContext.contextMessages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) {
      // If not found, return all context
      return this.conversationContext.contextMessages;
    }

    // Return all messages up to and including the target message
    return this.conversationContext.contextMessages.slice(0, messageIndex + 1);
  }

  /**
   * Create a prompt that includes conversation context
   * @param {string} userMessage - The user's message
   * @param {Array} context - The conversation context
   * @param {object} pageContent - The page content
   * @returns {string} - The prompt with context
   */
  createPromptWithContext(userMessage, context, pageContent) {
    // Start with basic page info
    let prompt = `URL: ${pageContent.url || 'Unknown'}\n`;
    prompt += `Title: ${pageContent.title || 'Unknown'}\n\n`;

    // Add conversation context
    if (context && context.length > 0) {
      prompt += "Previous conversation:\n";

      context.forEach(msg => {
        if (msg.sender === 'user') {
          prompt += `User: ${msg.content}\n`;
        } else {
          prompt += `Assistant: ${msg.content}\n`;
        }
      });

      prompt += "\n";
    }

    // Add current user message
    prompt += `User: ${userMessage}\n`;

    // Add page content (truncated if needed)
    if (pageContent && pageContent.content) {
      // Truncate content if it's too long
      const maxContentLength = 3000;
      const truncatedContent = pageContent.content.length > maxContentLength
        ? pageContent.content.substring(0, maxContentLength) + "... [content truncated]"
        : pageContent.content;

      prompt += "\nPage content:\n" + truncatedContent;
    }

    return prompt;
  }

  /**
   * Show typing indicator when AI is processing
   */
  showTypingIndicator() {
    // Get the existing typing indicator element
    const typingIndicator = document.getElementById('typingIndicator');

    // If it already exists, just make it visible
    if (typingIndicator) {
      typingIndicator.style.display = 'flex';
      this.scrollToBottom();
      return;
    }

    // Otherwise, create a new typing indicator in the chat
    const typingDiv = document.createElement('div');
    typingDiv.className = 'typing-indicator';
    typingDiv.id = 'typingIndicatorInChat';

    // Add the animated dots
    for (let i = 0; i < 3; i++) {
      const dot = document.createElement('div');
      dot.className = 'typing-dot';
      typingDiv.appendChild(dot);
    }

    this.chatMessages.appendChild(typingDiv);

    // Scroll to the indicator
    this.scrollToBottom();
  }

  /**
   * Remove typing indicator
   */
  removeTypingIndicator() {
    // Use the UI manager's method to remove loading indicators
    this.uiManager.removeLoadingIndicators();

    // Hide the fixed typing indicator
    const fixedIndicator = document.getElementById('typingIndicator');
    if (fixedIndicator) {
      fixedIndicator.style.display = 'none';
    }

    // Remove any in-chat typing indicators
    const inChatIndicator = document.getElementById('typingIndicatorInChat');
    if (inChatIndicator) {
      inChatIndicator.remove();
    }
  }

  /**
   * Clear chat history
   * @param {boolean} showConfirmation - Whether to show confirmation dialog
   */
  clearChat(showConfirmation = true) {
    console.log('clearChat method called');

    // Confirm with user if needed
    if (showConfirmation && this.chatHistory.length > 1) {
      if (!confirm('Are you sure you want to clear the chat history?')) {
        console.log('User cancelled clear chat');
        return;
      }
    }

    console.log('Clearing chat UI and history');

    // Clear UI - make sure we have access to the DOM element
    if (this.chatMessages) {
      while (this.chatMessages.firstChild) {
        this.chatMessages.removeChild(this.chatMessages.firstChild);
      }
    } else {
      console.error('Chat messages element not found');
      this.chatMessages = document.getElementById('chatMessages');
      if (this.chatMessages) {
        while (this.chatMessages.firstChild) {
          this.chatMessages.removeChild(this.chatMessages.firstChild);
        }
      }
    }

    // Add welcome message with typing effect
    this.addMessageToChat('👋 Hello! I\'m Browzy AI, your intelligent assistant. I can analyze any webpage you\'re viewing and help with any request - whether it\'s YouTube videos, news articles, coding problems, or anything else. How can I help you today?', 'ai', false, false, true);

    // Clear history
    this.chatHistory = [];

    // Save the empty chat to storage
    this.saveHistory();

    console.log('Chat cleared successfully');
  }

  /**
   * Save chat history to storage
   */
  async saveHistory() {
    if (this.chatHistory.length > 0) {
      const storageManager = this.apiManager.storageManager;
      const settings = await storageManager.getSettings();

      if (settings.rememberHistory) {
        // Generate a unique ID for this chat session
        const sessionId = 'chat_' + Date.now();

        // Get the first user message as the title, or use a default
        let title = 'Chat Session';
        const firstUserMsg = this.chatHistory.find(msg => msg.sender === 'user');
        if (firstUserMsg) {
          // Truncate long messages for the title
          title = firstUserMsg.content.length > 50
            ? firstUserMsg.content.substring(0, 50) + '...'
            : firstUserMsg.content;
        }

        // Get the current page info if available
        let pageInfo = {};
        try {
          const pageContent = await this.apiManager.getPageContent();
          if (pageContent && pageContent.title) {
            pageInfo = {
              title: pageContent.title,
              url: pageContent.url
            };
          }
        } catch (error) {
          console.error('Error getting page info:', error);
        }

        // Save the session with metadata
        await storageManager.saveSession(sessionId, {
          title: title,
          history: this.chatHistory,
          timestamp: new Date().toISOString(),
          pageInfo: pageInfo
        });
      }
    }
  }

  /**
   * Load chat history from storage
   */
  async loadHistory() {
    const storageManager = this.apiManager.storageManager;
    const settings = await storageManager.getSettings();

    if (settings.rememberHistory) {
      const session = await storageManager.getSession('chatHistory');

      if (session && session.history && session.history.length > 0) {
        // Clear current messages
        while (this.chatMessages.firstChild) {
          this.chatMessages.removeChild(this.chatMessages.firstChild);
        }

        // Add messages from history
        session.history.forEach(msg => {
          // Use typing effect for AI messages
          if (msg.sender === 'ai') {
            this.addMessageToChat(msg.content, msg.sender, false, false, true);
          } else {
            this.addMessageToChat(msg.content, msg.sender);
          }
        });

        this.chatHistory = session.history;
      }
    }
  }

  /**
   * Scroll chat to bottom
   */
  scrollToBottom() {
    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
  }

  /**
   * Check if a user message is related to chess
   * @param {string} message - The user's message
   * @returns {boolean} - Whether the message is chess-related
   */
  isChessQuestion(message) {
    if (!message) return false;

    const lowerMessage = message.toLowerCase();

    // Check for chess-specific terms
    const chessTerms = [
      'chess', 'move', 'piece', 'pawn', 'knight', 'bishop', 'rook', 'queen', 'king',
      'checkmate', 'check', 'castle', 'en passant', 'stalemate', 'draw', 'opening',
      'endgame', 'middlegame', 'position', 'board', 'square', 'attack', 'defend',
      'capture', 'promote', 'fork', 'pin', 'skewer', 'tactic', 'strategy'
    ];

    // Check for chess notation patterns
    const hasChessNotation = /[KQRBN]?[a-h][1-8]|O-O|O-O-O/.test(lowerMessage);

    // Check if the message contains chess-specific questions
    const chessQuestions = [
      'best move', 'analyze position', 'evaluate', 'what should i play',
      'how to win', 'is this winning', 'is this losing', 'what is the advantage',
      'who is better', 'who is winning', 'suggest a move', 'what would you play'
    ];

    // Check if any chess term is in the message
    for (const term of chessTerms) {
      if (lowerMessage.includes(term)) {
        return true;
      }
    }

    // Check if any chess question is in the message
    for (const question of chessQuestions) {
      if (lowerMessage.includes(question)) {
        return true;
      }
    }

    // If we have chess notation, it's likely a chess question
    if (hasChessNotation) {
      return true;
    }

    return false;
  }

  /**
   * Check if current page is a YouTube page
   * @param {object} pageContent - The page content
   * @returns {boolean} - Whether the page is YouTube-related
   */
  isYouTubePage(pageContent) {
    // Check URL for YouTube domain
    const url = pageContent.url.toLowerCase();
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return true;
    }

    // Check if we have YouTube-specific data
    if (pageContent.youtube) {
      return true;
    }

    // Check title for YouTube indicators
    const title = pageContent.title.toLowerCase();
    if (title.includes('youtube') || title.includes(' - youtube')) {
      return true;
    }

    return false;
  }

  /**
   * Check if a user message is related to coding
   * @param {string} message - The user's message
   * @returns {boolean} - Whether the message is coding-related
   */
  isCodingQuestion(message) {
    if (!message) return false;

    const lowerMessage = message.toLowerCase();

    // Check for coding-specific terms
    const codingTerms = [
      'code', 'program', 'function', 'algorithm', 'variable', 'class', 'object',
      'method', 'array', 'string', 'integer', 'boolean', 'loop', 'if', 'else',
      'while', 'for', 'return', 'import', 'export', 'module', 'package', 'library',
      'api', 'framework', 'bug', 'error', 'exception', 'debug', 'compile', 'runtime',
      'syntax', 'leetcode', 'problem', 'solution', 'complexity', 'time complexity',
      'space complexity', 'optimize', 'refactor', 'implement', 'data structure'
    ];

    // Check for coding-specific questions
    const codingQuestions = [
      'how to solve', 'fix this code', 'debug this', 'what is wrong', 'how to implement',
      'optimize this', 'refactor this', 'improve this code', 'what does this code do',
      'explain this algorithm', 'what is the time complexity', 'what is the space complexity',
      'how would you code', 'write a function', 'create a class', 'implement a method'
    ];

    // Check if any coding term is in the message
    for (const term of codingTerms) {
      if (lowerMessage.includes(term)) {
        return true;
      }
    }

    // Check if any coding question is in the message
    for (const question of codingQuestions) {
      if (lowerMessage.includes(question)) {
        return true;
      }
    }

    // Check for code snippets or syntax patterns
    const hasSyntaxPatterns = /function|class|if\s*\(|for\s*\(|while\s*\(|return|import|from|const|let|var/.test(lowerMessage);
    if (hasSyntaxPatterns) {
      return true;
    }

    return false;
  }

  /**
   * Truncate content to a maximum length
   * @param {string} content - The content to truncate
   * @param {number} maxLength - Maximum length
   * @returns {string} - Truncated content
   */
  truncateContent(content, maxLength = 6000) {
    if (!content) return '';
    if (content.length <= maxLength) return content;

    return content.substring(0, maxLength) + '... [content truncated due to length]';
  }

  /**
   * Clear the chat
   */
  clearChat() {
    this.chatMessages.innerHTML = '';
    this.chatHistory = [];
    this.addMessageToChat('How can I help you today?', 'ai', false, false, true);
  }

  /**
   * Get the last AI response text
   * @returns {string|null} - The last AI response text or null if none exists
   */
  getLastResponse() {
    // Find the last AI message in the chat history
    for (let i = this.chatHistory.length - 1; i >= 0; i--) {
      const message = this.chatHistory[i];
      if (message.sender === 'ai') {
        return message.content;
      }
    }

    // If no AI messages found, return null
    return null;
  }
}