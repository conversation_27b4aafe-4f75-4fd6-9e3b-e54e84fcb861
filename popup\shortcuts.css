/* Keyboard Shortcuts Styles */
.shortcuts-container {
  margin: 15px 0 25px;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.shortcut-card {
  background-color: var(--surface-color);
  border-radius: var(--border-radius-sm);
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: var(--primary-color);
  background-color: rgba(0, 166, 192, 0.1);
}

.shortcut-key {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--text-color);
  font-weight: 500;
}

kbd {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.2);
  color: var(--text-color);
  display: inline-block;
  font-family: var(--font-mono);
  font-size: 0.8rem;
  line-height: 1;
  padding: 4px 6px;
  white-space: nowrap;
}

.shortcut-description {
  font-size: 0.85rem;
  color: var(--text-light);
}

/* Keyboard Shortcuts Dialog */
.shortcuts-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.shortcuts-dialog.active {
  display: flex;
}

.shortcuts-content {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 600px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-hover);
  border: var(--glass-border);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.shortcuts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: var(--glass-border);
  background: var(--primary-gradient);
}

.shortcuts-header h3 {
  font-size: 1.2rem;
  color: var(--white);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.shortcuts-close-btn {
  background: transparent;
  border: none;
  color: var(--white);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.shortcuts-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.shortcuts-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) var(--bg-light);
}

.shortcuts-body::-webkit-scrollbar {
  width: 6px;
}

.shortcuts-body::-webkit-scrollbar-track {
  background: var(--bg-light);
}

.shortcuts-body::-webkit-scrollbar-thumb {
  background-color: var(--primary-light);
  border-radius: 20px;
}

.shortcuts-section {
  margin-bottom: 20px;
}

.shortcuts-section-title {
  font-size: 1rem;
  color: var(--primary-color);
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 5px;
}

.shortcuts-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
