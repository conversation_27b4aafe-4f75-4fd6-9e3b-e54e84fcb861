# BrowzyAI Environment Configuration

# Application
NODE_ENV=development
APP_NAME=BrowzyAI
APP_VERSION=1.0.1

# API Configuration
API_PORT=5000
API_HOST=0.0.0.0
API_BASE_URL=https://api.browzyai.com

# Dashboard Configuration
DASHBOARD_PORT=5173
DASHBOARD_HOST=0.0.0.0
VITE_API_URL=https://api.browzyai.com
VITE_APP_NAME=BrowzyAI Dashboard

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=browzyai
DB_USER=browzyai
DB_PASSWORD=browzyai_password
DATABASE_URL=*****************************************************/browzyai

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379

# AI API Keys (Add your actual keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# CORS Configuration
CORS_ORIGIN=https://browzyai.com,https://*.browzyai.com,chrome-extension://*

# Security
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Email Configuration (for development)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=<EMAIL>

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads/

# Extension Configuration
EXTENSION_ID=your_extension_id_here
EXTENSION_VERSION=1.0.1

# Development Tools
DEBUG=browzyai:*
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true

# Health Check
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# Analytics (optional)
GOOGLE_ANALYTICS_ID=
MIXPANEL_TOKEN=

# Feature Flags
ENABLE_ANALYTICS=false
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_COMPRESSION=true
