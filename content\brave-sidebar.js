'use strict';

/**
 * Custom sidebar implementation for Brave browser
 * This is used when the Chrome Side Panel API is not available or not working in Brave
 */

// Check if we're on a protected URL that doesn't allow content script injection
const checkProtectedUrl = () => {
  return window.location.href.startsWith('chrome://') ||
         window.location.href.startsWith('brave://') ||
         window.location.href.startsWith('edge://') ||
         window.location.href.startsWith('about:') ||
         window.location.href.startsWith('chrome-extension://') ||
         window.location.href.startsWith('devtools://');
};

// Check if we're on a blank page
const checkBlankPage = () => {
  return window.location.href === 'about:blank' ||
         window.location.href === 'about:newtab' ||
         window.location.href === 'chrome://newtab/' ||
         window.location.href === 'brave://newtab/' ||
         window.location.href === 'chrome-search://local-ntp/local-ntp.html' ||
         window.location.href === 'brave-search://local-ntp/local-ntp.html' ||
         window.location.href.includes('/_/chrome/newtab') ||
         window.location.href.includes('/_/brave/newtab') ||
         window.location.href.includes('chrome://new-tab-page') ||
         window.location.href.includes('brave://new-tab-page') ||
         (window.location.pathname === '/' && document.title === 'New Tab') ||
         (window.location.href === window.location.origin + '/' && document.title === 'New Tab') ||
         document.documentElement.textContent.trim() === '';
};

// Determine if we're on a protected URL or blank page
const isProtectedUrl = checkProtectedUrl();
const isBlankPage = checkBlankPage();

// Log if we're on a blank page or protected URL
if (isBlankPage) {
  console.log('Blank page detected, sidebar will show error message:', window.location.href);
}

// Log if we're on a protected URL
if (isProtectedUrl) {
  console.log('Protected URL detected, sidebar functionality may be limited:', window.location.href);
}

// Add a function to show an error message for blank pages
BraveSidebar.prototype.showBlankPageError = function() {
  console.log('Showing blank page error message in sidebar');

  try {
    // Create a container for the error message
    const errorContainer = document.createElement('div');
    errorContainer.className = 'blank-page-error';
    errorContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #283b48;
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      text-align: center;
      z-index: 9999;
    `;

    // Add the error message
    const errorMessage = document.createElement('div');
    errorMessage.innerHTML = `
      <h2 style="color: #00a6c0; margin-bottom: 20px;">Browzy AI Sidebar</h2>
      <p style="margin-bottom: 15px;">The sidebar cannot be used on blank pages or new tabs.</p>
      <p style="margin-bottom: 15px;">Please navigate to a website first.</p>
      <button id="openPopupButton" style="
        background-color: #00a6c0;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 20px;
      ">Open in Popup</button>
    `;

    errorContainer.appendChild(errorMessage);
    document.body.appendChild(errorContainer);

    // Add event listener to the button
    const openPopupButton = document.getElementById('openPopupButton');
    if (openPopupButton) {
      openPopupButton.addEventListener('click', () => {
        console.log('Opening popup from blank page error message');

        // Open the popup in a new tab
        chrome.runtime.sendMessage({
          action: 'openPopup',
          params: {
            blankPage: true,
            newTab: true,
            direct: true
          }
        });
      });
    }
  } catch (error) {
    console.error('Error showing blank page error message:', error);
  }
};
class BraveSidebar {
  constructor() {
    this.sidebarVisible = false;
    this.initialized = false;
    this.sidebar = null;
    this.toggleButton = null;

    // Store the protected URL status
    this.isProtectedUrl = isProtectedUrl;

    // Store the blank page status (should always be false at this point due to early exit)
    this.isBlankPage = isBlankPage;

    // We'll try to initialize the sidebar for all pages
    console.log('Attempting to initialize sidebar for all pages:', window.location.href);

    // Initialize the sidebar for regular pages
    console.log('Initializing Brave sidebar on regular page');
    this.init();
  }

  /**
   * Show a loading animation before the sidebar appears
   */
  showLoadingAnimation() {
    console.log('Showing loading animation before sidebar appears');

    try {
      // Create a container for the loading animation
      const loadingContainer = document.createElement('div');
      loadingContainer.id = 'browzyLoadingAnimation';
      loadingContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 2147483646;
        transition: opacity 0.5s ease;
      `;

      // Create a container for particles
      const particlesContainer = document.createElement('div');
      particlesContainer.className = 'particles-container';
      particlesContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        pointer-events: none;
      `;

      // Add particles
      for (let i = 0; i < 15; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Random size between 5px and 15px
        const size = Math.floor(Math.random() * 10) + 5;

        // Random position
        const posX = Math.floor(Math.random() * 100);
        const posY = Math.floor(Math.random() * 100);

        // Random opacity
        const opacity = (Math.random() * 0.5) + 0.1;

        // Random animation delay
        const animDelay = Math.random() * 5;

        // Random animation duration between 5 and 10 seconds
        const animDuration = Math.random() * 5 + 5;

        particle.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          background-color: #00a6c0;
          border-radius: 50%;
          left: ${posX}%;
          top: ${posY}%;
          opacity: ${opacity};
          pointer-events: none;
          animation-delay: ${animDelay}s;
          animation-duration: ${animDuration}s;
        `;

        particlesContainer.appendChild(particle);
      }

      // Add glow effect
      const glowEffect = document.createElement('div');
      glowEffect.className = 'glow-effect';
      glowEffect.style.cssText = `
        position: absolute;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(0,166,192,0.4) 0%, rgba(0,166,192,0) 70%);
        pointer-events: none;
      `;

      // Add the logo and brand text
      const logoContainer = document.createElement('div');
      logoContainer.className = 'logo-container';
      logoContainer.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
      `;

      const logo = document.createElement('div');
      logo.className = 'animated-logo';
      logo.style.cssText = `
        width: 100px;
        height: 100px;
        background-image: url(${chrome.runtime.getURL('images/icon.png')});
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
      `;

      // Add BrowzyAI text with character-by-character animation
      const brandText = document.createElement('div');
      brandText.className = 'brand-text';
      brandText.style.cssText = `
        color: white;
        font-size: 28px;
        font-weight: bold;
        font-family: Arial, sans-serif;
        letter-spacing: 1px;
        margin-bottom: 10px;
        text-shadow: 0 0 10px rgba(0, 166, 192, 0.7);
        display: flex;
        justify-content: center;
      `;

      // Create individual character spans for animation
      const brandName = 'BrowzyAI';
      for (let i = 0; i < brandName.length; i++) {
        const charSpan = document.createElement('span');
        charSpan.textContent = brandName[i];
        charSpan.style.cssText = `
          display: inline-block;
          opacity: 0;
          transform: translateY(10px);
          animation: charAppear 0.3s forwards;
          animation-delay: ${i * 0.1}s;
        `;
        brandText.appendChild(charSpan);
      }

      // Add logo and brand text to container
      logoContainer.appendChild(logo);
      logoContainer.appendChild(brandText);

      // Add welcome text with typing animation
      const welcomeTextContainer = document.createElement('div');
      welcomeTextContainer.className = 'welcome-text-container';
      welcomeTextContainer.style.cssText = `
        color: white;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        font-family: Arial, sans-serif;
        height: 30px;
        text-align: center;
      `;

      // Create an array of messages to display in sequence
      const messages = [
        'Initializing Browzy AI...',
        'Loading resources...',
        'Preparing your assistant...',
        'Browzy is ready to assist you'
      ];

      // Add the initial message
      welcomeTextContainer.textContent = messages[0];

      // Set up a timer to change the message with timing that ensures all messages
      // are shown before the sidebar appears at 3 seconds
      let messageIndex = 0;

      // Calculate timing to show all messages within our 3-second window
      // We have 4 messages and need to show them all before 2.8 seconds
      const messageDuration = 650; // Show each message for 650ms

      const messageInterval = setInterval(() => {
        messageIndex = (messageIndex + 1) % messages.length;

        // Create a fade effect
        welcomeTextContainer.style.opacity = '0';
        setTimeout(() => {
          welcomeTextContainer.textContent = messages[messageIndex];
          welcomeTextContainer.style.opacity = '1';

          // Add a special effect for the final message
          if (messageIndex === messages.length - 1) {
            welcomeTextContainer.style.color = '#48d7ce'; // Light turquoise accent
            welcomeTextContainer.style.fontWeight = 'bold';
            welcomeTextContainer.style.textShadow = '0 0 10px rgba(0, 166, 192, 0.5)';
          }
        }, 200);

        // Stop the interval after showing all messages
        if (messageIndex === messages.length - 1) {
          clearInterval(messageInterval);
        }
      }, messageDuration);

      // Add loading indicator
      const loadingIndicator = document.createElement('div');
      loadingIndicator.className = 'loading-indicator-animation';
      loadingIndicator.style.cssText = `
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 200px;
      `;

      // Add dots for loading animation
      const dotsContainer = document.createElement('div');
      dotsContainer.className = 'dots-container';
      dotsContainer.style.cssText = `
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
      `;

      for (let i = 0; i < 3; i++) {
        const dot = document.createElement('div');
        dot.className = 'dot';
        dot.style.cssText = `
          width: 10px;
          height: 10px;
          background-color: #00a6c0;
          border-radius: 50%;
          margin: 0 5px;
          animation: pulse 1s infinite ease-in-out;
          animation-delay: ${i * 0.2}s;
        `;
        dotsContainer.appendChild(dot);
      }

      // Add progress bar
      const progressBarContainer = document.createElement('div');
      progressBarContainer.className = 'progress-bar-container';
      progressBarContainer.style.cssText = `
        width: 100%;
        height: 6px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        overflow: hidden;
      `;

      const progressBar = document.createElement('div');
      progressBar.className = 'progress-bar';
      progressBar.style.cssText = `
        width: 0%;
        height: 100%;
        background-color: #00a6c0;
        border-radius: 3px;
        transition: width 0.3s ease-out;
      `;

      progressBarContainer.appendChild(progressBar);

      // Animate the progress bar to match our 3-second animation
      // Calculate exactly how much to increment per interval to reach 100% at 2.7 seconds
      // (slightly before the sidebar appears at 3 seconds)
      let progress = 0;
      const totalDuration = 2700; // 2.7 seconds (just before sidebar appears at 3s)
      const intervalTime = 100; // Update every 100ms
      const totalIntervals = totalDuration / intervalTime;
      const incrementPerInterval = 100 / totalIntervals;

      const progressInterval = setInterval(() => {
        progress += incrementPerInterval;

        // Add a slight acceleration effect
        if (progress < 50) {
          // Slower at the beginning
          progress += incrementPerInterval * 0.2;
        } else if (progress > 80) {
          // Faster at the end
          progress += incrementPerInterval * 0.5;
        }

        if (progress >= 100) {
          clearInterval(progressInterval);
          progress = 100;

          // Add a "complete" visual effect
          progressBar.style.transition = "background-color 0.3s ease";
          progressBar.style.backgroundColor = "#48d7ce"; // Light turquoise accent color

          // Add a subtle pulse effect
          setTimeout(() => {
            progressBar.style.boxShadow = "0 0 10px rgba(0, 166, 192, 0.8)";
          }, 100);
        }

        progressBar.style.width = `${progress}%`;
      }, intervalTime);

      // Add elements to loading indicator
      loadingIndicator.appendChild(dotsContainer);
      loadingIndicator.appendChild(progressBarContainer);

      // Add animation styles
      const style = document.createElement('style');
      style.textContent = `
        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.5); opacity: 0.7; }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-15px); }
        }

        @keyframes particleFloat {
          0% { transform: translate(0, 0); }
          25% { transform: translate(10px, -10px); }
          50% { transform: translate(20px, 0); }
          75% { transform: translate(10px, 10px); }
          100% { transform: translate(0, 0); }
        }

        @keyframes glowPulse {
          0%, 100% { opacity: 0.4; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.2); }
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes fadeOut {
          from { opacity: 1; }
          to { opacity: 0; }
        }

        @keyframes charAppear {
          0% { opacity: 0; transform: translateY(10px); }
          100% { opacity: 1; transform: translateY(0); }
        }

        #browzyLoadingAnimation .animated-logo {
          animation: float 2s infinite ease-in-out;
        }

        #browzyLoadingAnimation .brand-text {
          animation: glowPulse 2s infinite ease-in-out;
        }

        #browzyLoadingAnimation .particle {
          animation: particleFloat 8s infinite ease-in-out;
        }

        #browzyLoadingAnimation .glow-effect {
          animation: glowPulse 3s infinite ease-in-out;
        }

        #browzyLoadingAnimation .welcome-text-container {
          transition: opacity 0.2s ease-in-out;
        }
      `;

      // Assemble the loading animation
      loadingContainer.appendChild(particlesContainer);
      loadingContainer.appendChild(glowEffect);
      loadingContainer.appendChild(logoContainer);
      loadingContainer.appendChild(welcomeTextContainer);
      loadingContainer.appendChild(loadingIndicator);
      document.head.appendChild(style);
      document.body.appendChild(loadingContainer);

      // Fade out and remove the loading animation just before the sidebar appears
      setTimeout(() => {
        loadingContainer.style.opacity = '0';
        setTimeout(() => {
          if (document.body.contains(loadingContainer)) {
            document.body.removeChild(loadingContainer);
          }
        }, 500);
      }, 2800); // Fade out slightly before sidebar appears (at 3000ms)
    } catch (error) {
      console.error('Error showing loading animation:', error);
    }
  }

  /**
   * Show an error message for blank pages
   */
  showBlankPageError() {
    console.log('Showing blank page error message');

    try {
      // Create a notification element
      const notification = document.createElement('div');
      notification.className = 'browzy-blank-page-notification';
      notification.style.position = 'fixed';
      notification.style.top = '20px';
      notification.style.right = '20px';
      notification.style.backgroundColor = '#FF3B30';
      notification.style.color = 'white';
      notification.style.padding = '15px 20px';
      notification.style.borderRadius = '8px';
      notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
      notification.style.zIndex = '2147483647';
      notification.style.maxWidth = '350px';
      notification.style.fontFamily = 'Arial, sans-serif';
      notification.style.fontSize = '14px';
      notification.style.lineHeight = '1.5';
      notification.style.display = 'flex';
      notification.style.alignItems = 'center';
      notification.style.justifyContent = 'space-between';
      notification.style.animation = 'slideIn 0.3s ease-out forwards';

      // Add animation styles
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes fadeOut {
          from { opacity: 1; }
          to { opacity: 0; }
        }
      `;
      document.head.appendChild(style);

      // Add content
      notification.innerHTML = `
        <div style="display: flex; align-items: center;">
          <div style="margin-right: 12px; font-size: 20px;">⚠️</div>
          <div>
            <div style="font-weight: bold; margin-bottom: 4px;">Sidebar Not Available</div>
            <div>Browzy AI sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.</div>
          </div>
        </div>
        <div style="margin-left: 10px; cursor: pointer; font-size: 18px;" id="closeNotification">×</div>
      `;

      // Add to document
      document.body.appendChild(notification);

      // Add close functionality
      const closeButton = document.getElementById('closeNotification');
      if (closeButton) {
        closeButton.addEventListener('click', () => {
          notification.style.animation = 'fadeOut 0.3s ease-out forwards';
          setTimeout(() => {
            notification.remove();
          }, 300);
        });
      }

      // Auto-remove after 8 seconds
      setTimeout(() => {
        if (document.body.contains(notification)) {
          notification.style.animation = 'fadeOut 0.3s ease-out forwards';
          setTimeout(() => {
            notification.remove();
          }, 300);
        }
      }, 8000);

      // Also try to send a message to the background script
      try {
        chrome.runtime.sendMessage({
          action: 'blankPageError',
          message: 'Sidebar cannot be used on blank pages or new tabs'
        }).catch(e => console.error('Error sending message to background script:', e));
      } catch (e) {
        console.error('Error sending message to background script:', e);
      }

    } catch (error) {
      console.error('Error showing blank page notification:', error);

      // Fallback to alert if notification fails
      try {
        alert('Browzy AI sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.');
      } catch (alertError) {
        console.error('Error showing alert:', alertError);
      }
    }
  }

  /**
   * Initialize the sidebar
   */
  init() {
    if (this.initialized) return;

    console.log('Initializing Brave custom sidebar');

    // We'll try to initialize the sidebar for all pages, including blank pages
    console.log('Attempting to initialize sidebar for all page types in init()');

    // Create the sidebar container
    this.sidebar = document.createElement('div');
    this.sidebar.className = 'browzy-ai-brave-sidebar';
    this.sidebar.style.position = 'fixed';
    this.sidebar.style.top = '0';
    this.sidebar.style.right = '0';
    this.sidebar.style.bottom = '0';

    // Use consistent width for all pages
    this.sidebar.style.width = '400px';

    this.sidebar.style.height = '100%';
    this.sidebar.style.display = 'flex';
    this.sidebar.style.flexDirection = 'column';
    this.sidebar.style.zIndex = '2147483647';
    this.sidebar.style.transform = 'translateX(100%)';
    this.sidebar.style.transition = 'transform 0.3s ease-in-out';

    // Create the sidebar header
    const header = document.createElement('div');
    header.className = 'browzy-ai-brave-sidebar-header';
    header.style.height = '60px';
    header.style.display = 'flex';
    header.style.alignItems = 'center';
    header.style.justifyContent = 'space-between';
    header.style.padding = '15px';
    header.style.background = 'linear-gradient(135deg, #0088ff, #0066cc)';
    header.style.color = 'white';
    header.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
    header.style.boxSizing = 'border-box';
    header.style.flexShrink = '0';

    // Create the title with logo
    const title = document.createElement('div');
    title.className = 'browzy-ai-brave-sidebar-title';

    // Add logo
    const logo = document.createElement('img');
    logo.src = chrome.runtime.getURL('images/icon.png');
    logo.alt = 'Browzy AI';

    // Add title text
    const titleText = document.createElement('span');
    titleText.textContent = 'Browzy AI';

    // Assemble title
    title.appendChild(logo);
    title.appendChild(titleText);

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.className = 'browzy-ai-brave-sidebar-close';
    closeButton.innerHTML = '<i class="fas fa-times"></i>';
    closeButton.style.backgroundColor = '#FF0000';
    closeButton.style.color = '#FFFFFF';
    closeButton.style.width = '40px';
    closeButton.style.height = '40px';
    closeButton.style.fontSize = '20px';
    closeButton.style.border = '2px solid white';
    closeButton.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
    closeButton.style.zIndex = '9999';
    closeButton.style.cursor = 'pointer';
    closeButton.style.borderRadius = '50%';
    closeButton.addEventListener('click', () => {
      console.log('Close button clicked in Brave sidebar');

      // Use multiple methods to ensure the sidebar closes

      // Method 1: Toggle sidebar visibility
      this.toggleSidebar(false);

      // Method 2: Notify the iframe to close
      if (this.iframe && this.iframe.contentWindow) {
        try {
          this.iframe.contentWindow.postMessage({
            source: 'brave-sidebar',
            action: 'sidebarClosed'
          }, '*');
        } catch (error) {
          console.error('Error notifying iframe about sidebar closure:', error);
        }
      }

      // Method 3: Send message to background script
      try {
        chrome.runtime.sendMessage({ action: 'closeSidePanel' })
          .catch(e => console.error('Error sending closeSidePanel message:', e));
      } catch (e) {
        console.error('Error sending message to background script:', e);
      }

      // Method 4: Force hide the sidebar directly
      try {
        this.sidebar.style.transform = 'translateX(100%)';
        this.sidebar.classList.remove('visible');
        this.sidebarVisible = false;
      } catch (e) {
        console.error('Error hiding sidebar directly:', e);
      }
    });

    // Assemble header
    header.appendChild(title);
    header.appendChild(closeButton);

    // Create content area
    this.content = document.createElement('div');
    this.content.className = 'browzy-ai-brave-sidebar-content';
    this.content.style.flex = '1';
    this.content.style.display = 'flex';
    this.content.style.flexDirection = 'column';
    this.content.style.height = 'calc(100% - 60px)';
    this.content.style.overflow = 'hidden';

    // Create iframe to load the extension popup
    this.iframe = document.createElement('iframe');

    // Add special attributes to ensure proper loading
    this.iframe.setAttribute('allowtransparency', 'true');
    this.iframe.setAttribute('allowfullscreen', 'true');
    this.iframe.setAttribute('importance', 'high');
    this.iframe.setAttribute('loading', 'eager');

    // CRITICAL: We need to allow scripts and same-origin for functionality
    // This is necessary for the iframe to work properly on blank pages
    // The allow-scripts and allow-same-origin attributes are essential for event handlers to work
    this.iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-top-navigation-by-user-activation');

    // Set allow attribute for necessary features
    this.iframe.allow = 'clipboard-read; clipboard-write';

    // Set iframe styles
    this.iframe.style.width = '100%';
    this.iframe.style.height = '100%';
    this.iframe.style.border = 'none';
    this.iframe.style.display = 'block';
    this.iframe.style.flex = '1';

    // Use the already defined isBlankPage property from the class

    // Use a consistent approach for all page types with special handling for blank pages
    // Add a timestamp to prevent caching issues
    const timestamp = new Date().getTime();

    // Prepare parameters that will be consistent across all page types
    const commonParams = `sidebar=true&t=${timestamp}`;

    // Add page-specific parameters
    const pageTypeParams = this.isBlankPage ? '&blankPage=true&direct=true&newTab=true' : '';

    // Construct the full URL
    const fullUrl = chrome.runtime.getURL(`popup/popup.html?${commonParams}${pageTypeParams}`);
    console.log('Loading iframe with URL:', fullUrl);

    // Clear the iframe first to ensure a fresh load
    this.iframe.src = 'about:blank';

    // Wait a moment before setting the new URL
    setTimeout(() => {
      // Set the iframe source
      this.iframe.src = fullUrl;

      // Add data attributes for page type
      if (this.isBlankPage) {
        this.iframe.setAttribute('data-blank-page', 'true');
        this.iframe.setAttribute('data-new-tab', 'true');
      }

      // Apply consistent styling to ensure the iframe is visible and interactive
      this.iframe.style.cssText += `
        width: 100% !important;
        height: 100% !important;
        border: none !important;
        display: block !important;
        flex: 1 !important;
        pointer-events: auto !important;
        z-index: 9999 !important;
        position: relative !important;
        background-color: #000000 !important;
      `;

      // Add special attributes to ensure proper loading
      this.iframe.setAttribute('allowtransparency', 'true');
      this.iframe.setAttribute('allowfullscreen', 'true');
      this.iframe.setAttribute('importance', 'high');
      this.iframe.setAttribute('loading', 'eager');

      console.log('Enhanced iframe for all page types');
    }, 100);

    // Add load event listener with enhanced error handling
    this.iframe.addEventListener('load', () => {
      console.log('Iframe loaded successfully');

      // Try to communicate with the iframe to ensure it's working
      try {
        // Send initialization message - use * for origin to avoid cross-origin issues
        this.iframe.contentWindow.postMessage({
          source: 'brave-sidebar',
          action: 'sidebarInitialized',
          isBlankPage: this.isBlankPage
        }, '*');

        // For blank pages, add extra initialization with multiple attempts
        if (this.isBlankPage) {
          // Send initialization messages at different intervals to ensure it works
          const initializationAttempts = [100, 500, 1000, 2000, 3000, 5000];

          initializationAttempts.forEach(delay => {
            setTimeout(() => {
              try {
                console.log(`Sending initialization for blank page (delay: ${delay}ms)`);
                this.iframe.contentWindow.postMessage({
                  source: 'brave-sidebar',
                  action: 'forceInitializeBlankPage',
                  isBlankPage: true,
                  isNewTab: true,
                  timestamp: new Date().getTime()
                }, '*');

                // Try to focus the iframe to ensure it receives keyboard events
                this.iframe.focus();

                // Try to directly initialize the blank page handler in the iframe
                try {
                  const iframeDoc = this.iframe.contentDocument || this.iframe.contentWindow.document;
                  const iframeWin = this.iframe.contentWindow;

                  // Add the blank-page-sidebar class to the body
                  iframeDoc.body.classList.add('blank-page-sidebar');
                  iframeDoc.body.classList.add('new-tab-sidebar');

                  // Try to initialize the blank page handler directly
                  if (iframeWin.blankPageHandler) {
                    console.log('Found blankPageHandler, calling init()');
                    iframeWin.blankPageHandler.init();
                  } else {
                    console.log('No blankPageHandler found, trying to create it');

                    // Try to execute the blank page handler script directly
                    const script = iframeDoc.createElement('script');
                    script.textContent = `
                      if (!window.blankPageHandler && typeof BlankPageHandler === 'function') {
                        console.log('Creating BlankPageHandler instance');
                        window.blankPageHandler = new BlankPageHandler();
                      } else if (window.blankPageHandler) {
                        console.log('BlankPageHandler exists, calling init()');
                        window.blankPageHandler.init();
                      } else {
                        console.log('BlankPageHandler not available yet');
                      }
                    `;
                    iframeDoc.head.appendChild(script);
                  }

                  // Focus the input field
                  const userInput = iframeDoc.getElementById('userInput');
                  if (userInput) {
                    userInput.focus();
                    console.log('Focused input field');

                    // Make sure it's clickable
                    userInput.style.pointerEvents = 'auto';
                    userInput.style.zIndex = '9999';
                    userInput.style.position = 'relative';
                  }

                  // Make sure the chat tab is active
                  const chatTab = iframeDoc.getElementById('chatTab');
                  if (chatTab) {
                    chatTab.click();
                    console.log('Activated chat tab');

                    // Make sure it's clickable
                    chatTab.style.pointerEvents = 'auto';
                    chatTab.style.cursor = 'pointer';
                    chatTab.style.zIndex = '9999';
                    chatTab.style.position = 'relative';
                  }

                  // Make all buttons clickable
                  const allElements = iframeDoc.querySelectorAll('button, input, select, textarea, a, .tab-btn, .dropdown-item');
                  allElements.forEach(el => {
                    el.style.pointerEvents = 'auto';
                    el.style.cursor = 'pointer';
                    el.style.zIndex = '9999';
                    el.style.position = 'relative';
                  });

                  console.log('Made all elements clickable');
                } catch (focusError) {
                  console.error('Error interacting with iframe elements:', focusError);
                }
              } catch (error) {
                console.error(`Error sending initialization (delay: ${delay}ms):`, error);
              }
            }, delay);
          });

          // Also try to inject a direct script into the iframe
          setTimeout(() => {
            try {
              const iframeDoc = this.iframe.contentDocument || this.iframe.contentWindow.document;

              // Create a script element to inject our direct event handlers
              const script = iframeDoc.createElement('script');
              script.textContent = `
                // Make all elements clickable
                document.body.classList.add('blank-page-sidebar');
                document.body.classList.add('new-tab-sidebar');

                // Add direct event handlers to all buttons
                document.querySelectorAll('button').forEach(button => {
                  button.addEventListener('click', function() {
                    console.log('Direct click handler:', button.id || button.className);
                  });

                  // Make sure it's clickable
                  button.style.pointerEvents = 'auto';
                  button.style.cursor = 'pointer';
                  button.style.zIndex = '9999';
                  button.style.position = 'relative';
                });

                // Add direct event handler to send button
                const sendButton = document.getElementById('sendMessage');
                if (sendButton) {
                  sendButton.addEventListener('click', function() {
                    console.log('Send button clicked via direct handler');
                    const userInput = document.getElementById('userInput');
                    if (userInput && userInput.value.trim()) {
                      if (window.chatManager && typeof window.chatManager.sendMessage === 'function') {
                        window.chatManager.sendMessage();
                      }
                    }
                  });
                }

                console.log('Injected direct event handlers');
              `;
              iframeDoc.head.appendChild(script);

              console.log('Injected direct script into iframe');
            } catch (error) {
              console.error('Error injecting script into iframe:', error);
            }
          }, 1500);
        }
      } catch (error) {
        console.error('Error communicating with iframe after load:', error);

        // Try to reload the iframe if communication fails
        setTimeout(() => {
          try {
            console.log('Reloading iframe after communication failure');
            const newTimestamp = new Date().getTime();
            const blankPageParam = this.isBlankPage ? '&blankPage=true' : '';
            this.iframe.src = chrome.runtime.getURL(`popup/popup.html?sidebar=true&t=${newTimestamp}${blankPageParam}&retry=true`);
          } catch (reloadError) {
            console.error('Error reloading iframe:', reloadError);
          }
        }, 500);
      }
    });

    // Add iframe to content
    this.content.appendChild(this.iframe);

    // Create a floating close button that's always visible
    const floatingCloseButton = document.createElement('button');
    floatingCloseButton.className = 'browzy-ai-brave-sidebar-floating-close';
    floatingCloseButton.innerHTML = '<span style="font-size: 30px; font-weight: bold;">×</span>';
    floatingCloseButton.style.position = 'fixed'; // Use fixed instead of absolute
    floatingCloseButton.style.top = '10px';
    floatingCloseButton.style.right = '10px';
    floatingCloseButton.style.backgroundColor = '#FF0000';
    floatingCloseButton.style.color = '#FFFFFF';
    floatingCloseButton.style.width = '50px'; // Larger button
    floatingCloseButton.style.height = '50px'; // Larger button
    floatingCloseButton.style.borderRadius = '50%';
    floatingCloseButton.style.border = '3px solid white'; // Thicker border
    floatingCloseButton.style.boxShadow = '0 0 15px rgba(0,0,0,0.9)'; // Stronger shadow
    floatingCloseButton.style.zIndex = '9999999';
    floatingCloseButton.style.cursor = 'pointer';
    floatingCloseButton.style.display = 'flex';
    floatingCloseButton.style.alignItems = 'center';
    floatingCloseButton.style.justifyContent = 'center';

    // Add animation
    const animationStyle = document.createElement('style');
    animationStyle.textContent = `
      @keyframes pulseButton {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }
      .browzy-ai-brave-sidebar-floating-close {
        animation: pulseButton 2s infinite;
      }
    `;
    document.head.appendChild(animationStyle);

    floatingCloseButton.addEventListener('click', () => {
      console.log('Floating close button clicked in Brave sidebar');

      // Use multiple methods to ensure the sidebar closes

      // Method 1: Toggle sidebar visibility
      this.toggleSidebar(false);

      // Method 2: Notify the iframe to close
      if (this.iframe && this.iframe.contentWindow) {
        try {
          this.iframe.contentWindow.postMessage({
            source: 'brave-sidebar',
            action: 'sidebarClosed'
          }, '*');
        } catch (error) {
          console.error('Error notifying iframe about sidebar closure:', error);
        }
      }

      // Method 3: Send message to background script
      try {
        chrome.runtime.sendMessage({ action: 'closeSidePanel' })
          .catch(e => console.error('Error sending closeSidePanel message:', e));
      } catch (e) {
        console.error('Error sending message to background script:', e);
      }

      // Method 4: Force hide the sidebar directly
      try {
        this.sidebar.style.transform = 'translateX(100%)';
        this.sidebar.classList.remove('visible');
        this.sidebarVisible = false;
      } catch (e) {
        console.error('Error hiding sidebar directly:', e);
      }
    });

    // Assemble sidebar
    this.sidebar.appendChild(header);
    this.sidebar.appendChild(this.content);
    this.sidebar.appendChild(floatingCloseButton);

    // No toggle button needed - we'll control the sidebar from the extension

    // Add Font Awesome if not already present
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const fontAwesome = document.createElement('link');
      fontAwesome.rel = 'stylesheet';
      fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
      document.head.appendChild(fontAwesome);
    }

    // Add sidebar styles
    const style = document.createElement('style');
    style.textContent = `
      .browzy-ai-brave-sidebar {
        position: fixed !important;
        top: 0 !important;
        right: 0 !important;
        width: 400px !important; /* Consistent width for all pages */
        height: 100% !important;
        max-height: 100% !important;
        background-color: #000000 !important;
        background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.9) 40%, rgba(0, 0, 0, 0.9) 80%, rgba(0, 0, 0, 0.9) 100%) !important;
        background-attachment: fixed !important;
        background-position: center !important;
        background-size: cover !important;
        background-repeat: no-repeat !important;
        color: #FFFFFF !important;
        z-index: 2147483647 !important;
        display: flex !important;
        flex-direction: column !important;
        box-shadow: -5px 0 25px rgba(0, 0, 0, 0.5) !important;
        transition: transform 0.3s ease-in-out !important;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
        overflow: hidden !important;
        transform: translateX(100%) !important;
        bottom: 0 !important;
        visibility: visible !important;
        opacity: 1 !important;
      }

      /* Consistent width for all pages */
      @media (min-width: 1200px) {
        .browzy-ai-brave-sidebar {
          width: 400px !important; /* Same width on all screens */
        }
      }

      .browzy-ai-brave-sidebar.visible {
        transform: translateX(0) !important;
        visibility: visible !important;
        opacity: 1 !important;
        display: flex !important;
      }

      .browzy-ai-brave-sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px;
        background: #000000;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        color: white;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        height: 60px;
        box-sizing: border-box;
        flex-shrink: 0;
      }

      .browzy-ai-brave-sidebar-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 600;
        font-size: 16px;
      }

      .happy-ai-brave-sidebar-title img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
      }

      .browzy-ai-brave-sidebar-close {
        background: transparent;
        border: none;
        color: white;
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
      }

      .browzy-ai-brave-sidebar-content {
        flex: 1;
        overflow: hidden;
        height: calc(100% - 60px); /* Subtract header height */
        display: flex;
        flex-direction: column;
      }

      .happy-ai-brave-sidebar-content iframe {
        width: 100%;
        height: 100%;
        min-height: 100%;
        border: none;
        flex: 1;
        display: block;
      }

      /* Toggle button removed */

      /* Toggle button icon removed */

      @keyframes slideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
      }

      .browzy-ai-brave-sidebar.visible {
        animation: slideIn 0.3s ease-out forwards;
      }
    `;
    document.head.appendChild(style);

    // Add sidebar to the page
    document.body.appendChild(this.sidebar);

    // Mark as initialized
    this.initialized = true;

    // Listen for messages from the iframe with origin validation
    window.addEventListener('message', (event) => {
      console.log('Received message from iframe:', event.data);

      // Validate the origin of the message
      const extensionOrigin = 'chrome-extension://' + chrome.runtime.id;
      const isFromExtension = event.origin === extensionOrigin;

      // Only process messages from our extension
      if (isFromExtension && event.data && event.data.source === 'browzy-ai-popup' && event.data.action === 'closeSidebar') {
        console.log('Received close sidebar message from trusted iframe');
        this.toggleSidebar(false);
      } else if (!isFromExtension && event.data && event.data.source === 'browzy-ai-popup') {
        console.warn('Received message from untrusted origin:', event.origin);
      }
    });

    // Also listen for messages from the extension popup directly
    window.addEventListener('closeSidebar', () => {
      console.log('Received closeSidebar event');
      this.toggleSidebar(false);
    });

    console.log('Brave custom sidebar initialized');
  }

  /**
   * Toggle the sidebar visibility
   * @param {boolean} show - Whether to show or hide the sidebar
   */
  toggleSidebar(show) {
    console.log('Toggling sidebar visibility:', { show, current: this.sidebarVisible });

    // Use the class property for blank page detection directly
    this.sidebarVisible = show !== undefined ? show : !this.sidebarVisible;

    if (this.sidebarVisible) {
      // Create and show a loading animation before showing the sidebar
      this.showLoadingAnimation();

      // Delay showing the sidebar to allow the animation to play
      setTimeout(() => {
        // Show the sidebar with multiple methods to ensure it's visible
        this.sidebar.classList.add('visible');
        this.sidebar.style.transform = 'translateX(0)';

        // Make sure the sidebar is visible with !important to override any conflicting styles
        this.sidebar.style.cssText += `
          display: flex !important;
          opacity: 1 !important;
          visibility: visible !important;
          z-index: 2147483647 !important;
          position: fixed !important;
          top: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          width: 400px !important;
          height: 100% !important;
          transform: translateX(0) !important;
        `;
      }, 3000); // Delay for 3 seconds to allow animation to fully play

      // Add a timestamp to prevent caching issues
      const timestamp = new Date().getTime();

      // Use a special parameter for blank pages to trigger special handling in the popup
      const blankPageParam = this.isBlankPage ? '&blankPage=true' : '';

      // Start loading the iframe immediately during the animation
      // Set the iframe source with all necessary parameters
      if (this.isBlankPage) {
        // For blank pages, use a more direct approach with a full URL and forced parameters
        const fullUrl = chrome.runtime.getURL(`popup/popup.html?sidebar=true&blankPage=true&t=${timestamp}&direct=true&newTab=true`);
        console.log('Using direct URL for blank page in toggleSidebar:', fullUrl);

        // Clear the iframe first to ensure a fresh load
        this.iframe.src = 'about:blank';

        // Start loading the iframe immediately
        this.iframe.src = fullUrl;

        // Add a data attribute to mark this as a blank page iframe
        this.iframe.setAttribute('data-blank-page', 'true');
        this.iframe.setAttribute('data-new-tab', 'true');

        // Force the iframe to be interactive
        this.iframe.style.pointerEvents = 'auto';

        // Add special styles to ensure the iframe is visible and interactive
        this.iframe.style.zIndex = '9999999';
        this.iframe.style.position = 'relative';
        this.iframe.style.backgroundColor = '#121212'; // Match the popup background

        // Add special attributes to ensure proper loading
        this.iframe.setAttribute('allowtransparency', 'true');
        this.iframe.setAttribute('allowfullscreen', 'true');
        this.iframe.setAttribute('importance', 'high');
        this.iframe.setAttribute('loading', 'eager');
        // We need to allow scripts and same-origin for functionality
        // This is necessary for the iframe to work properly on blank pages
        this.iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-modals');
        // Remove all client hints features from the allow attribute
        this.iframe.allow = '';

        console.log('Enhanced iframe for blank page');
      } else {
        // For regular pages, use a similar approach for consistency
        const popupUrl = chrome.runtime.getURL(`popup/popup.html?sidebar=true&t=${timestamp}${blankPageParam}`);
        console.log('Loading iframe with URL for regular page:', popupUrl);

        // Clear the iframe first to ensure a fresh load
        this.iframe.src = 'about:blank';

        // Start loading the iframe immediately
        this.iframe.src = popupUrl;

        // Apply consistent styling to ensure the iframe is visible and interactive
        this.iframe.style.cssText += `
          width: 100% !important;
          height: 100% !important;
          border: none !important;
          display: block !important;
          flex: 1 !important;
          pointer-events: auto !important;
          z-index: 9999 !important;
          position: relative !important;
        `;

        // Add special attributes to ensure proper loading
        this.iframe.setAttribute('allowtransparency', 'true');
        this.iframe.setAttribute('allowfullscreen', 'true');
        this.iframe.setAttribute('importance', 'high');
        this.iframe.setAttribute('loading', 'eager');
        // We need to allow scripts and same-origin for functionality
        // This is necessary for the iframe to work properly
        this.iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-modals');
        // Remove all client hints features from the allow attribute
        this.iframe.allow = '';

        console.log('Enhanced iframe for regular page');
      }

      // Force a refresh of the iframe with enhanced error handling
      setTimeout(() => {
        try {
          // Send initialization message with wildcard target origin to avoid cross-origin issues
          this.iframe.contentWindow.postMessage({
            source: 'brave-sidebar',
            action: 'sidebarInitialized',
            isBlankPage: this.isBlankPage
          }, '*');

          // For blank pages, add extra initialization after a delay
          if (this.isBlankPage) {
            // Send multiple initialization messages with increasing delays to ensure it works
            const sendInitMessage = (delay) => {
              setTimeout(() => {
                try {
                  console.log(`Sending initialization for blank page (delay: ${delay}ms)`);
                  const extensionOrigin = 'chrome-extension://' + chrome.runtime.id;
                  this.iframe.contentWindow.postMessage({
                    source: 'brave-sidebar',
                    action: 'forceInitializeBlankPage',
                    isNewTab: true,
                    timestamp: new Date().getTime()
                  }, extensionOrigin);

                  // Try to focus the iframe to ensure it receives keyboard events
                  this.iframe.focus();

                  // Try to focus the input field inside the iframe
                  try {
                    const iframeDoc = this.iframe.contentDocument || this.iframe.contentWindow.document;
                    const inputField = iframeDoc.getElementById('userInput');
                    if (inputField) {
                      inputField.focus();
                    }

                    // Also try to click on the chat tab to ensure it's active
                    const chatTab = iframeDoc.getElementById('chatTab');
                    if (chatTab) {
                      chatTab.click();
                    }

                    // Make sure the send button is properly initialized
                    const sendButton = iframeDoc.getElementById('sendMessage');
                    if (sendButton) {
                      // Add a direct click handler
                      sendButton.addEventListener('click', function() {
                        console.log('Send button clicked in iframe');
                        const userInput = iframeDoc.getElementById('userInput');
                        if (userInput && userInput.value.trim()) {
                          // Dispatch a custom event
                          const event = new CustomEvent('sendMessage', {
                            detail: { message: userInput.value.trim() }
                          });
                          iframeDoc.dispatchEvent(event);
                        }
                      });
                    }
                  } catch (focusError) {
                    console.error('Error interacting with iframe elements:', focusError);
                  }
                } catch (error) {
                  console.error(`Error sending initialization (delay: ${delay}ms):`, error);
                }
              }, delay);
            };

            // Send multiple initialization messages with increasing delays
            sendInitMessage(500);
            sendInitMessage(1000);
            sendInitMessage(2000);
            sendInitMessage(3000);
          }
        } catch (error) {
          console.error('Error communicating with iframe after reload:', error);

          // Try to reload the iframe if communication fails
          setTimeout(() => {
            try {
              console.log('Reloading iframe after communication failure in toggleSidebar');
              const newTimestamp = new Date().getTime();
              const blankPageParam = this.isBlankPage ? '&blankPage=true' : '';
              this.iframe.src = chrome.runtime.getURL(`popup/popup.html?sidebar=true&t=${newTimestamp}${blankPageParam}&retry=true`);
            } catch (reloadError) {
              console.error('Error reloading iframe in toggleSidebar:', reloadError);
            }
          }, 500);
        }
      }, 500);

      // Force the sidebar to take full height and proper positioning
      this.sidebar.style.height = '100%';
      this.sidebar.style.bottom = '0';
      this.sidebar.style.top = '0';
      this.sidebar.style.position = 'fixed';
      this.sidebar.style.right = '0';
      this.sidebar.style.zIndex = '2147483647';

      // Store a reference to the content div
      if (!this.content) {
        this.content = this.sidebar.querySelector('.browzy-ai-brave-sidebar-content');
      }

      // Force the content to take full height
      if (this.content) {
        this.content.style.height = 'calc(100% - 60px)';
        this.content.style.flex = '1';
        this.content.style.display = 'flex';
        this.content.style.flexDirection = 'column';
        this.content.style.overflow = 'hidden';
      }

      // Force the iframe to take full height
      this.iframe.style.height = '100%';
      this.iframe.style.flex = '1';
      this.iframe.style.border = 'none';
      this.iframe.style.display = 'block';
      this.iframe.style.width = '100%';

      // Prevent body scrolling when sidebar is open
      document.body.style.overflow = 'hidden';

      // Use consistent width for all pages
      this.sidebar.style.width = '400px';

      // Remove this section as we now have consistent iframe loading for all page types

      // Make sure the floating close button is visible
      const floatingCloseButton = this.sidebar.querySelector('.browzy-ai-brave-sidebar-floating-close');
      if (floatingCloseButton) {
        floatingCloseButton.style.display = 'flex';
        floatingCloseButton.style.position = 'fixed';
        floatingCloseButton.style.zIndex = '9999999';
      }
    } else {
      // Hide the sidebar
      console.log('Hiding sidebar');
      this.sidebar.classList.remove('visible');
      this.sidebar.style.transform = 'translateX(100%)';

      // Restore body scrolling when sidebar is closed
      document.body.style.overflow = '';

      // If the iframe is loaded, try to notify it to clean up
      try {
        if (this.iframe && this.iframe.contentWindow) {
          const extensionOrigin = 'chrome-extension://' + chrome.runtime.id;
          this.iframe.contentWindow.postMessage({
            source: 'brave-sidebar',
            action: 'sidebarClosed'
          }, extensionOrigin);
        }
      } catch (error) {
        console.error('Error notifying iframe about sidebar closure:', error);
      }

      // Reload the iframe after closing on all page types for consistency
      try {
        // First clear the iframe
        this.iframe.src = 'about:blank';

        // Then reload it after a delay
        setTimeout(() => {
          if (this.iframe) {
            const popupUrl = chrome.runtime.getURL('popup/popup.html?sidebar=true');
            this.iframe.src = popupUrl;
            console.log('Reloaded iframe after closing');
          }
        }, 300);
      } catch (error) {
        console.error('Error reloading iframe after closing:', error);
      }

      // Force hide the sidebar with multiple methods
      this.sidebar.style.transform = 'translateX(100%)';
      this.sidebar.classList.remove('visible');

      // Additional hiding methods for stubborn cases
      setTimeout(() => {
        this.sidebar.style.display = 'none';
        setTimeout(() => {
          this.sidebar.style.display = 'flex';
          this.sidebar.style.transform = 'translateX(100%)';
        }, 50);
      }, 300);
    }

    // Notify the extension about the sidebar state
    try {
      chrome.runtime.sendMessage({
        action: 'sidebarStateChanged',
        visible: this.sidebarVisible,
        isBlankPage: this.isBlankPage
      }).catch(() => {
        // Ignore errors when the extension popup is not open
        console.log('Could not notify extension about sidebar state (popup might be closed)');
      });
    } catch (error) {
      console.error('Error notifying extension about sidebar state:', error);
    }

    return this.sidebarVisible;
  }
}

// Check if we should initialize the Brave sidebar
// Skip initialization for protected URLs
if (isProtectedUrl) {
  console.log('Protected URL detected, skipping Brave sidebar initialization:', window.location.href);
} else {
  // Initialize the sidebar but don't show it yet
  console.log('Initializing Brave custom sidebar');
  try {
    window.braveSidebar = new BraveSidebar();
    console.log('Brave sidebar initialized successfully');
  } catch (error) {
    console.error('Error initializing Brave sidebar:', error);
  }
}

// The sidebar will be shown when the user clicks the extension button
// or when a message is received from the extension

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  // We'll try to initialize the sidebar for all pages, including blank pages and protected URLs
  console.log('Attempting to handle message for all page types:', window.location.href);

  if (request.action === 'showBraveSidebar') {
    // Initialize the sidebar if not already initialized
    if (!window.braveSidebar) {
      window.braveSidebar = new BraveSidebar();
    }

    // Show the sidebar
    window.braveSidebar.toggleSidebar(true);

    // Send response
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'hideBraveSidebar') {
    console.log('Received hideBraveSidebar action');
    try {
      // Hide the sidebar if it exists
      if (window.braveSidebar) {
        console.log('Brave sidebar exists, hiding it');
        try {
          window.braveSidebar.toggleSidebar(false);
          sendResponse({ success: true });
        } catch (toggleError) {
          console.error('Error toggling sidebar visibility:', toggleError);
          // Try direct DOM manipulation as a fallback
          try {
            const sidebar = document.querySelector('.browzy-ai-brave-sidebar');
            if (sidebar) {
              sidebar.style.transform = 'translateX(100%)';
              sidebar.classList.remove('visible');
              console.log('Sidebar hidden via direct DOM manipulation');
            }
            sendResponse({ success: true, fallback: true });
          } catch (domError) {
            console.error('Error with direct DOM manipulation:', domError);
            // Still return success to allow UI to proceed
            sendResponse({ success: true, warning: 'Could not hide sidebar, but UI can proceed' });
          }
        }
      } else {
        console.log('Brave sidebar does not exist, creating minimal instance');
        try {
          // Create a minimal sidebar instance just to handle the close action
          const sidebar = document.querySelector('.browzy-ai-brave-sidebar');
          if (sidebar) {
            // If the DOM element exists but the JS object doesn't, just hide the element
            sidebar.style.transform = 'translateX(100%)';
            sidebar.classList.remove('visible');
            console.log('Existing sidebar element hidden via direct DOM manipulation');
            sendResponse({ success: true });
          } else {
            // Try to initialize a new sidebar and immediately hide it
            window.braveSidebar = new BraveSidebar();
            window.braveSidebar.toggleSidebar(false);
            sendResponse({ success: true });
          }
        } catch (initError) {
          console.error('Error initializing sidebar:', initError);
          // Still return success to allow UI to proceed
          sendResponse({ success: true, warning: 'Could not initialize sidebar, but UI can proceed' });
        }
      }

      // Also dispatch a custom event to ensure all listeners are notified
      try {
        window.dispatchEvent(new CustomEvent('closeSidebar'));
      } catch (eventError) {
        console.error('Error dispatching closeSidebar event:', eventError);
      }
    } catch (error) {
      console.error('Error handling hideBraveSidebar action:', error);
      // Still return success to allow UI to proceed
      sendResponse({ success: true, error: error.message });
    }
    return true;
  }

  // Ping to check if the sidebar is available
  if (request.action === 'ping') {
    sendResponse({
      success: true,
      sidebarInitialized: !!window.braveSidebar,
      sidebarVisible: window.braveSidebar ? window.braveSidebar.sidebarVisible : false
    });
    return true;
  }
});
