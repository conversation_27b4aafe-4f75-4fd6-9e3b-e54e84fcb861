'use strict';

/**
 * Manages storage for the extension
 */
class StorageManager {
  constructor() {
    // Default settings
    this.defaultSettings = {
      maxTokens: 1000,
      temperature: 0.7,
      autoAnalyze: false,
      rememberHistory: true
    };

    // Default empty stats with enhanced structure
    this.defaultStats = {
      gemini: {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      openrouter: {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'direct-openai': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'direct-gemini': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'direct-huggingface': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'anthropic': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'mistral': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'cohere': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'meta': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'stability': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      },
      'huggingface': {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      }
    };
  }

  /**
   * Initialize storage with default values if needed
   */
  async init() {
    const data = await chrome.storage.local.get(['apiKeys', 'settings', 'stats', 'selectedProvider']);

    // Initialize API keys if not present
    if (!data.apiKeys) {
      await chrome.storage.local.set({ apiKeys: {} });
    }

    // Initialize settings if not present
    if (!data.settings) {
      await chrome.storage.local.set({ settings: this.defaultSettings });
    }

    // Initialize stats if not present
    if (!data.stats) {
      await chrome.storage.local.set({ stats: this.defaultStats });
    }

    // Initialize selected provider if not present
    if (!data.selectedProvider || data.selectedProvider === 'openai' || data.selectedProvider === 'anthropic' || data.selectedProvider === 'deepseek') {
      await chrome.storage.local.set({ selectedProvider: 'openai/gpt-3.5-turbo' });
    }
  }

  /**
   * Get stored API keys
   * @returns {Promise<object>} - The stored API keys
   */
  async getAPIKeys() {
    const { apiKeys = {} } = await chrome.storage.local.get('apiKeys');
    return apiKeys;
  }

  /**
   * Set API keys
   * @param {object} keys - The API keys to store
   */
  async setAPIKeys(keys) {
    await chrome.storage.local.set({ apiKeys: keys });
  }

  /**
   * Get stored settings
   * @returns {Promise<object>} - The stored settings
   */
  async getSettings() {
    const { settings = this.defaultSettings } = await chrome.storage.local.get('settings');
    return settings;
  }

  /**
   * Set settings
   * @param {object} newSettings - The settings to store
   */
  async setSettings(newSettings) {
    // Merge with existing settings
    const currentSettings = await this.getSettings();
    const mergedSettings = { ...currentSettings, ...newSettings };
    await chrome.storage.local.set({ settings: mergedSettings });
  }

  /**
   * Get usage statistics
   * @returns {Promise<object>} - The stored statistics
   */
  async getStats() {
    const { stats = this.defaultStats } = await chrome.storage.local.get('stats');
    return stats;
  }

  /**
   * Reset usage statistics
   */
  async resetStats() {
    await chrome.storage.local.set({ stats: this.defaultStats });
  }

  /**
   * Get the selected AI provider
   * @returns {Promise<string>} - The selected provider
   */
  async getSelectedProvider() {
    const result = await chrome.storage.local.get('selectedProvider');
    // Get the actual selected provider from storage, default to GPT-3.5 Turbo if not found
    const selectedProvider = result.selectedProvider || 'openai/gpt-3.5-turbo';

    console.log('Storage Manager - getSelectedProvider raw result:', result);
    console.log('Storage Manager - selectedProvider value:', selectedProvider);

    // If the selected provider is one of the removed ones, default to Gemini 1.5 Flash
    if (selectedProvider === 'openai' || selectedProvider === 'anthropic' || selectedProvider === 'deepseek') {
      console.log('Storage Manager - Detected removed provider, defaulting to Gemini 1.5 Flash');
      await this.setSelectedProvider('direct-gemini/gemini-1.5-flash');
      return 'direct-gemini/gemini-1.5-flash';
    }

    // Check if the provider is a Gemini model and log it
    if (selectedProvider.startsWith('direct-gemini/')) {
      console.log('Storage Manager - Detected Gemini model:', selectedProvider);
      // Extract the model name for additional validation
      const geminiModel = selectedProvider.replace('direct-gemini/', '');
      console.log('Storage Manager - Gemini model name:', geminiModel);
    }

    // Check if the provider is a Hugging Face model and log it
    if (selectedProvider.startsWith('direct-huggingface/')) {
      console.log('Storage Manager - Detected Hugging Face model:', selectedProvider);
      // Extract the model name for additional validation
      const huggingfaceModel = selectedProvider.replace('direct-huggingface/', '');
      console.log('Storage Manager - Hugging Face model name:', huggingfaceModel);
    }

    // Check if the provider is an OpenRouter model and log it
    if (selectedProvider.includes('/') && !selectedProvider.startsWith('direct-')) {
      console.log('Storage Manager - Detected OpenRouter model:', selectedProvider);

      // Extract the provider and model parts
      const [provider, model] = selectedProvider.split('/');
      console.log('Storage Manager - OpenRouter provider/model parts:', { provider, model });

      // Validate OpenRouter model format
      if (!provider || !model) {
        console.error('Storage Manager - Invalid OpenRouter model format:', selectedProvider);
        console.warn('Storage Manager - Model should be in format "provider/model"');
      }

      // Check for specific OpenRouter models
      if (provider === 'openai') {
        console.log('Storage Manager - Using OpenAI model through OpenRouter:', model);

        // Check if this is a model that should be using direct OpenAI
        if (model === 'gpt-4-turbo') {
          console.warn('Storage Manager - Model may work better with direct OpenAI API:', model);
        }

        // Check if this is the default fallback model
        if (model === 'gpt-3.5-turbo-16k') {
          console.warn('Storage Manager - Using gpt-3.5-turbo-16k which may be a fallback model');
        }
      } else if (provider === 'anthropic') {
        console.log('Storage Manager - Using Anthropic model through OpenRouter:', model);
      } else if (provider === 'google') {
        console.log('Storage Manager - Using Google model through OpenRouter:', model);
      } else if (provider === 'meta') {
        console.log('Storage Manager - Using Meta model through OpenRouter:', model);
      } else if (provider === 'mistral') {
        console.log('Storage Manager - Using Mistral model through OpenRouter:', model);
      }
    }

    // Check if the provider is an OpenAI model and log it
    if (selectedProvider.startsWith('direct-openai/')) {
      console.log('Storage Manager - Detected OpenAI model:', selectedProvider);
      // Extract the model name for additional validation
      const openaiModel = selectedProvider.replace('direct-openai/', '');
      console.log('Storage Manager - OpenAI model name:', openaiModel);

      // Validate OpenAI model name
      const validOpenAIModels = ['gpt-4-turbo', 'gpt-3.5-turbo'];
      if (!validOpenAIModels.includes(openaiModel)) {
        console.warn('Storage Manager - Potentially invalid OpenAI model:', openaiModel);
      }
    }

    // Add a check for invalid provider values
    if (!selectedProvider || typeof selectedProvider !== 'string') {
      console.error('Storage Manager - Invalid provider value:', selectedProvider);
      console.log('Storage Manager - Defaulting to GPT-3.5 Turbo');
      await this.setSelectedProvider('openai/gpt-3.5-turbo');
      return 'openai/gpt-3.5-turbo';
    }

    return selectedProvider;
  }

  /**
   * Set the selected AI provider
   * @param {string} provider - The provider to select
   */
  async setSelectedProvider(provider) {
    console.log('Storage Manager - Setting provider to:', provider);

    // Use the actual provider that was passed in
    await chrome.storage.local.set({ selectedProvider: provider });

    // Verify the provider was set correctly
    const result = await chrome.storage.local.get('selectedProvider');
    console.log('Storage Manager - Verification after setting provider:', result);
  }

  /**
   * Store a user session or conversation history
   * @param {string} id - Unique identifier for the session
   * @param {object} data - Session data to store
   */
  async saveSession(id, data) {
    const { sessions = {} } = await chrome.storage.local.get('sessions');
    sessions[id] = {
      ...data,
      timestamp: Date.now()
    };
    await chrome.storage.local.set({ sessions });
  }

  /**
   * Get a stored session
   * @param {string} id - Session identifier
   * @returns {Promise<object|null>} - The stored session or null if not found
   */
  async getSession(id) {
    const { sessions = {} } = await chrome.storage.local.get('sessions');
    return sessions[id] || null;
  }

  /**
   * Get all stored sessions
   * @returns {Promise<object>} - All stored sessions
   */
  async getAllSessions() {
    const { sessions = {} } = await chrome.storage.local.get('sessions');
    return sessions;
  }

  /**
   * Delete a session
   * @param {string} id - Session identifier
   */
  async deleteSession(id) {
    const { sessions = {} } = await chrome.storage.local.get('sessions');
    if (sessions[id]) {
      delete sessions[id];
      await chrome.storage.local.set({ sessions });
    }
  }
}
