'use strict';

/**
 * Manages extension features and interactions
 */
class FeatureManager {
  /**
   * Create a new Feature Manager
   * @param {APIManager} apiManager - The API Manager instance
   * @param {UIManager} uiManager - The UI Manager instance
   */
  constructor(apiManager, uiManager) {
    this.apiManager = apiManager;
    this.uiManager = uiManager;
    this.researchAssistant = null;
    this.websiteAnalyzer = null;
    this.pdfProcessor = null;
    this.productivityIntegrations = null;
    this.learningTools = null;

    // Bind methods to preserve 'this' context
    this.analyzePage = this.analyzePage.bind(this);
    this.summarizePage = this.summarizePage.bind(this);
    this.extractInfo = this.extractInfo.bind(this);
    this.askQuestion = this.askQuestion.bind(this);
    this.chessAnalysis = this.chessAnalysis.bind(this);
    this.chessHelper = this.chessHelper.bind(this);
    this.leetcodeHelper = this.leetcodeHelper.bind(this);
    this.generatePrompts = this.generatePrompts.bind(this);
    this.explainConcept = this.explainConcept.bind(this);
    this.shareAnalysis = this.shareAnalysis.bind(this);
    this.useSharedAnalysis = this.useSharedAnalysis.bind(this);
    this.translateText = this.translateText.bind(this);
    this.scanOtherTab = this.scanOtherTab.bind(this);
    this.handlePDFSelection = this.handlePDFSelection.bind(this);
    this.conductResearch = this.conductResearch.bind(this);
    this.analyzeWebsite = this.analyzeWebsite.bind(this);
    this.analyzeVideo = this.analyzeVideo.bind(this);
    this.summarizePDF = this.summarizePDF.bind(this);
    this.askPDFQuestion = this.askPDFQuestion.bind(this);
    this.extractPDFInfo = this.extractPDFInfo.bind(this);
    this.createPDFTableOfContents = this.createPDFTableOfContents.bind(this);
    this.comparePDFs = this.comparePDFs.bind(this);
    this.generatePDFStudyGuide = this.generatePDFStudyGuide.bind(this);
    this.connectProductivityTool = this.connectProductivityTool.bind(this);
    this.disconnectProductivityTool = this.disconnectProductivityTool.bind(this);
    this.createProductivityItem = this.createProductivityItem.bind(this);
    this.getProductivityItems = this.getProductivityItems.bind(this);
    this.applyProductivityTemplate = this.applyProductivityTemplate.bind(this);
    this.browseWebsite = this.browseWebsite.bind(this);
    this.searchYouTube = this.searchYouTube.bind(this);
    this.searchInstagram = this.searchInstagram.bind(this);
    this.searchStackOverflow = this.searchStackOverflow.bind(this);
    this.searchX = this.searchX.bind(this);
    this.searchPinterest = this.searchPinterest.bind(this);
    this.searchMovie = this.searchMovie.bind(this);
    this.searchMusic = this.searchMusic.bind(this);

    // Learning Tools methods
    this.createFlashcards = this.createFlashcards.bind(this);
    this.reviewFlashcards = this.reviewFlashcards.bind(this);
    this.importFlashcards = this.importFlashcards.bind(this);
    this.createQuiz = this.createQuiz.bind(this);
    this.takeQuiz = this.takeQuiz.bind(this);
    this.generateQuestions = this.generateQuestions.bind(this);
    this.createStudyGuide = this.createStudyGuide.bind(this);
    this.summarizeContent = this.summarizeContent.bind(this);
    this.createConceptMap = this.createConceptMap.bind(this);
    this.createSmartNotes = this.createSmartNotes.bind(this);
    this.organizeNotes = this.organizeNotes.bind(this);
    this.extractKeyPoints = this.extractKeyPoints.bind(this);

    // Make sure searchFallback is bound before detectPlatformAndSearch
    this.searchFallback = this.searchFallback.bind(this);

    // Bind the helper methods first
    this.searchMovie = this.searchMovie.bind(this);
    this.searchMusic = this.searchMusic.bind(this);

    // Bind all the platform search methods
    this.searchYouTube = this.searchYouTube.bind(this);
    this.searchInstagram = this.searchInstagram.bind(this);
    this.searchX = this.searchX.bind(this);
    this.searchPinterest = this.searchPinterest.bind(this);
    this.searchLinkedin = this.searchLinkedin.bind(this);
    this.searchGithub = this.searchGithub.bind(this);
    this.searchStackOverflow = this.searchStackOverflow.bind(this);
    this.searchFacebook = this.searchFacebook.bind(this);
    this.searchReddit = this.searchReddit.bind(this);
    this.searchTiktok = this.searchTiktok.bind(this);
    this.searchMedium = this.searchMedium.bind(this);
    this.searchAmazon = this.searchAmazon.bind(this);
    this.searchEbay = this.searchEbay.bind(this);
    this.searchEtsy = this.searchEtsy.bind(this);
    this.searchWalmart = this.searchWalmart.bind(this);
    this.searchFlipkart = this.searchFlipkart.bind(this);
    this.searchNetflix = this.searchNetflix.bind(this);
    this.searchPrimeVideo = this.searchPrimeVideo.bind(this);
    this.searchHulu = this.searchHulu.bind(this);
    this.searchDisneyPlus = this.searchDisneyPlus.bind(this);
    this.searchDribbble = this.searchDribbble.bind(this);
    this.searchBehance = this.searchBehance.bind(this);
    this.searchUnsplash = this.searchUnsplash.bind(this);
    this.searchWikipedia = this.searchWikipedia.bind(this);
    this.searchNews = this.searchNews.bind(this);
    this.searchGoogle = this.searchGoogle.bind(this);
    this.searchAnyPlatform = this.searchAnyPlatform.bind(this);

    this.provideDestressRecommendations = this.provideDestressRecommendations.bind(this);

    // Bind detectPlatformAndSearch last, after all other methods it depends on are bound
    this.detectPlatformAndSearch = this.detectPlatformAndSearch.bind(this); // Smart Search Beta
  }

  /**
   * Analyze the current page
   */
  async analyzePage() {
    try {
      this.uiManager.showLoading('Analyzing page...');

      const pageContent = await this.apiManager.getPageContent();

      // Prepare a prompt for page analysis
      const prompt = `
        I'd like you to analyze this webpage content and provide insights.
        URL: ${pageContent.url}
        Title: ${pageContent.title}

        Please provide:
        1. A brief summary of what this page is about
        2. The main topics or themes
        3. Key points or takeaways
        4. Any notable entities (people, organizations, etc.) mentioned

        Here's the content:
        ${this.truncateContent(pageContent.content)}
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are an analytical assistant that helps users understand webpage content. Be concise but thorough."
      });

      this.uiManager.showResult(response.text, 'Page Analysis');

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Summarize the current page
   */
  async summarizePage() {
    try {
      this.uiManager.showLoading('Summarizing page...');

      const pageContent = await this.apiManager.getPageContent();

      // Prepare a prompt for summarization
      const prompt = `
        Please provide a concise summary of this webpage content.
        URL: ${pageContent.url}
        Title: ${pageContent.title}

        Content:
        ${this.truncateContent(pageContent.content)}
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a summarization assistant that creates clear, concise summaries of webpage content."
      });

      this.uiManager.showResult(response.text, 'Page Summary');

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Extract specific information from the page
   */
  async extractInfo() {
    try {
      // Ask what type of info to extract
      const infoType = await this.uiManager.prompt(
        'What type of information would you like to extract? (dates, names, organizations, code, or all)',
        'all'
      );

      if (!infoType) return;

      this.uiManager.showLoading(`Extracting ${infoType}...`);

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // Request the content script to extract the specific info
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'extractSpecificInfo',
        infoType: infoType.toLowerCase()
      });

      if (!response.success) {
        throw new Error('Failed to extract information');
      }

      // Format the extracted info for display
      let resultContent = '';

      if (response.data.dates && response.data.dates.length > 0) {
        resultContent += `**Dates Found:**\n${response.data.dates.join('\n')}\n\n`;
      }

      if (response.data.names && response.data.names.length > 0) {
        resultContent += `**Names Found:**\n${response.data.names.join('\n')}\n\n`;
      }

      if (response.data.organizations && response.data.organizations.length > 0) {
        resultContent += `**Organizations Found:**\n${response.data.organizations.join('\n')}\n\n`;
      }

      if (response.data.codeBlocks && response.data.codeBlocks.length > 0) {
        resultContent += `**Code Blocks Found:**\n`;
        response.data.codeBlocks.forEach((code, index) => {
          resultContent += `\nCode Block ${index + 1}:\n\`\`\`\n${code}\n\`\`\`\n`;
        });
      }

      if (resultContent === '') {
        resultContent = `No ${infoType} information found on this page.`;
      }

      this.uiManager.showResult(resultContent, `Extracted ${infoType.charAt(0).toUpperCase() + infoType.slice(1)}`);

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Ask a question about the page content
   * @param {string} question - The question to ask
   */
  async askQuestion(question) {
    try {
      this.uiManager.showLoading('Processing question...');

      const pageContent = await this.apiManager.getPageContent();

      // Check if there's selected text to focus on
      const selectedText = await this.apiManager.getSelectedText();

      let contentToUse = selectedText && selectedText.length > 0
        ? selectedText
        : this.truncateContent(pageContent.content);

      // Prepare a prompt for the question
      const prompt = `
        Please answer this question about the webpage content:

        Question: ${question}

        URL: ${pageContent.url}
        Title: ${pageContent.title}

        Content to analyze:
        ${contentToUse}
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a helpful assistant that accurately answers questions about webpage content. If the answer isn't in the content, say so clearly."
      });

      this.uiManager.showResult(response.text, `Q: ${question}`);

      // Highlight relevant parts of the text if possible
      if (response.text.length > 0) {
        // Extract potential key phrases from the response to highlight
        const keyPhrasesMatch = response.text.match(/"([^"]+)"|'([^']+)'/g);
        if (keyPhrasesMatch && keyPhrasesMatch.length > 0) {
          // Get the first quoted phrase and clean it
          let phraseToHighlight = keyPhrasesMatch[0].replace(/['"]/g, '').trim();

          if (phraseToHighlight.length > 5) {
            try {
              await this.apiManager.highlightText(phraseToHighlight);
            } catch (error) {
              console.warn('Failed to highlight text:', error);
            }
          }
        }
      }

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Analyze a chess position on the current page
   */
  async chessAnalysis() {
    try {
      this.uiManager.showLoading('Analyzing chess position...');

      const pageContent = await this.apiManager.getPageContent();

      // Check if page has chess content
      if (!pageContent.chess || (!pageContent.chess.fen && !pageContent.chess.pgn)) {
        throw new Error('No chess position found on this page.');
      }

      // Prepare a prompt for chess analysis
      let prompt = `
        Please analyze this chess position and provide insights:

        ${pageContent.chess.fen ? `FEN: ${pageContent.chess.fen}` : ''}
        ${pageContent.chess.pgn ? `PGN: ${pageContent.chess.pgn}` : ''}

        Please provide:
        1. An evaluation of the position
        2. Key tactical and strategic ideas
        3. Best moves for both sides
        4. Any notable patterns or themes
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a chess analysis assistant with expertise in analyzing chess positions and games. Provide clear, accurate analysis of positions and suggest best moves."
      });

      this.uiManager.showResult(response.text, 'Chess Analysis');

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Provide a chess move suggestion for the current position
   */
  async chessHelper() {
    try {
      this.uiManager.showLoading('Finding best move...');

      const pageContent = await this.apiManager.getPageContent();

      // Check if page has chess content
      if (!pageContent.chess || (!pageContent.chess.fen && !pageContent.chess.pgn)) {
        throw new Error('No chess position found on this page.');
      }

      // Prepare a prompt specifically for suggesting the best move
      let prompt = `
        You are looking at a chess position. Please suggest the best move for the side to play.

        ${pageContent.chess.fen ? `FEN: ${pageContent.chess.fen}` : ''}
        ${pageContent.chess.pgn ? `PGN: ${pageContent.chess.pgn}` : ''}

        IMPORTANT INSTRUCTIONS:
        1. Analyze the position briefly
        2. Determine which side is to move
        3. Suggest the single best move for the side to move
        4. Format your answer with "Suggested move: [move in algebraic notation]" (e.g., "Suggested move: e4" or "Suggested move: Nf3")
        5. Provide a very brief explanation of why this is the best move
        6. Do not ask for more information or screenshots - work with the information provided
        7. Always suggest a concrete move, even if the position is unclear
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a chess move suggestion assistant. Your primary purpose is to suggest the best move in a given position. Always provide a concrete move suggestion in standard algebraic notation."
      });

      this.uiManager.showResult(response.text, 'Best Move');

      // Extract and highlight the suggested move
      const moveMatch = response.text.match(/Suggested move:\s*([a-h][1-8][a-h][1-8]|[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?)/i);
      if (moveMatch && moveMatch[1]) {
        const suggestedMove = moveMatch[1];

        // Send a message to visualize the move on the chessboard
        chrome.tabs.query({active: true, currentWindow: true}, tabs => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: 'visualizeChessMove',
              move: suggestedMove
            });
          }
        });
      }

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Help with Leetcode or programming problems
   */
  async leetcodeHelper() {
    try {
      this.uiManager.showLoading('Analyzing coding problem...');

      const pageContent = await this.apiManager.getPageContent();

      // Check if we have the enhanced code data structure
      if (!pageContent.platform) {
        // If we don't have the enhanced data, check for basic code blocks
        if (!pageContent.codeBlocks || pageContent.codeBlocks.length === 0) {
          throw new Error('No code found on this page. This feature works best on LeetCode, HackerRank, or similar coding problem pages.');
        }
      }

      // Determine the platform and customize the approach
      let platformName = pageContent.platform || 'unknown';
      let problemTitle = pageContent.problemTitle || pageContent.title || 'Coding Problem';
      let problemDescription = pageContent.problemDescription || this.truncateContent(pageContent.content, 1500);
      let codeTemplate = pageContent.codeTemplate || (pageContent.codeBlocks && pageContent.codeBlocks.length > 0 ? pageContent.codeBlocks[0] : '');
      let examples = [];

      // Format examples if available
      if (pageContent.examples && pageContent.examples.length > 0) {
        examples = pageContent.examples.map(ex => `Example ${ex.index}:\n${ex.text}`).join('\n\n');
      }

      // Format constraints if available
      let constraints = pageContent.constraints || '';

      // Build a more detailed prompt based on the available information
      const prompt = `
        Help me solve this ${platformName.toUpperCase()} coding problem.

        PROBLEM TITLE: ${problemTitle}

        PROBLEM DESCRIPTION:
        ${problemDescription}

        ${examples ? `EXAMPLES:\n${examples}\n\n` : ''}
        ${constraints ? `CONSTRAINTS:\n${constraints}\n\n` : ''}
        ${codeTemplate ? `CODE TEMPLATE:\n\`\`\`\n${codeTemplate}\n\`\`\`\n\n` : ''}

        Please provide a comprehensive solution with:

        1. PROBLEM ANALYSIS:
           - Explain the problem in your own words
           - Identify the key challenges and edge cases
           - Clarify any assumptions

        2. SOLUTION APPROACH:
           - Describe your approach step-by-step
           - Explain the algorithm and data structures you'll use
           - Discuss alternative approaches if relevant

        3. IMPLEMENTATION:
           - Provide a complete, working solution in the same language as the template
           - Include detailed comments explaining each part
           - Make sure the code handles all edge cases

        4. COMPLEXITY ANALYSIS:
           - Time complexity with explanation
           - Space complexity with explanation
           - Any optimization opportunities

        5. TESTING:
           - Walk through the solution with the provided examples
           - Suggest additional test cases if needed
      `;

      // Customize the system prompt based on the platform
      let systemPrompt = "You are a programming expert specialized in solving coding problems. ";

      switch (platformName.toLowerCase()) {
        case 'leetcode':
          systemPrompt += "You excel at LeetCode problems and competitive programming challenges. ";
          break;
        case 'hackerrank':
          systemPrompt += "You excel at HackerRank problems and algorithm challenges. ";
          break;
        case 'codewars':
          systemPrompt += "You excel at Codewars katas and programming puzzles. ";
          break;
        case 'codeforces':
          systemPrompt += "You excel at Codeforces competitive programming problems. ";
          break;
        default:
          systemPrompt += "You excel at solving programming problems from various platforms. ";
      }

      systemPrompt += "Provide clear explanations, efficient solutions, and thorough analysis. ";
      systemPrompt += "Your code should be correct, optimized, and well-commented.";

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: systemPrompt
      });

      // Create a more descriptive title for the result
      const resultTitle = `Solution: ${problemTitle.length > 30 ? problemTitle.substring(0, 30) + '...' : problemTitle}`;

      this.uiManager.showResult(response.text, resultTitle);

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Generate prompts based on page content
   */
  async generatePrompts() {
    try {
      this.uiManager.showLoading('Generating prompts...');

      const pageContent = await this.apiManager.getPageContent();

      // Prepare a prompt for generating prompts
      const prompt = `
        Based on this webpage content, please generate 5 creative and useful prompts
        that could be used with AI systems for different purposes related to this content.

        URL: ${pageContent.url}
        Title: ${pageContent.title}

        Content summary:
        ${this.truncateContent(pageContent.content, 1000)}

        Generate prompts for these scenarios:
        1. Detailed analysis/research
        2. Creative writing based on the content
        3. Learning/educational use
        4. Professional/business application
        5. Problem-solving related to the topic
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a prompt engineering expert who creates effective AI prompts tailored to specific content and needs."
      });

      this.uiManager.showResult(response.text, 'Generated Prompts');

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Explain complex concepts from the page
   */
  async explainConcept() {
    try {
      // Get selected text or ask for concept
      let concept = await this.apiManager.getSelectedText();

      if (!concept || concept.length < 3) {
        concept = await this.uiManager.prompt('What concept would you like explained?');
        if (!concept) return;
      }

      this.uiManager.showLoading(`Explaining: ${concept}`);

      const pageContent = await this.apiManager.getPageContent();

      // Prepare a prompt for concept explanation
      const prompt = `
        Please explain this concept in detail: "${concept}"

        If relevant, use the following webpage content for context:
        URL: ${pageContent.url}
        Title: ${pageContent.title}

        Content:
        ${this.truncateContent(pageContent.content, 1500)}

        Provide:
        1. A clear, concise explanation of the concept
        2. Any relevant technical details
        3. Real-world examples or applications
        4. Related concepts or ideas
        5. Common misconceptions, if any
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are an educational assistant specializing in explaining complex concepts clearly. Adjust your explanation based on the complexity of the concept."
      });

      this.uiManager.showResult(response.text, `Explaining: ${concept}`);

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Share an analysis result with other tabs
   */
  async shareAnalysis() {
    try {
      this.uiManager.showLoading('Preparing to share analysis...');

      // First we need to determine what we're sharing
      const pageContent = await this.apiManager.getPageContent();

      // Ask user what type of analysis to share
      const analysisType = await this.uiManager.prompt(
        'What type of analysis would you like to share with other tabs?',
        'Page Summary'
      );

      if (!analysisType) return;

      // Generate the analysis based on the selected type
      let analysisPrompt = '';

      switch (analysisType.toLowerCase()) {
        case 'summary':
        case 'page summary':
          analysisPrompt = `
            Please provide a concise summary of this webpage content.
            URL: ${pageContent.url}
            Title: ${pageContent.title}

            Content:
            ${this.truncateContent(pageContent.content)}
          `;
          break;

        case 'key points':
        case 'main points':
          analysisPrompt = `
            Please extract the key points and main ideas from this webpage content.
            URL: ${pageContent.url}
            Title: ${pageContent.title}

            Content:
            ${this.truncateContent(pageContent.content)}
          `;
          break;

        case 'code analysis':
        case 'code explanation':
          if (!pageContent.codeBlocks || pageContent.codeBlocks.length === 0) {
            throw new Error('No code found on this page to analyze and share.');
          }

          analysisPrompt = `
            Please analyze and explain this code:
            URL: ${pageContent.url}
            Title: ${pageContent.title}

            Code:
            \`\`\`
            ${pageContent.codeBlocks[0]}
            \`\`\`

            Provide a clear explanation of what this code does, any notable patterns, and potential improvements.
          `;
          break;

        case 'chess analysis':
          if (!pageContent.chess || (!pageContent.chess.fen && !pageContent.chess.pgn)) {
            throw new Error('No chess position found on this page to analyze and share.');
          }

          analysisPrompt = `
            Please analyze this chess position and provide insights:

            ${pageContent.chess.fen ? `FEN: ${pageContent.chess.fen}` : ''}
            ${pageContent.chess.pgn ? `PGN: ${pageContent.chess.pgn}` : ''}

            Please provide:
            1. An evaluation of the position
            2. Key tactical and strategic ideas
            3. Best moves for both sides
          `;
          break;

        default:
          // Custom analysis
          analysisPrompt = `
            Please analyze this webpage content for the following: ${analysisType}
            URL: ${pageContent.url}
            Title: ${pageContent.title}

            Content:
            ${this.truncateContent(pageContent.content)}
          `;
      }

      // Get the analysis from the AI
      const response = await this.apiManager.sendRequest(analysisPrompt, {
        systemPrompt: "You are an analytical assistant that helps users understand content. Be thorough but concise."
      });

      // Save the analysis to the shared cache
      await chrome.runtime.sendMessage({
        action: 'saveSharedAnalysis',
        analysis: {
          type: analysisType,
          content: response.text,
          url: pageContent.url,
          title: pageContent.title,
          timestamp: new Date().toISOString()
        }
      });

      this.uiManager.showResult(
        `**Analysis Shared Successfully**\n\nType: ${analysisType}\n\n` +
        `This analysis can now be accessed from other tabs by using the "Use Shared Analysis" feature.\n\n` +
        `**Analysis Content:**\n\n${response.text}`,
        'Analysis Shared'
      );

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Use a previously shared analysis in the current tab
   */
  async useSharedAnalysis() {
    try {
      this.uiManager.showLoading('Retrieving shared analysis...');

      // Get the shared analysis from the background script
      const response = await chrome.runtime.sendMessage({
        action: 'getSharedAnalysis'
      });

      if (!response.success) {
        throw new Error('Failed to retrieve shared analysis');
      }

      if (!response.hasShared || !response.analysis.lastAnalysis) {
        throw new Error('No analysis has been shared yet. Use the "Share Analysis" feature on another tab first.');
      }

      const sharedAnalysis = response.analysis.lastAnalysis;
      const timeAgo = this.getTimeAgo(new Date(response.analysis.timestamp));

      // Display the shared analysis
      this.uiManager.showResult(
        `**Shared Analysis from Another Tab**\n\n` +
        `**Type:** ${sharedAnalysis.type}\n` +
        `**Source:** [${sharedAnalysis.title}](${sharedAnalysis.url})\n` +
        `**Shared:** ${timeAgo}\n\n` +
        `**Analysis Content:**\n\n${sharedAnalysis.content}`,
        'Shared Analysis'
      );

    } catch (error) {
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }



  /**
   * Translate text to a different language
   * @param {string} targetLanguage - The language to translate to
   * @param {string} textToTranslate - Optional specific text to translate (if not provided, will use selected text or page content)
   */
  async translateText(targetLanguage, textToTranslate) {
    try {
      // If no target language is provided, prompt the user
      if (!targetLanguage) {
        const languages = [
          'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian', 'Japanese',
          'Chinese', 'Korean', 'Arabic', 'Hindi', 'Dutch', 'Swedish', 'Greek', 'Turkish',
          'Polish', 'Vietnamese', 'Thai', 'Indonesian', 'Hebrew', 'Danish', 'Finnish',
          'Norwegian', 'Czech', 'Hungarian', 'Romanian', 'Ukrainian', 'Bulgarian', 'Croatian'
        ];

        // Sort languages alphabetically
        languages.sort();

        // Add English at the top for translating back
        languages.unshift('English');

        targetLanguage = await this.uiManager.selectFromOptions(
          'Select target language for translation:',
          languages,
          'English'
        );

        if (!targetLanguage) return; // User cancelled
      }

      this.uiManager.showLoading(`Translating to ${targetLanguage}...`);

      // If specific text is provided, use it; otherwise get selected text or page content
      let contentToTranslate = textToTranslate;

      if (!contentToTranslate) {
        // Try to get selected text first
        const selectedText = await this.apiManager.getSelectedText();

        if (selectedText && selectedText.trim().length > 0) {
          contentToTranslate = selectedText;
        } else {
          // If no text is selected, ask if user wants to translate the whole page
          const confirmFullPage = await this.uiManager.confirm(
            'No text is selected. Would you like to translate the main content of the page?'
          );

          if (!confirmFullPage) return; // User cancelled

          // Get page content for translation
          const pageContent = await this.apiManager.getPageContent();
          contentToTranslate = this.truncateContent(pageContent.content, 2500);
        }
      }

      // Prepare the translation prompt
      const prompt = `
        Please translate the following text to ${targetLanguage}:

        ${contentToTranslate}

        Important instructions:
        1. Maintain the original formatting, including paragraphs, bullet points, and line breaks
        2. Do NOT include any HTML tags or div elements in your response
        3. Keep names, brands, and technical terms as they are when appropriate
        4. Ensure the translation sounds natural in ${targetLanguage}
        5. If there are any ambiguous terms or phrases, provide the most likely translation
        6. Return ONLY the translated text without any additional formatting or explanation
      `;

      // Use a specialized system prompt for translation
      const systemPrompt = `You are a professional translator with expertise in multiple languages.
      Your task is to provide accurate, natural-sounding translations while preserving the original meaning,
      tone, and formatting. For technical content, ensure terminology is correctly translated using
      standard terms in the target language. For creative content, focus on conveying the style and emotion
      rather than literal translation when appropriate.

      IMPORTANT: Return ONLY the translated text without any HTML tags, div elements, or additional formatting.
      Do not include any explanations or notes about the translation process.`;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: systemPrompt
      });

      // Display the translation directly as a regular message
      this.uiManager.addTranslationToChat(
        response.text,
        targetLanguage
      );

    } catch (error) {
      // Add error message directly to chat
      this.uiManager.addMessageToChat(`Translation Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Scan content from another tab and send it to the chat
   */
  async scanOtherTab() {
    try {
      // Check if we already have scanned content - if so, clear it and return to current tab
      if (this.apiManager.hasScannedTabContent()) {
        this.apiManager.clearScannedTabContent();
        this.uiManager.addMessageToChat(
          "I've cleared the scanned tab content. I'm now back to analyzing the current tab.",
          'ai'
        );
        this.uiManager.showStatus('Returned to current tab content', false, 3000);
        return;
      }

      // Check if we already have a selected tab
      if (this.apiManager.hasSelectedTab()) {
        // We have a previously selected tab, so scan it directly
        await this.scanSelectedTab();
        return;
      }

      // No tab selected yet, so show the tab selection UI
      this.uiManager.showStatusLoading('Fetching tabs...');

      // Get all tabs
      const tabs = await chrome.tabs.query({});

      // Filter out the current tab and extension pages
      const currentTab = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentTabId = currentTab[0].id;

      const validTabs = tabs.filter(tab => {
        return tab.id !== currentTabId &&
               !tab.url.startsWith('chrome://') &&
               !tab.url.startsWith('chrome-extension://') &&
               !tab.url.startsWith('edge://') &&
               !tab.url.startsWith('about:');
      });

      if (validTabs.length === 0) {
        throw new Error('No other tabs available to scan. Please open some web pages in other tabs.');
      }

      // Create options for the dropdown with more descriptive labels
      const tabOptions = validTabs.map(tab => {
        // Create a shortened URL for display
        const urlObj = new URL(tab.url);
        const shortUrl = urlObj.hostname + urlObj.pathname.substring(0, 20) + (urlObj.pathname.length > 20 ? '...' : '');

        // Create a label that includes both title and URL for better identification
        const label = tab.title ?
          `${tab.title.substring(0, 40)}${tab.title.length > 40 ? '...' : ''} (${shortUrl})` :
          shortUrl;

        return {
          value: tab.id.toString(),
          label: label,
          title: tab.title || 'Untitled Tab',
          url: tab.url,
          favicon: tab.favIconUrl || null
        };
      });

      // Sort tabs by domain to group similar tabs together
      tabOptions.sort((a, b) => {
        const domainA = new URL(a.url).hostname;
        const domainB = new URL(b.url).hostname;
        return domainA.localeCompare(domainB);
      });

      // Ask user which tab to scan with improved UI
      const selectedTabOption = await this.uiManager.selectFromOptions(
        'Select a tab to scan:',
        tabOptions.map(opt => opt.label),
        null,
        true, // Show URLs
        tabOptions.map(opt => opt.favicon) // Pass favicon URLs
      );

      if (!selectedTabOption) return; // User cancelled

      // Find the selected tab object
      const selectedTabIndex = tabOptions.findIndex(opt => opt.label === selectedTabOption);
      if (selectedTabIndex === -1) return;

      const selectedTabId = parseInt(tabOptions[selectedTabIndex].value);
      const selectedTabInfo = tabOptions[selectedTabIndex];

      // Store the selected tab in the API manager
      this.apiManager.setSelectedTab(selectedTabId, selectedTabInfo);

      // Now scan the selected tab
      await this.scanSelectedTab();
    } catch (error) {
      console.error('Error in scanOtherTab:', error);
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Handle selected text from a PDF document
   * @param {string} [question] - Optional question to ask about the PDF text
   * @param {boolean} [silent=false] - If true, don't show loading indicators or error messages
   */
  async handlePDFSelection(question = null, silent = false) {
    try {
      if (!silent) {
        this.uiManager.showStatusLoading('Checking for PDF selection...');
      }

      // First try to get PDF selection from the background script
      const response = await chrome.runtime.sendMessage({
        action: 'getPDFSelection'
      });

      let pdfData = null;

      // If we have stored PDF data, use it
      if (response && response.success && response.hasSelection) {
        pdfData = response.data;
      }

      // If no stored PDF data, try to get it directly from the active tab
      if (!pdfData || !pdfData.text || pdfData.text.trim().length === 0) {
        if (!silent) {
          this.uiManager.showStatusLoading('No stored PDF selection found. Checking active tab...');
        }

        try {
          // Get the active tab
          const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
          if (tabs && tabs.length > 0) {
            const activeTab = tabs[0];

            // Try to get PDF text from the active tab
            const directResponse = await chrome.tabs.sendMessage(activeTab.id, {
              action: 'getSelectedTextFromPDF'
            }).catch(e => {
              console.log('Could not get PDF text directly:', e);
              return null;
            });

            if (directResponse && directResponse.success &&
                (directResponse.text || directResponse.lastSelected)) {
              // Use the current selection or fall back to last selection
              pdfData = {
                text: directResponse.text || directResponse.lastSelected,
                metadata: directResponse.metadata || {}
              };

              // Store this in the background for future use
              await chrome.runtime.sendMessage({
                action: 'sendPDFTextToChat',
                text: pdfData.text,
                metadata: pdfData.metadata
              });
            }
          }
        } catch (tabError) {
          console.error('Error getting PDF text from active tab:', tabError);
        }
      }

      // If we still don't have PDF data, try to inject the content script
      if (!pdfData || !pdfData.text || pdfData.text.trim().length === 0) {
        if (!silent) {
          this.uiManager.showStatusLoading('Trying to inject PDF content script...');
        }

        try {
          const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
          if (tabs && tabs.length > 0) {
            const activeTab = tabs[0];

            // Try to inject the PDF content script
            await chrome.scripting.executeScript({
              target: { tabId: activeTab.id },
              files: ['content/pdf-content.js']
            });

            // Wait a moment for the script to initialize
            await new Promise(resolve => setTimeout(resolve, 500));

            // Try again to get the PDF text
            const directResponse = await chrome.tabs.sendMessage(activeTab.id, {
              action: 'getSelectedTextFromPDF'
            }).catch(e => {
              console.log('Could not get PDF text after injection:', e);
              return null;
            });

            if (directResponse && directResponse.success &&
                (directResponse.text || directResponse.lastSelected)) {
              pdfData = {
                text: directResponse.text || directResponse.lastSelected,
                metadata: directResponse.metadata || {}
              };

              // Store this in the background for future use
              await chrome.runtime.sendMessage({
                action: 'sendPDFTextToChat',
                text: pdfData.text,
                metadata: pdfData.metadata
              });
            }
          }
        } catch (injectionError) {
          console.error('Error injecting PDF content script:', injectionError);
        }
      }

      // If we still don't have PDF data, try one more aggressive approach
      if (!pdfData || !pdfData.text || pdfData.text.trim().length === 0) {
        try {
          // Get the active tab
          const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
          if (tabs && tabs.length > 0) {
            const activeTab = tabs[0];

            // Execute a script to get all visible text as a last resort
            const result = await chrome.scripting.executeScript({
              target: { tabId: activeTab.id },
              func: () => {
                try {
                  // Get all text nodes that are visible
                  const textNodes = [];
                  const walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    { acceptNode: node => node.nodeValue.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT },
                    false
                  );

                  let node;
                  while (node = walker.nextNode()) {
                    textNodes.push(node.nodeValue.trim());
                  }

                  if (textNodes.length > 0) {
                    return textNodes.join(' ').trim();
                  }
                  return '';
                } catch (error) {
                  console.error('Error getting all text:', error);
                  return '';
                }
              }
            });

            if (result && result[0] && result[0].result) {
              const allText = result[0].result;
              if (allText && allText.length > 100) { // Only use if we got a substantial amount of text
                pdfData = {
                  text: allText,
                  metadata: {
                    title: tabs[0].title || 'PDF Document',
                    url: tabs[0].url || 'pdf://document'
                  }
                };

                // Store this in the background for future use
                await chrome.runtime.sendMessage({
                  action: 'sendPDFTextToChat',
                  text: pdfData.text,
                  metadata: pdfData.metadata
                });
              }
            }
          }
        } catch (error) {
          console.error('Error in aggressive PDF text extraction:', error);
        }
      }

      // If we still don't have PDF data after all attempts, prompt the user (unless silent mode)
      if (!pdfData || !pdfData.text || pdfData.text.trim().length === 0) {
        if (!silent) {
          // Ask the user to try the Extract All option
          this.uiManager.addMessageToChat(
            "I couldn't automatically detect PDF text selection. You have two options:\n\n1. Use the 'Extract All PDF Text' option from the actions menu (recommended)\n\n2. Manually copy text from your PDF and paste it in the chat, prefixed with 'PDF: ' (e.g., 'PDF: This is my selected text').",
            'ai'
          );
          this.uiManager.showStatus('Try using Extract All PDF Text option', true, 5000);
        }
        return;
      }

      // Show the selected text in the chat
      this.uiManager.addMessageToChat(
        `I've received text from a PDF document:\n\n` +
        `**Source:** ${pdfData.metadata?.title || 'PDF Document'}\n` +
        `**Selected text (${pdfData.text.length} characters):**\n\n` +
        `"${pdfData.text.substring(0, 150)}${pdfData.text.length > 150 ? '...' : ''}"\n\n` +
        `You can now ask me questions about this PDF content.`,
        'ai'
      );

      // If a question was provided, answer it immediately
      if (question) {
        await this.askAboutPDFSelection(question, pdfData.text, pdfData.metadata);
      }

      // Store the PDF content in the API manager for future reference
      this.apiManager.setScannedTabContent({
        content: pdfData.text,
        title: pdfData.metadata?.title || 'PDF Document',
        url: pdfData.metadata?.url || 'pdf://document',
        timestamp: new Date().toISOString(),
        isPDF: true
      });

      if (!silent) {
        this.uiManager.showStatus('PDF text loaded successfully', false, 3000);
      }
    } catch (error) {
      console.error('Error handling PDF selection:', error);
      if (!silent) {
        this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
        this.uiManager.showStatus(error.message, true, 5000);
      }
    }
  }

  /**
   * Extract all text from the current PDF document
   * This is a more aggressive approach that doesn't rely on user selection
   */
  async extractAllPdfText() {
    try {
      this.uiManager.showStatusLoading('Extracting all text from PDF document...');

      // Get the active tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tabs || tabs.length === 0) {
        throw new Error('No active tab found');
      }

      const activeTab = tabs[0];

      // Check if this is a PDF
      const isPdf = activeTab.url && (
        activeTab.url.toLowerCase().endsWith('.pdf') ||
        activeTab.url.toLowerCase().includes('pdf') ||
        activeTab.url.toLowerCase().includes('viewer.html')
      );

      if (!isPdf) {
        throw new Error('This feature only works with PDF documents. Please open a PDF file in your browser and try again.');
      }

      // Try to inject the PDF content script if not already loaded
      try {
        await chrome.scripting.executeScript({
          target: { tabId: activeTab.id },
          files: ['content/pdf-content.js']
        });

        // Wait a moment for the script to initialize
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (injectionError) {
        console.log('PDF content script might already be loaded:', injectionError);
      }

      // Execute a script to extract all text from the PDF
      const result = await chrome.scripting.executeScript({
        target: { tabId: activeTab.id },
        func: () => {
          try {
            // Try multiple methods to get all text

            // Method 1: Try PDF.js specific extraction
            if (window.PDFViewerApplication) {
              try {
                const pdfViewer = window.PDFViewerApplication.pdfViewer;
                const pageCount = pdfViewer.pagesCount || 0;
                let allText = [];

                // Extract text from each page
                for (let i = 1; i <= pageCount; i++) {
                  try {
                    const pageIndex = i - 1;
                    const page = pdfViewer.getPageView(pageIndex);
                    if (page && page.textLayer && page.textLayer.textContent) {
                      allText.push(page.textLayer.textContent);
                    }
                  } catch (pageError) {
                    console.log(`Error extracting text from page ${i}:`, pageError);
                  }
                }

                if (allText.length > 0) {
                  return allText.join('\n\n');
                }
              } catch (pdfJsError) {
                console.log('Could not extract text using PDF.js:', pdfJsError);
              }
            }

            // Method 2: Get all text nodes as fallback
            const textNodes = [];
            const walker = document.createTreeWalker(
              document.body,
              NodeFilter.SHOW_TEXT,
              { acceptNode: node => node.nodeValue.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT },
              false
            );

            let node;
            while (node = walker.nextNode()) {
              textNodes.push(node.nodeValue.trim());
            }

            if (textNodes.length > 0) {
              return textNodes.join(' ').trim();
            }

            return '';
          } catch (error) {
            console.error('Error extracting all PDF text:', error);
            return '';
          }
        }
      });

      if (!result || !result[0] || !result[0].result) {
        throw new Error('Failed to extract text from PDF');
      }

      const extractedText = result[0].result;

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('No text could be extracted from this PDF');
      }

      // Get PDF metadata
      const metadata = {
        title: activeTab.title || 'PDF Document',
        url: activeTab.url || 'pdf://document',
        isPDF: true
      };

      // Store the extracted text
      await chrome.runtime.sendMessage({
        action: 'sendPDFTextToChat',
        text: extractedText,
        metadata: metadata
      });

      // Show the extracted text in the chat
      this.uiManager.addMessageToChat(
        `I've extracted all text from the PDF document:

` +
        `**Source:** ${metadata.title}
` +
        `**Extracted text (${extractedText.length} characters):**

` +
        `"${extractedText.substring(0, 150)}${extractedText.length > 150 ? '...' : ''}"

` +
        `You can now ask me questions about this PDF content.`,
        'ai'
      );

      // Store the PDF content in the API manager for future reference
      this.apiManager.setScannedTabContent({
        content: extractedText,
        title: metadata.title,
        url: metadata.url,
        timestamp: new Date().toISOString(),
        isPDF: true
      });

      this.uiManager.showStatus('PDF text extracted successfully', false, 3000);
      return true;
    } catch (error) {
      console.error('Error extracting all PDF text:', error);
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true, 5000);
      return false;
    }
  }

  /**
   * Ask a question about the selected PDF text
   * @param {string} question - The question to ask
   * @param {string} pdfText - The selected PDF text
   * @param {object} metadata - Metadata about the PDF
   */
  async askAboutPDFSelection(question, pdfText, metadata = {}) {
    try {
      this.uiManager.showLoading(`Analyzing PDF content...`);

      // Prepare a prompt for the question about the PDF
      const prompt = `
        Please answer this question about the selected text from a PDF document:

        Question: ${question}

        Source: ${metadata?.title || 'PDF Document'}

        Selected PDF text:
        "${this.truncateContent(pdfText, 6000)}"

        Provide a detailed and accurate answer based only on the information in the PDF text.
        If the answer isn't in the provided text, say so clearly.
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a helpful assistant that accurately answers questions about PDF document content. Focus specifically on the provided text selection."
      });

      this.uiManager.showResult(response.text, `Q: ${question}`);
    } catch (error) {
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Scan the currently selected tab
   * @returns {Promise<void>}
   */
  async scanSelectedTab() {
    try {
      // Get the selected tab ID and info
      const selectedTabId = this.apiManager.getSelectedTabId();
      const selectedTabInfo = this.apiManager.getSelectedTabInfo();

      if (!selectedTabId || !selectedTabInfo) {
        throw new Error('No tab selected for scanning');
      }

      // Show a loading message
      this.uiManager.showStatusLoading(`Scanning content from tab: ${selectedTabInfo.title}...`);



      try {
        // Request permission to access all URLs if needed
        try {
          await chrome.permissions.request({
            permissions: ['scripting'],
            origins: ['<all_urls>']
          });
        } catch (permError) {
          console.log('Permission request error (continuing anyway):', permError);
        }

        // DIRECT APPROACH: Execute a script directly in the tab to extract content
        const result = await chrome.scripting.executeScript({
          target: { tabId: selectedTabId },
          func: () => {
            // Simple content extraction function that runs directly in the target tab
            try {
              // Get the page title
              const title = document.title || 'Untitled Page';

              // Get the page URL
              const url = window.location.href || '';

              // Get the main content
              let mainContent = '';

              try {
                // Clone the body to avoid modifying the actual page
                const clonedBody = document.body.cloneNode(true);

                // Remove scripts, styles, and other non-content elements
                const elementsToRemove = clonedBody.querySelectorAll('script, style, noscript, svg, img, iframe, video, audio, canvas');
                elementsToRemove.forEach(el => {
                  try { el.remove(); } catch (e) { /* ignore removal errors */ }
                });

                // Get the text content
                mainContent = clonedBody.innerText || '';

                // Trim and normalize whitespace
                mainContent = mainContent.replace(/\s+/g, ' ').trim();
              } catch (error) {
                // Fallback to simple extraction
                mainContent = document.body ? (document.body.innerText || '') : '';
                mainContent = mainContent.replace(/\s+/g, ' ').trim();
              }

              // Return the extracted content
              return {
                url: url,
                title: title,
                content: mainContent || 'No content available',
                timestamp: new Date().toISOString()
              };
            } catch (error) {
              // Return error information
              return {
                error: true,
                message: error.message,
                url: window.location.href || '',
                title: document.title || 'Untitled Page',
                content: 'Content extraction failed. Please try again.',
                timestamp: new Date().toISOString()
              };
            }
          }
        });

        if (!result || !result[0] || !result[0].result) {
          throw new Error('Failed to extract content from tab');
        }

        const tabContent = result[0].result;

        // Check if the result contains an error
        if (tabContent.error) {
          throw new Error(`Content extraction failed: ${tabContent.message || 'Unknown error'}`);
        }

        // Validate the content
        if (!tabContent.content || tabContent.content.length < 10) {
          throw new Error('The scanned content appears to be empty or invalid');
        }

        // Add a message to the chat with the scanned content
        this.uiManager.addMessageToChat(
          `I've scanned content from tab: **${selectedTabInfo.title}**\n\n` +
          `URL: ${tabContent.url}\n` +
          `Content length: ${tabContent.content.length} characters\n\n` +
          `You can now ask me questions about this content. For example:\n` +
          `- Summarize this page\n` +
          `- What are the main points?\n` +
          `- Extract key information\n` +
          `- Explain the concept discussed here\n\n` +
          `To return to the current tab's content, type "clear scanned content" or click the scan button again.`,
          'ai'
        );

        // Show a status message
        this.uiManager.showStatus(`Successfully scanned content from "${selectedTabInfo.title}"`, false, 3000);

        // Store the scanned content in the API manager for future reference
        this.apiManager.setScannedTabContent(tabContent, selectedTabId, selectedTabInfo);
      } catch (error) {
        console.error('Error scanning tab:', error);

        // Handle specific error cases
        let errorMessage = error.message;

        if (error.message.includes('Cannot access contents of url')) {
          errorMessage = 'Cannot access the contents of this tab due to browser security restrictions. Try a different tab.';
        } else if (error.message.includes('permission')) {
          errorMessage = 'Permission denied to access tab content. Please allow the extension to access site content when prompted.';
        } else if (error.message.includes('Failed to execute script')) {
          errorMessage = 'Failed to execute content script in the tab. The site may have security restrictions.';
        }

        // Show error message
        this.uiManager.removeLoadingIndicators();
        this.uiManager.addMessageToChat('Error: ' + errorMessage, 'ai', 'error-message');
        this.uiManager.showStatus(errorMessage, true, 5000);
      }

    } catch (error) {
      console.error('Error in scanOtherTab:', error);
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Get a human-readable time difference (e.g., "5 minutes ago")
   * @param {Date} date - The date to compute difference from
   * @returns {string} - Formatted time ago string
   */
  getTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) return `${diffSec} seconds ago`;

    const diffMin = Math.floor(diffSec / 60);
    if (diffMin < 60) return `${diffMin} minute${diffMin === 1 ? '' : 's'} ago`;

    const diffHour = Math.floor(diffMin / 60);
    if (diffHour < 24) return `${diffHour} hour${diffHour === 1 ? '' : 's'} ago`;

    const diffDay = Math.floor(diffHour / 24);
    return `${diffDay} day${diffDay === 1 ? '' : 's'} ago`;
  }

  /**
   * Truncate content to a maximum length
   * @param {string} content - The content to truncate
   * @param {number} maxLength - Maximum length
   * @returns {string} - Truncated content
   */
  truncateContent(content, maxLength = 4000) {
    if (!content) return '';
    if (content.length <= maxLength) return content;

    return content.substring(0, maxLength) + '... [content truncated due to length]';
  }

  /**
   * Conduct research on a topic using multiple sources
   * @param {string} topic - Optional topic to research, if not provided will prompt the user
   * @returns {Promise<void>}
   */
  async conductResearch(topic = null) {
    try {
      // Lazy-load the ResearchAssistant if not already loaded
      if (!this.researchAssistant) {
        // Check if ResearchAssistant class is available
        if (typeof ResearchAssistant === 'undefined') {
          throw new Error('Research Assistant module is not loaded. Please refresh the extension.');
        }
        this.researchAssistant = new ResearchAssistant(this.apiManager, this.uiManager);
      }

      // If no topic provided, prompt the user
      if (!topic) {
        topic = await this.uiManager.prompt('What topic would you like to research?');
        if (!topic) return; // User cancelled
      }

      // Show a message in the chat that research is starting
      this.uiManager.addMessageToChat(
        `Starting comprehensive research on: **${topic}**\n\n` +
        `I'll search for information from multiple sources and compile a detailed research report. ` +
        `This may take a minute or two, please wait...`,
        'ai'
      );

      // Conduct the research
      this.uiManager.showStatusLoading(`Researching "${topic}"...`);
      const report = await this.researchAssistant.conductResearch(topic);

      // Show the research report in the chat
      this.uiManager.addMessageToChat(
        `# Research Report: ${topic}\n\n${report}`,
        'ai'
      );

      this.uiManager.showStatus(`Research on "${topic}" completed`, false, 3000);
    } catch (error) {
      console.error('Research error:', error);
      this.uiManager.addMessageToChat(`Research Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Analyze the current website for SEO, accessibility, performance, security, and content issues
   * @param {Object} options - Analysis options
   * @param {Array<string>} [options.categories] - Specific categories to analyze (e.g., ['seo', 'accessibility'])
   * @param {boolean} [options.detailed] - Whether to perform a detailed analysis
   * @returns {Promise<void>}
   */
  async analyzeWebsite(options = {}) {
    try {
      // Lazy-load the WebsiteAnalyzer if not already loaded
      if (!this.websiteAnalyzer) {
        // Check if WebsiteAnalyzer class is available
        if (typeof WebsiteAnalyzer === 'undefined') {
          throw new Error('Website Analyzer module is not loaded. Please refresh the extension.');
        }
        this.websiteAnalyzer = new WebsiteAnalyzer(this.apiManager, this.uiManager);
      }

      // Determine which categories to analyze
      const categories = options.categories || ['seo', 'accessibility', 'performance', 'security', 'content'];
      const isDetailedAnalysis = options.detailed || false;

      // Show a message in the chat that analysis is starting
      let analysisMessage = `Starting comprehensive website analysis...\n\n`;

      if (categories.length < 5) {
        analysisMessage = `Starting website analysis for: ${categories.map(c => c.toUpperCase()).join(', ')}...\n\n`;
      }

      if (isDetailedAnalysis) {
        analysisMessage += `I'll perform a detailed analysis of this website. `;
      } else {
        analysisMessage += `I'll analyze this website for issues and opportunities. `;
      }

      analysisMessage += `This may take a moment, please wait...`;

      this.uiManager.addMessageToChat(analysisMessage, 'ai');

      // Show progress indicator
      this.uiManager.showStatusLoading('Analyzing website...');

      // Conduct the analysis with options
      const results = await this.websiteAnalyzer.analyzeWebsite({
        categories: categories,
        detailed: isDetailedAnalysis
      });

      // Generate the report
      const report = this.websiteAnalyzer.generateReport(results);

      // Show the analysis report in the chat
      this.uiManager.addMessageToChat(report, 'ai');

      // Add export option
      this.uiManager.addMessageToChat(
        `Would you like to take any of the following actions?\n\n` +
        `1. **Export Report** - Save this analysis as a markdown file\n` +
        `2. **Detailed Analysis** - Run a more comprehensive analysis\n` +
        `3. **Focus on SEO** - Get more detailed SEO recommendations\n` +
        `4. **Focus on Performance** - Get more detailed performance recommendations\n` +
        `5. **Focus on Accessibility** - Get more detailed accessibility recommendations`,
        'ai'
      );

      this.uiManager.showStatus('Website analysis completed', false, 3000);

      // Return the results for potential further processing
      return results;
    } catch (error) {
      console.error('Website analysis error:', error);
      this.uiManager.addMessageToChat(`Website Analysis Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return null;
    }
  }

  /**
   * Export the website analysis report to a file
   * @param {Object} results - The analysis results
   * @returns {Promise<void>}
   */
  async exportWebsiteAnalysisReport(results) {
    try {
      if (!this.websiteAnalyzer) {
        throw new Error('Website Analyzer is not initialized. Please run an analysis first.');
      }

      // Generate the report
      const report = this.websiteAnalyzer.generateReport(results);

      // Get the current date and website URL for the filename
      const date = new Date().toISOString().split('T')[0];
      const url = new URL(results.url);
      const hostname = url.hostname.replace(/[^a-z0-9]/gi, '_');
      const filename = `website_analysis_${hostname}_${date}.md`;

      // Create a blob with the report content
      const blob = new Blob([report], { type: 'text/markdown' });

      // Create a download link and trigger the download
      const downloadUrl = URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      downloadLink.href = downloadUrl;
      downloadLink.download = filename;
      downloadLink.click();

      // Clean up
      URL.revokeObjectURL(downloadUrl);

      this.uiManager.addMessageToChat(
        `Report exported successfully as "${filename}".`,
        'ai'
      );

      this.uiManager.showStatus('Report exported', false, 3000);
    } catch (error) {
      console.error('Export report error:', error);
      this.uiManager.addMessageToChat(`Error exporting report: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Run a focused analysis on a specific category
   * @param {string} category - The category to focus on (e.g., 'seo', 'accessibility')
   * @returns {Promise<void>}
   */
  async runFocusedAnalysis(category) {
    try {
      if (!['seo', 'accessibility', 'performance', 'security', 'content'].includes(category)) {
        throw new Error(`Invalid category: ${category}`);
      }

      this.uiManager.addMessageToChat(
        `Starting focused analysis for ${category.toUpperCase()}...\n\n` +
        `I'll perform a detailed analysis of this website's ${category} aspects. ` +
        `This may take a moment, please wait...`,
        'ai'
      );

      // Run the analysis with just the specified category and detailed mode
      await this.analyzeWebsite({
        categories: [category],
        detailed: true
      });

    } catch (error) {
      console.error('Focused analysis error:', error);
      this.uiManager.addMessageToChat(`Error running focused analysis: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Generate a summary of the current PDF document
   * @param {string} [summaryType='executive'] - Type of summary to generate (executive, detailed, chapter)
   * @returns {Promise<void>}
   */
  async summarizePDF(summaryType = 'executive') {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') ||
                   tab.url.toLowerCase().includes('pdf') ||
                   tab.url.toLowerCase().includes('viewer.html');

      if (!isPdf) {
        throw new Error('This feature only works with PDF documents. Please open a PDF file in your browser and try again.');
      }

      // Lazy-load the PDFProcessor if not already loaded
      if (!this.pdfProcessor) {
        // Check if PDFProcessor class is available
        if (typeof PDFProcessor === 'undefined') {
          throw new Error('PDF Processor module is not loaded. Please refresh the extension.');
        }
        this.pdfProcessor = new PDFProcessor(this.apiManager, this.uiManager);
      }

      // Show a message in the chat that summarization is starting
      this.uiManager.addMessageToChat(
        `Starting PDF summarization...\n\n` +
        `I'll create a ${summaryType} summary of this PDF document. ` +
        `This may take a moment, please wait...`,
        'ai'
      );

      // Generate the summary
      this.uiManager.showStatusLoading(`Summarizing PDF (${summaryType} summary)...`);
      const summary = await this.pdfProcessor.summarizePDF(summaryType);

      // Show the summary in the chat
      this.uiManager.addMessageToChat(
        `# ${summaryType.charAt(0).toUpperCase() + summaryType.slice(1)} Summary of PDF\n\n${summary}`,
        'ai'
      );

      this.uiManager.showStatus('PDF summarization completed', false, 3000);
    } catch (error) {
      console.error('PDF summarization error:', error);
      this.uiManager.addMessageToChat(`PDF Summarization Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Answer a question about the current PDF document
   * @param {string} [question] - The question to answer, if not provided will prompt the user
   * @returns {Promise<void>}
   */
  async askPDFQuestion(question = null) {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') ||
                   tab.url.toLowerCase().includes('pdf') ||
                   tab.url.toLowerCase().includes('viewer.html');

      if (!isPdf) {
        throw new Error('This feature only works with PDF documents. Please open a PDF file in your browser and try again.');
      }

      // Lazy-load the PDFProcessor if not already loaded
      if (!this.pdfProcessor) {
        // Check if PDFProcessor class is available
        if (typeof PDFProcessor === 'undefined') {
          throw new Error('PDF Processor module is not loaded. Please refresh the extension.');
        }
        this.pdfProcessor = new PDFProcessor(this.apiManager, this.uiManager);
      }

      // If no question provided, prompt the user
      if (!question) {
        question = await this.uiManager.prompt('What would you like to ask about this PDF document?');
        if (!question) return; // User cancelled
      }

      // Show a message in the chat that question answering is starting
      this.uiManager.addMessageToChat(
        `Analyzing PDF to answer: **${question}**\n\n` +
        `I'll search through the PDF document to find the answer. ` +
        `This may take a moment, please wait...`,
        'ai'
      );

      // Answer the question
      this.uiManager.showStatusLoading('Analyzing PDF to answer question...');
      const answer = await this.pdfProcessor.answerPDFQuestion(question);

      // Show the answer in the chat
      this.uiManager.addMessageToChat(
        `## Answer to: ${question}\n\n${answer}`,
        'ai'
      );

      this.uiManager.showStatus('PDF question answered', false, 3000);
    } catch (error) {
      console.error('PDF question answering error:', error);
      this.uiManager.addMessageToChat(`PDF Question Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Process a custom query about a PDF document
   * @param {string} query - The query to process
   * @returns {Promise<Object>} - Response object with answer and success status
   */
  async processCustomPDFQuery(query) {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') ||
                   tab.url.toLowerCase().includes('pdf') ||
                   tab.url.toLowerCase().includes('viewer.html');

      if (!isPdf) {
        return {
          success: false,
          error: 'This feature only works with PDF documents. Please open a PDF file in your browser and try again.'
        };
      }

      // Extract PDF content
      let pdfText = '';

      try {
        // Try to inject the PDF content script if not already loaded
        try {
          await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content/pdf-content.js']
          });

          // Wait a moment for the script to initialize
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (injectionError) {
          console.log('PDF content script might already be loaded:', injectionError);
        }

        // Execute a script to extract text from the PDF
        const result = await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: () => {
            try {
              // Try multiple methods to get text

              // Method 1: Try PDF.js specific extraction
              if (window.PDFViewerApplication) {
                try {
                  const pdfViewer = window.PDFViewerApplication.pdfViewer;
                  const pageCount = pdfViewer.pagesCount || 0;
                  let allText = [];

                  // Extract text from each page
                  for (let i = 1; i <= pageCount; i++) {
                    try {
                      const pageIndex = i - 1;
                      const page = pdfViewer.getPageView(pageIndex);
                      if (page && page.textLayer && page.textLayer.textContent) {
                        allText.push(page.textLayer.textContent);
                      }
                    } catch (pageError) {
                      console.log(`Error extracting text from page ${i}:`, pageError);
                    }
                  }

                  if (allText.length > 0) {
                    return allText.join('\n\n');
                  }
                } catch (pdfJsError) {
                  console.log('Could not extract text using PDF.js:', pdfJsError);
                }
              }

              // Method 2: Try to get text from DOM elements
              const textElements = document.querySelectorAll('.textLayer div');
              if (textElements && textElements.length > 0) {
                return Array.from(textElements).map(el => el.textContent).join(' ');
              }

              // Method 3: Get all text from the document
              return document.body.innerText;
            } catch (error) {
              console.error('Error extracting PDF text:', error);
              return '';
            }
          }
        });

        pdfText = result[0].result || '';
      } catch (extractionError) {
        console.error('Error extracting PDF text:', extractionError);
        pdfText = 'Error extracting PDF text. Please try again.';
      }

      if (!pdfText || pdfText.trim().length === 0) {
        return {
          success: false,
          error: 'Could not extract text from the PDF document. Please try again.'
        };
      }

      // Truncate the PDF text if it's too long
      const truncatedText = this.truncateContent(pdfText, 6000);

      // Prepare a prompt for the query
      const prompt = `
        Please answer this query about the PDF document:

        Query: ${query}

        PDF Content:
        "${truncatedText}"

        Provide a detailed and accurate answer based only on the information in the PDF text.
        If the answer isn't in the provided text, say so clearly.
      `;

      // Send the request to the API
      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a helpful assistant that accurately answers questions about PDF document content. Focus specifically on the provided text."
      });

      return {
        success: true,
        answer: response.text,
        query: query
      };
    } catch (error) {
      console.error('Error processing PDF query:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Extract specific information from the current PDF document
   * @param {string} [infoType='all'] - Type of information to extract (all, topics, entities, dates, etc.)
   * @returns {Promise<void>}
   */
  async extractPDFInfo(infoType = 'all') {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') ||
                   tab.url.toLowerCase().includes('pdf') ||
                   tab.url.toLowerCase().includes('viewer.html');

      if (!isPdf) {
        throw new Error('This feature only works with PDF documents. Please open a PDF file in your browser and try again.');
      }

      // Lazy-load the PDFProcessor if not already loaded
      if (!this.pdfProcessor) {
        // Check if PDFProcessor class is available
        if (typeof PDFProcessor === 'undefined') {
          throw new Error('PDF Processor module is not loaded. Please refresh the extension.');
        }
        this.pdfProcessor = new PDFProcessor(this.apiManager, this.uiManager);
      }

      // If infoType is not provided, prompt the user
      if (infoType === 'all') {
        const options = ['all', 'topics', 'entities', 'dates', 'statistics', 'citations'];
        infoType = await this.uiManager.prompt(
          'What type of information would you like to extract from the PDF?\n\n' +
          'Options: all, topics, entities, dates, statistics, citations',
          'all'
        );
        if (!infoType) return; // User cancelled
        if (!options.includes(infoType.toLowerCase())) {
          infoType = 'all'; // Default to 'all' if invalid option
        }
      }

      // Show a message in the chat that information extraction is starting
      this.uiManager.addMessageToChat(
        `Extracting ${infoType} information from PDF...\n\n` +
        `I'll analyze the PDF document to extract the requested information. ` +
        `This may take a moment, please wait...`,
        'ai'
      );

      // Extract the information
      this.uiManager.showStatusLoading(`Extracting ${infoType} information from PDF...`);
      const info = await this.pdfProcessor.extractPDFInfo(infoType);

      // Show the extracted information in the chat
      this.uiManager.addMessageToChat(
        `# ${infoType.charAt(0).toUpperCase() + infoType.slice(1)} Information Extracted from PDF\n\n${info}`,
        'ai'
      );

      this.uiManager.showStatus('PDF information extraction completed', false, 3000);
    } catch (error) {
      console.error('PDF information extraction error:', error);
      this.uiManager.addMessageToChat(`PDF Information Extraction Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create a table of contents for the current PDF document
   * @returns {Promise<void>}
   */
  async createPDFTableOfContents() {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') ||
                   tab.url.toLowerCase().includes('pdf') ||
                   tab.url.toLowerCase().includes('viewer.html');

      if (!isPdf) {
        throw new Error('This feature only works with PDF documents. Please open a PDF file in your browser and try again.');
      }

      // Lazy-load the PDFProcessor if not already loaded
      if (!this.pdfProcessor) {
        // Check if PDFProcessor class is available
        if (typeof PDFProcessor === 'undefined') {
          throw new Error('PDF Processor module is not loaded. Please refresh the extension.');
        }
        this.pdfProcessor = new PDFProcessor(this.apiManager, this.uiManager);
      }

      // Show a message in the chat that TOC creation is starting
      this.uiManager.addMessageToChat(
        `Creating table of contents for PDF...\n\n` +
        `I'll analyze the PDF document to identify all major sections and create a detailed table of contents. ` +
        `This may take a moment, please wait...`,
        'ai'
      );

      // Create the table of contents
      this.uiManager.showStatusLoading('Creating PDF table of contents...');
      const toc = await this.pdfProcessor.createPDFTableOfContents();

      // Show the table of contents in the chat
      this.uiManager.addMessageToChat(
        `# Table of Contents for PDF\n\n${toc}`,
        'ai'
      );

      this.uiManager.showStatus('PDF table of contents created', false, 3000);
    } catch (error) {
      console.error('PDF table of contents error:', error);
      this.uiManager.addMessageToChat(`PDF Table of Contents Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Compare the current PDF with another PDF document
   * @param {string} [secondPdfUrl] - URL of the second PDF to compare, if not provided will prompt the user
   * @returns {Promise<void>}
   */
  async comparePDFs(secondPdfUrl = null) {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') ||
                   tab.url.toLowerCase().includes('pdf') ||
                   tab.url.toLowerCase().includes('viewer.html');

      if (!isPdf) {
        throw new Error('This feature only works with PDF documents. Please open a PDF file in your browser and try again.');
      }

      // Lazy-load the PDFProcessor if not already loaded
      if (!this.pdfProcessor) {
        // Check if PDFProcessor class is available
        if (typeof PDFProcessor === 'undefined') {
          throw new Error('PDF Processor module is not loaded. Please refresh the extension.');
        }
        this.pdfProcessor = new PDFProcessor(this.apiManager, this.uiManager);
      }

      // If no second PDF URL provided, prompt the user
      if (!secondPdfUrl) {
        secondPdfUrl = await this.uiManager.prompt('Enter the URL of the second PDF to compare:');
        if (!secondPdfUrl) return; // User cancelled
      }

      // Validate the URL
      if (!secondPdfUrl.toLowerCase().endsWith('.pdf')) {
        throw new Error('The provided URL does not appear to be a PDF document. Please provide a URL ending with .pdf');
      }

      // Show a message in the chat that comparison is starting
      this.uiManager.addMessageToChat(
        `Comparing PDFs...\n\n` +
        `I'll analyze both PDF documents to identify similarities and differences. ` +
        `This may take a moment, please wait...`,
        'ai'
      );

      // Compare the PDFs
      this.uiManager.showStatusLoading('Comparing PDF documents...');
      const comparison = await this.pdfProcessor.comparePDFs(secondPdfUrl);

      // Show the comparison results in the chat
      this.uiManager.addMessageToChat(
        `# PDF Comparison Results\n\n${comparison}`,
        'ai'
      );

      this.uiManager.showStatus('PDF comparison completed', false, 3000);
    } catch (error) {
      console.error('PDF comparison error:', error);
      this.uiManager.addMessageToChat(`PDF Comparison Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Generate a study guide from the current PDF document
   * @returns {Promise<void>}
   */
  async generatePDFStudyGuide() {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') ||
                   tab.url.toLowerCase().includes('pdf') ||
                   tab.url.toLowerCase().includes('viewer.html');

      if (!isPdf) {
        throw new Error('This feature only works with PDF documents. Please open a PDF file in your browser and try again.');
      }

      // Lazy-load the PDFProcessor if not already loaded
      if (!this.pdfProcessor) {
        // Check if PDFProcessor class is available
        if (typeof PDFProcessor === 'undefined') {
          throw new Error('PDF Processor module is not loaded. Please refresh the extension.');
        }
        this.pdfProcessor = new PDFProcessor(this.apiManager, this.uiManager);
      }

      // Show a message in the chat that study guide generation is starting
      this.uiManager.addMessageToChat(
        `Generating study guide from PDF...\n\n` +
        `I'll analyze the PDF document to create a comprehensive study guide with key concepts, definitions, and practice questions. ` +
        `This may take a moment, please wait...`,
        'ai'
      );

      // Generate the study guide
      this.uiManager.showStatusLoading('Generating PDF study guide...');
      const studyGuide = await this.pdfProcessor.generateStudyGuide();

      // Show the study guide in the chat
      this.uiManager.addMessageToChat(
        `# Study Guide for PDF\n\n${studyGuide}`,
        'ai'
      );

      this.uiManager.showStatus('PDF study guide generated', false, 3000);
    } catch (error) {
      console.error('PDF study guide error:', error);
      this.uiManager.addMessageToChat(`PDF Study Guide Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Connect to a productivity tool (Notion, Trello, Google Workspace)
   * @param {string} platform - The platform to connect to (notion, trello, google)
   * @returns {Promise<void>}
   */
  async connectProductivityTool(platform) {
    try {
      // Lazy-load the ProductivityIntegrations if not already loaded
      if (!this.productivityIntegrations) {
        // Check if ProductivityIntegrations class is available
        if (typeof ProductivityIntegrations === 'undefined') {
          throw new Error('Productivity Integrations module is not loaded. Please refresh the extension.');
        }

        // Check if the required integration classes are available
        if (platform === 'notion' && typeof NotionIntegration === 'undefined') {
          throw new Error('Notion Integration module is not loaded. Please refresh the extension.');
        }
        if (platform === 'trello' && typeof TrelloIntegration === 'undefined') {
          throw new Error('Trello Integration module is not loaded. Please refresh the extension.');
        }
        if (platform === 'google' && typeof GoogleIntegration === 'undefined') {
          throw new Error('Google Integration module is not loaded. Please refresh the extension.');
        }

        this.productivityIntegrations = new ProductivityIntegrations(this.apiManager, this.uiManager, this.apiManager.storageManager);
      }

      // Show a message in the chat that connection is starting
      this.uiManager.addMessageToChat(
        `Connecting to ${platform}...\n\n` +
        `You'll be redirected to ${platform} to authorize the connection. ` +
        `Please follow the instructions on the ${platform} website.`,
        'ai'
      );

      // Connect to the platform
      this.uiManager.showStatusLoading(`Connecting to ${platform}...`);
      const success = await this.productivityIntegrations.authenticate(platform);

      if (success) {
        // Show success message
        this.uiManager.addMessageToChat(
          `# Connected to ${platform}\n\n` +
          `Your ${platform} account is now connected to SoulAI. ` +
          `You can now use the productivity features with ${platform}.`,
          'ai'
        );

        this.uiManager.showStatus(`Connected to ${platform}`, false, 3000);
      } else {
        throw new Error(`Failed to connect to ${platform}`);
      }
    } catch (error) {
      console.error(`${platform} connection error:`, error);
      this.uiManager.addMessageToChat(`${platform} Connection Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Disconnect from a productivity tool
   * @param {string} platform - The platform to disconnect from (notion, trello, google)
   * @returns {Promise<void>}
   */
  async disconnectProductivityTool(platform) {
    try {
      // Check if ProductivityIntegrations is loaded
      if (!this.productivityIntegrations) {
        throw new Error('Productivity Integrations module is not loaded. Please refresh the extension.');
      }

      // Show a message in the chat that disconnection is starting
      this.uiManager.addMessageToChat(
        `Disconnecting from ${platform}...`,
        'ai'
      );

      // Disconnect from the platform
      this.uiManager.showStatusLoading(`Disconnecting from ${platform}...`);
      const success = await this.productivityIntegrations.disconnect(platform);

      if (success) {
        // Show success message
        this.uiManager.addMessageToChat(
          `# Disconnected from ${platform}\n\n` +
          `Your ${platform} account has been disconnected from SoulAI.`,
          'ai'
        );

        this.uiManager.showStatus(`Disconnected from ${platform}`, false, 3000);
      } else {
        throw new Error(`Failed to disconnect from ${platform}`);
      }
    } catch (error) {
      console.error(`${platform} disconnection error:`, error);
      this.uiManager.addMessageToChat(`${platform} Disconnection Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create a new item in a productivity tool
   * @param {string} platform - The platform to create the item in (notion, trello, google)
   * @param {string} itemType - The type of item to create (page, card, document, etc.)
   * @param {Object} options - Additional options for the item
   * @returns {Promise<void>}
   */
  async createProductivityItem(platform, itemType, options = {}) {
    try {
      // Check if ProductivityIntegrations is loaded
      if (!this.productivityIntegrations) {
        // Check if ProductivityIntegrations class is available
        if (typeof ProductivityIntegrations === 'undefined') {
          throw new Error('Productivity Integrations module is not loaded. Please refresh the extension.');
        }
        this.productivityIntegrations = new ProductivityIntegrations(this.apiManager, this.uiManager, this.apiManager.storageManager);
      }

      // Check if authenticated with the platform
      const authStatus = this.productivityIntegrations.getAuthStatus();
      if (!authStatus[platform.toLowerCase()]) {
        // Prompt to connect
        const connectNow = await this.uiManager.confirm(
          `You are not connected to ${platform}. Would you like to connect now?`
        );

        if (connectNow) {
          await this.connectProductivityTool(platform);
        } else {
          throw new Error(`You must connect to ${platform} before creating items.`);
        }
      }

      // Show a message in the chat that item creation is starting
      this.uiManager.addMessageToChat(
        `Creating ${itemType} in ${platform}...\n\n` +
        `I'll create a new ${itemType} in your ${platform} account based on the current page content.`,
        'ai'
      );

      // Create the item
      this.uiManager.showStatusLoading(`Creating ${itemType} in ${platform}...`);
      const result = await this.productivityIntegrations.createItemFromPage(platform, itemType, options);

      // Show success message
      this.uiManager.addMessageToChat(
        `# Created ${itemType} in ${platform}\n\n` +
        `Successfully created a new ${itemType} in your ${platform} account.\n\n` +
        `**Title:** ${result.name || result.title || 'Untitled'}\n` +
        (result.url ? `**URL:** [Open in ${platform}](${result.url})` : ''),
        'ai'
      );

      this.uiManager.showStatus(`Created ${itemType} in ${platform}`, false, 3000);
    } catch (error) {
      console.error(`${platform} item creation error:`, error);
      this.uiManager.addMessageToChat(`${platform} Item Creation Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Get items from a productivity tool
   * @param {string} platform - The platform to get items from (notion, trello, google)
   * @param {string} itemType - The type of items to get (pages, cards, documents, etc.)
   * @param {Object} options - Options for filtering and sorting items
   * @returns {Promise<void>}
   */
  async getProductivityItems(platform, itemType, options = {}) {
    try {
      // Check if ProductivityIntegrations is loaded
      if (!this.productivityIntegrations) {
        // Check if ProductivityIntegrations class is available
        if (typeof ProductivityIntegrations === 'undefined') {
          throw new Error('Productivity Integrations module is not loaded. Please refresh the extension.');
        }
        this.productivityIntegrations = new ProductivityIntegrations(this.apiManager, this.uiManager, this.apiManager.storageManager);
      }

      // Check if authenticated with the platform
      const authStatus = this.productivityIntegrations.getAuthStatus();
      if (!authStatus[platform.toLowerCase()]) {
        // Prompt to connect
        const connectNow = await this.uiManager.confirm(
          `You are not connected to ${platform}. Would you like to connect now?`
        );

        if (connectNow) {
          await this.connectProductivityTool(platform);
        } else {
          throw new Error(`You must connect to ${platform} before getting items.`);
        }
      }

      // Show a message in the chat that item retrieval is starting
      this.uiManager.addMessageToChat(
        `Getting ${itemType} from ${platform}...\n\n` +
        `I'll retrieve your ${itemType} from ${platform}.`,
        'ai'
      );

      // Get the items
      this.uiManager.showStatusLoading(`Getting ${itemType} from ${platform}...`);
      const items = await this.productivityIntegrations.getItems(platform, itemType, options);

      // Format the items for display
      let formattedItems = '';
      if (items.length === 0) {
        formattedItems = 'No items found.';
      } else {
        formattedItems = items.map(item => {
          const title = item.name || item.title || 'Untitled';
          const url = item.url || item.htmlUrl || '';
          const date = item.createdTime || item.created || item.date || '';

          return `- **${title}**${url ? ` - [Open](${url})` : ''}${date ? ` - ${new Date(date).toLocaleDateString()}` : ''}`;
        }).join('\n');
      }

      // Show the items in the chat
      this.uiManager.addMessageToChat(
        `# ${itemType} from ${platform}\n\n` +
        `Found ${items.length} ${itemType}:\n\n` +
        formattedItems,
        'ai'
      );

      this.uiManager.showStatus(`Retrieved ${items.length} ${itemType} from ${platform}`, false, 3000);
    } catch (error) {
      console.error(`${platform} item retrieval error:`, error);
      this.uiManager.addMessageToChat(`${platform} Item Retrieval Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Apply a template to create a new item in a productivity tool
   * @param {string} platform - The platform to create the item in (notion, trello, google)
   * @param {string} templateName - The name of the template to apply
   * @param {Object} variables - Variables to replace in the template
   * @returns {Promise<void>}
   */
  async applyProductivityTemplate(platform, templateName, variables = {}) {
    try {
      // Check if ProductivityIntegrations is loaded
      if (!this.productivityIntegrations) {
        // Check if ProductivityIntegrations class is available
        if (typeof ProductivityIntegrations === 'undefined') {
          throw new Error('Productivity Integrations module is not loaded. Please refresh the extension.');
        }
        this.productivityIntegrations = new ProductivityIntegrations(this.apiManager, this.uiManager, this.apiManager.storageManager);
      }

      // Check if authenticated with the platform
      const authStatus = this.productivityIntegrations.getAuthStatus();
      if (!authStatus[platform.toLowerCase()]) {
        // Prompt to connect
        const connectNow = await this.uiManager.confirm(
          `You are not connected to ${platform}. Would you like to connect now?`
        );

        if (connectNow) {
          await this.connectProductivityTool(platform);
        } else {
          throw new Error(`You must connect to ${platform} before applying templates.`);
        }
      }

      // If no template name provided, show available templates and prompt for selection
      if (!templateName) {
        const templates = await this.productivityIntegrations.getTemplates(platform);

        if (templates.length === 0) {
          throw new Error(`No templates available for ${platform}.`);
        }

        const templateOptions = templates.map(t => t.name);
        templateName = await this.uiManager.prompt(
          `Select a template for ${platform}:\n\n${templateOptions.join('\n')}`,
          templateOptions[0]
        );

        if (!templateName) {
          throw new Error('No template selected.');
        }
      }

      // If no variables provided, prompt for them
      if (Object.keys(variables).length === 0) {
        // Get the template to see what variables it needs
        const templates = await this.productivityIntegrations.getTemplates(platform);
        const template = templates.find(t => t.name === templateName);

        if (!template) {
          throw new Error(`Template not found: ${templateName}`);
        }

        // Extract variable names from the template content
        const variableNames = this.extractVariableNames(template.content);

        // Prompt for each variable
        for (const varName of variableNames) {
          const value = await this.uiManager.prompt(`Enter value for ${varName}:`);
          if (value) {
            variables[varName] = value;
          }
        }
      }

      // Show a message in the chat that template application is starting
      this.uiManager.addMessageToChat(
        `Applying ${templateName} template in ${platform}...\n\n` +
        `I'll create a new item in your ${platform} account using the ${templateName} template.`,
        'ai'
      );

      // Apply the template
      this.uiManager.showStatusLoading(`Applying ${templateName} template in ${platform}...`);
      const result = await this.productivityIntegrations.applyTemplate(platform, templateName, variables);

      // Show success message
      this.uiManager.addMessageToChat(
        `# Applied ${templateName} template in ${platform}\n\n` +
        `Successfully created a new item in your ${platform} account using the ${templateName} template.\n\n` +
        `**Title:** ${result.name || result.title || 'Untitled'}\n` +
        (result.url ? `**URL:** [Open in ${platform}](${result.url})` : ''),
        'ai'
      );

      this.uiManager.showStatus(`Applied ${templateName} template in ${platform}`, false, 3000);
    } catch (error) {
      console.error(`${platform} template application error:`, error);
      this.uiManager.addMessageToChat(`${platform} Template Application Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Extract variable names from a template content
   * @param {Object|string} content - The template content
   * @returns {Array<string>} - The variable names
   */
  extractVariableNames(content) {
    const variableNames = new Set();

    // Function to recursively search for variables in an object
    const searchForVariables = (obj) => {
      if (typeof obj === 'string') {
        // Look for {{variableName}} pattern
        const matches = obj.match(/{{([^}]+)}}/g);
        if (matches) {
          matches.forEach(match => {
            const varName = match.substring(2, match.length - 2);
            variableNames.add(varName);
          });
        }
      } else if (Array.isArray(obj)) {
        obj.forEach(item => searchForVariables(item));
      } else if (obj !== null && typeof obj === 'object') {
        Object.values(obj).forEach(value => searchForVariables(value));
      }
    };

    searchForVariables(content);

    return Array.from(variableNames);
  }

  /**
   * Get the current active tab
   * @returns {Promise<object>} - The current tab object
   */
  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tab) {
        throw new Error('No active tab found');
      }
      return tab;
    } catch (error) {
      console.error('Error getting current tab:', error);
      // Return a minimal fallback tab object
      return {
        id: -1,
        url: 'unknown',
        title: 'Unknown Page'
      };
    }
  }

  /**
   * Get the content of the current page
   * @returns {Promise<string>} - The page content
   */
  async getPageContent() {
    try {
      // Use the API manager to get the page content
      const pageContent = await this.apiManager.getPageContent();

      // Return the content as a string
      if (typeof pageContent === 'string') {
        return pageContent;
      } else if (pageContent && typeof pageContent === 'object') {
        // If it's an object, return the content property or stringify the object
        return pageContent.content || JSON.stringify(pageContent);
      } else {
        return 'No content available';
      }
    } catch (error) {
      console.error('Error getting page content:', error);
      return 'Error retrieving page content';
    }
  }

  /**
   * Create flashcards from the current page content
   * @returns {Promise<void>}
   */
  async createFlashcards() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that flashcard creation is starting
      this.uiManager.addMessageToChat(
        `Creating flashcards from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and generate useful flashcards for studying.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a flashcard creation prompt
      const prompt = `Please create a set of flashcards based on the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please create 10 high-quality flashcards that:
      1. Cover the most important concepts from the content
      2. Include a mix of definitions, facts, and conceptual questions
      3. Have clear, concise questions on the front
      4. Have comprehensive but concise answers on the back

      Format each flashcard as:

      ## Flashcard 1
      **Front:** [Question or term]
      **Back:** [Answer or definition]

      ## Flashcard 2
      **Front:** [Question or term]
      **Back:** [Answer or definition]

      And so on...`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating flashcards...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the flashcards in the chat
      this.uiManager.addMessageToChat(
        `# Flashcards for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Flashcards created', false, 3000);
    } catch (error) {
      console.error('Create flashcards error:', error);
      this.uiManager.addMessageToChat(`Create Flashcards Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Review flashcards
   * @returns {Promise<void>}
   */
  async reviewFlashcards() {
    try {
      // Prompt the user to enter or paste flashcards
      const userFlashcards = await this.uiManager.prompt(
        'Please enter or paste the flashcards you want to review:',
        '',
        true
      );

      if (!userFlashcards) {
        throw new Error('No flashcards provided for review.');
      }

      // Show a message in the chat that flashcard review is starting
      this.uiManager.addMessageToChat(
        `Reviewing flashcards...\n\n` +
        `I'll help you review these flashcards with spaced repetition techniques.`,
        'ai'
      );

      // Generate a flashcard review prompt
      const prompt = `I want to review the following flashcards using spaced repetition techniques:

      ${userFlashcards}

      Please:
      1. Organize these flashcards for an effective review session
      2. Present each flashcard with the question first, then the answer hidden
      3. Provide additional context or explanations for complex concepts
      4. Suggest mnemonics or memory techniques for difficult cards
      5. Recommend a review schedule for these flashcards

      Format your response as an interactive review session.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Preparing flashcard review...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the flashcard review in the chat
      this.uiManager.addMessageToChat(
        `# Flashcard Review Session\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Flashcard review prepared', false, 3000);
    } catch (error) {
      console.error('Review flashcards error:', error);
      this.uiManager.addMessageToChat(`Review Flashcards Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Import flashcards from content
   * @returns {Promise<void>}
   */
  async importFlashcards() {
    try {
      // Prompt the user to enter or paste content
      const userContent = await this.uiManager.prompt(
        'Please enter or paste the content you want to convert to flashcards:',
        '',
        true
      );

      if (!userContent) {
        throw new Error('No content provided for flashcard creation.');
      }

      const title = await this.uiManager.prompt(
        'Enter a title for this flashcard set:',
        'My Flashcards'
      );

      // Show a message in the chat that flashcard import is starting
      this.uiManager.addMessageToChat(
        `Creating flashcards from your content...\n\n` +
        `I'll analyze the content and generate useful flashcards for studying.`,
        'ai'
      );

      // Generate a flashcard import prompt
      const prompt = `Please create a set of flashcards based on the following content:

      Title: ${title}

      Content:
      ${userContent}

      Please create 10 high-quality flashcards that:
      1. Cover the most important concepts from the content
      2. Include a mix of definitions, facts, and conceptual questions
      3. Have clear, concise questions on the front
      4. Have comprehensive but concise answers on the back

      Format each flashcard as:

      ## Flashcard 1
      **Front:** [Question or term]
      **Back:** [Answer or definition]

      ## Flashcard 2
      **Front:** [Question or term]
      **Back:** [Answer or definition]

      And so on...`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating flashcards...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the flashcards in the chat
      this.uiManager.addMessageToChat(
        `# Flashcards: ${title}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Flashcards created', false, 3000);
    } catch (error) {
      console.error('Import flashcards error:', error);
      this.uiManager.addMessageToChat(`Import Flashcards Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create a quiz from the current page content
   * @returns {Promise<void>}
   */
  async createQuiz() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that quiz creation is starting
      this.uiManager.addMessageToChat(
        `Creating quiz from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and generate a quiz to test your knowledge.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a quiz creation prompt
      const prompt = `Please create a quiz based on the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please create a 10-question quiz that:
      1. Covers the most important concepts from the content
      2. Includes a mix of multiple-choice, true/false, and short answer questions
      3. Provides clear, concise questions
      4. Includes the correct answer and explanation for each question

      Format the quiz as:

      # Quiz: ${pageTitle}

      ## Question 1
      [Question text]

      Options:
      A. [Option A]
      B. [Option B]
      C. [Option C]
      D. [Option D]

      **Correct Answer:** [Letter]
      **Explanation:** [Explanation of why this is correct]

      ## Question 2
      [Question text]

      Options:
      A. [Option A]
      B. [Option B]
      C. [Option C]
      D. [Option D]

      **Correct Answer:** [Letter]
      **Explanation:** [Explanation of why this is correct]

      And so on...`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating quiz...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the quiz in the chat
      this.uiManager.addMessageToChat(
        `# Quiz for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Quiz created', false, 3000);
    } catch (error) {
      console.error('Create quiz error:', error);
      this.uiManager.addMessageToChat(`Create Quiz Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Take a quiz
   * @returns {Promise<void>}
   */
  async takeQuiz() {
    try {
      // Prompt the user to enter or paste a quiz
      const userQuiz = await this.uiManager.prompt(
        'Please enter or paste the quiz you want to take:',
        '',
        true
      );

      if (!userQuiz) {
        throw new Error('No quiz provided.');
      }

      // Show a message in the chat that quiz taking is starting
      this.uiManager.addMessageToChat(
        `Preparing interactive quiz...\n\n` +
        `I'll present the questions one by one and provide feedback on your answers.`,
        'ai'
      );

      // Generate a quiz taking prompt
      const prompt = `I want to take the following quiz interactively:

      ${userQuiz}

      Please:
      1. Present the quiz in an interactive format
      2. Show one question at a time
      3. For multiple-choice questions, show the options but hide the correct answer
      4. For each question, include a note that says "To answer, simply type your response (e.g., 'A', 'True', or your short answer)"
      5. Don't show the explanations until after I've answered

      Start by showing just the first question, and wait for my response.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Preparing interactive quiz...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the interactive quiz in the chat
      this.uiManager.addMessageToChat(
        `# Interactive Quiz\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Quiz ready', false, 3000);
    } catch (error) {
      console.error('Take quiz error:', error);
      this.uiManager.addMessageToChat(`Take Quiz Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Generate questions from the current page content
   * @returns {Promise<void>}
   */
  async generateQuestions() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that question generation is starting
      this.uiManager.addMessageToChat(
        `Generating questions from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and generate thought-provoking questions.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a question generation prompt
      const prompt = `Please generate thought-provoking questions based on the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please generate 15 questions that:
      1. Cover the most important concepts from the content
      2. Include a mix of factual, conceptual, analytical, and evaluative questions
      3. Range from basic understanding to advanced critical thinking
      4. Are clear and specific
      5. Would be useful for studying, discussion, or deeper exploration

      Organize the questions into these categories:
      - Factual Questions (testing recall of facts)
      - Conceptual Questions (testing understanding of concepts)
      - Analytical Questions (requiring analysis or comparison)
      - Evaluative Questions (requiring judgment or evaluation)
      - Application Questions (requiring application of knowledge)`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Generating questions...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the questions in the chat
      this.uiManager.addMessageToChat(
        `# Questions for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Questions generated', false, 3000);
    } catch (error) {
      console.error('Generate questions error:', error);
      this.uiManager.addMessageToChat(`Generate Questions Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create a study guide from the current page content
   * @returns {Promise<void>}
   */
  async createStudyGuide() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that study guide creation is starting
      this.uiManager.addMessageToChat(
        `Creating study guide from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and generate a comprehensive study guide.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a study guide creation prompt
      const prompt = `Please create a comprehensive study guide based on the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please create a study guide that:
      1. Summarizes the key concepts, theories, and facts
      2. Organizes the information in a logical, hierarchical structure
      3. Includes definitions of important terms
      4. Highlights important relationships between concepts
      5. Provides examples to illustrate complex ideas
      6. Includes review questions at the end of each section

      Format the study guide with clear headings, subheadings, bullet points, and numbered lists for easy reading and reference.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating study guide...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the study guide in the chat
      this.uiManager.addMessageToChat(
        `# Study Guide for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Study guide created', false, 3000);
    } catch (error) {
      console.error('Create study guide error:', error);
      this.uiManager.addMessageToChat(`Create Study Guide Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Summarize content from the current page
   * @returns {Promise<void>}
   */
  async summarizeContent() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that content summarization is starting
      this.uiManager.addMessageToChat(
        `Summarizing content from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and provide a comprehensive summary.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a content summarization prompt
      const prompt = `Please provide a comprehensive summary of the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please provide:
      1. A brief overview (1-2 paragraphs)
      2. Key points and main ideas
      3. Important details and supporting evidence
      4. Conclusions or implications

      Format the summary with clear headings and bullet points for easy reading.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Summarizing content...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the summary in the chat
      this.uiManager.addMessageToChat(
        `# Summary of: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Content summarized', false, 3000);
    } catch (error) {
      console.error('Summarize content error:', error);
      this.uiManager.addMessageToChat(`Summarize Content Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create a concept map from the current page content
   * @returns {Promise<void>}
   */
  async createConceptMap() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that concept map creation is starting
      this.uiManager.addMessageToChat(
        `Creating concept map from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and generate a concept map showing relationships between key ideas.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a concept map creation prompt
      const prompt = `Please create a concept map based on the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please create a concept map that:
      1. Identifies the key concepts and ideas
      2. Shows the relationships between these concepts
      3. Uses a hierarchical structure with the main concept at the top
      4. Includes brief descriptions of the relationships between concepts

      Since this is a text-based format, please represent the concept map as:

      1. A list of all key concepts
      2. A hierarchical outline showing the relationships
      3. A text description of how each concept relates to others

      For example:

      # Concept Map: [Main Topic]

      ## Key Concepts:
      - Concept A
      - Concept B
      - Concept C

      ## Hierarchy and Relationships:

      [Main Topic]
      ├── Concept A
      │   ├── Sub-concept A1 (relates to Concept B by...)
      │   └── Sub-concept A2
      ├── Concept B
      │   ├── Sub-concept B1
      │   └── Sub-concept B2 (influences Concept C by...)
      └── Concept C
          ├── Sub-concept C1
          └── Sub-concept C2

      ## Relationship Descriptions:
      - Concept A relates to Concept B through...
      - Concept B influences Concept C by...
      - etc.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating concept map...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the concept map in the chat
      this.uiManager.addMessageToChat(
        `# Concept Map for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Concept map created', false, 3000);
    } catch (error) {
      console.error('Create concept map error:', error);
      this.uiManager.addMessageToChat(`Create Concept Map Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create smart notes from the current page content
   * @returns {Promise<void>}
   */
  async createSmartNotes() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that smart notes creation is starting
      this.uiManager.addMessageToChat(
        `Creating smart notes from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and generate comprehensive, well-structured notes.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a smart notes creation prompt
      const prompt = `Please create comprehensive, well-structured notes based on the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please create smart notes that:
      1. Capture the key information in a concise, organized manner
      2. Use the Cornell Notes system with:
        - Main notes section
        - Cue/question column
        - Summary section
      3. Include headings, subheadings, bullet points, and numbered lists
      4. Highlight key terms and definitions
      5. Include visual cues (described in text) where appropriate

      Format the notes for maximum clarity and retention.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating smart notes...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the smart notes in the chat
      this.uiManager.addMessageToChat(
        `# Smart Notes for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Smart notes created', false, 3000);
    } catch (error) {
      console.error('Create smart notes error:', error);
      this.uiManager.addMessageToChat(`Create Smart Notes Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Organize notes
   * @returns {Promise<void>}
   */
  async organizeNotes() {
    try {
      // Prompt the user to enter or paste notes
      const userNotes = await this.uiManager.prompt(
        'Please enter or paste the notes you want to organize:',
        '',
        true
      );

      if (!userNotes) {
        throw new Error('No notes provided for organization.');
      }

      // Show a message in the chat that notes organization is starting
      this.uiManager.addMessageToChat(
        `Organizing notes...\n\n` +
        `I'll restructure and format your notes for better clarity and organization.`,
        'ai'
      );

      // Generate a notes organization prompt
      const prompt = `Please organize and improve the following notes:

      ${userNotes}

      Please:
      1. Restructure the notes into a logical hierarchy with clear headings and subheadings
      2. Format the content using bullet points, numbered lists, and indentation
      3. Highlight key terms, concepts, and definitions
      4. Add section summaries where appropriate
      5. Suggest any missing information or connections that would improve comprehension

      The goal is to transform these notes into a well-structured, easy-to-study format.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Organizing notes...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the organized notes in the chat
      this.uiManager.addMessageToChat(
        `# Organized Notes\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Notes organized', false, 3000);
    } catch (error) {
      console.error('Organize notes error:', error);
      this.uiManager.addMessageToChat(`Organize Notes Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Extract key points from the current page content
   * @returns {Promise<void>}
   */
  async extractKeyPoints() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that key points extraction is starting
      this.uiManager.addMessageToChat(
        `Extracting key points from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and extract the most important points.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a key points extraction prompt
      const prompt = `Please extract the key points from the following content:

      Title: ${pageTitle}
      URL: ${pageUrl}

      Content:
      ${pageContent.substring(0, 3000)}...

      Please:
      1. Identify and extract the 15-20 most important points
      2. Organize them by topic or theme
      3. Present them in a clear, concise format
      4. Include any critical definitions, facts, or statistics
      5. Note any important relationships between key points

      Format the key points as a well-structured list with headings for different topics or themes.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Extracting key points...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the key points in the chat
      this.uiManager.addMessageToChat(
        `# Key Points from: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Key points extracted', false, 3000);
    } catch (error) {
      console.error('Extract key points error:', error);
      this.uiManager.addMessageToChat(`Extract Key Points Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Research a topic using AI
   * @returns {Promise<void>}
   */
  async researchTopic() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that research is starting
      this.uiManager.addMessageToChat(
        `Researching topic based on current page: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and provide comprehensive research on this topic.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.apiManager.getPageContent();

      // Generate a research prompt
      const prompt = `Please conduct comprehensive research on the following topic based on this page content:

      Page Title: ${pageTitle}
      Page URL: ${pageUrl}

      Page Content Summary:
      ${pageContent.content ? pageContent.content.substring(0, 1000) : 'No content available'}...

      Please provide:
      1. A clear explanation of the main topic
      2. Key concepts and definitions
      3. Important facts and statistics
      4. Different perspectives or viewpoints
      5. Recent developments or trends
      6. Reliable sources for further reading

      Format your response as a well-structured research report with headings and bullet points where appropriate.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Generating research report...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the research in the chat
      this.uiManager.addMessageToChat(
        `# Research Report: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Research completed', false, 3000);
    } catch (error) {
      console.error('Research error:', error);
      this.uiManager.addMessageToChat(`Research Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Find sources for a topic
   * @returns {Promise<void>}
   */
  async findSources() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that source finding is starting
      this.uiManager.addMessageToChat(
        `Finding sources related to: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and suggest reliable sources for further reading.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.apiManager.getPageContent();

      // Generate a sources prompt
      const prompt = `Please find reliable sources related to the following topic:

      Page Title: ${pageTitle}
      Page URL: ${pageUrl}

      Page Content Summary:
      ${pageContent.content ? pageContent.content.substring(0, 1000) : 'No content available'}...

      Please provide:
      1. Academic journals and research papers
      2. Books by respected authors
      3. Reputable news sources and articles
      4. Official websites and reports
      5. Educational resources

      For each source, include:
      - Title/Name
      - Author(s) if applicable
      - Publication date if known
      - A brief description of what information it provides
      - Why it's relevant to this topic

      Format your response as a well-structured list with categories.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Finding sources...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the sources in the chat
      this.uiManager.addMessageToChat(
        `# Sources for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Sources found', false, 3000);
    } catch (error) {
      console.error('Find sources error:', error);
      this.uiManager.addMessageToChat(`Find Sources Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Summarize an article
   * @returns {Promise<void>}
   */
  async summarizeArticle() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that summarization is starting
      this.uiManager.addMessageToChat(
        `Summarizing article: "${pageTitle}"...\n\n` +
        `I'll analyze the article content and provide a comprehensive summary.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing article content...');
      const pageContent = await this.apiManager.getPageContent();

      // Generate a summarization prompt
      const prompt = `Please provide a comprehensive summary of the following article:

      Article Title: ${pageTitle}
      Article URL: ${pageUrl}

      Article Content:
      ${pageContent.content ? pageContent.content.substring(0, 3000) : 'No content available'}...

      Please include:
      1. A brief overview (1-2 sentences)
      2. The main points or arguments
      3. Key evidence or examples presented
      4. The author's conclusions
      5. Any limitations or considerations

      Format your response as a well-structured summary with sections.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Generating summary...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the summary in the chat
      this.uiManager.addMessageToChat(
        `# Summary of: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Article summarized', false, 3000);
    } catch (error) {
      console.error('Summarize article error:', error);
      this.uiManager.addMessageToChat(`Summarize Article Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Improve writing
   * @returns {Promise<void>}
   */
  async improveWriting() {
    try {
      // Prompt the user to enter or paste text
      const userText = await this.uiManager.prompt(
        'Please enter or paste the text you want to improve:',
        '',
        true
      );

      if (!userText) {
        throw new Error('No text provided for improvement.');
      }

      // Show a message in the chat that writing improvement is starting
      this.uiManager.addMessageToChat(
        `Improving your writing...\n\n` +
        `I'll analyze your text and suggest improvements for clarity, style, and impact.`,
        'ai'
      );

      // Generate an improvement prompt
      const prompt = `Please improve the following text to enhance its clarity, style, and impact:

      Original Text:
      "${userText}"

      Please provide:
      1. An improved version of the text
      2. Specific changes made and why
      3. Suggestions for further improvement

      Focus on:
      - Clarity and conciseness
      - Engaging language and tone
      - Proper grammar and punctuation
      - Logical flow and structure
      - Powerful word choice`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Improving writing...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the improved writing in the chat
      this.uiManager.addMessageToChat(
        `# Writing Improvement\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Writing improved', false, 3000);
    } catch (error) {
      console.error('Improve writing error:', error);
      this.uiManager.addMessageToChat(`Improve Writing Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Check grammar
   * @returns {Promise<void>}
   */
  async checkGrammar() {
    try {
      // Prompt the user to enter or paste text
      const userText = await this.uiManager.prompt(
        'Please enter or paste the text you want to check for grammar:',
        '',
        true
      );

      if (!userText) {
        throw new Error('No text provided for grammar check.');
      }

      // Show a message in the chat that grammar check is starting
      this.uiManager.addMessageToChat(
        `Checking grammar...\n\n` +
        `I'll analyze your text and identify any grammar, spelling, or punctuation issues.`,
        'ai'
      );

      // Generate a grammar check prompt
      const prompt = `Please check the following text for grammar, spelling, punctuation, and style issues:

      Text to Check:
      "${userText}"

      Please provide:
      1. A corrected version of the text
      2. A list of all errors found with explanations
      3. Suggestions for improving the overall writing style

      Be thorough and check for:
      - Grammar errors
      - Spelling mistakes
      - Punctuation issues
      - Word choice problems
      - Sentence structure issues
      - Consistency in tense, voice, and tone`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Checking grammar...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the grammar check results in the chat
      this.uiManager.addMessageToChat(
        `# Grammar Check Results\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Grammar check completed', false, 3000);
    } catch (error) {
      console.error('Grammar check error:', error);
      this.uiManager.addMessageToChat(`Grammar Check Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Paraphrase text
   * @returns {Promise<void>}
   */
  async paraphraseText() {
    try {
      // Prompt the user to enter or paste text
      const userText = await this.uiManager.prompt(
        'Please enter or paste the text you want to paraphrase:',
        '',
        true
      );

      if (!userText) {
        throw new Error('No text provided for paraphrasing.');
      }

      // Show a message in the chat that paraphrasing is starting
      this.uiManager.addMessageToChat(
        `Paraphrasing text...\n\n` +
        `I'll rewrite your text while preserving its original meaning.`,
        'ai'
      );

      // Generate a paraphrasing prompt
      const prompt = `Please paraphrase the following text while preserving its original meaning:

      Original Text:
      "${userText}"

      Please provide:
      1. A paraphrased version of the text
      2. Multiple alternative paraphrased versions (at least 2)
      3. A brief explanation of the changes made

      Ensure that the paraphrased versions:
      - Maintain the original meaning
      - Use different vocabulary and sentence structures
      - Sound natural and fluent
      - Avoid plagiarism concerns`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Paraphrasing text...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the paraphrased text in the chat
      this.uiManager.addMessageToChat(
        `# Paraphrased Text\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Text paraphrased', false, 3000);
    } catch (error) {
      console.error('Paraphrase text error:', error);
      this.uiManager.addMessageToChat(`Paraphrase Text Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Explain code
   * @returns {Promise<void>}
   */
  async explainCode() {
    try {
      // Prompt the user to enter or paste code
      const userCode = await this.uiManager.prompt(
        'Please enter or paste the code you want explained:',
        '',
        true
      );

      if (!userCode) {
        throw new Error('No code provided for explanation.');
      }

      // Show a message in the chat that code explanation is starting
      this.uiManager.addMessageToChat(
        `Explaining code...\n\n` +
        `I'll analyze your code and provide a detailed explanation of how it works.`,
        'ai'
      );

      // Generate a code explanation prompt
      const prompt = `Please explain the following code in detail:

      \`\`\`
      ${userCode}
      \`\`\`

      Please provide:
      1. A high-level overview of what the code does
      2. A line-by-line or section-by-section explanation
      3. Explanation of key functions, variables, and logic
      4. Any potential issues, edge cases, or improvements
      5. Examples of how the code might be used (if applicable)

      Make your explanation clear and accessible, assuming the reader has basic programming knowledge but may not be familiar with this specific code or language.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Explaining code...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the code explanation in the chat
      this.uiManager.addMessageToChat(
        `# Code Explanation\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Code explained', false, 3000);
    } catch (error) {
      console.error('Explain code error:', error);
      this.uiManager.addMessageToChat(`Explain Code Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Optimize code
   * @returns {Promise<void>}
   */
  async optimizeCode() {
    try {
      // Prompt the user to enter or paste code
      const userCode = await this.uiManager.prompt(
        'Please enter or paste the code you want to optimize:',
        '',
        true
      );

      if (!userCode) {
        throw new Error('No code provided for optimization.');
      }

      // Show a message in the chat that code optimization is starting
      this.uiManager.addMessageToChat(
        `Optimizing code...\n\n` +
        `I'll analyze your code and suggest optimizations for performance, readability, and best practices.`,
        'ai'
      );

      // Generate a code optimization prompt
      const prompt = `Please optimize the following code for performance, readability, and best practices:

      \`\`\`
      ${userCode}
      \`\`\`

      Please provide:
      1. An optimized version of the code
      2. Explanation of the optimizations made
      3. Performance improvements expected
      4. Readability improvements
      5. Best practices implemented

      Focus on:
      - Algorithmic efficiency
      - Memory usage
      - Code organization and structure
      - Naming conventions
      - Error handling
      - Documentation`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Optimizing code...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the optimized code in the chat
      this.uiManager.addMessageToChat(
        `# Optimized Code\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Code optimized', false, 3000);
    } catch (error) {
      console.error('Optimize code error:', error);
      this.uiManager.addMessageToChat(`Optimize Code Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Debug code
   * @returns {Promise<void>}
   */
  async debugCode() {
    try {
      // Prompt the user to enter or paste code and describe the issue
      const userCode = await this.uiManager.prompt(
        'Please enter or paste the code you want to debug:',
        '',
        true
      );

      if (!userCode) {
        throw new Error('No code provided for debugging.');
      }

      const issueDescription = await this.uiManager.prompt(
        'Please describe the issue or error you are experiencing:',
        '',
        true
      );

      // Show a message in the chat that debugging is starting
      this.uiManager.addMessageToChat(
        `Debugging code...\n\n` +
        `I'll analyze your code and help identify and fix the issues you're experiencing.`,
        'ai'
      );

      // Generate a debugging prompt
      const prompt = `Please debug the following code and help fix the issues described:

      \`\`\`
      ${userCode}
      \`\`\`

      Issue Description:
      "${issueDescription}"

      Please provide:
      1. Identification of potential bugs or issues
      2. Explanation of what's causing each issue
      3. Fixed version of the code
      4. Explanation of the fixes made
      5. Suggestions to prevent similar issues in the future

      Focus on:
      - Syntax errors
      - Logical errors
      - Runtime errors
      - Edge cases
      - Performance issues
      - Best practices`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Debugging code...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the debugging results in the chat
      this.uiManager.addMessageToChat(
        `# Debugging Results\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Debugging completed', false, 3000);
    } catch (error) {
      console.error('Debug code error:', error);
      this.uiManager.addMessageToChat(`Debug Code Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Analyze data
   * @returns {Promise<void>}
   */
  async analyzeData() {
    try {
      // Prompt the user to enter or paste data
      const userData = await this.uiManager.prompt(
        'Please enter or paste the data you want to analyze (CSV, JSON, or plain text):',
        '',
        true
      );

      if (!userData) {
        throw new Error('No data provided for analysis.');
      }

      // Show a message in the chat that data analysis is starting
      this.uiManager.addMessageToChat(
        `Analyzing data...\n\n` +
        `I'll analyze your data and provide insights and visualizations.`,
        'ai'
      );

      // Generate a data analysis prompt
      const prompt = `Please analyze the following data and provide insights:

      \`\`\`
      ${userData}
      \`\`\`

      Please provide:
      1. A summary of the data structure and content
      2. Key statistics and metrics
      3. Patterns, trends, and correlations
      4. Anomalies or outliers
      5. Visualizations (described in text)
      6. Insights and conclusions

      Focus on extracting meaningful information that would be useful for decision-making.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Analyzing data...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the data analysis in the chat
      this.uiManager.addMessageToChat(
        `# Data Analysis Results\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Data analyzed', false, 3000);
    } catch (error) {
      console.error('Analyze data error:', error);
      this.uiManager.addMessageToChat(`Analyze Data Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create chart
   * @returns {Promise<void>}
   */
  async createChart() {
    try {
      // Prompt the user to enter or paste data
      const userData = await this.uiManager.prompt(
        'Please enter or paste the data you want to visualize (CSV, JSON, or plain text):',
        '',
        true
      );

      if (!userData) {
        throw new Error('No data provided for chart creation.');
      }

      const chartType = await this.uiManager.prompt(
        'What type of chart would you like to create? (bar, line, pie, scatter, etc.)',
        'bar'
      );

      // Show a message in the chat that chart creation is starting
      this.uiManager.addMessageToChat(
        `Creating ${chartType} chart...\n\n` +
        `I'll analyze your data and provide a detailed description of how to visualize it as a ${chartType} chart.`,
        'ai'
      );

      // Generate a chart creation prompt
      const prompt = `Please create a ${chartType} chart visualization for the following data:

      \`\`\`
      ${userData}
      \`\`\`

      Please provide:
      1. A detailed description of how the chart would look
      2. The data structure needed for the chart
      3. Axis labels, titles, and legends
      4. Color schemes and styling recommendations
      5. Code example for creating this chart (using a popular library like Chart.js, D3.js, or similar)
      6. Insights that can be derived from this visualization

      Make your description detailed enough that someone could recreate this chart based on your instructions.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating chart...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the chart creation in the chat
      this.uiManager.addMessageToChat(
        `# ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart Creation\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Chart created', false, 3000);
    } catch (error) {
      console.error('Create chart error:', error);
      this.uiManager.addMessageToChat(`Create Chart Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Extract data
   * @returns {Promise<void>}
   */
  async extractData() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that data extraction is starting
      this.uiManager.addMessageToChat(
        `Extracting data from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and extract structured data.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.apiManager.getPageContent();

      // Generate a data extraction prompt
      const prompt = `Please extract structured data from the following webpage content:

      Page Title: ${pageTitle}
      Page URL: ${pageUrl}

      Page Content:
      ${pageContent.content ? pageContent.content.substring(0, 3000) : 'No content available'}...

      Please:
      1. Identify tables, lists, and other structured data on the page
      2. Extract this data into a structured format (CSV, JSON, or markdown tables)
      3. Organize the data logically
      4. Provide column/field names that accurately describe the data
      5. Clean and format the data appropriately

      If there are multiple datasets on the page, extract each one separately and label them clearly.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Extracting data...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the extracted data in the chat
      this.uiManager.addMessageToChat(
        `# Extracted Data from: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Data extracted', false, 3000);
    } catch (error) {
      console.error('Extract data error:', error);
      this.uiManager.addMessageToChat(`Extract Data Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Research a topic using AI
   * @returns {Promise<void>}
   */
  async researchTopic() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that research is starting
      this.uiManager.addMessageToChat(
        `Researching topic based on current page: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and provide comprehensive research on this topic.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a research prompt
      const prompt = `Please conduct comprehensive research on the following topic based on this page content:

      Page Title: ${pageTitle}
      Page URL: ${pageUrl}

      Page Content Summary:
      ${pageContent.substring(0, 1000)}...

      Please provide:
      1. A clear explanation of the main topic
      2. Key concepts and definitions
      3. Important facts and statistics
      4. Different perspectives or viewpoints
      5. Recent developments or trends
      6. Reliable sources for further reading

      Format your response as a well-structured research report with headings and bullet points where appropriate.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Generating research report...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the research in the chat
      this.uiManager.addMessageToChat(
        `# Research Report: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Research completed', false, 3000);
    } catch (error) {
      console.error('Research error:', error);
      this.uiManager.addMessageToChat(`Research Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Find sources for a topic
   * @returns {Promise<void>}
   */
  async findSources() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that source finding is starting
      this.uiManager.addMessageToChat(
        `Finding sources related to: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and suggest reliable sources for further reading.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a sources prompt
      const prompt = `Please find reliable sources related to the following topic:

      Page Title: ${pageTitle}
      Page URL: ${pageUrl}

      Page Content Summary:
      ${pageContent.substring(0, 1000)}...

      Please provide:
      1. Academic journals and research papers
      2. Books by respected authors
      3. Reputable news sources and articles
      4. Official websites and reports
      5. Educational resources

      For each source, include:
      - Title/Name
      - Author(s) if applicable
      - Publication date if known
      - A brief description of what information it provides
      - Why it's relevant to this topic

      Format your response as a well-structured list with categories.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Finding sources...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the sources in the chat
      this.uiManager.addMessageToChat(
        `# Sources for: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Sources found', false, 3000);
    } catch (error) {
      console.error('Find sources error:', error);
      this.uiManager.addMessageToChat(`Find Sources Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Summarize an article
   * @returns {Promise<void>}
   */
  async summarizeArticle() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that summarization is starting
      this.uiManager.addMessageToChat(
        `Summarizing article: "${pageTitle}"...\n\n` +
        `I'll analyze the article content and provide a comprehensive summary.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing article content...');
      const pageContent = await this.getPageContent();

      // Generate a summarization prompt
      const prompt = `Please provide a comprehensive summary of the following article:

      Article Title: ${pageTitle}
      Article URL: ${pageUrl}

      Article Content:
      ${pageContent.substring(0, 3000)}...

      Please include:
      1. A brief overview (1-2 sentences)
      2. The main points or arguments
      3. Key evidence or examples presented
      4. The author's conclusions
      5. Any limitations or considerations

      Format your response as a well-structured summary with sections.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Generating summary...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the summary in the chat
      this.uiManager.addMessageToChat(
        `# Summary of: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Article summarized', false, 3000);
    } catch (error) {
      console.error('Summarize article error:', error);
      this.uiManager.addMessageToChat(`Summarize Article Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Improve writing
   * @returns {Promise<void>}
   */
  async improveWriting() {
    try {
      // Prompt the user to enter or paste text
      const userText = await this.uiManager.prompt(
        'Please enter or paste the text you want to improve:',
        '',
        true
      );

      if (!userText) {
        throw new Error('No text provided for improvement.');
      }

      // Show a message in the chat that writing improvement is starting
      this.uiManager.addMessageToChat(
        `Improving your writing...\n\n` +
        `I'll analyze your text and suggest improvements for clarity, style, and impact.`,
        'ai'
      );

      // Generate an improvement prompt
      const prompt = `Please improve the following text to enhance its clarity, style, and impact:

      Original Text:
      "${userText}"

      Please provide:
      1. An improved version of the text
      2. Specific changes made and why
      3. Suggestions for further improvement

      Focus on:
      - Clarity and conciseness
      - Engaging language and tone
      - Proper grammar and punctuation
      - Logical flow and structure
      - Powerful word choice`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Improving writing...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the improved writing in the chat
      this.uiManager.addMessageToChat(
        `# Writing Improvement\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Writing improved', false, 3000);
    } catch (error) {
      console.error('Improve writing error:', error);
      this.uiManager.addMessageToChat(`Improve Writing Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Check grammar
   * @returns {Promise<void>}
   */
  async checkGrammar() {
    try {
      // Prompt the user to enter or paste text
      const userText = await this.uiManager.prompt(
        'Please enter or paste the text you want to check for grammar:',
        '',
        true
      );

      if (!userText) {
        throw new Error('No text provided for grammar check.');
      }

      // Show a message in the chat that grammar check is starting
      this.uiManager.addMessageToChat(
        `Checking grammar...\n\n` +
        `I'll analyze your text and identify any grammar, spelling, or punctuation issues.`,
        'ai'
      );

      // Generate a grammar check prompt
      const prompt = `Please check the following text for grammar, spelling, punctuation, and style issues:

      Text to Check:
      "${userText}"

      Please provide:
      1. A corrected version of the text
      2. A list of all errors found with explanations
      3. Suggestions for improving the overall writing style

      Be thorough and check for:
      - Grammar errors
      - Spelling mistakes
      - Punctuation issues
      - Word choice problems
      - Sentence structure issues
      - Consistency in tense, voice, and tone`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Checking grammar...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the grammar check results in the chat
      this.uiManager.addMessageToChat(
        `# Grammar Check Results\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Grammar check completed', false, 3000);
    } catch (error) {
      console.error('Grammar check error:', error);
      this.uiManager.addMessageToChat(`Grammar Check Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Paraphrase text
   * @returns {Promise<void>}
   */
  async paraphraseText() {
    try {
      // Prompt the user to enter or paste text
      const userText = await this.uiManager.prompt(
        'Please enter or paste the text you want to paraphrase:',
        '',
        true
      );

      if (!userText) {
        throw new Error('No text provided for paraphrasing.');
      }

      // Show a message in the chat that paraphrasing is starting
      this.uiManager.addMessageToChat(
        `Paraphrasing text...\n\n` +
        `I'll rewrite your text while preserving its original meaning.`,
        'ai'
      );

      // Generate a paraphrasing prompt
      const prompt = `Please paraphrase the following text while preserving its original meaning:

      Original Text:
      "${userText}"

      Please provide:
      1. A paraphrased version of the text
      2. Multiple alternative paraphrased versions (at least 2)
      3. A brief explanation of the changes made

      Ensure that the paraphrased versions:
      - Maintain the original meaning
      - Use different vocabulary and sentence structures
      - Sound natural and fluent
      - Avoid plagiarism concerns`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Paraphrasing text...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the paraphrased text in the chat
      this.uiManager.addMessageToChat(
        `# Paraphrased Text\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Text paraphrased', false, 3000);
    } catch (error) {
      console.error('Paraphrase text error:', error);
      this.uiManager.addMessageToChat(`Paraphrase Text Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Explain code
   * @returns {Promise<void>}
   */
  async explainCode() {
    try {
      // Prompt the user to enter or paste code
      const userCode = await this.uiManager.prompt(
        'Please enter or paste the code you want explained:',
        '',
        true
      );

      if (!userCode) {
        throw new Error('No code provided for explanation.');
      }

      // Show a message in the chat that code explanation is starting
      this.uiManager.addMessageToChat(
        `Explaining code...\n\n` +
        `I'll analyze your code and provide a detailed explanation of how it works.`,
        'ai'
      );

      // Generate a code explanation prompt
      const prompt = `Please explain the following code in detail:

      \`\`\`
      ${userCode}
      \`\`\`

      Please provide:
      1. A high-level overview of what the code does
      2. A line-by-line or section-by-section explanation
      3. Explanation of key functions, variables, and logic
      4. Any potential issues, edge cases, or improvements
      5. Examples of how the code might be used (if applicable)

      Make your explanation clear and accessible, assuming the reader has basic programming knowledge but may not be familiar with this specific code or language.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Explaining code...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the code explanation in the chat
      this.uiManager.addMessageToChat(
        `# Code Explanation\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Code explained', false, 3000);
    } catch (error) {
      console.error('Explain code error:', error);
      this.uiManager.addMessageToChat(`Explain Code Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Optimize code
   * @returns {Promise<void>}
   */
  async optimizeCode() {
    try {
      // Prompt the user to enter or paste code
      const userCode = await this.uiManager.prompt(
        'Please enter or paste the code you want to optimize:',
        '',
        true
      );

      if (!userCode) {
        throw new Error('No code provided for optimization.');
      }

      // Show a message in the chat that code optimization is starting
      this.uiManager.addMessageToChat(
        `Optimizing code...\n\n` +
        `I'll analyze your code and suggest optimizations for performance, readability, and best practices.`,
        'ai'
      );

      // Generate a code optimization prompt
      const prompt = `Please optimize the following code for performance, readability, and best practices:

      \`\`\`
      ${userCode}
      \`\`\`

      Please provide:
      1. An optimized version of the code
      2. Explanation of the optimizations made
      3. Performance improvements expected
      4. Readability improvements
      5. Best practices implemented

      Focus on:
      - Algorithmic efficiency
      - Memory usage
      - Code organization and structure
      - Naming conventions
      - Error handling
      - Documentation`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Optimizing code...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the optimized code in the chat
      this.uiManager.addMessageToChat(
        `# Optimized Code\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Code optimized', false, 3000);
    } catch (error) {
      console.error('Optimize code error:', error);
      this.uiManager.addMessageToChat(`Optimize Code Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Debug code
   * @returns {Promise<void>}
   */
  async debugCode() {
    try {
      // Prompt the user to enter or paste code and describe the issue
      const userCode = await this.uiManager.prompt(
        'Please enter or paste the code you want to debug:',
        '',
        true
      );

      if (!userCode) {
        throw new Error('No code provided for debugging.');
      }

      const issueDescription = await this.uiManager.prompt(
        'Please describe the issue or error you are experiencing:',
        '',
        true
      );

      // Show a message in the chat that debugging is starting
      this.uiManager.addMessageToChat(
        `Debugging code...\n\n` +
        `I'll analyze your code and help identify and fix the issues you're experiencing.`,
        'ai'
      );

      // Generate a debugging prompt
      const prompt = `Please debug the following code and help fix the issues described:

      \`\`\`
      ${userCode}
      \`\`\`

      Issue Description:
      "${issueDescription}"

      Please provide:
      1. Identification of potential bugs or issues
      2. Explanation of what's causing each issue
      3. Fixed version of the code
      4. Explanation of the fixes made
      5. Suggestions to prevent similar issues in the future

      Focus on:
      - Syntax errors
      - Logical errors
      - Runtime errors
      - Edge cases
      - Performance issues
      - Best practices`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Debugging code...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the debugging results in the chat
      this.uiManager.addMessageToChat(
        `# Debugging Results\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Debugging completed', false, 3000);
    } catch (error) {
      console.error('Debug code error:', error);
      this.uiManager.addMessageToChat(`Debug Code Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Analyze data
   * @returns {Promise<void>}
   */
  async analyzeData() {
    try {
      // Prompt the user to enter or paste data
      const userData = await this.uiManager.prompt(
        'Please enter or paste the data you want to analyze (CSV, JSON, or plain text):',
        '',
        true
      );

      if (!userData) {
        throw new Error('No data provided for analysis.');
      }

      // Show a message in the chat that data analysis is starting
      this.uiManager.addMessageToChat(
        `Analyzing data...\n\n` +
        `I'll analyze your data and provide insights and visualizations.`,
        'ai'
      );

      // Generate a data analysis prompt
      const prompt = `Please analyze the following data and provide insights:

      \`\`\`
      ${userData}
      \`\`\`

      Please provide:
      1. A summary of the data structure and content
      2. Key statistics and metrics
      3. Patterns, trends, and correlations
      4. Anomalies or outliers
      5. Visualizations (described in text)
      6. Insights and conclusions

      Focus on extracting meaningful information that would be useful for decision-making.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Analyzing data...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the data analysis in the chat
      this.uiManager.addMessageToChat(
        `# Data Analysis Results\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Data analyzed', false, 3000);
    } catch (error) {
      console.error('Analyze data error:', error);
      this.uiManager.addMessageToChat(`Analyze Data Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Create chart
   * @returns {Promise<void>}
   */
  async createChart() {
    try {
      // Prompt the user to enter or paste data
      const userData = await this.uiManager.prompt(
        'Please enter or paste the data you want to visualize (CSV, JSON, or plain text):',
        '',
        true
      );

      if (!userData) {
        throw new Error('No data provided for chart creation.');
      }

      const chartType = await this.uiManager.prompt(
        'What type of chart would you like to create? (bar, line, pie, scatter, etc.)',
        'bar'
      );

      // Show a message in the chat that chart creation is starting
      this.uiManager.addMessageToChat(
        `Creating ${chartType} chart...\n\n` +
        `I'll analyze your data and provide a detailed description of how to visualize it as a ${chartType} chart.`,
        'ai'
      );

      // Generate a chart creation prompt
      const prompt = `Please create a ${chartType} chart visualization for the following data:

      \`\`\`
      ${userData}
      \`\`\`

      Please provide:
      1. A detailed description of how the chart would look
      2. The data structure needed for the chart
      3. Axis labels, titles, and legends
      4. Color schemes and styling recommendations
      5. Code example for creating this chart (using a popular library like Chart.js, D3.js, or similar)
      6. Insights that can be derived from this visualization

      Make your description detailed enough that someone could recreate this chart based on your instructions.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Creating chart...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the chart creation in the chat
      this.uiManager.addMessageToChat(
        `# ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart Creation\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Chart created', false, 3000);
    } catch (error) {
      console.error('Create chart error:', error);
      this.uiManager.addMessageToChat(`Create Chart Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Extract data
   * @returns {Promise<void>}
   */
  async extractData() {
    try {
      // Get the current tab's title and URL
      const tab = await this.getCurrentTab();
      const pageTitle = tab.title || 'Unknown Page';
      const pageUrl = tab.url || 'Unknown URL';

      // Show a message in the chat that data extraction is starting
      this.uiManager.addMessageToChat(
        `Extracting data from: "${pageTitle}"...\n\n` +
        `I'll analyze the page content and extract structured data.`,
        'ai'
      );

      // Get the page content
      this.uiManager.showStatusLoading('Analyzing page content...');
      const pageContent = await this.getPageContent();

      // Generate a data extraction prompt
      const prompt = `Please extract structured data from the following webpage content:

      Page Title: ${pageTitle}
      Page URL: ${pageUrl}

      Page Content:
      ${pageContent.substring(0, 3000)}...

      Please:
      1. Identify tables, lists, and other structured data on the page
      2. Extract this data into a structured format (CSV, JSON, or markdown tables)
      3. Organize the data logically
      4. Provide column/field names that accurately describe the data
      5. Clean and format the data appropriately

      If there are multiple datasets on the page, extract each one separately and label them clearly.`;

      // Send the prompt to the AI
      this.uiManager.showStatusLoading('Extracting data...');
      const response = await this.apiManager.sendChatRequest(prompt);

      // Show the extracted data in the chat
      this.uiManager.addMessageToChat(
        `# Extracted Data from: ${pageTitle}\n\n${response}`,
        'ai'
      );

      this.uiManager.showStatus('Data extracted', false, 3000);
    } catch (error) {
      console.error('Extract data error:', error);
      this.uiManager.addMessageToChat(`Extract Data Error: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Browse to a specific website
   * @param {string} url - The URL or website name to browse to
   * @returns {Promise<boolean>} - Whether the browsing was successful
   */
  async browseWebsite(url) {
    try {
      // Normalize the input
      let inputUrl = url.trim().toLowerCase();

      // Special case for Instagram (as per user's request)
      if (inputUrl === 'instagram') {
        // Show a message that we're opening Instagram
        this.uiManager.addMessageToChat(
          `Opening Instagram website\n\n` +
          `I'm opening Instagram in a new browser tab for you.`,
          'ai'
        );

        // Try to open Instagram with different domain extensions
        return await this.tryInstagramDomains();
      }

      // Check if this is just a website name without domain (e.g., "facebook")
      const isSimpleWebsiteName = !inputUrl.includes('.') && !inputUrl.includes('/') && !inputUrl.includes(':');

      // If it's a simple website name, try to guess the full URL
      if (isSimpleWebsiteName) {
        // Show a message that we're trying to find the website
        this.uiManager.addMessageToChat(
          `Looking for website: ${inputUrl}\n\n` +
          `I'll try to find the correct website for "${inputUrl}".`,
          'ai'
        );

        // Try to browse to the website with different domain extensions
        return await this.tryMultipleDomains(inputUrl);
      }

      // Regular URL handling
      let validUrl = inputUrl;

      // Check if the URL has a protocol, if not add https://
      if (!inputUrl.startsWith('http://') && !inputUrl.startsWith('https://')) {
        validUrl = 'https://' + inputUrl;
      }

      try {
        // Test if it's a valid URL
        new URL(validUrl);
      } catch (e) {
        // If it's not a valid URL, try to browse with multiple domains
        return await this.tryMultipleDomains(inputUrl);
      }

      // Show a message in the chat that we're opening the website
      this.uiManager.addMessageToChat(
        `Opening website: ${validUrl}\n\n` +
        `I'm opening this website in a new browser tab for you.`,
        'ai'
      );

      // Open the URL in a new tab
      await chrome.tabs.create({ url: validUrl });

      // Show success status
      this.uiManager.showStatus(`Opened ${validUrl} in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Browse website error:', error);
      this.uiManager.addMessageToChat(`Error opening website: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Try to browse to Instagram with different domain extensions
   * @returns {Promise<boolean>} - Whether any attempt was successful
   */
  async tryInstagramDomains() {
    // Instagram-specific domain extensions to try
    const instagramDomains = [
      'https://www.instagram.com',
      'https://instagram.com',
      'https://www.instagram.in',
      'https://instagram.in',
      'https://www.instagram.xyz',
      'https://instagram.xyz'
    ];

    // Show status that we're trying to open Instagram
    this.uiManager.showStatus('Trying to open Instagram...', false);

    // Try each Instagram domain
    for (const domain of instagramDomains) {
      try {
        console.log(`Trying Instagram domain: ${domain}`);

        // Create a new tab with the Instagram domain
        await chrome.tabs.create({ url: domain });

        // Show success message
        this.uiManager.addMessageToChat(
          `Successfully opened Instagram at ${domain}\n\n` +
          `I've opened Instagram in a new browser tab for you.`,
          'ai'
        );

        // Show success status
        this.uiManager.showStatus(`Opened Instagram at ${domain}`, false, 3000);

        return true;
      } catch (error) {
        console.log(`Failed to open Instagram at ${domain}: ${error.message}`);
        // Continue to the next domain
      }
    }

    // If all Instagram domains fail, try a search fallback
    return await this.searchFallback('instagram');
  }

  /**
   * Try to browse to a website with multiple domain extensions
   * @param {string} websiteName - The website name to try
   * @returns {Promise<boolean>} - Whether any attempt was successful
   */
  async tryMultipleDomains(websiteName) {
    // Clean up the website name
    const cleanName = websiteName.replace(/[^a-zA-Z0-9]/g, '');

    // Check if this is a known website name
    const knownWebsite = this.getKnownWebsiteUrl(cleanName);
    if (knownWebsite) {
      try {
        // Show a message that we're opening the known website
        this.uiManager.addMessageToChat(
          `Opening website: ${knownWebsite}\n\n` +
          `I recognize "${cleanName}" as a popular website and I'm opening it for you.`,
          'ai'
        );

        // Open the known website URL in a new tab
        await chrome.tabs.create({ url: knownWebsite });

        // Show success status
        this.uiManager.showStatus(`Opened ${knownWebsite} in a new tab`, false, 3000);

        return true;
      } catch (error) {
        console.log(`Failed to open known website ${knownWebsite}: ${error.message}`);
        // Continue with the regular domain extension attempts
      }
    }

    // Common domain extensions to try
    const domainExtensions = [
      '.com', '.net', '.org', '.io', '.co', '.in', '.us', '.uk',
      '.app', '.xyz', '.me', '.info', '.biz', '.tv'
    ];

    // Show status that we're trying multiple domains
    this.uiManager.showStatus(`Trying to find the correct website for "${cleanName}"...`, false);

    // Try each domain extension
    for (const extension of domainExtensions) {
      const urlToTry = `https://${cleanName}${extension}`;

      try {
        // Try to open the URL
        console.log(`Trying URL: ${urlToTry}`);

        // Create a new tab with the URL
        await chrome.tabs.create({ url: urlToTry });

        // Show success message
        this.uiManager.addMessageToChat(
          `Opening website: ${urlToTry}\n\n` +
          `I've found a website that matches "${cleanName}" and opened it in a new tab.`,
          'ai'
        );

        // Show success status
        this.uiManager.showStatus(`Opened ${urlToTry} in a new tab`, false, 3000);

        return true;
      } catch (error) {
        console.log(`Failed to open ${urlToTry}: ${error.message}`);
        // Continue to the next domain extension
      }
    }

    // If all domain extensions fail, try a search fallback
    return await this.searchFallback(cleanName);
  }

  /**
   * Get the URL for a known website name
   * @param {string} websiteName - The website name to check
   * @returns {string|null} - The known website URL or null if not found
   */
  getKnownWebsiteUrl(websiteName) {
    // Dictionary of known website names and their URLs
    const knownWebsites = {
      // Social media
      'instagram': 'https://www.instagram.com',
      'facebook': 'https://www.facebook.com',
      'twitter': 'https://twitter.com',
      'linkedin': 'https://www.linkedin.com',
      'pinterest': 'https://www.pinterest.com',
      'tiktok': 'https://www.tiktok.com',
      'snapchat': 'https://www.snapchat.com',
      'reddit': 'https://www.reddit.com',
      'tumblr': 'https://www.tumblr.com',

      // Video platforms
      'youtube': 'https://www.youtube.com',
      'vimeo': 'https://vimeo.com',
      'twitch': 'https://www.twitch.tv',
      'dailymotion': 'https://www.dailymotion.com',

      // Shopping
      'amazon': 'https://www.amazon.com',
      'ebay': 'https://www.ebay.com',
      'walmart': 'https://www.walmart.com',
      'etsy': 'https://www.etsy.com',
      'aliexpress': 'https://www.aliexpress.com',
      'flipkart': 'https://www.flipkart.com',

      // Tech
      'google': 'https://www.google.com',
      'bing': 'https://www.bing.com',
      'yahoo': 'https://www.yahoo.com',
      'github': 'https://github.com',
      'stackoverflow': 'https://stackoverflow.com',
      'microsoft': 'https://www.microsoft.com',
      'apple': 'https://www.apple.com',

      // News
      'cnn': 'https://www.cnn.com',
      'bbc': 'https://www.bbc.com',
      'nytimes': 'https://www.nytimes.com',
      'washingtonpost': 'https://www.washingtonpost.com',
      'reuters': 'https://www.reuters.com',

      // Entertainment
      'netflix': 'https://www.netflix.com',
      'hulu': 'https://www.hulu.com',
      'disney': 'https://www.disneyplus.com',
      'spotify': 'https://www.spotify.com',
      'imdb': 'https://www.imdb.com',

      // Other popular sites
      'wikipedia': 'https://www.wikipedia.org',
      'gmail': 'https://mail.google.com',
      'outlook': 'https://outlook.live.com',
      'yahoo': 'https://mail.yahoo.com',
      'weather': 'https://weather.com',
      'maps': 'https://maps.google.com'
    };

    // Normalize the website name
    const normalizedName = websiteName.toLowerCase();

    // Check if the website name is in our dictionary
    return knownWebsites[normalizedName] || null;
  }

  /**
   * Search fallback when all domain attempts fail
   * @param {string} searchTerm - The term to search for
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchFallback(searchTerm) {
    try {
      // Create a Google search URL
      const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchTerm)}`;

      // Show a message that we're searching for the website
      this.uiManager.addMessageToChat(
        `I couldn't find a direct match for "${searchTerm}", so I'm searching for it on Google instead.\n\n` +
        `Opening search results for "${searchTerm}" in a new tab.`,
        'ai'
      );

      // Open the search URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success status
      this.uiManager.showStatus(`Opened search results for "${searchTerm}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Search fallback error:', error);
      this.uiManager.addMessageToChat(
        `I couldn't find a website matching "${searchTerm}" and also failed to search for it. Please try a different website or provide a full URL.`,
        'ai',
        'error-message'
      );
      return false;
    }
  }

  /**
   * Search YouTube for a specific query
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchYouTube(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for YouTube.');
      }

      // Create the YouTube search URL
      const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(cleanQuery)}`;

      // Show a message in the chat that we're searching YouTube
      this.uiManager.addMessageToChat(
        `Searching YouTube for: "${cleanQuery}"\n\n` +
        `I'm opening YouTube search results in a new browser tab for you.`,
        'ai'
      );

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success status
      this.uiManager.showStatus(`Opened YouTube search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('YouTube search error:', error);
      this.uiManager.addMessageToChat(`Error searching YouTube: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search Instagram for a specific profile or hashtag
   * @param {string} query - The search query (username or hashtag)
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchInstagram(query) {
    try {
      // Clean up the query
      let cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a username or hashtag to search on Instagram.');
      }

      // Remove spaces and special characters to create a valid Instagram username
      // For usernames, remove spaces and special characters
      const usernameQuery = cleanQuery.replace(/[^a-zA-Z0-9._]/g, '').toLowerCase();

      // Check if it's a hashtag search
      const isHashtag = cleanQuery.startsWith('#') || cleanQuery.toLowerCase().includes('hashtag');

      let searchUrl;
      let searchType;

      if (isHashtag) {
        // For hashtags, remove the # if present and spaces
        const hashtagQuery = cleanQuery.replace('#', '').replace(/\s+/g, '');
        searchUrl = `https://www.instagram.com/explore/tags/${encodeURIComponent(hashtagQuery)}/`;
        searchType = 'hashtag';
        cleanQuery = `#${hashtagQuery}`;
      } else {
        // For profiles, use the cleaned username
        searchUrl = `https://www.instagram.com/${encodeURIComponent(usernameQuery)}/`;
        searchType = 'profile';
        cleanQuery = usernameQuery;
      }

      // Show a message in the chat that we're searching Instagram
      this.uiManager.addMessageToChat(
        `Searching Instagram for ${searchType}: "${cleanQuery}"\n\n` +
        `I'm opening the Instagram ${searchType} in a new browser tab for you.`,
        'ai'
      );

      // Try multiple Instagram domains if needed
      const instagramDomains = [
        searchUrl,
        searchUrl.replace('www.instagram.com', 'instagram.com'),
        searchUrl.replace('www.instagram.com', 'www.instagram.in'),
        searchUrl.replace('www.instagram.com', 'instagram.in'),
        searchUrl.replace('www.instagram.com', 'www.instagram.xyz'),
        searchUrl.replace('www.instagram.com', 'instagram.xyz')
      ];

      // Show status that we're trying to search Instagram
      this.uiManager.showStatus(`Searching Instagram for ${searchType}: "${cleanQuery}"...`, false);

      // Try each Instagram domain
      for (const domain of instagramDomains) {
        try {
          console.log(`Trying Instagram domain: ${domain}`);

          // Create a new tab with the Instagram domain
          await chrome.tabs.create({ url: domain });

          // Show success message
          this.uiManager.addMessageToChat(
            `Successfully opened Instagram ${searchType} for "${cleanQuery}" at ${domain}\n\n` +
            `I've opened the Instagram ${searchType} in a new browser tab for you.`,
            'ai'
          );

          // Show success status
          this.uiManager.showStatus(`Opened Instagram ${searchType} for "${cleanQuery}"`, false, 3000);

          return true;
        } catch (error) {
          console.log(`Failed to open Instagram at ${domain}: ${error.message}`);
          // Continue to the next domain
        }
      }

      // If all Instagram domains fail, try a search fallback
      return await this.searchFallback(`instagram ${searchType} ${cleanQuery}`);
    } catch (error) {
      console.error('Instagram search error:', error);
      this.uiManager.addMessageToChat(`Error searching Instagram: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search Pinterest for inspiration and ideas
   * @param {string} query - The search query (inspiration topic)
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchPinterest(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a topic to search for on Pinterest.');
      }

      // Create the Pinterest search URL
      const searchUrl = `https://www.pinterest.com/search/pins/?q=${encodeURIComponent(cleanQuery)}`;

      // Show a message in the chat that we're searching Pinterest
      this.uiManager.addMessageToChat(
        `Searching Pinterest for: "${cleanQuery}"\n\n` +
        `I'm opening Pinterest search results in a new browser tab for you. You should find some great inspiration there!`,
        'ai'
      );

      // Show status that we're searching Pinterest
      this.uiManager.showStatus(`Searching Pinterest for "${cleanQuery}"...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened Pinterest search results for "${cleanQuery}"\n\n` +
        `I've opened Pinterest in a new browser tab for you. You should be able to find inspiration and ideas related to "${cleanQuery}" there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened Pinterest search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Pinterest search error:', error);
      this.uiManager.addMessageToChat(`Error searching Pinterest: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search X (Twitter) for users or topics
   * @param {string} query - The search query (username or topic)
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchX(query) {
    try {
      // Clean up the query
      let cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a username or topic to search on X.');
      }

      // Check if it's a username search (starts with @ or contains "user" or "account")
      const isUserSearch = cleanQuery.startsWith('@') ||
                          cleanQuery.toLowerCase().includes('user') ||
                          cleanQuery.toLowerCase().includes('account') ||
                          cleanQuery.toLowerCase().includes('profile');

      let searchUrl;
      let searchType;

      if (isUserSearch) {
        // For usernames, remove the @ if present but preserve underscores and other special characters
        // First, check if there's an underscore in the query
        if (cleanQuery.includes('_')) {
          // If there's an underscore, be careful with spaces around it
          // Replace spaces but preserve underscores
          const username = cleanQuery.replace('@', '')
            // Replace any space followed by underscore with just underscore
            .replace(/\s+_/g, '_')
            // Replace any underscore followed by space with just underscore
            .replace(/_\s+/g, '_')
            // Replace any remaining spaces
            .replace(/\s+/g, '');

          searchUrl = `https://twitter.com/${encodeURIComponent(username)}`;
          searchType = 'user';
          cleanQuery = username;
        } else {
          // If no underscore, just remove spaces as before
          const username = cleanQuery.replace('@', '').replace(/\s+/g, '');
          searchUrl = `https://twitter.com/${encodeURIComponent(username)}`;
          searchType = 'user';
          cleanQuery = username;
        }
      } else {
        // For general searches
        searchUrl = `https://twitter.com/search?q=${encodeURIComponent(cleanQuery)}`;
        searchType = 'topic';
      }

      // Show a message in the chat that we're searching X
      this.uiManager.addMessageToChat(
        `Searching X (Twitter) for ${searchType}: "${cleanQuery}"\n\n` +
        `I'm opening X (Twitter) in a new browser tab for you.`,
        'ai'
      );

      // Show status that we're searching X
      this.uiManager.showStatus(`Searching X (Twitter) for ${searchType}: "${cleanQuery}"...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened X (Twitter) ${searchType} for "${cleanQuery}"\n\n` +
        `I've opened X (Twitter) in a new browser tab for you.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened X (Twitter) ${searchType} for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('X (Twitter) search error:', error);
      this.uiManager.addMessageToChat(`Error searching X (Twitter): ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search Stack Overflow for developer issues
   * @param {string} query - The search query (technical issue or problem)
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchStackOverflow(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a technical issue to search for on Stack Overflow.');
      }

      // Create the Stack Overflow search URL
      const searchUrl = `https://stackoverflow.com/search?q=${encodeURIComponent(cleanQuery)}`;

      // Show a message in the chat that we're searching Stack Overflow
      this.uiManager.addMessageToChat(
        `Searching Stack Overflow for: "${cleanQuery}"\n\n` +
        `I'm opening Stack Overflow search results in a new browser tab for you. You might find solutions to your development issue there.`,
        'ai'
      );

      // Show status that we're searching Stack Overflow
      this.uiManager.showStatus(`Searching Stack Overflow for "${cleanQuery}"...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened Stack Overflow search results for "${cleanQuery}"\n\n` +
        `I've opened Stack Overflow in a new browser tab for you. You should be able to find solutions to your development issue there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened Stack Overflow search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Stack Overflow search error:', error);
      this.uiManager.addMessageToChat(`Error searching Stack Overflow: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Provide destress recommendations based on user preferences
   * @returns {Promise<boolean>} - Whether recommendations were provided successfully
   */
  async provideDestressRecommendations() {
    try {
      // Show a message that we're providing recommendations
      this.uiManager.showStatus('Finding relaxation recommendations...', false);

      // If we have a destress mode instance, use it to show the dialog
      if (this.uiManager.destressMode) {
        this.uiManager.destressMode.showDestressDialog();
        return true;
      } else {
        // Fallback if destress mode is not available
        this.uiManager.addMessageToChat(
          "I recommend taking a short break to relax. You could try:\n\n" +
          "1. Deep breathing exercises\n" +
          "2. Listening to calming music\n" +
          "3. Taking a short walk\n" +
          "4. Stretching for a few minutes\n" +
          "5. Practicing mindfulness meditation",
          'ai'
        );
        return true;
      }
    } catch (error) {
      console.error('Error providing destress recommendations:', error);
      this.uiManager.addMessageToChat(`Error providing recommendations: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Detect platform and search query using AI-based approach
   * @param {string} message - The user's message
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async detectPlatformAndSearch(message) {
    try {
      if (!message || typeof message !== 'string') {
        throw new Error('Please provide a valid message.');
      }

      // Log the original message for debugging
      console.log('Smart Search Beta processing message:', message);

      // Convert message to lowercase for easier matching
      const lowerMessage = message.toLowerCase();

      // Check if this is explicitly a Smart Search Beta request with expanded patterns
      const smartSearchPatterns = [
        "smart search",
        "search for me",
        "search ",
        "find ",
        "look for ",
        "look up ",
        "browse ",
        "search the web for ",
        "find information about ",
        "find info on ",
        "search online for ",
        "web search ",
        "internet search ",
        "google ",
        "search google for ",
        "can you search ",
        "can you find ",
        "please search ",
        "please find ",
        "i need to find ",
        "i want to search for ",
        "help me find ",
        "help me search for "
      ];

      const isExplicitSmartSearch = smartSearchPatterns.some(pattern =>
        lowerMessage.includes(pattern) || lowerMessage.startsWith(pattern));

      if (!isExplicitSmartSearch) {
        console.log('Not an explicit Smart Search Beta request, treating as regular chat message:', message);
        return false; // Not a Smart Search Beta request, let the AI handle it
      }

      // First, check if this is likely a search query or just a regular chat message
      // Common greetings and conversation starters should not trigger search
      const commonChatPhrases = [
        'hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening',
        'how are you', 'what\'s up', 'how\'s it going', 'nice to meet you',
        'thanks', 'thank you', 'okay', 'ok', 'sure', 'yes', 'no', 'maybe',
        'can you help', 'i need help', 'please help', 'assist me'
      ];

      // Check if the message is a common chat phrase
      for (const phrase of commonChatPhrases) {
        if (lowerMessage === phrase || lowerMessage.startsWith(phrase + ' ')) {
          console.log('Detected common chat phrase, not treating as search query:', message);
          return false; // Not a search query, let the AI handle it
        }
      }

      // Use the new searchAnyPlatform method which has more advanced platform detection
      console.log('Using enhanced platform detection with searchAnyPlatform');
      return await this.searchAnyPlatform(message);
    } catch (error) {
      console.error('Platform detection error:', error);
      this.uiManager.addMessageToChat(`Error detecting platform: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search for movies on streaming platforms
   * @param {string} query - The search query (movie or show title)
   * @param {string} platform - The streaming platform to search on (netflix, primevideo, jiohotstar)
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchMovie(query, platform = 'netflix') {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a movie or show title to search for.');
      }

      // Determine the platform and create the appropriate search URL
      let searchUrl;
      let platformName;

      switch (platform.toLowerCase()) {
        case 'netflix':
          searchUrl = `https://www.netflix.com/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Netflix';
          break;
        case 'primevideo':
        case 'prime':
        case 'amazon':
        case 'amazonprime':
          searchUrl = `https://www.primevideo.com/search/ref=atv_sr_sug_7?phrase=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Prime Video';
          break;
        case 'jiohotstar':
        case 'hotstar':
        case 'jio':
        case 'disney+hotstar':
        case 'disneyhotstar':
          searchUrl = `https://www.hotstar.com/in/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Disney+ Hotstar';
          break;
        case 'hulu':
          searchUrl = `https://www.hulu.com/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Hulu';
          break;
        case 'disneyplus':
        case 'disney+':
        case 'disney':
          searchUrl = `https://www.disneyplus.com/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Disney+';
          break;
        case 'hbomax':
        case 'hbo':
        case 'max':
          searchUrl = `https://www.max.com/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Max (HBO)';
          break;
        case 'appletv':
        case 'appletv+':
        case 'apple':
          searchUrl = `https://tv.apple.com/search?term=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Apple TV+';
          break;
        default:
          // Try to search on multiple platforms if platform is not specified
          if (platform === 'all') {
            // Open multiple tabs for different platforms
            const platforms = [
              { url: `https://www.netflix.com/search?q=${encodeURIComponent(cleanQuery)}`, name: 'Netflix' },
              { url: `https://www.primevideo.com/search/ref=atv_sr_sug_7?phrase=${encodeURIComponent(cleanQuery)}`, name: 'Prime Video' },
              { url: `https://www.hotstar.com/in/search?q=${encodeURIComponent(cleanQuery)}`, name: 'Disney+ Hotstar' }
            ];

            // Show a message in the chat that we're searching on multiple platforms
            this.uiManager.addMessageToChat(
              `Searching for "${cleanQuery}" on multiple streaming platforms\n\n` +
              `I'm opening search results in new browser tabs for you.`,
              'ai'
            );

            // Show status that we're searching on multiple platforms
            this.uiManager.showStatus(`Searching for "${cleanQuery}" on multiple streaming platforms...`, false);

            // Open URLs in new tabs
            for (const platform of platforms) {
              await chrome.tabs.create({ url: platform.url });
            }

            // Show success message
            this.uiManager.addMessageToChat(
              `Successfully opened search results for "${cleanQuery}" on multiple streaming platforms\n\n` +
              `I've opened Netflix, Prime Video, and Disney+ Hotstar in new browser tabs for you. You should be able to find "${cleanQuery}" on one of these platforms.`,
              'ai'
            );

            // Show success status
            this.uiManager.showStatus(`Opened search for "${cleanQuery}" on multiple streaming platforms`, false, 3000);

            return true;
          } else {
            // Default to Netflix if platform is not recognized
            searchUrl = `https://www.netflix.com/search?q=${encodeURIComponent(cleanQuery)}`;
            platformName = 'Netflix';
          }
      }

      // Show a message in the chat that we're searching for the movie
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on ${platformName}\n\n` +
        `I'm opening ${platformName} search results in a new browser tab for you.`,
        'ai'
      );

      // Show status that we're searching for the movie
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on ${platformName}...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened ${platformName} search results for "${cleanQuery}"\n\n` +
        `I've opened ${platformName} in a new browser tab for you. You should be able to find "${cleanQuery}" there if it's available.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened ${platformName} search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Movie search error:', error);
      this.uiManager.addMessageToChat(`Error searching for movie: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search for music on various music platforms
   * @param {string} query - The search query (song, artist, album)
   * @param {string} platform - The music platform to search on (spotify, apple, youtube)
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchMusic(query, platform = 'spotify') {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a song, artist, or album to search for.');
      }

      // Determine the platform and create the appropriate search URL
      let searchUrl;
      let platformName;

      switch (platform.toLowerCase()) {
        case 'spotify':
          searchUrl = `https://open.spotify.com/search/${encodeURIComponent(cleanQuery)}`;
          platformName = 'Spotify';
          break;
        case 'apple':
          searchUrl = `https://music.apple.com/us/search?term=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Apple Music';
          break;
        case 'youtube':
          searchUrl = `https://music.youtube.com/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'YouTube Music';
          break;
        case 'soundcloud':
          searchUrl = `https://soundcloud.com/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'SoundCloud';
          break;
        case 'deezer':
          searchUrl = `https://www.deezer.com/search/${encodeURIComponent(cleanQuery)}`;
          platformName = 'Deezer';
          break;
        case 'tidal':
          searchUrl = `https://listen.tidal.com/search?q=${encodeURIComponent(cleanQuery)}`;
          platformName = 'Tidal';
          break;
        default:
          searchUrl = `https://open.spotify.com/search/${encodeURIComponent(cleanQuery)}`;
          platformName = 'Spotify';
      }

      // Show a message in the chat that we're searching for music
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on ${platformName}\n\n` +
        `I'm opening ${platformName} search results in a new browser tab for you.`,
        'ai'
      );

      // Show status that we're searching for music
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on ${platformName}...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened ${platformName} search results for "${cleanQuery}"\n\n` +
        `I've opened ${platformName} in a new browser tab for you. You should be able to play "${cleanQuery}" there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened ${platformName} search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Music search error:', error);
      this.uiManager.addMessageToChat(`Error searching for music: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on any platform based on platform detection
   * @param {string} userInput - The user's message
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchAnyPlatform(userInput) {
    try {
      // Log the user input for debugging
      console.log('Smart Search Beta processing input:', userInput);

      // Check if the top websites data is available
      if (window.TOP_WEBSITES && window.EnhancedWebsiteNLPManager) {
        // Use the enhanced website NLP manager for better platform detection
        console.log('Using EnhancedWebsiteNLPManager for platform detection');
        const enhancedNlpManager = new EnhancedWebsiteNLPManager(this.apiManager);
        const platformInfo = enhancedNlpManager.detectPlatformAndSearch(userInput);

        console.log('Enhanced platform detection result:', platformInfo);

        // If we have a URL, open it directly
        if (platformInfo.url) {
          // Show a message in the chat
          this.uiManager.addMessageToChat(
            `Searching for "${platformInfo.processedQuery}" on ${platformInfo.platform}\n\n` +
            `I'm opening search results in a new browser tab for you.`,
            'ai'
          );

          // Show status
          this.uiManager.showStatus(`Searching for "${platformInfo.processedQuery}" on ${platformInfo.platform}...`, false);

          // Open the URL in a new tab
          await chrome.tabs.create({ url: platformInfo.url });

          // Show success message
          this.uiManager.addMessageToChat(
            `Successfully opened search results for "${platformInfo.processedQuery}" on ${platformInfo.platform}\n\n` +
            `I've opened the search results in a new browser tab for you.`,
            'ai'
          );

          // Show success status
          this.uiManager.showStatus(`Opened search for "${platformInfo.processedQuery}" on ${platformInfo.platform} in a new tab`, false, 3000);

          return true;
        }
      }

      // Fall back to the original NLP manager if enhanced one is not available or didn't return a URL
      const nlpManager = new ImprovedNLPManager(this.apiManager);
      const platformInfo = nlpManager.detectPlatformAndSearch(userInput);

      console.log('Platform detection result:', platformInfo);

      // If we couldn't detect a platform or query with sufficient confidence, provide feedback
      if (!platformInfo.platform || !platformInfo.query || platformInfo.confidence < 0.5) {
        console.log('Insufficient confidence in platform detection, providing feedback to user');

        // Provide helpful feedback to the user
        this.uiManager.addMessageToChat(
          `I'm not sure what you're looking for. Please try being more specific about what you want to search for and where.\n\n` +
          `For example:\n` +
          `- "Search for shoes on Amazon"\n` +
          `- "Find iPhone cases on Flipkart"\n` +
          `- "Look for JavaScript tutorials on YouTube"\n` +
          `- "Search for profile pictures on Instagram"`,
          'ai'
        );

        return false;
      }

      // If we have a platform and query with good confidence, search on that platform
      if (platformInfo.platform && platformInfo.query && platformInfo.confidence > 0.5) {
        // Map the platform to the appropriate search method
        const platformMap = {
          // Social Media
          'youtube': this.searchYouTube,
          'instagram': this.searchInstagram,
          'twitter': this.searchX,
          'pinterest': this.searchPinterest,
          'linkedin': this.searchLinkedin,
          'facebook': this.searchFacebook,
          'github': this.searchGithub,
          'reddit': (query) => this.searchFallback('reddit', query),
          'tiktok': (query) => this.searchFallback('tiktok', query),
          'snapchat': (query) => this.searchFallback('snapchat', query),
          'threads': (query) => this.searchFallback('threads', query),
          'discord': (query) => this.searchFallback('discord', query),
          'telegram': (query) => this.searchFallback('telegram', query),
          'whatsapp': (query) => this.searchFallback('whatsapp', query),

          // Professional & Development
          'github': this.searchGithub,
          'stackoverflow': this.searchStackOverflow,
          'medium': (query) => this.searchFallback('medium', query),
          'dev.to': (query) => this.searchFallback('dev.to', query),
          'gitlab': (query) => this.searchFallback('gitlab', query),
          'bitbucket': (query) => this.searchFallback('bitbucket', query),
          'jira': (query) => this.searchFallback('jira', query),
          'trello': (query) => this.searchFallback('trello', query),
          'notion': (query) => this.searchFallback('notion', query),

          // E-commerce
          'amazon': (query) => this.searchEcommerce('amazon', query),
          'ebay': (query) => this.searchEcommerce('ebay', query),
          'etsy': (query) => this.searchEcommerce('etsy', query),
          'walmart': (query) => this.searchEcommerce('walmart', query),
          'aliexpress': (query) => this.searchEcommerce('aliexpress', query),
          'flipkart': (query) => this.searchEcommerce('flipkart', query),
          'bestbuy': (query) => this.searchEcommerce('bestbuy', query),
          'target': (query) => this.searchEcommerce('target', query),
          'shopify': (query) => this.searchEcommerce('shopify', query),
          'wayfair': (query) => this.searchEcommerce('wayfair', query),
          'ikea': (query) => this.searchEcommerce('ikea', query),
          'myntra': (query) => this.searchEcommerce('myntra', query),
          'ajio': (query) => this.searchEcommerce('ajio', query),
          'snapdeal': (query) => this.searchEcommerce('snapdeal', query),
          'paytmmall': (query) => this.searchEcommerce('paytmmall', query),
          'meesho': (query) => this.searchEcommerce('meesho', query),
          'nykaa': (query) => this.searchEcommerce('nykaa', query),
          'tatacliq': (query) => this.searchEcommerce('tatacliq', query),
          'croma': (query) => this.searchEcommerce('croma', query),
          'reliancedigital': (query) => this.searchEcommerce('reliancedigital', query),
          'homedepot': (query) => this.searchEcommerce('homedepot', query),
          'lowes': (query) => this.searchEcommerce('lowes', query),
          'costco': (query) => this.searchEcommerce('costco', query),
          'samsclub': (query) => this.searchEcommerce('samsclub', query),
          'macys': (query) => this.searchEcommerce('macys', query),
          'kohls': (query) => this.searchEcommerce('kohls', query),
          'jcpenney': (query) => this.searchEcommerce('jcpenney', query),
          'newegg': (query) => this.searchEcommerce('newegg', query),
          'overstock': (query) => this.searchEcommerce('overstock', query),
          'zappos': (query) => this.searchEcommerce('zappos', query),
          'nordstrom': (query) => this.searchEcommerce('nordstrom', query),
          'sephora': (query) => this.searchEcommerce('sephora', query),
          'ulta': (query) => this.searchEcommerce('ulta', query),

          // Music
          'spotify': this.searchMusic,
          'apple music': (query) => this.searchMusic(query, 'apple'),
          'youtube music': (query) => this.searchMusic(query, 'youtube'),
          'soundcloud': (query) => this.searchMusic(query, 'soundcloud'),
          'deezer': (query) => this.searchMusic(query, 'deezer'),
          'tidal': (query) => this.searchMusic(query, 'tidal'),
          'pandora': (query) => this.searchFallback('pandora', query),
          'bandcamp': (query) => this.searchFallback('bandcamp', query),

          // Streaming
          'netflix': this.searchNetflix,
          'prime video': this.searchPrimeVideo,
          'hulu': (query) => this.searchMovie(query, 'hulu'),
          'disney plus': (query) => this.searchMovie(query, 'disneyplus'),
          'hbo max': (query) => this.searchMovie(query, 'hbomax'),
          'apple tv': (query) => this.searchMovie(query, 'appletv'),
          'peacock': (query) => this.searchFallback('peacock', query),
          'paramount plus': (query) => this.searchFallback('paramount plus', query),
          'crunchyroll': (query) => this.searchFallback('crunchyroll', query),
          'funimation': (query) => this.searchFallback('funimation', query),

          // Design & Creative
          'dribbble': this.searchDribbble,
          'behance': (query) => this.searchFallback('behance', query),
          'unsplash': (query) => this.searchFallback('unsplash', query),
          'figma': (query) => this.searchFallback('figma', query),
          'canva': (query) => this.searchFallback('canva', query),
          'adobe': (query) => this.searchFallback('adobe', query),
          'sketch': (query) => this.searchFallback('sketch', query),
          'invision': (query) => this.searchFallback('invision', query),
          'shutterstock': (query) => this.searchFallback('shutterstock', query),
          'getty images': (query) => this.searchFallback('getty images', query),

          // News & Information
          'wikipedia': (query) => this.searchFallback('wikipedia', query),
          'news': (query) => this.searchFallback('news', query),
          'google news': (query) => this.searchFallback('google news', query),
          'bbc': (query) => this.searchFallback('bbc', query),
          'cnn': (query) => this.searchFallback('cnn', query),
          'nytimes': (query) => this.searchFallback('nytimes', query),
          'reuters': (query) => this.searchFallback('reuters', query),
          'ap news': (query) => this.searchFallback('ap news', query),

          // Travel & Maps
          'google maps': (query) => this.searchFallback('google maps', query),
          'airbnb': (query) => this.searchFallback('airbnb', query),
          'booking.com': (query) => this.searchFallback('booking.com', query),
          'expedia': (query) => this.searchFallback('expedia', query),
          'tripadvisor': (query) => this.searchFallback('tripadvisor', query),
          'kayak': (query) => this.searchFallback('kayak', query),
          'waze': (query) => this.searchFallback('waze', query),

          // General
          'google': this.searchGoogle,
          'bing': (query) => this.searchFallback('bing', query),
          'duckduckgo': (query) => this.searchFallback('duckduckgo', query),
          'yahoo': (query) => this.searchFallback('yahoo', query)
        };

        // Get the search method for the detected platform
        const searchMethod = platformMap[platformInfo.platform];

        // If we have a search method for this platform, use it
        if (searchMethod) {
          return await searchMethod(platformInfo.query);
        } else {
          // If we don't have a specific method, use the fallback
          return await this.searchFallback(platformInfo.platform, platformInfo.query);
        }
      } else {
        // If we couldn't detect a platform or query, use the fallback
        return await this.searchFallback(null, userInput);
      }
    } catch (error) {
      console.error('Error in searchAnyPlatform:', error);
      this.uiManager.addMessageToChat(`Error searching: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on LinkedIn
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchLinkedin(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for LinkedIn.');
      }

      // Determine if this is a profile search or a general search
      const isProfileSearch = cleanQuery.toLowerCase().includes('profile') ||
                             cleanQuery.toLowerCase().includes('person') ||
                             cleanQuery.toLowerCase().includes('user');

      // Create the appropriate search URL
      let searchUrl;
      let searchType;

      if (isProfileSearch) {
        // Extract the name from the query
        const nameMatch = cleanQuery.match(/(?:profile|person|user|find|search|lookup)\s+(?:of|for|about)?\s+([^]+)/i);
        const name = nameMatch ? nameMatch[1].trim() : cleanQuery;

        searchUrl = `https://www.linkedin.com/search/results/people/?keywords=${encodeURIComponent(name)}`;
        searchType = 'profile';
      } else if (cleanQuery.toLowerCase().includes('job') || cleanQuery.toLowerCase().includes('career')) {
        // Job search
        const jobMatch = cleanQuery.match(/(?:job|career|position|opening|vacancy)\s+(?:for|as|in)?\s+([^]+)/i);
        const jobTitle = jobMatch ? jobMatch[1].trim() : cleanQuery;

        searchUrl = `https://www.linkedin.com/jobs/search/?keywords=${encodeURIComponent(jobTitle)}`;
        searchType = 'job';
      } else if (cleanQuery.toLowerCase().includes('company') || cleanQuery.toLowerCase().includes('organization')) {
        // Company search
        const companyMatch = cleanQuery.match(/(?:company|organization|business|firm)\s+(?:called|named|like)?\s+([^]+)/i);
        const companyName = companyMatch ? companyMatch[1].trim() : cleanQuery;

        searchUrl = `https://www.linkedin.com/search/results/companies/?keywords=${encodeURIComponent(companyName)}`;
        searchType = 'company';
      } else {
        // General search
        searchUrl = `https://www.linkedin.com/search/results/all/?keywords=${encodeURIComponent(cleanQuery)}`;
        searchType = 'general';
      }

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on LinkedIn\n\n` +
        `I'm opening LinkedIn search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on LinkedIn...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened LinkedIn search results for "${cleanQuery}"`;

      if (searchType === 'profile') {
        successMessage += `\n\nI've opened LinkedIn people search in a new browser tab. You should be able to find profiles matching "${cleanQuery}" there.`;
      } else if (searchType === 'job') {
        successMessage += `\n\nI've opened LinkedIn job search in a new browser tab. You should be able to find job listings for "${cleanQuery}" there.`;
      } else if (searchType === 'company') {
        successMessage += `\n\nI've opened LinkedIn company search in a new browser tab. You should be able to find companies matching "${cleanQuery}" there.`;
      } else {
        successMessage += `\n\nI've opened LinkedIn search in a new browser tab. You should be able to find results for "${cleanQuery}" there.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened LinkedIn search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('LinkedIn search error:', error);
      this.uiManager.addMessageToChat(`Error searching LinkedIn: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Facebook
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchFacebook(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Facebook.');
      }

      // Determine if this is a profile search, page search, or general search
      const isProfileSearch = cleanQuery.toLowerCase().includes('profile') ||
                             cleanQuery.toLowerCase().includes('person') ||
                             cleanQuery.toLowerCase().includes('user');

      const isPageSearch = cleanQuery.toLowerCase().includes('page') ||
                          cleanQuery.toLowerCase().includes('business') ||
                          cleanQuery.toLowerCase().includes('company');

      const isGroupSearch = cleanQuery.toLowerCase().includes('group') ||
                           cleanQuery.toLowerCase().includes('community');

      // Create the appropriate search URL
      let searchUrl;
      let searchType;

      if (isProfileSearch) {
        // Extract the name from the query
        const nameMatch = cleanQuery.match(/(?:profile|person|user|find|search|lookup)\s+(?:of|for|about)?\s+([^]+)/i);
        const name = nameMatch ? nameMatch[1].trim() : cleanQuery;

        searchUrl = `https://www.facebook.com/search/people/?q=${encodeURIComponent(name)}`;
        searchType = 'profile';
      } else if (isPageSearch) {
        // Extract the page name from the query
        const pageMatch = cleanQuery.match(/(?:page|business|company)\s+(?:called|named|for|about)?\s+([^]+)/i);
        const pageName = pageMatch ? pageMatch[1].trim() : cleanQuery;

        searchUrl = `https://www.facebook.com/search/pages/?q=${encodeURIComponent(pageName)}`;
        searchType = 'page';
      } else if (isGroupSearch) {
        // Extract the group name from the query
        const groupMatch = cleanQuery.match(/(?:group|community)\s+(?:called|named|for|about)?\s+([^]+)/i);
        const groupName = groupMatch ? groupMatch[1].trim() : cleanQuery;

        searchUrl = `https://www.facebook.com/search/groups/?q=${encodeURIComponent(groupName)}`;
        searchType = 'group';
      } else {
        // General search
        searchUrl = `https://www.facebook.com/search/top/?q=${encodeURIComponent(cleanQuery)}`;
        searchType = 'general';
      }

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Facebook\n\n` +
        `I'm opening Facebook search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Facebook...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened Facebook search results for "${cleanQuery}"`;

      if (searchType === 'profile') {
        successMessage += `\n\nI've opened Facebook people search in a new browser tab. You should be able to find profiles matching "${cleanQuery}" there.`;
      } else if (searchType === 'page') {
        successMessage += `\n\nI've opened Facebook page search in a new browser tab. You should be able to find pages matching "${cleanQuery}" there.`;
      } else if (searchType === 'group') {
        successMessage += `\n\nI've opened Facebook group search in a new browser tab. You should be able to find groups matching "${cleanQuery}" there.`;
      } else {
        successMessage += `\n\nI've opened Facebook search in a new browser tab. You should be able to find results for "${cleanQuery}" there.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened Facebook search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Facebook search error:', error);
      this.uiManager.addMessageToChat(`Error searching Facebook: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on GitHub
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchGithub(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for GitHub.');
      }

      // Determine if this is a repository search, user search, or code search
      const isRepoSearch = cleanQuery.toLowerCase().includes('repo') ||
                          cleanQuery.toLowerCase().includes('repository') ||
                          cleanQuery.toLowerCase().includes('project');

      const isUserSearch = cleanQuery.toLowerCase().includes('user') ||
                          cleanQuery.toLowerCase().includes('profile') ||
                          cleanQuery.toLowerCase().includes('developer');

      const isCodeSearch = cleanQuery.toLowerCase().includes('code') ||
                          cleanQuery.toLowerCase().includes('file') ||
                          cleanQuery.toLowerCase().includes('implementation');

      // Check for direct repository access pattern (username/repository)
      const isDirectRepoAccess = /^[\w\-\.]+\/[\w\-\.]+$/.test(cleanQuery);

      // Create the appropriate search URL
      let searchUrl;
      let searchType;

      if (isDirectRepoAccess) {
        // Direct repository access (username/repository format)
        searchUrl = `https://github.com/${cleanQuery}`;
        searchType = 'direct repository';
      } else if (isRepoSearch) {
        // Extract the repository name from the query
        const repoMatch = cleanQuery.match(/(?:repo|repository|project)\s+(?:called|named|for|about)?\s+([^]+)/i);
        const repoName = repoMatch ? repoMatch[1].trim() : cleanQuery;

        // Check if the repo name contains a slash (username/repo format)
        if (repoName.includes('/') && /^[\w\-\.]+\/[\w\-\.]+$/.test(repoName)) {
          searchUrl = `https://github.com/${repoName}`;
          searchType = 'specific repository';
        } else {
          searchUrl = `https://github.com/search?q=${encodeURIComponent(repoName)}&type=repositories`;
          searchType = 'repository';
        }
      } else if (isUserSearch) {
        // Extract the username from the query
        const userMatch = cleanQuery.match(/(?:user|profile|developer)\s+(?:called|named|for|about)?\s+([^]+)/i);
        const userName = userMatch ? userMatch[1].trim() : cleanQuery;

        searchUrl = `https://github.com/search?q=${encodeURIComponent(userName)}&type=users`;
        searchType = 'user';
      } else if (isCodeSearch) {
        // Extract the code query from the query
        const codeMatch = cleanQuery.match(/(?:code|file|implementation)\s+(?:for|of|about)?\s+([^]+)/i);
        const codeQuery = codeMatch ? codeMatch[1].trim() : cleanQuery;

        searchUrl = `https://github.com/search?q=${encodeURIComponent(codeQuery)}&type=code`;
        searchType = 'code';
      } else {
        // General search
        searchUrl = `https://github.com/search?q=${encodeURIComponent(cleanQuery)}`;
        searchType = 'general';
      }

      // Show a message in the chat
      if (searchType === 'direct repository' || searchType === 'specific repository') {
        this.uiManager.addMessageToChat(
          `Opening GitHub repository "${cleanQuery}"\n\n` +
          `I'm opening this specific GitHub repository in a new browser tab for you.`,
          'ai'
        );
      } else {
        this.uiManager.addMessageToChat(
          `Searching for "${cleanQuery}" on GitHub\n\n` +
          `I'm opening GitHub search results in a new browser tab for you.`,
          'ai'
        );
      }

      // Show status
      if (searchType === 'direct repository' || searchType === 'specific repository') {
        this.uiManager.showStatus(`Opening GitHub repository "${cleanQuery}"...`, false);
      } else {
        this.uiManager.showStatus(`Searching for "${cleanQuery}" on GitHub...`, false);
      }

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened GitHub search results for "${cleanQuery}"`;

      if (searchType === 'direct repository' || searchType === 'specific repository') {
        successMessage = `Successfully opened GitHub repository "${cleanQuery}"`;
        successMessage += `\n\nI've opened the specific GitHub repository in a new browser tab. You can now browse the code, issues, pull requests, and other repository information.`;
      } else if (searchType === 'repository') {
        successMessage += `\n\nI've opened GitHub repository search in a new browser tab. You should be able to find repositories matching "${cleanQuery}" there.`;
      } else if (searchType === 'user') {
        successMessage += `\n\nI've opened GitHub user search in a new browser tab. You should be able to find users matching "${cleanQuery}" there.`;
      } else if (searchType === 'code') {
        successMessage += `\n\nI've opened GitHub code search in a new browser tab. You should be able to find code matching "${cleanQuery}" there.`;
      } else {
        successMessage += `\n\nI've opened GitHub search in a new browser tab. You should be able to find results for "${cleanQuery}" there.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      if (searchType === 'direct repository' || searchType === 'specific repository') {
        this.uiManager.showStatus(`Opened GitHub repository "${cleanQuery}" in a new tab`, false, 3000);
      } else {
        this.uiManager.showStatus(`Opened GitHub search for "${cleanQuery}" in a new tab`, false, 3000);
      }

      return true;
    } catch (error) {
      console.error('GitHub search error:', error);
      this.uiManager.addMessageToChat(`Error searching GitHub: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Dribbble
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchDribbble(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Dribbble.');
      }

      // Determine if this is a designer search, shot search, or general search
      const isDesignerSearch = cleanQuery.toLowerCase().includes('designer') ||
                              cleanQuery.toLowerCase().includes('profile') ||
                              cleanQuery.toLowerCase().includes('portfolio');

      const isShotSearch = cleanQuery.toLowerCase().includes('shot') ||
                          cleanQuery.toLowerCase().includes('design') ||
                          cleanQuery.toLowerCase().includes('artwork');

      // Create the appropriate search URL
      let searchUrl;
      let searchType;

      if (isDesignerSearch) {
        // Extract the designer name from the query
        const designerMatch = cleanQuery.match(/(?:designer|profile|portfolio)\s+(?:of|for|by|about)?\s+([^]+)/i);
        const designerName = designerMatch ? designerMatch[1].trim() : cleanQuery;

        searchUrl = `https://dribbble.com/search/users/${encodeURIComponent(designerName)}`;
        searchType = 'designer';
      } else if (isShotSearch) {
        // Extract the shot description from the query
        const shotMatch = cleanQuery.match(/(?:shot|design|artwork)\s+(?:of|for|about)?\s+([^]+)/i);
        const shotDescription = shotMatch ? shotMatch[1].trim() : cleanQuery;

        searchUrl = `https://dribbble.com/search/shots/${encodeURIComponent(shotDescription)}`;
        searchType = 'shot';
      } else {
        // General search
        searchUrl = `https://dribbble.com/search/${encodeURIComponent(cleanQuery)}`;
        searchType = 'general';
      }

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Dribbble\n\n` +
        `I'm opening Dribbble search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Dribbble...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened Dribbble search results for "${cleanQuery}"`;

      if (searchType === 'designer') {
        successMessage += `\n\nI've opened Dribbble designer search in a new browser tab. You should be able to find designers matching "${cleanQuery}" there.`;
      } else if (searchType === 'shot') {
        successMessage += `\n\nI've opened Dribbble shot search in a new browser tab. You should be able to find designs matching "${cleanQuery}" there.`;
      } else {
        successMessage += `\n\nI've opened Dribbble search in a new browser tab. You should be able to find design inspiration for "${cleanQuery}" there.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened Dribbble search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Dribbble search error:', error);
      this.uiManager.addMessageToChat(`Error searching Dribbble: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Google (fallback search method)
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchGoogle(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term.');
      }

      // Create the search URL
      const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}`;

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Google\n\n` +
        `I'm opening Google search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Google...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened Google search results for "${cleanQuery}"\n\n` +
        `I've opened Google search in a new browser tab. You should be able to find information about "${cleanQuery}" there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened Google search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Google search error:', error);
      this.uiManager.addMessageToChat(`Error searching Google: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search for music on various music platforms
   * @param {string} query - The search query
   * @param {string} platform - The music platform to search on (default: 'spotify')
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchMusic(query, platform = 'spotify') {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error(`Please provide a search term for music.`);
      }

      // Define platform-specific URLs and names
      const platformInfo = {
        'spotify': {
          url: `https://open.spotify.com/search/${encodeURIComponent(cleanQuery)}`,
          name: 'Spotify'
        },
        'apple': {
          url: `https://music.apple.com/us/search?term=${encodeURIComponent(cleanQuery)}`,
          name: 'Apple Music'
        },
        'youtube': {
          url: `https://music.youtube.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'YouTube Music'
        },
        'soundcloud': {
          url: `https://soundcloud.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'SoundCloud'
        },
        'deezer': {
          url: `https://www.deezer.com/search/${encodeURIComponent(cleanQuery)}`,
          name: 'Deezer'
        },
        'tidal': {
          url: `https://listen.tidal.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Tidal'
        },
        'pandora': {
          url: `https://www.pandora.com/search/${encodeURIComponent(cleanQuery)}/all`,
          name: 'Pandora'
        },
        'bandcamp': {
          url: `https://bandcamp.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Bandcamp'
        }
      };

      // Get the platform info or use a fallback
      const info = platformInfo[platform] || {
        url: `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}+${encodeURIComponent(platform)}+music`,
        name: platform.charAt(0).toUpperCase() + platform.slice(1) // Capitalize first letter
      };

      // Determine if this is an artist search, album search, or song search
      const isArtistSearch = cleanQuery.toLowerCase().includes('artist') ||
                            cleanQuery.toLowerCase().includes('band') ||
                            cleanQuery.toLowerCase().includes('musician');

      const isAlbumSearch = cleanQuery.toLowerCase().includes('album') ||
                           cleanQuery.toLowerCase().includes('record') ||
                           cleanQuery.toLowerCase().includes('ep');

      const isSongSearch = cleanQuery.toLowerCase().includes('song') ||
                          cleanQuery.toLowerCase().includes('track') ||
                          cleanQuery.toLowerCase().includes('single');

      // Create a more specific message based on the search type
      let searchType = 'music';
      if (isArtistSearch) {
        searchType = 'artist';
      } else if (isAlbumSearch) {
        searchType = 'album';
      } else if (isSongSearch) {
        searchType = 'song';
      }

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on ${info.name}\n\n` +
        `I'm opening ${info.name} search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on ${info.name}...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: info.url });

      // Show success message
      let successMessage = `Successfully opened ${info.name} search results for "${cleanQuery}"`;

      if (searchType === 'artist') {
        successMessage += `\n\nI've opened ${info.name} search in a new browser tab. You should be able to find artists matching "${cleanQuery}" there.`;
      } else if (searchType === 'album') {
        successMessage += `\n\nI've opened ${info.name} search in a new browser tab. You should be able to find albums matching "${cleanQuery}" there.`;
      } else if (searchType === 'song') {
        successMessage += `\n\nI've opened ${info.name} search in a new browser tab. You should be able to find songs matching "${cleanQuery}" there.`;
      } else {
        successMessage += `\n\nI've opened ${info.name} search in a new browser tab. You should be able to find music related to "${cleanQuery}" there.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened ${info.name} search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error(`Music search error (${platform}):`, error);
      this.uiManager.addMessageToChat(`Error searching for music: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Netflix
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchNetflix(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Netflix.');
      }

      // Determine if this is a specific type of search
      const isMovieSearch = cleanQuery.toLowerCase().includes('movie') ||
                           cleanQuery.toLowerCase().includes('film');

      const isShowSearch = cleanQuery.toLowerCase().includes('show') ||
                          cleanQuery.toLowerCase().includes('series') ||
                          cleanQuery.toLowerCase().includes('tv');

      const isGenreSearch = cleanQuery.toLowerCase().includes('genre') ||
                           cleanQuery.toLowerCase().includes('category') ||
                           /\b(action|comedy|drama|horror|thriller|documentary|sci-fi|fantasy|romance|animation)\b/i.test(cleanQuery);

      const isActorSearch = cleanQuery.toLowerCase().includes('actor') ||
                           cleanQuery.toLowerCase().includes('actress') ||
                           cleanQuery.toLowerCase().includes('star') ||
                           cleanQuery.toLowerCase().includes('cast');

      const isDirectorSearch = cleanQuery.toLowerCase().includes('director') ||
                              cleanQuery.toLowerCase().includes('filmmaker') ||
                              cleanQuery.toLowerCase().includes('directed by');

      // Extract the actual search term
      let searchTerm = cleanQuery;
      let searchType = 'general';

      if (isMovieSearch) {
        const movieMatch = cleanQuery.match(/(?:movie|film)\s+(?:called|named|titled|about)?\s+([^]+)/i);
        searchTerm = movieMatch ? movieMatch[1].trim() : cleanQuery.replace(/\b(?:movie|film)\b/gi, '').trim();
        searchType = 'movie';
      } else if (isShowSearch) {
        const showMatch = cleanQuery.match(/(?:show|series|tv)\s+(?:called|named|titled|about)?\s+([^]+)/i);
        searchTerm = showMatch ? showMatch[1].trim() : cleanQuery.replace(/\b(?:show|series|tv)\b/gi, '').trim();
        searchType = 'show';
      } else if (isGenreSearch) {
        // Extract the genre
        const genreMatch = cleanQuery.match(/\b(action|comedy|drama|horror|thriller|documentary|sci-fi|fantasy|romance|animation)\b/i);
        if (genreMatch) {
          searchTerm = genreMatch[1];
          searchType = 'genre';
        } else {
          const genreNameMatch = cleanQuery.match(/(?:genre|category)\s+(?:of|like|such as)?\s+([^]+)/i);
          searchTerm = genreNameMatch ? genreNameMatch[1].trim() : cleanQuery;
          searchType = 'genre';
        }
      } else if (isActorSearch) {
        const actorMatch = cleanQuery.match(/(?:actor|actress|star|cast)\s+(?:named|called|like)?\s+([^]+)/i);
        searchTerm = actorMatch ? actorMatch[1].trim() : cleanQuery.replace(/\b(?:actor|actress|star|cast)\b/gi, '').trim();
        searchType = 'actor';
      } else if (isDirectorSearch) {
        const directorMatch = cleanQuery.match(/(?:director|filmmaker|directed by)\s+([^]+)/i);
        searchTerm = directorMatch ? directorMatch[1].trim() : cleanQuery.replace(/\b(?:director|filmmaker|directed by)\b/gi, '').trim();
        searchType = 'director';
      }

      // Create the search URL
      const searchUrl = `https://www.netflix.com/search?q=${encodeURIComponent(searchTerm)}`;

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Netflix\n\n` +
        `I'm opening Netflix search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Netflix...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened Netflix search results for "${cleanQuery}"`;

      if (searchType === 'movie') {
        successMessage += `\n\nI've opened Netflix search in a new browser tab. You should be able to find movies matching "${searchTerm}" there if they're available.`;
      } else if (searchType === 'show') {
        successMessage += `\n\nI've opened Netflix search in a new browser tab. You should be able to find TV shows or series matching "${searchTerm}" there if they're available.`;
      } else if (searchType === 'genre') {
        successMessage += `\n\nI've opened Netflix search in a new browser tab. You should be able to find ${searchTerm} content there if it's available.`;
      } else if (searchType === 'actor') {
        successMessage += `\n\nI've opened Netflix search in a new browser tab. You should be able to find movies and shows starring ${searchTerm} there if they're available.`;
      } else if (searchType === 'director') {
        successMessage += `\n\nI've opened Netflix search in a new browser tab. You should be able to find movies and shows directed by ${searchTerm} there if they're available.`;
      } else {
        successMessage += `\n\nI've opened Netflix search in a new browser tab. You should be able to find content matching "${searchTerm}" there if it's available.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened Netflix search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Netflix search error:', error);
      this.uiManager.addMessageToChat(`Error searching Netflix: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search for movies or shows on various streaming platforms
   * @param {string} query - The search query
   * @param {string} platform - The streaming platform to search on
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchMovie(query, platform) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error(`Please provide a search term for ${platform}.`);
      }

      // Define platform-specific URLs and names
      const platformInfo = {
        'netflix': {
          url: `https://www.netflix.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Netflix'
        },
        'primevideo': {
          url: `https://www.primevideo.com/search/ref=atv_sr_sug_7?phrase=${encodeURIComponent(cleanQuery)}`,
          name: 'Prime Video'
        },
        'hulu': {
          url: `https://www.hulu.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Hulu'
        },
        'disneyplus': {
          url: `https://www.disneyplus.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Disney+'
        },
        'hbomax': {
          url: `https://www.max.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'HBO Max'
        },
        'appletv': {
          url: `https://tv.apple.com/search?term=${encodeURIComponent(cleanQuery)}`,
          name: 'Apple TV+'
        },
        'peacock': {
          url: `https://www.peacocktv.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Peacock'
        },
        'paramountplus': {
          url: `https://www.paramountplus.com/search/?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Paramount+'
        }
      };

      // Get the platform info or use a fallback
      const info = platformInfo[platform] || {
        url: `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}+${encodeURIComponent(platform)}+streaming`,
        name: platform.charAt(0).toUpperCase() + platform.slice(1) // Capitalize first letter
      };

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on ${info.name}\n\n` +
        `I'm opening ${info.name} search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on ${info.name}...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: info.url });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened ${info.name} search results for "${cleanQuery}"\n\n` +
        `I've opened ${info.name} search in a new browser tab. You should be able to find shows or movies matching "${cleanQuery}" there if they're available.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened ${info.name} search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error(`Movie search error (${platform}):`, error);
      this.uiManager.addMessageToChat(`Error searching for movies: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search for movies or shows on various streaming platforms
   * @param {string} query - The search query
   * @param {string} platform - The streaming platform to search on
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchMovie(query, platform) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error(`Please provide a search term for ${platform}.`);
      }

      // Define platform-specific URLs and names
      const platformInfo = {
        'netflix': {
          url: `https://www.netflix.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Netflix'
        },
        'primevideo': {
          url: `https://www.primevideo.com/search/ref=atv_sr_sug_7?phrase=${encodeURIComponent(cleanQuery)}`,
          name: 'Prime Video'
        },
        'hulu': {
          url: `https://www.hulu.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Hulu'
        },
        'disneyplus': {
          url: `https://www.disneyplus.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Disney+'
        },
        'hbomax': {
          url: `https://www.max.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'HBO Max'
        },
        'appletv': {
          url: `https://tv.apple.com/search?term=${encodeURIComponent(cleanQuery)}`,
          name: 'Apple TV+'
        },
        'peacock': {
          url: `https://www.peacocktv.com/search?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Peacock'
        },
        'paramountplus': {
          url: `https://www.paramountplus.com/search/?q=${encodeURIComponent(cleanQuery)}`,
          name: 'Paramount+'
        }
      };

      // Get the platform info or use a fallback
      const info = platformInfo[platform] || {
        url: `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}+${encodeURIComponent(platform)}+streaming`,
        name: platform.charAt(0).toUpperCase() + platform.slice(1) // Capitalize first letter
      };

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on ${info.name}\n\n` +
        `I'm opening ${info.name} search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on ${info.name}...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: info.url });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened ${info.name} search results for "${cleanQuery}"\n\n` +
        `I've opened ${info.name} search in a new browser tab. You should be able to find shows or movies matching "${cleanQuery}" there if they're available.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened ${info.name} search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error(`Movie search error (${platform}):`, error);
      this.uiManager.addMessageToChat(`Error searching for movies: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Prime Video
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchPrimeVideo(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Prime Video.');
      }

      // Determine if this is a specific type of search
      const isMovieSearch = cleanQuery.toLowerCase().includes('movie') ||
                           cleanQuery.toLowerCase().includes('film');

      const isShowSearch = cleanQuery.toLowerCase().includes('show') ||
                          cleanQuery.toLowerCase().includes('series') ||
                          cleanQuery.toLowerCase().includes('tv');

      const isGenreSearch = cleanQuery.toLowerCase().includes('genre') ||
                           cleanQuery.toLowerCase().includes('category') ||
                           /\b(action|comedy|drama|horror|thriller|documentary|sci-fi|fantasy|romance|animation)\b/i.test(cleanQuery);

      const isActorSearch = cleanQuery.toLowerCase().includes('actor') ||
                           cleanQuery.toLowerCase().includes('actress') ||
                           cleanQuery.toLowerCase().includes('star') ||
                           cleanQuery.toLowerCase().includes('cast');

      const isDirectorSearch = cleanQuery.toLowerCase().includes('director') ||
                              cleanQuery.toLowerCase().includes('filmmaker') ||
                              cleanQuery.toLowerCase().includes('directed by');

      const isPrimeOriginalSearch = cleanQuery.toLowerCase().includes('prime original') ||
                                   cleanQuery.toLowerCase().includes('amazon original') ||
                                   cleanQuery.toLowerCase().includes('original series');

      // Extract the actual search term
      let searchTerm = cleanQuery;
      let searchType = 'general';

      if (isMovieSearch) {
        const movieMatch = cleanQuery.match(/(?:movie|film)\s+(?:called|named|titled|about)?\s+([^]+)/i);
        searchTerm = movieMatch ? movieMatch[1].trim() : cleanQuery.replace(/\b(?:movie|film)\b/gi, '').trim();
        searchType = 'movie';
      } else if (isShowSearch) {
        const showMatch = cleanQuery.match(/(?:show|series|tv)\s+(?:called|named|titled|about)?\s+([^]+)/i);
        searchTerm = showMatch ? showMatch[1].trim() : cleanQuery.replace(/\b(?:show|series|tv)\b/gi, '').trim();
        searchType = 'show';
      } else if (isGenreSearch) {
        // Extract the genre
        const genreMatch = cleanQuery.match(/\b(action|comedy|drama|horror|thriller|documentary|sci-fi|fantasy|romance|animation)\b/i);
        if (genreMatch) {
          searchTerm = genreMatch[1];
          searchType = 'genre';
        } else {
          const genreNameMatch = cleanQuery.match(/(?:genre|category)\s+(?:of|like|such as)?\s+([^]+)/i);
          searchTerm = genreNameMatch ? genreNameMatch[1].trim() : cleanQuery;
          searchType = 'genre';
        }
      } else if (isActorSearch) {
        const actorMatch = cleanQuery.match(/(?:actor|actress|star|cast)\s+(?:named|called|like)?\s+([^]+)/i);
        searchTerm = actorMatch ? actorMatch[1].trim() : cleanQuery.replace(/\b(?:actor|actress|star|cast)\b/gi, '').trim();
        searchType = 'actor';
      } else if (isDirectorSearch) {
        const directorMatch = cleanQuery.match(/(?:director|filmmaker|directed by)\s+([^]+)/i);
        searchTerm = directorMatch ? directorMatch[1].trim() : cleanQuery.replace(/\b(?:director|filmmaker|directed by)\b/gi, '').trim();
        searchType = 'director';
      } else if (isPrimeOriginalSearch) {
        searchTerm = "amazon original";
        searchType = 'prime original';
      }

      // Create the search URL
      const searchUrl = `https://www.primevideo.com/search/ref=atv_sr_sug_7?phrase=${encodeURIComponent(searchTerm)}`;

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Prime Video\n\n` +
        `I'm opening Prime Video search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Prime Video...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened Prime Video search results for "${cleanQuery}"`;

      if (searchType === 'movie') {
        successMessage += `\n\nI've opened Prime Video search in a new browser tab. You should be able to find movies matching "${searchTerm}" there if they're available.`;
      } else if (searchType === 'show') {
        successMessage += `\n\nI've opened Prime Video search in a new browser tab. You should be able to find TV shows or series matching "${searchTerm}" there if they're available.`;
      } else if (searchType === 'genre') {
        successMessage += `\n\nI've opened Prime Video search in a new browser tab. You should be able to find ${searchTerm} content there if it's available.`;
      } else if (searchType === 'actor') {
        successMessage += `\n\nI've opened Prime Video search in a new browser tab. You should be able to find movies and shows starring ${searchTerm} there if they're available.`;
      } else if (searchType === 'director') {
        successMessage += `\n\nI've opened Prime Video search in a new browser tab. You should be able to find movies and shows directed by ${searchTerm} there if they're available.`;
      } else if (searchType === 'prime original') {
        successMessage += `\n\nI've opened Prime Video search in a new browser tab. You should be able to find Amazon Original content there.`;
      } else {
        successMessage += `\n\nI've opened Prime Video search in a new browser tab. You should be able to find content matching "${searchTerm}" there if it's available.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened Prime Video search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Prime Video search error:', error);
      this.uiManager.addMessageToChat(`Error searching Prime Video: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on YouTube
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchYouTube(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for YouTube.');
      }

      // Create the search URL
      const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(cleanQuery)}`;

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on YouTube\n\n` +
        `I'm opening YouTube search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on YouTube...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened YouTube search results for "${cleanQuery}"\n\n` +
        `I've opened YouTube search in a new browser tab. You should be able to find videos related to "${cleanQuery}" there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened YouTube search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('YouTube search error:', error);
      this.uiManager.addMessageToChat(`Error searching YouTube: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Instagram
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchInstagram(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Instagram.');
      }

      // Determine if this is a profile search, hashtag search, or general search
      const isProfileSearch = cleanQuery.toLowerCase().includes('profile') ||
                             cleanQuery.toLowerCase().includes('account') ||
                             cleanQuery.toLowerCase().includes('user');

      const isHashtagSearch = cleanQuery.toLowerCase().includes('hashtag') ||
                             cleanQuery.toLowerCase().includes('tag') ||
                             cleanQuery.startsWith('#') ||
                             cleanQuery.includes(' #');

      // Create the appropriate search URL
      let searchUrl;
      let searchType;

      if (isProfileSearch) {
        // Extract the username from the query
        const usernameMatch = cleanQuery.match(/(?:profile|account|user|find|search|lookup)\s+(?:of|for|about)?\s+([^]+)/i);
        let username = usernameMatch ? usernameMatch[1].trim() : cleanQuery;

        // Remove @ if present
        if (username.startsWith('@')) {
          username = username.substring(1);
        }

        searchUrl = `https://www.instagram.com/${encodeURIComponent(username)}/`;
        searchType = 'profile';
      } else if (isHashtagSearch) {
        // Extract the hashtag from the query
        let hashtag = cleanQuery;

        if (hashtag.includes('#')) {
          // Extract the first hashtag
          const hashtagMatch = hashtag.match(/#([a-zA-Z0-9_]+)/);
          if (hashtagMatch && hashtagMatch[1]) {
            hashtag = hashtagMatch[1];
          } else {
            // If no hashtag found, remove the # character if it exists
            hashtag = hashtag.replace('#', '');
          }
        } else if (hashtag.toLowerCase().includes('hashtag') || hashtag.toLowerCase().includes('tag')) {
          // Extract the hashtag name
          const hashtagMatch = hashtag.match(/(?:hashtag|tag)\s+(?:of|for|about)?\s+([^]+)/i);
          hashtag = hashtagMatch ? hashtagMatch[1].trim() : hashtag;
        }

        searchUrl = `https://www.instagram.com/explore/tags/${encodeURIComponent(hashtag)}/`;
        searchType = 'hashtag';
      } else {
        // General search - Instagram doesn't have a direct search URL, so we'll go to the explore page
        searchUrl = `https://www.instagram.com/explore/`;
        searchType = 'general';
      }

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Instagram\n\n` +
        `I'm opening Instagram search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Instagram...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened Instagram search for "${cleanQuery}"`;

      if (searchType === 'profile') {
        successMessage += `\n\nI've opened the Instagram profile page in a new browser tab. If the profile exists, you should see it now.`;
      } else if (searchType === 'hashtag') {
        successMessage += `\n\nI've opened the Instagram hashtag page in a new browser tab. You should see posts with this hashtag.`;
      } else {
        successMessage += `\n\nI've opened Instagram's explore page in a new browser tab. You can search for "${cleanQuery}" using Instagram's search bar.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened Instagram search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Instagram search error:', error);
      this.uiManager.addMessageToChat(`Error searching Instagram: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on X (Twitter)
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchX(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for X (Twitter).');
      }

      // Determine if this is a profile search, hashtag search, or general search
      const isProfileSearch = cleanQuery.toLowerCase().includes('profile') ||
                             cleanQuery.toLowerCase().includes('account') ||
                             cleanQuery.toLowerCase().includes('user') ||
                             cleanQuery.startsWith('@');

      const isHashtagSearch = cleanQuery.toLowerCase().includes('hashtag') ||
                             cleanQuery.toLowerCase().includes('tag') ||
                             cleanQuery.startsWith('#') ||
                             cleanQuery.includes(' #');

      // Create the appropriate search URL
      let searchUrl;
      let searchType;

      if (isProfileSearch) {
        // Extract the username from the query
        const usernameMatch = cleanQuery.match(/(?:profile|account|user|find|search|lookup)\s+(?:of|for|about)?\s+([^]+)/i);
        let username = usernameMatch ? usernameMatch[1].trim() : cleanQuery;

        // Remove @ if present
        if (username.startsWith('@')) {
          username = username.substring(1);
        }

        searchUrl = `https://twitter.com/${encodeURIComponent(username)}`;
        searchType = 'profile';
      } else if (isHashtagSearch) {
        // Extract the hashtag from the query
        let hashtag = cleanQuery;

        if (hashtag.includes('#')) {
          // Extract the first hashtag
          const hashtagMatch = hashtag.match(/#([a-zA-Z0-9_]+)/);
          if (hashtagMatch && hashtagMatch[1]) {
            hashtag = hashtagMatch[1];
          } else {
            // If no hashtag found, remove the # character if it exists
            hashtag = hashtag.replace('#', '');
          }
        } else if (hashtag.toLowerCase().includes('hashtag') || hashtag.toLowerCase().includes('tag')) {
          // Extract the hashtag name
          const hashtagMatch = hashtag.match(/(?:hashtag|tag)\s+(?:of|for|about)?\s+([^]+)/i);
          hashtag = hashtagMatch ? hashtagMatch[1].trim() : hashtag;
        }

        searchUrl = `https://twitter.com/hashtag/${encodeURIComponent(hashtag)}?src=hashtag_click`;
        searchType = 'hashtag';
      } else {
        // General search
        searchUrl = `https://twitter.com/search?q=${encodeURIComponent(cleanQuery)}&src=typed_query`;
        searchType = 'general';
      }

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on X (Twitter)\n\n` +
        `I'm opening X (Twitter) search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on X (Twitter)...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened X (Twitter) search for "${cleanQuery}"`;

      if (searchType === 'profile') {
        successMessage += `\n\nI've opened the X (Twitter) profile page in a new browser tab. If the profile exists, you should see it now.`;
      } else if (searchType === 'hashtag') {
        successMessage += `\n\nI've opened the X (Twitter) hashtag page in a new browser tab. You should see tweets with this hashtag.`;
      } else {
        successMessage += `\n\nI've opened X (Twitter) search results in a new browser tab. You should see tweets related to "${cleanQuery}".`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened X (Twitter) search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('X (Twitter) search error:', error);
      this.uiManager.addMessageToChat(`Error searching X (Twitter): ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Pinterest
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchPinterest(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Pinterest.');
      }

      // Create the search URL
      const searchUrl = `https://www.pinterest.com/search/pins/?q=${encodeURIComponent(cleanQuery)}`;

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Pinterest\n\n` +
        `I'm opening Pinterest search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Pinterest...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened Pinterest search results for "${cleanQuery}"\n\n` +
        `I've opened Pinterest search in a new browser tab. You should be able to find pins related to "${cleanQuery}" there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened Pinterest search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Pinterest search error:', error);
      this.uiManager.addMessageToChat(`Error searching Pinterest: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Stack Overflow
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchStackOverflow(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Stack Overflow.');
      }

      // Create the search URL
      const searchUrl = `https://stackoverflow.com/search?q=${encodeURIComponent(cleanQuery)}`;

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Stack Overflow\n\n` +
        `I'm opening Stack Overflow search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Stack Overflow...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened Stack Overflow search results for "${cleanQuery}"\n\n` +
        `I've opened Stack Overflow search in a new browser tab. You should be able to find questions and answers related to "${cleanQuery}" there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened Stack Overflow search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Stack Overflow search error:', error);
      this.uiManager.addMessageToChat(`Error searching Stack Overflow: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Reddit
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchReddit(query) {
    return await this.searchFallback('reddit', query);
  }

  /**
   * Search on TikTok
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchTiktok(query) {
    return await this.searchFallback('tiktok', query);
  }

  /**
   * Search on Medium
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchMedium(query) {
    return await this.searchFallback('medium', query);
  }

  /**
   * Search on Amazon
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchAmazon(query) {
    return await this.searchFallback('amazon', query);
  }

  /**
   * Search on eBay
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchEbay(query) {
    return await this.searchFallback('ebay', query);
  }

  /**
   * Search on Flipkart
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchFlipkart(query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term for Flipkart.');
      }

      // Determine if this is a specific type of search
      const isProductSearch = cleanQuery.toLowerCase().includes('product') ||
                             cleanQuery.toLowerCase().includes('item');

      const isCategorySearch = cleanQuery.toLowerCase().includes('category') ||
                              cleanQuery.toLowerCase().includes('department');

      const isBrandSearch = cleanQuery.toLowerCase().includes('brand') ||
                           cleanQuery.toLowerCase().includes('manufacturer') ||
                           cleanQuery.toLowerCase().includes('company');

      const isElectronicsSearch = cleanQuery.toLowerCase().includes('electronics') ||
                                 cleanQuery.toLowerCase().includes('gadget') ||
                                 cleanQuery.toLowerCase().includes('device') ||
                                 /\b(phone|laptop|computer|tv|camera|headphone|speaker)\b/i.test(cleanQuery);

      const isClothingSearch = cleanQuery.toLowerCase().includes('clothing') ||
                              cleanQuery.toLowerCase().includes('fashion') ||
                              cleanQuery.toLowerCase().includes('apparel') ||
                              /\b(shirt|pant|dress|shoe|jacket|watch|bag)\b/i.test(cleanQuery);

      // Extract the actual search term
      let searchTerm = cleanQuery;
      let searchType = 'general';

      if (isProductSearch) {
        const productMatch = cleanQuery.match(/(?:product|item)\s+(?:called|named|like)?\s+([^]+)/i);
        searchTerm = productMatch ? productMatch[1].trim() : cleanQuery.replace(/\b(?:product|item)\b/gi, '').trim();
        searchType = 'product';
      } else if (isCategorySearch) {
        const categoryMatch = cleanQuery.match(/(?:category|department)\s+(?:of|like|such as)?\s+([^]+)/i);
        searchTerm = categoryMatch ? categoryMatch[1].trim() : cleanQuery.replace(/\b(?:category|department)\b/gi, '').trim();
        searchType = 'category';
      } else if (isBrandSearch) {
        const brandMatch = cleanQuery.match(/(?:brand|manufacturer|company)\s+(?:called|named|like)?\s+([^]+)/i);
        searchTerm = brandMatch ? brandMatch[1].trim() : cleanQuery.replace(/\b(?:brand|manufacturer|company)\b/gi, '').trim();
        searchType = 'brand';
      } else if (isElectronicsSearch) {
        // Extract the electronics item
        const electronicsMatch = cleanQuery.match(/\b(phone|laptop|computer|tv|camera|headphone|speaker)\b/i);
        if (electronicsMatch) {
          searchTerm = electronicsMatch[1];
          searchType = 'electronics';
        } else {
          searchType = 'electronics';
        }
      } else if (isClothingSearch) {
        // Extract the clothing item
        const clothingMatch = cleanQuery.match(/\b(shirt|pant|dress|shoe|jacket|watch|bag)\b/i);
        if (clothingMatch) {
          searchTerm = clothingMatch[1];
          searchType = 'clothing';
        } else {
          searchType = 'clothing';
        }
      }

      // Create the search URL
      const searchUrl = `https://www.flipkart.com/search?q=${encodeURIComponent(searchTerm)}`;

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on Flipkart\n\n` +
        `I'm opening Flipkart search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on Flipkart...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      let successMessage = `Successfully opened Flipkart search results for "${cleanQuery}"`;

      if (searchType === 'product') {
        successMessage += `\n\nI've opened Flipkart search in a new browser tab. You should be able to find products matching "${searchTerm}" there.`;
      } else if (searchType === 'category') {
        successMessage += `\n\nI've opened Flipkart search in a new browser tab. You should be able to find items in the ${searchTerm} category there.`;
      } else if (searchType === 'brand') {
        successMessage += `\n\nI've opened Flipkart search in a new browser tab. You should be able to find products from ${searchTerm} there.`;
      } else if (searchType === 'electronics') {
        successMessage += `\n\nI've opened Flipkart search in a new browser tab. You should be able to find electronics matching "${searchTerm}" there.`;
      } else if (searchType === 'clothing') {
        successMessage += `\n\nI've opened Flipkart search in a new browser tab. You should be able to find clothing and fashion items matching "${searchTerm}" there.`;
      } else {
        successMessage += `\n\nI've opened Flipkart search in a new browser tab. You should be able to find items matching "${searchTerm}" there.`;
      }

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened Flipkart search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Flipkart search error:', error);
      this.uiManager.addMessageToChat(`Error searching Flipkart: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Etsy
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchEtsy(query) {
    return await this.searchEcommerce('etsy', query);
  }

  /**
   * Search on Walmart
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchWalmart(query) {
    return await this.searchEcommerce('walmart', query);
  }

  /**
   * Search on any e-commerce platform
   * @param {string} platform - The e-commerce platform to search on
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchEcommerce(platform, query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error(`Please provide a search term for ${platform}.`);
      }

      // Define platform-specific URLs and names
      const platformConfig = {
        'amazon': {
          name: 'Amazon',
          url: `https://www.amazon.com/s?k=${encodeURIComponent(cleanQuery)}`
        },
        'ebay': {
          name: 'eBay',
          url: `https://www.ebay.com/sch/i.html?_nkw=${encodeURIComponent(cleanQuery)}`
        },
        'etsy': {
          name: 'Etsy',
          url: `https://www.etsy.com/search?q=${encodeURIComponent(cleanQuery)}`
        },
        'walmart': {
          name: 'Walmart',
          url: `https://www.walmart.com/search?q=${encodeURIComponent(cleanQuery)}`
        },
        'aliexpress': {
          name: 'AliExpress',
          url: `https://www.aliexpress.com/wholesale?SearchText=${encodeURIComponent(cleanQuery)}`
        },
        'flipkart': {
          name: 'Flipkart',
          url: `https://www.flipkart.com/search?q=${encodeURIComponent(cleanQuery)}`
        },
        'bestbuy': {
          name: 'Best Buy',
          url: `https://www.bestbuy.com/site/searchpage.jsp?st=${encodeURIComponent(cleanQuery)}`
        },
        'target': {
          name: 'Target',
          url: `https://www.target.com/s?searchTerm=${encodeURIComponent(cleanQuery)}`
        },
        'ikea': {
          name: 'IKEA',
          url: `https://www.ikea.com/us/en/search/products/?q=${encodeURIComponent(cleanQuery)}`
        },
        'myntra': {
          name: 'Myntra',
          url: `https://www.myntra.com/${encodeURIComponent(cleanQuery)}`
        },
        'ajio': {
          name: 'AJIO',
          url: `https://www.ajio.com/search/?text=${encodeURIComponent(cleanQuery)}`
        },
        'snapdeal': {
          name: 'Snapdeal',
          url: `https://www.snapdeal.com/search?keyword=${encodeURIComponent(cleanQuery)}`
        },
        'paytmmall': {
          name: 'Paytm Mall',
          url: `https://paytmmall.com/shop/search?q=${encodeURIComponent(cleanQuery)}`
        },
        'meesho': {
          name: 'Meesho',
          url: `https://www.meesho.com/search?q=${encodeURIComponent(cleanQuery)}`
        },
        'nykaa': {
          name: 'Nykaa',
          url: `https://www.nykaa.com/search/result/?q=${encodeURIComponent(cleanQuery)}`
        },
        'tatacliq': {
          name: 'Tata CLiQ',
          url: `https://www.tatacliq.com/search/?searchCategory=all&text=${encodeURIComponent(cleanQuery)}`
        },
        'croma': {
          name: 'Croma',
          url: `https://www.croma.com/searchB?q=${encodeURIComponent(cleanQuery)}:relevance`
        },
        'reliancedigital': {
          name: 'Reliance Digital',
          url: `https://www.reliancedigital.in/search?q=${encodeURIComponent(cleanQuery)}:relevance`
        },
        'homedepot': {
          name: 'Home Depot',
          url: `https://www.homedepot.com/s/${encodeURIComponent(cleanQuery)}`
        },
        'lowes': {
          name: 'Lowe\'s',
          url: `https://www.lowes.com/search?searchTerm=${encodeURIComponent(cleanQuery)}`
        },
        'costco': {
          name: 'Costco',
          url: `https://www.costco.com/CatalogSearch?keyword=${encodeURIComponent(cleanQuery)}`
        },
        'samsclub': {
          name: 'Sam\'s Club',
          url: `https://www.samsclub.com/s/${encodeURIComponent(cleanQuery)}`
        },
        'macys': {
          name: 'Macy\'s',
          url: `https://www.macys.com/shop/featured/${encodeURIComponent(cleanQuery)}`
        },
        'kohls': {
          name: 'Kohl\'s',
          url: `https://www.kohls.com/search.jsp?search=${encodeURIComponent(cleanQuery)}`
        },
        'jcpenney': {
          name: 'JCPenney',
          url: `https://www.jcpenney.com/s/${encodeURIComponent(cleanQuery)}`
        },
        'newegg': {
          name: 'Newegg',
          url: `https://www.newegg.com/p/pl?d=${encodeURIComponent(cleanQuery)}`
        },
        'overstock': {
          name: 'Overstock',
          url: `https://www.overstock.com/search?keywords=${encodeURIComponent(cleanQuery)}`
        },
        'zappos': {
          name: 'Zappos',
          url: `https://www.zappos.com/search?term=${encodeURIComponent(cleanQuery)}`
        },
        'nordstrom': {
          name: 'Nordstrom',
          url: `https://www.nordstrom.com/sr?keyword=${encodeURIComponent(cleanQuery)}`
        },
        'sephora': {
          name: 'Sephora',
          url: `https://www.sephora.com/search?keyword=${encodeURIComponent(cleanQuery)}`
        },
        'ulta': {
          name: 'Ulta Beauty',
          url: `https://www.ulta.com/ulta/a/_/Ntt-${encodeURIComponent(cleanQuery)}`
        }
      };

      // Get the platform configuration or use a fallback
      const config = platformConfig[platform] || {
        name: platform.charAt(0).toUpperCase() + platform.slice(1),
        url: `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}+site%3A${platform}.com`
      };

      // Show a message in the chat
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}" on ${config.name}\n\n` +
        `I'm opening ${config.name} search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}" on ${config.name}...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: config.url });

      // Show success message
      const successMessage = `Successfully opened ${config.name} search results for "${cleanQuery}"\n\n` +
                            `I've opened ${config.name} search in a new browser tab. You should be able to find products matching "${cleanQuery}" there.`;

      this.uiManager.addMessageToChat(successMessage, 'ai');

      // Show success status
      this.uiManager.showStatus(`Opened ${config.name} search for "${cleanQuery}" in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error(`E-commerce search error (${platform}):`, error);
      this.uiManager.addMessageToChat(`Error searching ${platform}: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Search on Hulu
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchHulu(query) {
    return await this.searchMovie(query, 'hulu');
  }

  /**
   * Search on Disney+
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchDisneyPlus(query) {
    return await this.searchMovie(query, 'disneyplus');
  }

  /**
   * Search on Behance
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchBehance(query) {
    return await this.searchFallback('behance', query);
  }

  /**
   * Search on Unsplash
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchUnsplash(query) {
    return await this.searchFallback('unsplash', query);
  }

  /**
   * Search on Wikipedia
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchWikipedia(query) {
    return await this.searchFallback('wikipedia', query);
  }

  /**
   * Search for news
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchNews(query) {
    return await this.searchFallback('news', query);
  }

  /**
   * Fallback search method for platforms without specific implementations
   * @param {string} platform - The platform to search on (or null for general search)
   * @param {string} query - The search query
   * @returns {Promise<boolean>} - Whether the search was successful
   */
  async searchFallback(platform, query) {
    try {
      // Clean up the query
      const cleanQuery = query.trim();

      if (!cleanQuery) {
        throw new Error('Please provide a search term.');
      }

      // Define platform-specific search URLs
      const platformUrls = {
        // Social Media
        'facebook': `https://www.facebook.com/search/top/?q=${encodeURIComponent(cleanQuery)}`,
        'reddit': `https://www.reddit.com/search/?q=${encodeURIComponent(cleanQuery)}`,
        'tiktok': `https://www.tiktok.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'snapchat': `https://story.snapchat.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'threads': `https://www.threads.net/search?q=${encodeURIComponent(cleanQuery)}`,
        'discord': `https://discord.com/channels/@me?q=${encodeURIComponent(cleanQuery)}`,
        'telegram': `https://t.me/s/${encodeURIComponent(cleanQuery)}`,

        // Professional & Development
        'medium': `https://medium.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'dev.to': `https://dev.to/search?q=${encodeURIComponent(cleanQuery)}`,
        'gitlab': `https://gitlab.com/search?search=${encodeURIComponent(cleanQuery)}`,
        'bitbucket': `https://bitbucket.org/repo/all?name=${encodeURIComponent(cleanQuery)}`,
        'jira': `https://issues.apache.org/jira/secure/QuickSearch.jspa?searchString=${encodeURIComponent(cleanQuery)}`,
        'trello': `https://trello.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'notion': `https://www.notion.so/search?q=${encodeURIComponent(cleanQuery)}`,

        // E-commerce
        'amazon': `https://www.amazon.com/s?k=${encodeURIComponent(cleanQuery)}`,
        'ebay': `https://www.ebay.com/sch/i.html?_nkw=${encodeURIComponent(cleanQuery)}`,
        'etsy': `https://www.etsy.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'walmart': `https://www.walmart.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'aliexpress': `https://www.aliexpress.com/wholesale?SearchText=${encodeURIComponent(cleanQuery)}`,
        'flipkart': `https://www.flipkart.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'bestbuy': `https://www.bestbuy.com/site/searchpage.jsp?st=${encodeURIComponent(cleanQuery)}`,
        'target': `https://www.target.com/s?searchTerm=${encodeURIComponent(cleanQuery)}`,
        'shopify': `https://www.shopify.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'wayfair': `https://www.wayfair.com/keyword.php?keyword=${encodeURIComponent(cleanQuery)}`,
        'ikea': `https://www.ikea.com/us/en/search/?q=${encodeURIComponent(cleanQuery)}`,

        // Design & Creative
        'behance': `https://www.behance.net/search?search=${encodeURIComponent(cleanQuery)}`,
        'unsplash': `https://unsplash.com/s/photos/${encodeURIComponent(cleanQuery)}`,
        'figma': `https://www.figma.com/community/search?model_type=files&q=${encodeURIComponent(cleanQuery)}`,
        'canva': `https://www.canva.com/search/templates?q=${encodeURIComponent(cleanQuery)}`,
        'adobe': `https://www.adobe.com/search.html?q=${encodeURIComponent(cleanQuery)}`,
        'sketch': `https://www.sketch.com/s/${encodeURIComponent(cleanQuery)}`,
        'invision': `https://www.invisionapp.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'shutterstock': `https://www.shutterstock.com/search/${encodeURIComponent(cleanQuery)}`,
        'getty images': `https://www.gettyimages.com/search/2/image?phrase=${encodeURIComponent(cleanQuery)}`,

        // News & Information
        'wikipedia': `https://en.wikipedia.org/wiki/Special:Search?search=${encodeURIComponent(cleanQuery)}`,
        'news': `https://news.google.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'google news': `https://news.google.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'bbc': `https://www.bbc.co.uk/search?q=${encodeURIComponent(cleanQuery)}`,
        'cnn': `https://www.cnn.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'nytimes': `https://www.nytimes.com/search?query=${encodeURIComponent(cleanQuery)}`,
        'reuters': `https://www.reuters.com/search/news?blob=${encodeURIComponent(cleanQuery)}`,
        'ap news': `https://apnews.com/search?q=${encodeURIComponent(cleanQuery)}`,

        // Travel & Maps
        'google maps': `https://www.google.com/maps/search/${encodeURIComponent(cleanQuery)}`,
        'airbnb': `https://www.airbnb.com/s/${encodeURIComponent(cleanQuery)}/homes`,
        'booking.com': `https://www.booking.com/search.html?ss=${encodeURIComponent(cleanQuery)}`,
        'expedia': `https://www.expedia.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'tripadvisor': `https://www.tripadvisor.com/Search?q=${encodeURIComponent(cleanQuery)}`,
        'kayak': `https://www.kayak.com/search/${encodeURIComponent(cleanQuery)}`,

        // General
        'google': `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'bing': `https://www.bing.com/search?q=${encodeURIComponent(cleanQuery)}`,
        'duckduckgo': `https://duckduckgo.com/?q=${encodeURIComponent(cleanQuery)}`,
        'yahoo': `https://search.yahoo.com/search?p=${encodeURIComponent(cleanQuery)}`
      };

      // Determine the search URL
      let searchUrl;
      let platformName = platform;

      if (platform && platformUrls[platform.toLowerCase()]) {
        // Use the platform-specific URL if available
        searchUrl = platformUrls[platform.toLowerCase()];
        platformName = platform.charAt(0).toUpperCase() + platform.slice(1); // Capitalize first letter
      } else if (platform) {
        // If platform is specified but not in our list, search for it on Google
        searchUrl = `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}+${encodeURIComponent(platform)}`;
      } else {
        // Default to Google search
        searchUrl = `https://www.google.com/search?q=${encodeURIComponent(cleanQuery)}`;
        platformName = 'Google';
      }

      // Show a message in the chat
      const platformText = platform ? ` on ${platformName}` : '';
      this.uiManager.addMessageToChat(
        `Searching for "${cleanQuery}"${platformText}\n\n` +
        `I'm opening search results in a new browser tab for you.`,
        'ai'
      );

      // Show status
      this.uiManager.showStatus(`Searching for "${cleanQuery}"${platformText}...`, false);

      // Open the URL in a new tab
      await chrome.tabs.create({ url: searchUrl });

      // Show success message
      this.uiManager.addMessageToChat(
        `Successfully opened search results for "${cleanQuery}"${platformText}\n\n` +
        `I've opened search results in a new browser tab. You should be able to find information about "${cleanQuery}"${platformText} there.`,
        'ai'
      );

      // Show success status
      this.uiManager.showStatus(`Opened search for "${cleanQuery}"${platformText} in a new tab`, false, 3000);

      return true;
    } catch (error) {
      console.error('Search fallback error:', error);
      this.uiManager.addMessageToChat(`Error searching: ${error.message}`, 'ai', 'error-message');
      this.uiManager.showStatus(error.message, true);
      return false;
    }
  }

  /**
   * Analyze videos on the current page
   * Detects and analyzes video content, providing insights and information
   * including timestamp analysis for better understanding of video content
   */
  async analyzeVideo() {
    try {
      this.uiManager.showLoading('Analyzing video content...');

      // Get the current page content
      const pageContent = await this.apiManager.getPageContent();

      // Check if we're on a video platform
      const isYouTube = pageContent.url.includes('youtube.com') || pageContent.url.includes('youtu.be');
      const isVimeo = pageContent.url.includes('vimeo.com');
      const isNetflix = pageContent.url.includes('netflix.com');
      const isVideoPage = isYouTube || isVimeo || isNetflix ||
                         pageContent.url.includes('dailymotion.com') ||
                         pageContent.url.includes('twitch.tv') ||
                         pageContent.url.includes('tiktok.com');

      // Extract video elements from the page
      const tabQueryResult = await chrome.tabs.query({ active: true, currentWindow: true });
      const tab = tabQueryResult[0];

      // Request video information from the content script
      const videoInfo = await chrome.tabs.sendMessage(tab.id, { action: 'getVideoInfo' })
        .catch(() => ({ success: false, message: 'Could not detect videos on this page' }));

      if (!videoInfo || !videoInfo.success) {
        // If we couldn't get video info but we're on a known video platform
        if (isVideoPage) {
          // Analyze based on the URL and page content
          const prompt = `
            Please analyze this video page and provide insights:

            URL: ${pageContent.url}
            Title: ${pageContent.title}

            Please provide:
            1. A brief summary of what this video appears to be about
            2. The main topics or themes based on the title and page content
            3. Information about the creator/channel if available
            4. Estimated video type (educational, entertainment, tutorial, etc.)
            5. If you can identify any key moments or sections in the video based on the content
            6. Estimated timeline of the video content (introduction, main points, conclusion)

            Page content excerpt:
            ${this.truncateContent(pageContent.content, 2000)}
          `;

          const analysisResponse = await this.apiManager.sendRequest(prompt, {
            systemPrompt: "You are a video analysis assistant that helps users understand video content. Provide clear, concise analysis based on available information. Include emoji in your response to make it more engaging. If you identify timestamps or key moments in the video, highlight them clearly."
          });

          this.uiManager.showResult(analysisResponse.text, 'Video Analysis');
          return;
        } else {
          // Check if there are any video elements on the page
          const hasVideoElements = await chrome.tabs.sendMessage(tab.id, { action: 'checkForVideoElements' })
            .catch(() => ({ success: false }));

          if (!hasVideoElements || !hasVideoElements.success) {
            throw new Error('No videos detected on this page. Try using this feature on a page with video content.');
          }

          // If we found video elements but couldn't get detailed info
          const prompt = `
            Please analyze this page that contains video elements:

            URL: ${pageContent.url}
            Title: ${pageContent.title}

            The page contains embedded videos, but detailed information couldn't be extracted.
            Based on the page content, please provide:
            1. What the videos on this page might be about
            2. The context of these videos based on the surrounding content
            3. Any relevant information about the videos that can be inferred
            4. Potential key moments or sections that might be in these videos

            Page content excerpt:
            ${this.truncateContent(pageContent.content, 2000)}
          `;

          const analysisResponse = await this.apiManager.sendRequest(prompt, {
            systemPrompt: "You are a video analysis assistant that helps users understand video content. Provide clear, concise analysis based on available information. Include emoji in your response to make it more engaging."
          });

          this.uiManager.showResult(analysisResponse.text, 'Video Analysis');
          return;
        }
      }

      // If we successfully got video information
      const videos = videoInfo.videos || [];

      if (videos.length === 0) {
        throw new Error('No videos detected on this page. Try using this feature on a page with video content.');
      }

      // Prepare video information for analysis including timestamp data
      const videoDetails = videos.map((video, index) => {
        // Basic video details
        let details = `
          Video ${index + 1}:
          - Title: ${video.title || 'Unknown'}
          - Duration: ${video.duration || 'Unknown'}
          - Current Position: ${video.currentTimeFormatted || '0:00'} (${video.progress || 0}% complete)
          - Dimensions: ${video.width || '?'}x${video.height || '?'}
          - Type: ${video.type || 'Unknown'}
        `;

        // Add timestamp information if available
        if (video.timestamps && video.timestamps.length > 0) {
          details += `\n          - Timestamps Found: ${video.timestamps.length}`;
          details += `\n          - Key Moments:`;

          // Add up to 10 timestamps with context
          video.timestamps.slice(0, 10).forEach(timestamp => {
            details += `\n            * ${timestamp.text} - ${timestamp.context}`;
          });
        }

        // Add chapter information if available
        if (video.chapters && video.chapters.length > 0) {
          details += `\n          - Chapters Found: ${video.chapters.length}`;
          details += `\n          - Video Structure:`;

          // Add chapters
          video.chapters.forEach(chapter => {
            details += `\n            * ${chapter.time} - ${chapter.title}`;
          });
        }

        return details;
      }).join('\n\n');

      // Prepare the prompt for video analysis with timestamp focus
      const prompt = `
        Please analyze the video content on this page with special attention to timestamps and video structure:

        URL: ${pageContent.url}
        Title: ${pageContent.title}

        ${videos.length} video${videos.length > 1 ? 's' : ''} detected on this page.

        Video details:
        ${videoDetails}

        Page content excerpt:
        ${this.truncateContent(pageContent.content, 1500)}

        Please provide:
        1. A summary of what the video content appears to be about
        2. The main topics or themes
        3. The likely purpose of the video(s) (educational, entertainment, tutorial, etc.)
        4. Analysis of the video structure based on timestamps and chapters
        5. Explanation of key moments in the video and what happens at those timestamps
        6. Where the user currently is in the video narrative (based on current position)
        7. What the user can expect to see in the remainder of the video
        8. Recommendations for specific timestamps that might be most valuable to the user

        Make your analysis engaging and helpful, focusing on the content at specific timestamps.
      `;

      const analysisResponse = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a video analysis assistant that helps users understand video content. Provide clear, concise analysis based on available information. Focus on timestamps and video structure to help users navigate the content efficiently. Include emoji in your response to make it more engaging. Format timestamps in bold when mentioning them."
      });

      this.uiManager.showResult(analysisResponse.text, 'Video Analysis');

      // Set up listener for timestamp updates
      this.setupTimestampUpdateListener(tab.id);

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }

  /**
   * Set up listener for video timestamp updates
   * @param {number} tabId - ID of the current tab
   */
  setupTimestampUpdateListener(tabId) {
    // Remove any existing listeners first
    chrome.runtime.onMessage.removeListener(this.timestampUpdateListener);

    // Create and store the listener function
    this.timestampUpdateListener = (message, sender, sendResponse) => {
      if (message.action === 'videoTimestampUpdate' && sender.tab && sender.tab.id === tabId) {
        // Update the UI with the new timestamp information
        const timestampInfo = `🎬 Video position updated: ${message.currentTimeFormatted} (${message.progress}% complete)`;
        this.uiManager.showStatus(timestampInfo);

        // If the user is seeking (manually changing position), offer to analyze that section
        if (message.isSeeking) {
          const seekMsg = `The user has jumped to ${message.currentTimeFormatted} in the video. Would you like to analyze this specific section?`;

          // Add a suggestion to the chat
          const chatMessages = document.getElementById('chatMessages');
          if (chatMessages) {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'message system-message';
            suggestionDiv.innerHTML = `
              <div class="message-content">
                <p>${seekMsg}</p>
                <div class="suggestion-chips">
                  <span class="suggestion-chip" data-timestamp="${message.currentTime}">
                    <i class="fas fa-video"></i> Analyze at ${message.currentTimeFormatted}
                  </span>
                </div>
              </div>
            `;
            chatMessages.appendChild(suggestionDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // Add click handler for the suggestion chip
            const chip = suggestionDiv.querySelector('.suggestion-chip');
            if (chip) {
              chip.addEventListener('click', () => {
                this.analyzeVideoAtTimestamp(tabId, message.videoIndex, message.currentTime, message.currentTimeFormatted);
              });
            }
          }
        }

        sendResponse({ success: true });
      }
      return true; // Keep the message channel open for async response
    };

    // Add the listener
    chrome.runtime.onMessage.addListener(this.timestampUpdateListener);
  }

  /**
   * Analyze a video at a specific timestamp
   * @param {number} tabId - ID of the current tab
   * @param {number} videoIndex - Index of the video
   * @param {number} timestamp - Timestamp in seconds
   * @param {string} formattedTimestamp - Formatted timestamp (MM:SS)
   */
  async analyzeVideoAtTimestamp(tabId, videoIndex, timestamp, formattedTimestamp) {
    try {
      this.uiManager.showLoading(`Analyzing video at ${formattedTimestamp}...`);

      // Get the current page content
      const pageContent = await this.apiManager.getPageContent();

      // Request video information again to get the latest data
      const videoInfo = await chrome.tabs.sendMessage(tabId, { action: 'getVideoInfo' })
        .catch(() => ({ success: false }));

      if (!videoInfo || !videoInfo.success) {
        throw new Error('Could not get video information');
      }

      const videos = videoInfo.videos || [];
      if (videos.length <= videoIndex) {
        throw new Error('Video not found');
      }

      const video = videos[videoIndex];

      // Find the closest timestamps to the current position
      let relevantTimestamps = [];
      let relevantChapter = null;

      if (video.timestamps && video.timestamps.length > 0) {
        // Sort timestamps by time
        const sortedTimestamps = [...video.timestamps].sort((a, b) => a.seconds - b.seconds);

        // Find timestamps within 30 seconds before and after the current position
        relevantTimestamps = sortedTimestamps.filter(ts =>
          Math.abs(ts.seconds - timestamp) <= 30
        );
      }

      if (video.chapters && video.chapters.length > 0) {
        // Sort chapters by time
        const sortedChapters = [...video.chapters].sort((a, b) => a.seconds - b.seconds);

        // Find the current chapter
        for (let i = 0; i < sortedChapters.length; i++) {
          if (i === sortedChapters.length - 1 ||
              (timestamp >= sortedChapters[i].seconds && timestamp < sortedChapters[i+1].seconds)) {
            relevantChapter = sortedChapters[i];
            break;
          }
        }
      }

      // Prepare the prompt for timestamp-specific analysis
      const prompt = `
        Please analyze this specific moment in the video:

        URL: ${pageContent.url}
        Title: ${pageContent.title}
        Current Position: ${formattedTimestamp} (${video.progress || 0}% complete)

        ${relevantChapter ? `Current Chapter: "${relevantChapter.title}" (starts at ${relevantChapter.time})` : ''}

        ${relevantTimestamps.length > 0 ? `Nearby Timestamps:
        ${relevantTimestamps.map(ts => `* ${ts.text} - ${ts.context}`).join('\n')}` : 'No specific timestamps found near this position.'}

        Video details:
        - Title: ${video.title || 'Unknown'}
        - Duration: ${video.duration || 'Unknown'}
        - Type: ${video.type || 'Unknown'}

        Page content excerpt:
        ${this.truncateContent(pageContent.content, 1500)}

        Please provide:
        1. What is likely happening at this specific moment in the video
        2. Context of this moment within the overall video narrative
        3. Why this moment might be significant
        4. What the user might have missed before this point
        5. What the user can expect to see next
        6. Any recommendations for other timestamps to check based on the user's interest in this moment
      `;

      const analysisResponse = await this.apiManager.sendRequest(prompt, {
        systemPrompt: "You are a video analysis assistant that helps users understand specific moments in videos. Provide clear, concise analysis of what's happening at the current timestamp. Include emoji in your response to make it more engaging. Format timestamps in bold when mentioning them."
      });

      this.uiManager.showResult(analysisResponse.text, `Video Analysis at ${formattedTimestamp}`);

    } catch (error) {
      this.uiManager.showResult(`Error: ${error.message}`, 'Error');
      this.uiManager.showStatus(error.message, true);
    }
  }
}
