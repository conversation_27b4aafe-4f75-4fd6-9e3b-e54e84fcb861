'use strict';

/**
 * Simple NLP Manager for basic natural language processing capabilities
 * Simplified version without complex regex patterns to avoid errors
 */
class SimpleNLPManager {
  /**
   * Create a new Simple NLP Manager
   * @param {APIManager} apiManager - The API Manager instance
   */
  constructor(apiManager) {
    this.apiManager = apiManager;
    
    // Intent patterns for recognition (simplified)
    this.intentPatterns = {
      greeting: ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening', 'greetings', 'howdy'],
      farewell: ['goodbye', 'bye', 'see you', 'farewell', 'until next time', 'talk to you later'],
      help: ['help', 'assist', 'support', 'guide', 'how do i', 'how do you', 'what can you do', 'show me how'],
      summarize: ['summarize', 'summary', 'summarization', 'give me a summary', 'tldr', 'brief overview'],
      analyze: ['analyze', 'analysis', 'examine', 'inspect', 'review'],
      search: ['search', 'find', 'look for', 'where is', 'can you find'],
      translate: ['translate', 'translation'],
      explain: ['explain', 'explanation', 'clarify', 'elaborate', 'tell me about'],
      compare: ['compare', 'comparison', 'contrast', 'difference between', 'similarities between'],
      code: ['code', 'program', 'script', 'function', 'algorithm', 'implementation'],
      clearContent: ['clear scanned content', 'clear scanned tab', 'return to current tab']
    };
  }

  /**
   * Analyze user input to detect intent
   * @param {string} userInput - The user's message
   * @returns {object} - The detected intent and confidence
   */
  detectIntent(userInput) {
    if (!userInput || typeof userInput !== 'string') {
      return { intent: 'unknown', confidence: 0 };
    }

    const normalizedInput = userInput.trim().toLowerCase();
    
    // Check each intent pattern
    for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
      for (const pattern of patterns) {
        if (normalizedInput.includes(pattern)) {
          // Calculate confidence based on pattern length relative to input
          const confidence = Math.min(pattern.length / normalizedInput.length, 0.9);
          
          return {
            intent,
            confidence: confidence
          };
        }
      }
    }
    
    // Detect general intent when no specific pattern matches
    return this.detectGeneralIntent(userInput);
  }
  
  /**
   * Detect general intent when no specific pattern matches
   * @param {string} userInput - The user's message
   * @returns {object} - The detected general intent
   */
  detectGeneralIntent(userInput) {
    const normalizedInput = userInput.trim().toLowerCase();
    
    // Check for question patterns
    if (normalizedInput.startsWith('what') || 
        normalizedInput.startsWith('how') || 
        normalizedInput.startsWith('why') || 
        normalizedInput.startsWith('when') || 
        normalizedInput.startsWith('where') || 
        normalizedInput.startsWith('who') || 
        normalizedInput.startsWith('which') ||
        normalizedInput.endsWith('?')) {
      return {
        intent: 'question',
        confidence: 0.7
      };
    }
    
    // Check for command patterns (imperative verbs at the beginning)
    const commandVerbs = ['find', 'get', 'show', 'tell', 'give', 'list', 'create', 'make', 'build', 'write'];
    for (const verb of commandVerbs) {
      if (normalizedInput.startsWith(verb + ' ')) {
        return {
          intent: 'command',
          confidence: 0.6
        };
      }
    }
    
    // Default to conversation
    return {
      intent: 'conversation',
      confidence: 0.4
    };
  }
  
  /**
   * Analyze page context to enhance understanding
   * @param {object} pageContent - The page content object
   * @returns {object} - Context analysis results
   */
  async analyzeContext(pageContent) {
    if (!pageContent) {
      try {
        pageContent = await this.apiManager.getPageContent();
      } catch (error) {
        console.error('Error getting page content for context analysis:', error);
        return { type: 'unknown', confidence: 0 };
      }
    }
    
    const url = pageContent.url?.toLowerCase() || '';
    const title = pageContent.title?.toLowerCase() || '';
    const content = pageContent.content?.toLowerCase() || '';
    
    // Detect page type based on URL, title, and content (simplified)
    const contextTypes = {
      coding: {
        urlPatterns: ['github.com', 'stackoverflow.com', 'leetcode.com', 'hackerrank.com', 'codewars.com', 'codeforces.com'],
        titlePatterns: ['code', 'programming', 'developer', 'algorithm', 'problem', 'solution'],
        contentPatterns: ['function', 'class', 'method', 'variable', 'const', 'let', 'var', 'import', 'export', 'return']
      },
      news: {
        urlPatterns: ['news', 'article', 'blog', 'post'],
        titlePatterns: ['news', 'article', 'report', 'update', 'latest'],
        contentPatterns: ['published', 'author', 'reported', 'according to', 'source']
      },
      shopping: {
        urlPatterns: ['shop', 'store', 'product', 'item', 'buy', 'purchase'],
        titlePatterns: ['shop', 'store', 'product', 'item', 'price', 'discount', 'sale'],
        contentPatterns: ['price', 'buy now', 'add to cart', 'checkout', 'shipping', 'delivery']
      },
      video: {
        urlPatterns: ['youtube.com', 'vimeo.com', 'dailymotion.com', 'twitch.tv'],
        titlePatterns: ['video', 'watch', 'stream', 'episode', 'tutorial'],
        contentPatterns: ['watch', 'view', 'subscribe', 'channel', 'like', 'comment', 'share']
      },
      social: {
        urlPatterns: ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com', 'reddit.com'],
        titlePatterns: ['post', 'feed', 'timeline', 'profile', 'social'],
        contentPatterns: ['post', 'comment', 'like', 'share', 'follow', 'friend', 'connection']
      },
      documentation: {
        urlPatterns: ['docs', 'documentation', 'guide', 'tutorial', 'reference', 'manual'],
        titlePatterns: ['documentation', 'guide', 'tutorial', 'reference', 'manual', 'how to'],
        contentPatterns: ['documentation', 'guide', 'tutorial', 'reference', 'manual', 'example', 'usage']
      },
      chess: {
        urlPatterns: ['chess.com', 'lichess.org', 'chess24.com', 'chessbase.com'],
        titlePatterns: ['chess', 'game', 'match', 'tournament', 'puzzle'],
        contentPatterns: ['chess', 'board', 'piece', 'move', 'knight', 'bishop', 'rook', 'queen', 'king', 'pawn']
      }
    };
    
    // Calculate confidence scores for each context type
    const scores = {};
    
    for (const [type, patterns] of Object.entries(contextTypes)) {
      let score = 0;
      
      // Check URL patterns
      for (const pattern of patterns.urlPatterns) {
        if (url.includes(pattern)) {
          score += 2; // URL is a strong indicator
        }
      }
      
      // Check title patterns
      for (const pattern of patterns.titlePatterns) {
        if (title.includes(pattern)) {
          score += 1.5; // Title is a good indicator
        }
      }
      
      // Check content patterns (simplified to avoid regex)
      let contentMatches = 0;
      for (const pattern of patterns.contentPatterns) {
        if (content.includes(pattern)) {
          contentMatches++;
        }
      }
      
      // Add score based on percentage of content patterns matched
      if (patterns.contentPatterns.length > 0) {
        score += (contentMatches / patterns.contentPatterns.length) * 1.5;
      }
      
      scores[type] = score;
    }
    
    // Find the context type with the highest score
    let maxScore = 0;
    let detectedType = 'general';
    
    for (const [type, score] of Object.entries(scores)) {
      if (score > maxScore) {
        maxScore = score;
        detectedType = type;
      }
    }
    
    // Calculate confidence (normalize score to 0-1 range)
    const confidence = Math.min(maxScore / 5, 0.95);
    
    return {
      type: detectedType,
      confidence: confidence,
      scores: scores
    };
  }
  
  /**
   * Generate a specialized system prompt based on intent and context
   * @param {string} intent - The detected intent
   * @param {string} contextType - The detected context type
   * @returns {string} - Specialized system prompt
   */
  generateSpecializedSystemPrompt(intent, contextType) {
    // Base system prompt that works for all contexts
    let systemPrompt = `You are a versatile AI assistant embedded in a Chrome extension that can see and analyze the current webpage and uploaded files. Your purpose is to help users with any request related to the webpage they're viewing or files they've uploaded.

You can:
- Analyze and explain webpage content
- Answer questions about what's visible on the page
- Provide summaries and extract key information
- Help with coding problems if on a programming site
- Assist with chess analysis if on a chess site
- Analyze uploaded documents, PDFs, spreadsheets, and text files
- Answer questions about uploaded files
- Compare file content with webpage content when relevant
- Offer general assistance for any other type of content

Adapt your responses based on the context of the webpage, uploaded files, and the user's question. Be helpful, informative, and concise. Format your responses with Markdown for better readability when appropriate.`;

    // Add intent-specific instructions
    if (intent === 'summarize') {
      systemPrompt += `\n\nThe user is asking for a summary. Focus on providing a concise overview of the main points, key information, and important details. Structure your summary with clear sections and bullet points where appropriate. Aim to be comprehensive yet brief.`;
    } else if (intent === 'analyze') {
      systemPrompt += `\n\nThe user is asking for analysis. Provide a detailed examination of the content, including patterns, insights, and implications. Consider different perspectives and highlight both strengths and weaknesses. Support your analysis with specific examples from the content.`;
    } else if (intent === 'explain') {
      systemPrompt += `\n\nThe user is asking for an explanation. Break down complex concepts into simpler terms, use analogies where helpful, and provide step-by-step clarification. Make sure your explanation is accessible and easy to understand.`;
    } else if (intent === 'code') {
      systemPrompt += `\n\nThe user is asking about code. Provide clear, well-commented code examples, explain the logic and approach, and consider efficiency and best practices. If debugging, identify potential issues and suggest fixes.`;
    } else if (intent === 'search') {
      systemPrompt += `\n\nThe user is searching for specific information. Focus on providing precise, relevant results that directly address their search query. Highlight the most important matches and provide context for each result.`;
    }

    // Add context-specific instructions
    if (contextType === 'coding') {
      systemPrompt += `\n\nThe current context is related to coding or programming. Focus on providing technically accurate information, code examples, algorithm explanations, or debugging help as appropriate. Consider efficiency, best practices, and potential edge cases in your responses.`;
    } else if (contextType === 'news') {
      systemPrompt += `\n\nThe current context is related to news or articles. Focus on providing factual information, summarizing key points, identifying main arguments, and distinguishing between facts and opinions. Consider the source and potential biases in your analysis.`;
    } else if (contextType === 'shopping') {
      systemPrompt += `\n\nThe current context is related to shopping or products. Focus on providing objective information about products, comparing features, discussing value propositions, and highlighting important considerations for purchasing decisions.`;
    } else if (contextType === 'video') {
      systemPrompt += `\n\nThe current context is related to video content. Focus on discussing the video content, summarizing key points, analyzing visual elements, and providing context about the creator or platform as relevant.`;
    } else if (contextType === 'chess') {
      systemPrompt += `\n\nThe current context is related to chess. Focus on analyzing positions, explaining moves, discussing strategies, and providing accurate chess notation. Consider both tactical and strategic elements in your analysis.`;
    } else if (contextType === 'documentation') {
      systemPrompt += `\n\nThe current context is related to documentation or guides. Focus on clarifying technical concepts, explaining procedures, providing examples of usage, and connecting related information to help the user understand the documentation better.`;
    }

    return systemPrompt;
  }
  
  /**
   * Process user input to enhance the prompt with NLP insights
   * @param {string} userInput - The user's message
   * @param {object} pageContent - The page content object
   * @returns {object} - Enhanced prompt information
   */
  async processUserInput(userInput, pageContent) {
    // Detect intent from user input
    const intentAnalysis = this.detectIntent(userInput);
    
    // Analyze context from page content
    const contextAnalysis = await this.analyzeContext(pageContent);
    
    // Generate specialized system prompt
    const systemPrompt = this.generateSpecializedSystemPrompt(
      intentAnalysis.intent, 
      contextAnalysis.type
    );
    
    return {
      originalInput: userInput,
      intentAnalysis,
      contextAnalysis,
      systemPrompt,
      enhancedPrompt: userInput // Keep original input for now, could be modified in future versions
    };
  }
}

// Export the class
window.SimpleNLPManager = SimpleNLPManager;
