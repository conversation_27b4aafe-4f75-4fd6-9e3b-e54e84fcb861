/**
 * Creative Studio Dialog
 * Provides drawing tools for annotating web pages
 */
class CreativeStudioDialog {
  /**
   * Initialize the Creative Studio Dialog
   * @param {FeatureManager} featureManager - The feature manager instance
   */
  constructor(featureManager) {
    this.featureManager = featureManager;
    this.uiManager = featureManager.uiManager;
    this.isActive = false;
    this.currentTool = 'pen'; // Default tool: pen, highlighter, eraser
    this.currentColor = '#FF4F18'; // Default color: Radiant Orange
    this.lineWidth = 3; // Default line width
    this.highlighterOpacity = 0.5; // Default highlighter opacity
    this.canvas = null;
    this.ctx = null;
    this.isDrawing = false;
    this.lastX = 0;
    this.lastY = 0;

    // Create dialog element if it doesn't exist
    this.createDialogElement();
  }

  /**
   * Create the dialog element
   */
  createDialogElement() {
    // Check if dialog already exists
    if (document.getElementById('creativeStudioDialog')) {
      return;
    }

    // Create dialog container
    const dialog = document.createElement('div');
    dialog.id = 'creativeStudioDialog';
    dialog.className = 'modal-dialog creative-studio-dialog';

    // Create dialog content
    dialog.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header-content">
            <div class="modal-icon">
              <i class="fas fa-paint-brush"></i>
            </div>
            <div class="modal-title">
              <h3>Creative Studio</h3>
              <p>Draw and annotate on any webpage</p>
            </div>
          </div>
          <button id="closeCreativeStudioDialog" class="close-btn"><i class="fas fa-times"></i></button>
        </div>
        <div class="modal-body">
          <p>Use these tools to draw and annotate on the current webpage.</p>

          <div class="drawing-tools">
            <div class="tool-section">
              <h4>Drawing Tools</h4>
              <div class="tool-buttons">
                <button id="penTool" class="tool-btn active" title="Pen Tool">
                  <i class="fas fa-pen"></i>
                </button>
                <button id="highlighterTool" class="tool-btn" title="Highlighter Tool">
                  <i class="fas fa-highlighter"></i>
                </button>
                <button id="eraserTool" class="tool-btn" title="Clear Canvas">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </div>

            <div class="tool-section">
              <h4>Colors</h4>
              <div class="color-picker">
                <button class="color-btn active" data-color="#FF4F18" style="background-color: #FF4F18;"></button>
                <button class="color-btn" data-color="#4361EE" style="background-color: #4361EE;"></button>
                <button class="color-btn" data-color="#3A0CA3" style="background-color: #3A0CA3;"></button>
                <button class="color-btn" data-color="#4CC9F0" style="background-color: #4CC9F0;"></button>
                <button class="color-btn" data-color="#F72585" style="background-color: #F72585;"></button>
                <button class="color-btn" data-color="#4ECB71" style="background-color: #4ECB71;"></button>
                <button class="color-btn" data-color="#FFFFFF" style="background-color: #FFFFFF; border: 1px solid #ccc;"></button>
                <button class="color-btn" data-color="#000000" style="background-color: #000000;"></button>
              </div>
            </div>
          </div>


        </div>
        <div class="modal-footer">
          <button id="startDrawingBtn">
            <i class="fas fa-paint-brush"></i>
            <span>Start Drawing</span>
            <div class="button-glow"></div>
          </button>
        </div>
      </div>
    `;

    // Add dialog to body
    document.body.appendChild(dialog);

    // Add event listeners
    this.addEventListeners();
  }

  /**
   * Add event listeners to dialog elements
   */
  addEventListeners() {
    // Close button
    const closeBtn = document.getElementById('closeCreativeStudioDialog');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideDialog());
    }

    // Start drawing button
    const startDrawingBtn = document.getElementById('startDrawingBtn');
    if (startDrawingBtn) {
      startDrawingBtn.addEventListener('click', () => this.startDrawing());
    }

    // Tool buttons
    const penTool = document.getElementById('penTool');
    const highlighterTool = document.getElementById('highlighterTool');
    const eraserTool = document.getElementById('eraserTool');

    if (penTool) {
      penTool.addEventListener('click', () => this.setTool('pen'));
    }

    if (highlighterTool) {
      highlighterTool.addEventListener('click', () => this.setTool('highlighter'));
    }

    if (eraserTool) {
      eraserTool.addEventListener('click', () => {
        // Clear the canvas instead of setting eraser tool
        this.clearCanvas();

        // Keep pen tool active
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
        penTool.classList.add('active');
        this.setTool('pen');
      });
    }

    // Color buttons
    const colorBtns = document.querySelectorAll('.color-btn');
    colorBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const color = btn.getAttribute('data-color');
        this.setColor(color);

        // Update active state
        colorBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
      });
    });

    // Close dialog when clicking outside
    const dialog = document.getElementById('creativeStudioDialog');
    if (dialog) {
      dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
          this.hideDialog();
        }
      });
    }
  }

  /**
   * Show the Creative Studio dialog
   */
  showDialog() {
    const dialog = document.getElementById('creativeStudioDialog');
    if (dialog) {
      dialog.style.display = 'flex';
      this.uiManager.showStatus('Creative Studio opened', false, 3000);
    }
  }

  /**
   * Hide the Creative Studio dialog
   */
  hideDialog() {
    const dialog = document.getElementById('creativeStudioDialog');
    if (dialog) {
      dialog.style.display = 'none';

      // If drawing is active, stop it
      if (this.isActive) {
        this.stopDrawing();
      }
    }
  }

  /**
   * Set the current drawing tool
   * @param {string} tool - The tool to set (pen, highlighter, eraser)
   */
  setTool(tool) {
    this.currentTool = tool;

    // Update UI
    const toolBtns = document.querySelectorAll('.tool-btn');
    toolBtns.forEach(btn => btn.classList.remove('active'));

    const activeBtn = document.getElementById(`${tool}Tool`);
    if (activeBtn) {
      activeBtn.classList.add('active');
    }

    // Update cursor if canvas exists
    if (this.canvas) {
      switch (tool) {
        case 'pen':
          this.canvas.style.cursor = 'url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'><path d=\'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25z\'/></svg>"), auto';
          break;
        case 'highlighter':
          this.canvas.style.cursor = 'url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'><path d=\'M10 20H5v2h5v-2zm9-18h-5v2h5V2zm-9 18H5v2h5v-2z\'/></svg>"), auto';
          break;
        case 'eraser':
          this.canvas.style.cursor = 'url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'><path d=\'M15.14 3c-.51 0-1.02.2-1.41.59L2.59 14.73c-.78.77-.78 2.04 0 2.83L5.03 20h7.66l8.72-8.72c.79-.78.79-2.05 0-2.83l-4.85-4.86c-.39-.39-.9-.59-1.42-.59z\'/></svg>"), auto';
          break;
        default:
          this.canvas.style.cursor = 'crosshair';
      }
    }
  }

  /**
   * Set the current drawing color
   * @param {string} color - The color to set (hex code)
   */
  setColor(color) {
    this.currentColor = color;
  }

  /**
   * Start drawing mode
   */
  async startDrawing() {
    try {
      // Hide the dialog
      this.hideDialog();

      // Get the active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // First, ensure the drawing tools script is loaded
      try {
        // Show loading status
        this.uiManager.showStatus('Initializing drawing tools...', false, 3000);

        console.log('Attempting to load drawing tools for tab:', tab.id);

        // Directly inject the drawing tools script
        try {
          const injectionResult = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content/drawing-tools.js']
          });

          console.log('Drawing tools script injection result:', injectionResult);

          // Wait a moment for the script to initialize
          await new Promise(resolve => setTimeout(resolve, 1000));

          console.log('Drawing tools script initialized');
        } catch (injectionError) {
          console.error('Error injecting drawing tools script:', injectionError);
          console.error('Error details:', JSON.stringify(injectionError, Object.getOwnPropertyNames(injectionError)));
          throw new Error(`Failed to inject drawing tools: ${injectionError.message || 'Unknown error'}`);
        }
      } catch (error) {
        console.error('Error loading drawing tools:', error);
        console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
        this.uiManager.showStatus(`Error loading drawing tools: ${error.message}. Try refreshing the page.`, true, 5000);
        return;
      }

      // Send message to content script to create canvas
      console.log('Sending createDrawingCanvas message to tab:', tab.id);

      try {
        chrome.tabs.sendMessage(tab.id, {
          action: 'createDrawingCanvas'
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error creating drawing canvas:', chrome.runtime.lastError);
            console.error('Error details:', JSON.stringify(chrome.runtime.lastError, Object.getOwnPropertyNames(chrome.runtime.lastError)));
            this.uiManager.showStatus('Error creating drawing canvas. Try refreshing the page.', true, 5000);
            return;
          }

          if (response && response.success) {
            console.log('Drawing canvas created successfully:', response);
            this.isActive = true;
            this.uiManager.showStatus('Drawing mode activated. Click the Stop button when finished.', false, 5000);

            // Show floating controls
            this.showFloatingControls();
          } else {
            console.error('Failed to create drawing canvas:', response ? response.error : 'Unknown error');
            this.uiManager.showStatus(`Error creating drawing canvas: ${response ? response.error : 'Unknown error'}`, true, 5000);
          }
        });
      } catch (error) {
        console.error('Exception sending message to create drawing canvas:', error);
        console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
        this.uiManager.showStatus(`Error creating drawing canvas: ${error.message || 'Unknown error'}`, true, 5000);
      }
    } catch (error) {
      console.error('Error starting drawing:', error);
      this.uiManager.showStatus('Error starting drawing', true, 5000);
    }
  }

  /**
   * Stop drawing mode
   */
  stopDrawing() {
    try {
      // Get the active tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const tab = tabs[0];

        // Send message to content script to remove canvas
        chrome.tabs.sendMessage(tab.id, {
          action: 'removeDrawingCanvas'
        }, () => {
          if (chrome.runtime.lastError) {
            console.error('Error removing drawing canvas:', chrome.runtime.lastError);
            return;
          }

          this.isActive = false;
          this.uiManager.showStatus('Drawing mode deactivated', false, 3000);

          // Hide floating controls
          this.hideFloatingControls();
        });
      });
    } catch (error) {
      console.error('Error stopping drawing:', error);
    }
  }

  /**
   * Show floating drawing controls
   */
  showFloatingControls() {
    // Create floating controls if they don't exist
    if (!document.getElementById('drawingFloatingControls')) {
      const controls = document.createElement('div');
      controls.id = 'drawingFloatingControls';
      controls.className = 'drawing-floating-controls';

      controls.innerHTML = `
        <div class="drawing-tools-container">
          <button id="floatingPenTool" class="floating-tool-btn active" title="Pen Tool">
            <i class="fas fa-pen"></i>
          </button>
          <button id="floatingHighlighterTool" class="floating-tool-btn" title="Highlighter Tool">
            <i class="fas fa-highlighter"></i>
          </button>
          <button id="floatingTextTool" class="floating-tool-btn" title="Text Tool">
            <i class="fas fa-font"></i>
          </button>
          <button id="floatingEraserTool" class="floating-tool-btn" title="Clear Canvas">
            <i class="fas fa-trash-alt"></i>
          </button>
          <div class="floating-color-picker">
            <button class="floating-color-btn active" data-color="#FF4F18" style="background-color: #FF4F18;"></button>
            <button class="floating-color-btn" data-color="#4361EE" style="background-color: #4361EE;"></button>
            <button class="floating-color-btn" data-color="#F72585" style="background-color: #F72585;"></button>
            <button class="floating-color-btn" data-color="#4ECB71" style="background-color: #4ECB71;"></button>
            <button class="floating-color-btn" data-color="#FFFF00" style="background-color: #FFFF00;"></button>
            <button class="floating-color-btn" data-color="#00FFFF" style="background-color: #00FFFF;"></button>
            <button class="floating-color-btn" data-color="#FFFFFF" style="background-color: #FFFFFF; border: 1px solid #333;"></button>
            <button class="floating-color-btn" data-color="#9C27B0" style="background-color: #9C27B0;"></button>
          </div>
          <button id="stopDrawingBtn" class="stop-drawing-btn" title="Stop Drawing">
            <i class="fas fa-stop"></i>
          </button>
        </div>
      `;

      document.body.appendChild(controls);

      // Position the controls in the top-right corner
      controls.style.top = '20px';
      controls.style.right = '20px';
      controls.style.left = 'auto';
      controls.style.bottom = 'auto';
      controls.setAttribute('title', 'Drag to move');

      // Make the controls draggable
      this.makeElementDraggable(controls);

      // Add event listeners
      const stopBtn = document.getElementById('stopDrawingBtn');
      if (stopBtn) {
        stopBtn.addEventListener('click', () => this.stopDrawing());
      }

      // Tool buttons
      const penTool = document.getElementById('floatingPenTool');
      const highlighterTool = document.getElementById('floatingHighlighterTool');
      const textTool = document.getElementById('floatingTextTool');
      const eraserTool = document.getElementById('floatingEraserTool');

      if (penTool) {
        penTool.addEventListener('click', () => {
          this.setTool('pen');
          document.querySelectorAll('.floating-tool-btn').forEach(btn => btn.classList.remove('active'));
          penTool.classList.add('active');

          // Send message to content script to update cursor
          this.updateContentScriptTool('pen');
        });
      }

      if (highlighterTool) {
        highlighterTool.addEventListener('click', () => {
          this.setTool('highlighter');
          document.querySelectorAll('.floating-tool-btn').forEach(btn => btn.classList.remove('active'));
          highlighterTool.classList.add('active');

          // Send message to content script to update cursor
          this.updateContentScriptTool('highlighter');
        });
      }

      if (textTool) {
        textTool.addEventListener('click', () => {
          this.setTool('text');
          document.querySelectorAll('.floating-tool-btn').forEach(btn => btn.classList.remove('active'));
          textTool.classList.add('active');

          // Send message to content script to update cursor and prompt for text
          this.addText();
        });
      }

      if (eraserTool) {
        eraserTool.addEventListener('click', () => {
          // Instead of setting eraser tool, clear the entire canvas
          this.clearCanvas();

          // Keep pen tool active
          document.querySelectorAll('.floating-tool-btn').forEach(btn => btn.classList.remove('active'));
          penTool.classList.add('active');
          this.setTool('pen');
          this.updateContentScriptTool('pen');
        });
      }

      // Color buttons
      const colorBtns = document.querySelectorAll('.floating-color-btn');
      colorBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          const color = btn.getAttribute('data-color');
          this.setColor(color);

          // Update active state
          colorBtns.forEach(b => b.classList.remove('active'));
          btn.classList.add('active');

          // Send message to content script to update color
          this.updateContentScriptColor(color);
        });
      });
    } else {
      // Show existing controls
      document.getElementById('drawingFloatingControls').style.display = 'block';
    }
  }

  /**
   * Update the tool in the content script
   * @param {string} tool - The tool to set
   */
  updateContentScriptTool(tool) {
    try {
      // Get the active tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const tab = tabs[0];

        // Send message to content script to set tool
        chrome.tabs.sendMessage(tab.id, {
          action: 'setDrawingTool',
          tool: tool
        });
      });
    } catch (error) {
      console.error('Error updating content script tool:', error);
    }
  }

  /**
   * Update the color in the content script
   * @param {string} color - The color to set
   */
  updateContentScriptColor(color) {
    try {
      // Get the active tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const tab = tabs[0];

        // Send message to content script to set color
        chrome.tabs.sendMessage(tab.id, {
          action: 'setDrawingColor',
          color: color
        });
      });
    } catch (error) {
      console.error('Error updating content script color:', error);
    }
  }

  /**
   * Clear the entire canvas
   */
  clearCanvas() {
    try {
      // Get the active tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const tab = tabs[0];

        // Send message to content script to clear canvas
        chrome.tabs.sendMessage(tab.id, {
          action: 'clearDrawingCanvas'
        });

        // Show status message
        if (this.uiManager) {
          this.uiManager.showStatus('Canvas cleared', false, 2000);
        }
      });
    } catch (error) {
      console.error('Error clearing canvas:', error);
    }
  }

  /**
   * Add text to the canvas
   */
  addText() {
    try {
      // Prompt user for text
      const text = prompt('Enter text to add to the canvas:', '');

      // If user cancels or enters empty text, return
      if (!text) {
        // Reset to pen tool
        document.querySelectorAll('.floating-tool-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById('floatingPenTool').classList.add('active');
        this.setTool('pen');
        this.updateContentScriptTool('pen');
        return;
      }

      // Get the active tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const tab = tabs[0];

        // Send message to content script to add text
        chrome.tabs.sendMessage(tab.id, {
          action: 'addText',
          text: text,
          color: this.currentColor
        });

        // Show status message
        if (this.uiManager) {
          this.uiManager.showStatus('Text added to canvas', false, 2000);
        }

        // Reset to pen tool after adding text
        setTimeout(() => {
          document.querySelectorAll('.floating-tool-btn').forEach(btn => btn.classList.remove('active'));
          document.getElementById('floatingPenTool').classList.add('active');
          this.setTool('pen');
          this.updateContentScriptTool('pen');
        }, 500);
      });
    } catch (error) {
      console.error('Error adding text:', error);
    }
  }

  /**
   * Hide floating drawing controls
   */
  hideFloatingControls() {
    const controls = document.getElementById('drawingFloatingControls');
    if (controls) {
      controls.style.display = 'none';
    }
  }

  /**
   * Make an element draggable
   * @param {HTMLElement} element - The element to make draggable
   */
  makeElementDraggable(element) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

    // Mouse events for desktop
    element.onmousedown = dragMouseDown;

    // Touch events for mobile
    element.ontouchstart = dragTouchStart;

    function dragMouseDown(e) {
      // Skip if the click is on a button or interactive element
      if (e.target.tagName === 'BUTTON' || e.target.closest('button') ||
          e.target.tagName === 'I' || e.target.closest('i')) {
        return;
      }

      e.preventDefault();

      // Get the mouse cursor position at startup
      pos3 = e.clientX;
      pos4 = e.clientY;

      document.onmouseup = closeDragElement;
      document.onmousemove = elementDrag;

      // Add active dragging class
      element.classList.add('dragging');
    }

    function dragTouchStart(e) {
      // Skip if the touch is on a button or interactive element
      if (e.target.tagName === 'BUTTON' || e.target.closest('button') ||
          e.target.tagName === 'I' || e.target.closest('i')) {
        return;
      }

      if (e.touches && e.touches.length) {
        // Get the touch position at startup
        pos3 = e.touches[0].clientX;
        pos4 = e.touches[0].clientY;

        document.ontouchend = closeDragElement;
        document.ontouchmove = elementTouchDrag;

        // Add active dragging class
        element.classList.add('dragging');
      }
    }

    function elementDrag(e) {
      e.preventDefault();

      // Calculate the new cursor position
      pos1 = pos3 - e.clientX;
      pos2 = pos4 - e.clientY;
      pos3 = e.clientX;
      pos4 = e.clientY;

      // Set the element's new position
      element.style.top = (element.offsetTop - pos2) + "px";
      element.style.left = (element.offsetLeft - pos1) + "px";

      // Ensure the element stays within viewport bounds
      keepInBounds(element);
    }

    function elementTouchDrag(e) {
      if (e.touches && e.touches.length) {
        // Calculate the new touch position
        pos1 = pos3 - e.touches[0].clientX;
        pos2 = pos4 - e.touches[0].clientY;
        pos3 = e.touches[0].clientX;
        pos4 = e.touches[0].clientY;

        // Set the element's new position
        element.style.top = (element.offsetTop - pos2) + "px";
        element.style.left = (element.offsetLeft - pos1) + "px";

        // Ensure the element stays within viewport bounds
        keepInBounds(element);
      }
    }

    function closeDragElement() {
      // Stop moving when mouse/touch is released
      document.onmouseup = null;
      document.onmousemove = null;
      document.ontouchend = null;
      document.ontouchmove = null;

      // Remove active dragging class
      element.classList.remove('dragging');
    }

    function keepInBounds(el) {
      const rect = el.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Keep element within horizontal bounds
      if (rect.left < 0) {
        el.style.left = "0px";
      } else if (rect.right > viewportWidth) {
        el.style.left = (viewportWidth - rect.width) + "px";
      }

      // Keep element within vertical bounds
      if (rect.top < 0) {
        el.style.top = "0px";
      } else if (rect.bottom > viewportHeight) {
        el.style.top = (viewportHeight - rect.height) + "px";
      }
    }
  }
}
