'use strict';

/**
 * Top 100 Most Visited Websites Data
 * This file contains information about the most popular websites on the internet
 * Used for enhancing the Smart Search feature's platform detection capabilities
 */

// Top 100 websites based on global traffic data
const TOP_WEBSITES = [
  {
    name: 'Google',
    domain: 'google.com',
    category: 'search',
    keywords: ['google', 'search engine', 'google search', 'google it', 'look up on google']
  },
  {
    name: 'YouTube',
    domain: 'youtube.com',
    category: 'video',
    keywords: ['youtube', 'yt', 'video', 'videos', 'watch', 'channel', 'youtuber', 'tube']
  },
  {
    name: 'Facebook',
    domain: 'facebook.com',
    category: 'social',
    keywords: ['facebook', 'fb', 'social media', 'facebook page', 'facebook profile', 'facebook group']
  },
  {
    name: 'Wikipedia',
    domain: 'wikipedia.org',
    category: 'reference',
    keywords: ['wikipedia', 'wiki', 'encyclopedia', 'information about', 'wiki page', 'look up on wikipedia', 'search wikipedia', 'wikipedia article', 'wikipedia search', 'find on wikipedia', 'wikipedia entry', 'research', 'facts about', 'definition of', 'meaning of']
  },
  {
    name: 'Instagram',
    domain: 'instagram.com',
    category: 'social',
    keywords: ['instagram', 'ig', 'insta', 'instagram profile', 'instagram photos', 'instagram stories']
  },
  {
    name: 'Bing',
    domain: 'bing.com',
    category: 'search',
    keywords: ['bing', 'microsoft search', 'bing search', 'search on bing']
  },
  {
    name: 'Reddit',
    domain: 'reddit.com',
    category: 'social',
    keywords: ['reddit', 'subreddit', 'reddit thread', 'reddit post', 'r/', 'redditor']
  },
  {
    name: 'X (Twitter)',
    domain: 'x.com',
    category: 'social',
    keywords: ['x', 'twitter', 'tweet', 'x.com', 'twitter.com', 'tweets']
  },
  {
    name: 'ChatGPT',
    domain: 'chatgpt.com',
    category: 'ai',
    keywords: ['chatgpt', 'openai chat', 'ai chat', 'gpt', 'chat ai']
  },
  {
    name: 'Yandex',
    domain: 'yandex.ru',
    category: 'search',
    keywords: ['yandex', 'russian search', 'yandex search']
  },
  {
    name: 'WhatsApp',
    domain: 'whatsapp.com',
    category: 'messaging',
    keywords: ['whatsapp', 'whatsapp web', 'messaging app', 'whatsapp messenger']
  },
  {
    name: 'Amazon',
    domain: 'amazon.com',
    category: 'shopping',
    keywords: ['amazon', 'amazon shop', 'buy on amazon', 'amazon product', 'amazon prime']
  },
  {
    name: 'Yahoo',
    domain: 'yahoo.com',
    category: 'search',
    keywords: ['yahoo', 'yahoo search', 'yahoo mail', 'yahoo news']
  },
  {
    name: 'Yahoo Japan',
    domain: 'yahoo.co.jp',
    category: 'search',
    keywords: ['yahoo japan', 'japanese yahoo', 'yahoo.co.jp']
  },
  {
    name: 'Weather.com',
    domain: 'weather.com',
    category: 'weather',
    keywords: ['weather', 'weather forecast', 'weather.com', 'check weather']
  },
  {
    name: 'DuckDuckGo',
    domain: 'duckduckgo.com',
    category: 'search',
    keywords: ['duckduckgo', 'ddg', 'private search', 'duck duck go', 'privacy search']
  },
  {
    name: 'TikTok',
    domain: 'tiktok.com',
    category: 'social',
    keywords: ['tiktok', 'tik tok', 'tiktok videos', 'tiktok trends', 'tiktok dance']
  },
  {
    name: 'Temu',
    domain: 'temu.com',
    category: 'shopping',
    keywords: ['temu', 'temu shop', 'buy on temu', 'temu deals', 'temu products']
  },
  {
    name: 'Naver',
    domain: 'naver.com',
    category: 'search',
    keywords: ['naver', 'korean search', 'naver search', 'naver korea']
  },
  {
    name: 'Microsoft Online',
    domain: 'microsoftonline.com',
    category: 'productivity',
    keywords: ['microsoft online', 'microsoft account', 'microsoft login', 'office online']
  },
  {
    name: 'Twitch',
    domain: 'twitch.tv',
    category: 'streaming',
    keywords: ['twitch', 'twitch stream', 'twitch streamer', 'live stream', 'twitch gaming']
  },
  {
    name: 'LinkedIn',
    domain: 'linkedin.com',
    category: 'professional',
    keywords: ['linkedin', 'professional network', 'job search', 'linkedin profile', 'linkedin job']
  },
  {
    name: 'Microsoft Live',
    domain: 'live.com',
    category: 'productivity',
    keywords: ['live.com', 'microsoft live', 'outlook', 'hotmail', 'live mail']
  },
  {
    name: 'Fandom',
    domain: 'fandom.com',
    category: 'entertainment',
    keywords: ['fandom', 'wiki', 'fan wiki', 'fandom wiki', 'fan page']
  },
  {
    name: 'Microsoft',
    domain: 'microsoft.com',
    category: 'technology',
    keywords: ['microsoft', 'windows', 'microsoft products', 'microsoft support']
  },
  {
    name: 'MSN',
    domain: 'msn.com',
    category: 'news',
    keywords: ['msn', 'msn news', 'microsoft news', 'msn homepage']
  },
  {
    name: 'Netflix',
    domain: 'netflix.com',
    category: 'streaming',
    keywords: ['netflix', 'netflix shows', 'netflix movies', 'streaming', 'netflix series']
  },
  {
    name: 'Microsoft Office',
    domain: 'office.com',
    category: 'productivity',
    keywords: ['office', 'microsoft office', 'office 365', 'word', 'excel', 'powerpoint']
  },
  {
    name: 'Pinterest',
    domain: 'pinterest.com',
    category: 'social',
    keywords: ['pinterest', 'pins', 'pinterest board', 'pinterest ideas', 'pin it']
  },
  {
    name: 'Mail.ru',
    domain: 'mail.ru',
    category: 'email',
    keywords: ['mail.ru', 'russian email', 'mail ru', 'mail russia']
  },
  {
    name: 'OpenAI',
    domain: 'openai.com',
    category: 'ai',
    keywords: ['openai', 'ai', 'artificial intelligence', 'openai api', 'gpt']
  },
  {
    name: 'AliExpress',
    domain: 'aliexpress.com',
    category: 'shopping',
    keywords: ['aliexpress', 'ali express', 'chinese shopping', 'buy from china', 'aliexpress products']
  },
  {
    name: 'PayPal',
    domain: 'paypal.com',
    category: 'finance',
    keywords: ['paypal', 'payment', 'send money', 'paypal account', 'online payment']
  },
  {
    name: 'VK',
    domain: 'vk.com',
    category: 'social',
    keywords: ['vk', 'vkontakte', 'russian facebook', 'vk social', 'vk.com']
  },
  {
    name: 'Canva',
    domain: 'canva.com',
    category: 'design',
    keywords: ['canva', 'design', 'graphic design', 'canva templates', 'canva design']
  },
  {
    name: 'GitHub',
    domain: 'github.com',
    category: 'development',
    keywords: ['github', 'git', 'code repository', 'open source', 'github repo', 'source code']
  },
  {
    name: 'Spotify',
    domain: 'spotify.com',
    category: 'music',
    keywords: ['spotify', 'music', 'spotify playlist', 'songs', 'spotify music', 'listen to music']
  },
  {
    name: 'Discord',
    domain: 'discord.com',
    category: 'communication',
    keywords: ['discord', 'discord server', 'discord chat', 'discord app', 'discord community']
  },
  {
    name: 'Apple',
    domain: 'apple.com',
    category: 'technology',
    keywords: ['apple', 'iphone', 'mac', 'ipad', 'apple store', 'apple products']
  },
  {
    name: 'IMDb',
    domain: 'imdb.com',
    category: 'entertainment',
    keywords: ['imdb', 'movies', 'tv shows', 'movie database', 'film ratings', 'actor', 'actress']
  },
  {
    name: 'Globo',
    domain: 'globo.com',
    category: 'news',
    keywords: ['globo', 'brazilian news', 'brazil news', 'globo tv', 'globo.com']
  },
  {
    name: 'Roblox',
    domain: 'roblox.com',
    category: 'gaming',
    keywords: ['roblox', 'roblox games', 'roblox player', 'roblox studio', 'play roblox']
  },
  {
    name: 'Amazon Japan',
    domain: 'amazon.co.jp',
    category: 'shopping',
    keywords: ['amazon japan', 'japanese amazon', 'amazon.co.jp', 'buy from japan']
  },
  {
    name: 'Quora',
    domain: 'quora.com',
    category: 'qa',
    keywords: ['quora', 'questions', 'answers', 'quora question', 'ask on quora']
  },
  {
    name: 'Bilibili',
    domain: 'bilibili.com',
    category: 'video',
    keywords: ['bilibili', 'chinese youtube', 'bilibili videos', 'bili', 'b site']
  },
  {
    name: 'Samsung',
    domain: 'samsung.com',
    category: 'technology',
    keywords: ['samsung', 'galaxy', 'samsung phone', 'samsung products', 'samsung support']
  },
  {
    name: 'eBay',
    domain: 'ebay.com',
    category: 'shopping',
    keywords: ['ebay', 'auction', 'buy on ebay', 'ebay listing', 'ebay seller', 'ebay items']
  },
  {
    name: 'New York Times',
    domain: 'nytimes.com',
    category: 'news',
    keywords: ['nytimes', 'new york times', 'nyt', 'times newspaper', 'ny times']
  },
  {
    name: 'Walmart',
    domain: 'walmart.com',
    category: 'shopping',
    keywords: ['walmart', 'walmart online', 'walmart shopping', 'walmart products', 'walmart.com']
  },
  {
    name: 'Amazon Germany',
    domain: 'amazon.de',
    category: 'shopping',
    keywords: ['amazon germany', 'german amazon', 'amazon.de', 'buy from germany']
  },
  {
    name: 'ESPN',
    domain: 'espn.com',
    category: 'sports',
    keywords: ['espn', 'sports', 'sports news', 'espn scores', 'sports updates', 'espn fantasy']
  },
  {
    name: 'Dailymotion',
    domain: 'dailymotion.com',
    category: 'video',
    keywords: ['dailymotion', 'videos', 'dailymotion videos', 'watch on dailymotion']
  },
  {
    name: 'Google Brazil',
    domain: 'google.com.br',
    category: 'search',
    keywords: ['google brazil', 'brazilian google', 'google.com.br', 'brazil search']
  },
  {
    name: 'BBC',
    domain: 'bbc.com',
    category: 'news',
    keywords: ['bbc', 'british news', 'bbc news', 'british broadcasting', 'bbc articles']
  },
  {
    name: 'BBC UK',
    domain: 'bbc.co.uk',
    category: 'news',
    keywords: ['bbc uk', 'british news', 'bbc.co.uk', 'uk news', 'british broadcasting']
  },
  {
    name: 'Rakuten',
    domain: 'rakuten.co.jp',
    category: 'shopping',
    keywords: ['rakuten', 'japanese shopping', 'rakuten japan', 'rakuten market']
  },
  {
    name: 'Telegram',
    domain: 'telegram.org',
    category: 'messaging',
    keywords: ['telegram', 'telegram app', 'telegram messenger', 'telegram chat']
  },
  {
    name: 'Indeed',
    domain: 'indeed.com',
    category: 'jobs',
    keywords: ['indeed', 'job search', 'jobs', 'indeed jobs', 'job listings', 'employment']
  },
  {
    name: 'CNN',
    domain: 'cnn.com',
    category: 'news',
    keywords: ['cnn', 'news', 'cnn news', 'cable news', 'us news', 'breaking news']
  },
  {
    name: 'Zen',
    domain: 'dzen.ru',
    category: 'news',
    keywords: ['dzen', 'zen', 'russian news', 'dzen.ru', 'yandex zen']
  },
  {
    name: 'Booking.com',
    domain: 'booking.com',
    category: 'travel',
    keywords: ['booking', 'hotel booking', 'accommodation', 'book hotel', 'travel booking']
  },
  {
    name: 'Google UK',
    domain: 'google.co.uk',
    category: 'search',
    keywords: ['google uk', 'british google', 'google.co.uk', 'uk search']
  },
  {
    name: 'USPS',
    domain: 'usps.com',
    category: 'shipping',
    keywords: ['usps', 'postal service', 'mail tracking', 'shipping', 'post office', 'mail delivery']
  },
  {
    name: 'Zoom',
    domain: 'zoom.us',
    category: 'video conferencing',
    keywords: ['zoom', 'video call', 'zoom meeting', 'zoom call', 'video conference']
  },
  {
    name: 'Amazon UK',
    domain: 'amazon.co.uk',
    category: 'shopping',
    keywords: ['amazon uk', 'british amazon', 'amazon.co.uk', 'buy from uk']
  },
  {
    name: 'Adobe',
    domain: 'adobe.com',
    category: 'software',
    keywords: ['adobe', 'photoshop', 'illustrator', 'creative cloud', 'adobe software', 'pdf']
  },
  {
    name: 'UOL',
    domain: 'uol.com.br',
    category: 'news',
    keywords: ['uol', 'brazilian portal', 'uol brazil', 'uol news', 'uol.com.br']
  },
  {
    name: 'Etsy',
    domain: 'etsy.com',
    category: 'shopping',
    keywords: ['etsy', 'handmade', 'crafts', 'etsy shop', 'handcrafted', 'artisan']
  },
  {
    name: 'Steam',
    domain: 'steampowered.com',
    category: 'gaming',
    keywords: ['steam', 'steam games', 'valve', 'pc games', 'game store', 'steam store']
  },
  {
    name: 'Marca',
    domain: 'marca.com',
    category: 'sports',
    keywords: ['marca', 'spanish sports', 'football news', 'marca sports', 'la liga']
  },
  {
    name: 'Ozon',
    domain: 'ozon.ru',
    category: 'shopping',
    keywords: ['ozon', 'russian amazon', 'ozon.ru', 'russian shopping', 'buy in russia']
  },
  {
    name: 'Cricbuzz',
    domain: 'cricbuzz.com',
    category: 'sports',
    keywords: ['cricbuzz', 'cricket', 'cricket scores', 'cricket news', 'cricket live']
  },
  {
    name: 'Prime Video',
    domain: 'primevideo.com',
    category: 'streaming',
    keywords: ['prime video', 'amazon prime', 'amazon video', 'prime movies', 'prime shows']
  },
  {
    name: 'Google Germany',
    domain: 'google.de',
    category: 'search',
    keywords: ['google germany', 'german google', 'google.de', 'germany search']
  },
  {
    name: 'AccuWeather',
    domain: 'accuweather.com',
    category: 'weather',
    keywords: ['accuweather', 'weather forecast', 'weather report', 'accurate weather']
  },
  {
    name: 'Rutube',
    domain: 'rutube.ru',
    category: 'video',
    keywords: ['rutube', 'russian youtube', 'rutube videos', 'russian videos']
  },
  {
    name: 'Canvas',
    domain: 'instructure.com',
    category: 'education',
    keywords: ['canvas', 'instructure', 'learning management', 'online courses', 'education platform']
  },
  {
    name: 'Disney+',
    domain: 'disneyplus.com',
    category: 'streaming',
    keywords: ['disney plus', 'disney+', 'disney streaming', 'disney shows', 'disney movies']
  },
  {
    name: 'Wikipedia (Spanish)',
    domain: 'es.wikipedia.org',
    category: 'reference',
    keywords: ['spanish wikipedia', 'wikipedia en español', 'wikipedia española', 'wikipedia spain', 'spanish wiki', 'spanish encyclopedia']
  },
  {
    name: 'Wikipedia (French)',
    domain: 'fr.wikipedia.org',
    category: 'reference',
    keywords: ['french wikipedia', 'wikipedia en français', 'wikipedia française', 'wikipedia france', 'french wiki', 'french encyclopedia']
  },
  {
    name: 'Wikipedia (German)',
    domain: 'de.wikipedia.org',
    category: 'reference',
    keywords: ['german wikipedia', 'wikipedia auf deutsch', 'wikipedia deutsche', 'wikipedia germany', 'german wiki', 'german encyclopedia']
  },
  {
    name: 'Wikipedia (Chinese)',
    domain: 'zh.wikipedia.org',
    category: 'reference',
    keywords: ['chinese wikipedia', 'wikipedia 中文', 'wikipedia china', 'chinese wiki', 'chinese encyclopedia', 'mandarin wikipedia']
  },
  {
    name: 'Wikipedia (Japanese)',
    domain: 'ja.wikipedia.org',
    category: 'reference',
    keywords: ['japanese wikipedia', 'wikipedia 日本語', 'wikipedia japan', 'japanese wiki', 'japanese encyclopedia']
  },
  {
    name: 'Wikipedia (Russian)',
    domain: 'ru.wikipedia.org',
    category: 'reference',
    keywords: ['russian wikipedia', 'wikipedia на русском', 'wikipedia russia', 'russian wiki', 'russian encyclopedia']
  },
  {
    name: 'Wikipedia (Portuguese)',
    domain: 'pt.wikipedia.org',
    category: 'reference',
    keywords: ['portuguese wikipedia', 'wikipedia em português', 'wikipedia portuguesa', 'wikipedia brazil', 'portuguese wiki', 'portuguese encyclopedia']
  },
  {
    name: 'Wikipedia (Italian)',
    domain: 'it.wikipedia.org',
    category: 'reference',
    keywords: ['italian wikipedia', 'wikipedia in italiano', 'wikipedia italiana', 'wikipedia italy', 'italian wiki', 'italian encyclopedia']
  }
];

// Export the data
window.TOP_WEBSITES = TOP_WEBSITES;
