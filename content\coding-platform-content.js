'use strict';

/**
 * Coding Platform Content Script
 * This script adds functionality to interact with coding platforms like LeetCode, HackerRank, etc.
 */

console.log('BlowAI Coding Platform content script loaded');

// Detect the coding platform
function detectCodingPlatform() {
  const url = window.location.href.toLowerCase();

  if (url.includes('leetcode.com')) {
    return 'leetcode';
  } else if (url.includes('hackerrank.com')) {
    return 'hackerrank';
  } else if (url.includes('codewars.com')) {
    return 'codewars';
  } else if (url.includes('codeforces.com')) {
    return 'codeforces';
  } else if (url.includes('hackerearth.com')) {
    return 'hackerearth';
  } else if (url.includes('topcoder.com')) {
    return 'topcoder';
  } else if (url.includes('codingame.com')) {
    return 'codingame';
  } else if (url.includes('geeksforgeeks.org')) {
    return 'geeksforgeeks';
  } else if (url.includes('codesignal.com')) {
    return 'codesignal';
  } else if (url.includes('codechef.com')) {
    return 'codechef';
  } else if (url.includes('replit.com')) {
    return 'replit';
  } else if (url.includes('codesandbox.io')) {
    return 'codesandbox';
  } else if (url.includes('jsfiddle.net')) {
    return 'jsfiddle';
  } else if (url.includes('codepen.io')) {
    return 'codepen';
  } else {
    return null;
  }
}

// Get the code editor element based on the platform
function getCodeEditor() {
  console.log('Looking for code editor...');
  const platform = detectCodingPlatform();
  console.log('Detected platform:', platform);

  // Common editor selectors
  const commonSelectors = [
    // Monaco editor
    '.monaco-editor textarea',
    '.monaco-editor .inputarea',
    '.monaco-editor',
    // CodeMirror
    '.CodeMirror textarea',
    '.CodeMirror',
    // Ace editor
    '.ace_editor textarea',
    '.ace_editor',
    // Generic editors
    '[role="code"]',
    '[role="textbox"]',
    '.editor-field',
    '.inputarea',
    'textarea[data-gramm="false"]',
    'textarea.code',
    'textarea.editor',
    // Last resort - any textarea
    'textarea'
  ];

  // Platform-specific selectors to try first
  let platformSpecificSelectors = [];

  if (platform) {
    switch (platform) {
      case 'leetcode':
        platformSpecificSelectors = [
          '.monaco-editor textarea',
          '.monaco-editor .inputarea',
          '.CodeMirror',
          '[role="code"]',
          '.editor-container textarea'
        ];
        break;

      case 'hackerrank':
        platformSpecificSelectors = [
          '.monaco-editor textarea',
          '.monaco-editor .inputarea',
          '.inputarea',
          '.CodeMirror',
          '#code-editor'
        ];
        break;

      case 'codewars':
        platformSpecificSelectors = [
          '.CodeMirror',
          '.editor-field',
          'textarea.code'
        ];
        break;

      // Add more platform-specific selectors as needed
    }
  }

  // Try platform-specific selectors first
  for (const selector of platformSpecificSelectors) {
    const editor = document.querySelector(selector);
    if (editor) {
      console.log('Found editor with platform-specific selector:', selector);
      return editor;
    }
  }

  // Try common selectors
  for (const selector of commonSelectors) {
    const editor = document.querySelector(selector);
    if (editor) {
      console.log('Found editor with common selector:', selector);
      return editor;
    }
  }

  // If still not found, try to find any element that looks like a code editor
  console.log('No editor found with standard selectors, trying advanced detection...');

  // Look for Monaco editor instances
  if (window.monaco && window.monaco.editor) {
    const editors = window.monaco.editor.getEditors();
    if (editors && editors.length > 0) {
      const editorElement = editors[0].getDomNode();
      if (editorElement) {
        console.log('Found Monaco editor instance via API');
        return editorElement;
      }
    }
  }

  // Look for Ace editor instances
  if (window.ace) {
    const aceEditors = document.querySelectorAll('.ace_editor');
    if (aceEditors.length > 0) {
      console.log('Found Ace editor instance');
      return aceEditors[0];
    }
  }

  // Look for CodeMirror instances
  if (window.CodeMirror) {
    const cmEditors = document.querySelectorAll('.CodeMirror');
    if (cmEditors.length > 0) {
      console.log('Found CodeMirror instance');
      return cmEditors[0];
    }
  }

  // Last resort: find any textarea or contenteditable div
  const textareas = document.querySelectorAll('textarea');
  if (textareas.length > 0) {
    // Try to find the most likely code editor textarea
    for (const textarea of textareas) {
      // Check if it's visible and reasonably sized
      const rect = textarea.getBoundingClientRect();
      if (rect.width > 200 && rect.height > 100) {
        console.log('Found likely code editor textarea based on size');
        return textarea;
      }
    }

    // If no suitable textarea found, return the first one
    console.log('Falling back to first textarea');
    return textareas[0];
  }

  // Try contenteditable divs
  const editableDivs = document.querySelectorAll('[contenteditable="true"]');
  if (editableDivs.length > 0) {
    console.log('Falling back to contenteditable div');
    return editableDivs[0];
  }

  console.log('No code editor found');
  return null;
}

// Insert code into the editor
function insertCode(code) {
  try {
    console.log('Attempting to insert code into editor');
    const editor = getCodeEditor();

    if (!editor) {
      console.error('Could not find code editor on this page');
      return { success: false, error: 'Could not find code editor on this page' };
    }

    console.log('Found editor:', editor.tagName, editor.className);

    // Different approaches based on editor type
    if (editor.classList && editor.classList.contains('CodeMirror')) {
      console.log('Detected CodeMirror editor');
      // CodeMirror editor
      const cm = editor.CodeMirror || window.CodeMirror;
      if (cm && cm.setValue) {
        console.log('Using CodeMirror setValue method');
        cm.setValue(code);
        return { success: true, method: 'codemirror' };
      }
    } else if (editor.tagName === 'TEXTAREA') {
      console.log('Detected textarea editor');
      // Direct textarea
      editor.value = code;

      // Trigger input event to notify the editor of changes
      const event = new Event('input', { bubbles: true });
      editor.dispatchEvent(event);

      // Also trigger change event
      const changeEvent = new Event('change', { bubbles: true });
      editor.dispatchEvent(changeEvent);

      return { success: true, method: 'textarea' };
    } else if (editor.classList && (editor.classList.contains('monaco-editor') || editor.classList.contains('ace_editor'))) {
      console.log('Detected Monaco or Ace editor');
      // Monaco or Ace editor - try to access through the textarea
      const textarea = editor.querySelector('textarea');
      if (textarea) {
        console.log('Found textarea within editor');
        textarea.value = code;

        // Trigger input event
        const event = new Event('input', { bubbles: true });
        textarea.dispatchEvent(event);

        // Also trigger change event
        const changeEvent = new Event('change', { bubbles: true });
        textarea.dispatchEvent(changeEvent);

        return { success: true, method: 'monaco-textarea' };
      }
    }

    // Try to find Monaco editor model
    if (window.monaco && window.monaco.editor) {
      console.log('Trying Monaco editor API');
      const editors = window.monaco.editor.getEditors();
      if (editors && editors.length > 0) {
        console.log('Found Monaco editor instance');
        const model = editors[0].getModel();
        if (model) {
          console.log('Setting value in Monaco model');
          model.setValue(code);
          return { success: true, method: 'monaco-api' };
        }
      }
    }

    // Try to find Ace editor instance
    if (window.ace) {
      console.log('Trying Ace editor API');
      const aceEditor = window.ace.edit(editor) || window.ace.edit(document.querySelector('.ace_editor'));
      if (aceEditor) {
        console.log('Found Ace editor instance');
        aceEditor.setValue(code, -1); // -1 moves cursor to start
        return { success: true, method: 'ace-api' };
      }
    }

    // If we couldn't insert using specific methods, try a more aggressive approach
    // This uses clipboard and keyboard events to simulate user pasting
    console.log('Using clipboard fallback method');

    // Set the clipboard to our code
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');

      // Focus the editor
      if (editor.focus) {
        editor.focus();
      }

      // Try to click the editor to ensure it's focused
      try {
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        });
        editor.dispatchEvent(clickEvent);
      } catch (e) {
        console.log('Click simulation failed:', e);
      }

      // Wait a moment for focus
      setTimeout(() => {
        // Select all existing content (Ctrl+A)
        try {
          document.execCommand('selectAll', false, null);
          console.log('Selected all text');
        } catch (e) {
          console.log('Select all failed:', e);
        }

        // Wait a moment for selection
        setTimeout(() => {
          // Paste the new code (Ctrl+V)
          try {
            document.execCommand('paste', false, null);
            console.log('Pasted code');
          } catch (e) {
            console.log('Paste failed:', e);
          }
        }, 100);
      }, 100);
    }).catch(err => {
      console.error('Clipboard write failed:', err);
    });

    return { success: true, method: 'clipboard' };
  } catch (error) {
    console.error('Error inserting code:', error);
    return { success: false, error: error.message };
  }
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    console.log('Received message in coding platform content script:', message);

    if (!message || !message.action) {
      console.error('Invalid message format');
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }

    // Simple ping to check if content script is loaded
    if (message.action === 'ping') {
      console.log('Received ping request');
      const platform = detectCodingPlatform();
      const hasEditor = !!getCodeEditor();
      console.log('Responding to ping:', { platform, hasEditor });

      sendResponse({
        success: true,
        status: 'coding_platform_content_script_active',
        platform: platform,
        hasEditor: hasEditor
      });
      return true;
    }

    // Insert code into the editor
    if (message.action === 'insertCode') {
      console.log('Received insertCode request');

      if (!message.code) {
        console.error('No code provided');
        sendResponse({ success: false, error: 'No code provided' });
        return true;
      }

      console.log('Inserting code:', message.code.substring(0, 50) + '...');
      const result = insertCode(message.code);
      console.log('Insert result:', result);

      sendResponse(result);
      return true;
    }

    // Unknown action
    console.error('Unknown action:', message.action);
    sendResponse({ success: false, error: 'Unknown action' });
    return true;
  } catch (error) {
    console.error('Error in coding platform content script:', error);
    sendResponse({ success: false, error: 'Coding platform content script error: ' + error.message });
    return true;
  }
});

// Initialize
const platform = detectCodingPlatform();
if (platform) {
  console.log(`Coding platform detected: ${platform}`);

  // Check if we have a code editor
  const editor = getCodeEditor();
  if (editor) {
    console.log('Code editor found on page');
  } else {
    console.log('No code editor found on page');
  }
}
