'use strict';

/**
 * Notion Integration class for connecting with Notion API
 */
class NotionIntegration {
  /**
   * Initialize the Notion Integration
   * @param {StorageManager} storageManager - The storage manager instance
   */
  constructor(storageManager) {
    this.storageManager = storageManager;
    this.apiKey = null;
    this.apiBaseUrl = 'https://api.notion.com/v1';
    this.authWindowId = null;

    // Initialize
    this.init();
  }

  /**
   * Initialize the integration
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // Load API key from storage using chrome.storage.local directly
      const result = await chrome.storage.local.get('notion_api_key');
      if (result.notion_api_key) {
        this.apiKey = result.notion_api_key;
        console.log('Notion API key loaded from storage');
      }
    } catch (error) {
      console.error('Error initializing Notion integration:', error);
    }
  }

  /**
   * Check if authenticated with Notion
   * @returns {Promise<boolean>} - Whether authenticated
   */
  async isAuthenticated() {
    try {
      if (!this.apiKey) {
        return false;
      }

      // Verify the API key by making a test request
      const response = await this.makeRequest('/users/me', 'GET');

      return response && response.id ? true : false;
    } catch (error) {
      console.error('Error checking Notion authentication:', error);
      return false;
    }
  }

  /**
   * Authenticate with Notion
   * @returns {Promise<boolean>} - Whether authentication was successful
   */
  async authenticate() {
    try {
      // For Notion, we need to get an API key from the user
      const apiKey = await this.promptForApiKey();

      if (!apiKey) {
        throw new Error('No API key provided');
      }

      // Save the API key temporarily
      this.apiKey = apiKey;

      // Verify the API key
      const isValid = await this.isAuthenticated();

      if (!isValid) {
        this.apiKey = null;
        throw new Error('Invalid API key');
      }

      // Save the API key to storage using chrome.storage.local directly
      await chrome.storage.local.set({ notion_api_key: apiKey });

      return true;
    } catch (error) {
      console.error('Error authenticating with Notion:', error);
      this.apiKey = null;
      throw error;
    }
  }

  /**
   * Prompt the user for a Notion API key
   * @returns {Promise<string|null>} - The API key or null if cancelled
   */
  async promptForApiKey() {
    return new Promise(resolve => {
      const apiKey = prompt(
        'Please enter your Notion API key.\n\n' +
        'You can create an integration and get an API key at:\n' +
        'https://www.notion.so/my-integrations',
        ''
      );

      resolve(apiKey);
    });
  }

  /**
   * Disconnect from Notion
   * @returns {Promise<boolean>} - Whether disconnection was successful
   */
  async disconnect() {
    try {
      // Clear the API key
      this.apiKey = null;

      // Remove from storage using chrome.storage.local directly
      await chrome.storage.local.remove('notion_api_key');

      return true;
    } catch (error) {
      console.error('Error disconnecting from Notion:', error);
      throw error;
    }
  }

  /**
   * Make a request to the Notion API
   * @param {string} endpoint - The API endpoint
   * @param {string} method - The HTTP method
   * @param {Object} data - The request data
   * @returns {Promise<Object>} - The response data
   */
  async makeRequest(endpoint, method = 'GET', data = null) {
    try {
      if (!this.apiKey) {
        throw new Error('Not authenticated with Notion');
      }

      const url = `${this.apiBaseUrl}${endpoint}`;

      const headers = {
        'Authorization': `Bearer ${this.apiKey}`,
        'Notion-Version': '2022-06-28',
        'Content-Type': 'application/json'
      };

      const options = {
        method: method,
        headers: headers
      };

      if (data && (method === 'POST' || method === 'PATCH')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Notion API error: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error making Notion API request:', error);
      throw error;
    }
  }

  /**
   * Create a new item in Notion
   * @param {string} itemType - The type of item to create (page, database, block)
   * @param {Object} content - The content of the item
   * @param {Object} options - Additional options for the item
   * @returns {Promise<Object>} - The created item
   */
  async createItem(itemType, content, options = {}) {
    try {
      switch (itemType.toLowerCase()) {
        case 'page':
          return await this.createPage(content, options);
        case 'database':
          return await this.createDatabase(content, options);
        case 'block':
          return await this.createBlock(content, options);
        default:
          throw new Error(`Unknown item type: ${itemType}`);
      }
    } catch (error) {
      console.error(`Error creating ${itemType} in Notion:`, error);
      throw error;
    }
  }

  /**
   * Create a new page in Notion
   * @param {Object} content - The content of the page
   * @param {Object} options - Additional options for the page
   * @returns {Promise<Object>} - The created page
   */
  async createPage(content, options = {}) {
    try {
      // Check if parent is provided
      if (!options.parent) {
        // Default to creating in user's workspace
        options.parent = {
          type: 'workspace',
          workspace: true
        };
      }

      // Prepare the page data
      const pageData = {
        parent: options.parent,
        properties: {
          title: {
            title: [
              {
                text: {
                  content: content.title || 'Untitled'
                }
              }
            ]
          }
        },
        children: []
      };

      // Add content as blocks if provided
      if (content.content) {
        // Convert markdown content to Notion blocks
        pageData.children = this.convertMarkdownToBlocks(content.content);
      }

      // Create the page
      const response = await this.makeRequest('/pages', 'POST', pageData);

      return response;
    } catch (error) {
      console.error('Error creating page in Notion:', error);
      throw error;
    }
  }

  /**
   * Create a new database in Notion
   * @param {Object} content - The content of the database
   * @param {Object} options - Additional options for the database
   * @returns {Promise<Object>} - The created database
   */
  async createDatabase(content, options = {}) {
    try {
      // Check if parent is provided
      if (!options.parent) {
        throw new Error('Parent page ID is required to create a database');
      }

      // Prepare the database data
      const databaseData = {
        parent: options.parent,
        title: [
          {
            text: {
              content: content.title || 'Untitled Database'
            }
          }
        ],
        properties: content.properties || {
          Name: {
            title: {}
          }
        }
      };

      // Create the database
      const response = await this.makeRequest('/databases', 'POST', databaseData);

      return response;
    } catch (error) {
      console.error('Error creating database in Notion:', error);
      throw error;
    }
  }

  /**
   * Create a new block in Notion
   * @param {Object} content - The content of the block
   * @param {Object} options - Additional options for the block
   * @returns {Promise<Object>} - The created block
   */
  async createBlock(content, options = {}) {
    try {
      // Check if parent is provided
      if (!options.parentId) {
        throw new Error('Parent block or page ID is required');
      }

      // Prepare the block data
      const blockData = {
        children: Array.isArray(content) ? content : [content]
      };

      // Create the block
      const response = await this.makeRequest(`/blocks/${options.parentId}/children`, 'PATCH', blockData);

      return response;
    } catch (error) {
      console.error('Error creating block in Notion:', error);
      throw error;
    }
  }

  /**
   * Get items from Notion
   * @param {string} itemType - The type of items to get (pages, databases, blocks)
   * @param {Object} options - Options for filtering and sorting items
   * @returns {Promise<Array>} - The retrieved items
   */
  async getItems(itemType, options = {}) {
    try {
      switch (itemType.toLowerCase()) {
        case 'pages':
          return await this.getPages(options);
        case 'databases':
          return await this.getDatabases(options);
        case 'blocks':
          return await this.getBlocks(options);
        default:
          throw new Error(`Unknown item type: ${itemType}`);
      }
    } catch (error) {
      console.error(`Error getting ${itemType} from Notion:`, error);
      throw error;
    }
  }

  /**
   * Get pages from Notion
   * @param {Object} options - Options for filtering and sorting pages
   * @returns {Promise<Array>} - The retrieved pages
   */
  async getPages(options = {}) {
    try {
      // If a database ID is provided, query the database
      if (options.databaseId) {
        const queryData = {
          filter: options.filter || {},
          sorts: options.sorts || []
        };

        const response = await this.makeRequest(`/databases/${options.databaseId}/query`, 'POST', queryData);

        return response.results;
      }

      // Otherwise, search for pages
      const searchData = {
        query: options.query || '',
        filter: {
          property: 'object',
          value: 'page'
        },
        sort: {
          direction: 'descending',
          timestamp: 'last_edited_time'
        }
      };

      const response = await this.makeRequest('/search', 'POST', searchData);

      return response.results;
    } catch (error) {
      console.error('Error getting pages from Notion:', error);
      throw error;
    }
  }

  /**
   * Get databases from Notion
   * @param {Object} options - Options for filtering and sorting databases
   * @returns {Promise<Array>} - The retrieved databases
   */
  async getDatabases(options = {}) {
    try {
      // Search for databases
      const searchData = {
        query: options.query || '',
        filter: {
          property: 'object',
          value: 'database'
        },
        sort: {
          direction: 'descending',
          timestamp: 'last_edited_time'
        }
      };

      const response = await this.makeRequest('/search', 'POST', searchData);

      return response.results;
    } catch (error) {
      console.error('Error getting databases from Notion:', error);
      throw error;
    }
  }

  /**
   * Get blocks from Notion
   * @param {Object} options - Options for getting blocks
   * @returns {Promise<Array>} - The retrieved blocks
   */
  async getBlocks(options = {}) {
    try {
      if (!options.blockId && !options.pageId) {
        throw new Error('Block ID or page ID is required');
      }

      const id = options.blockId || options.pageId;

      const response = await this.makeRequest(`/blocks/${id}/children`, 'GET');

      return response.results;
    } catch (error) {
      console.error('Error getting blocks from Notion:', error);
      throw error;
    }
  }

  /**
   * Convert markdown content to Notion blocks
   * @param {string} markdown - The markdown content
   * @returns {Array} - The Notion blocks
   */
  convertMarkdownToBlocks(markdown) {
    if (!markdown) return [];

    const blocks = [];
    const lines = markdown.split('\n');

    let currentListItems = [];
    let inList = false;
    let listType = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (!line) {
        // Empty line, end any current list
        if (inList) {
          if (listType === 'bulleted') {
            blocks.push({
              object: 'block',
              type: 'bulleted_list_item',
              bulleted_list_item: {
                rich_text: currentListItems.map(item => ({
                  type: 'text',
                  text: { content: item }
                }))
              }
            });
          } else if (listType === 'numbered') {
            blocks.push({
              object: 'block',
              type: 'numbered_list_item',
              numbered_list_item: {
                rich_text: currentListItems.map(item => ({
                  type: 'text',
                  text: { content: item }
                }))
              }
            });
          }

          inList = false;
          listType = null;
          currentListItems = [];
        }

        continue;
      }

      // Check for headings
      if (line.startsWith('# ')) {
        blocks.push({
          object: 'block',
          type: 'heading_1',
          heading_1: {
            rich_text: [{
              type: 'text',
              text: { content: line.substring(2) }
            }]
          }
        });
      } else if (line.startsWith('## ')) {
        blocks.push({
          object: 'block',
          type: 'heading_2',
          heading_2: {
            rich_text: [{
              type: 'text',
              text: { content: line.substring(3) }
            }]
          }
        });
      } else if (line.startsWith('### ')) {
        blocks.push({
          object: 'block',
          type: 'heading_3',
          heading_3: {
            rich_text: [{
              type: 'text',
              text: { content: line.substring(4) }
            }]
          }
        });
      }
      // Check for lists
      else if (line.startsWith('- ') || line.startsWith('* ')) {
        if (inList && listType !== 'bulleted') {
          // End the current list
          if (listType === 'numbered') {
            blocks.push({
              object: 'block',
              type: 'numbered_list_item',
              numbered_list_item: {
                rich_text: currentListItems.map(item => ({
                  type: 'text',
                  text: { content: item }
                }))
              }
            });
          }

          currentListItems = [];
        }

        inList = true;
        listType = 'bulleted';
        currentListItems.push(line.substring(2));
      } else if (/^\d+\.\s/.test(line)) {
        if (inList && listType !== 'numbered') {
          // End the current list
          if (listType === 'bulleted') {
            blocks.push({
              object: 'block',
              type: 'bulleted_list_item',
              bulleted_list_item: {
                rich_text: currentListItems.map(item => ({
                  type: 'text',
                  text: { content: item }
                }))
              }
            });
          }

          currentListItems = [];
        }

        inList = true;
        listType = 'numbered';
        currentListItems.push(line.replace(/^\d+\.\s/, ''));
      }
      // Check for code blocks
      else if (line.startsWith('```')) {
        // Find the end of the code block
        const language = line.substring(3).trim();
        let codeContent = '';
        let j = i + 1;

        while (j < lines.length && !lines[j].trim().startsWith('```')) {
          codeContent += lines[j] + '\n';
          j++;
        }

        blocks.push({
          object: 'block',
          type: 'code',
          code: {
            rich_text: [{
              type: 'text',
              text: { content: codeContent }
            }],
            language: language || 'plain text'
          }
        });

        i = j; // Skip to the end of the code block
      }
      // Check for blockquotes
      else if (line.startsWith('> ')) {
        blocks.push({
          object: 'block',
          type: 'quote',
          quote: {
            rich_text: [{
              type: 'text',
              text: { content: line.substring(2) }
            }]
          }
        });
      }
      // Default to paragraph
      else {
        blocks.push({
          object: 'block',
          type: 'paragraph',
          paragraph: {
            rich_text: [{
              type: 'text',
              text: { content: line }
            }]
          }
        });
      }
    }

    // Handle any remaining list items
    if (inList) {
      if (listType === 'bulleted') {
        blocks.push({
          object: 'block',
          type: 'bulleted_list_item',
          bulleted_list_item: {
            rich_text: currentListItems.map(item => ({
              type: 'text',
              text: { content: item }
            }))
          }
        });
      } else if (listType === 'numbered') {
        blocks.push({
          object: 'block',
          type: 'numbered_list_item',
          numbered_list_item: {
            rich_text: currentListItems.map(item => ({
              type: 'text',
              text: { content: item }
            }))
          }
        });
      }
    }

    return blocks;
  }
}
