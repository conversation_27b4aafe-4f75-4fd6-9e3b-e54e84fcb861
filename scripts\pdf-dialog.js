'use strict';

/**
 * PDF Dialog class for handling PDF tools in a simple dialog
 */
class PDFDialog {
  /**
   * Initialize the PDF Dialog
   * @param {FeatureManager} featureManager - The feature manager instance
   */
  constructor(featureManager) {
    this.featureManager = featureManager;
    this.dialogElement = null;
    this.isPdfPage = false;
    this.pdfProcessor = null;
    this.queryHandler = null;
    this.chatHistory = [];

    // Import dependencies for PDF processing
    this.importDependencies().then(() => {
      // Initialize the PDF processor and query handler if dependencies are loaded
      this.initializeProcessors();
    });

    // Create the dialog element
    this.createDialog();
  }

  /**
   * Import dependencies for PDF processing
   */
  async importDependencies() {
    try {
      // Import PDF.js library if not already loaded
      if (typeof pdfjsLib === 'undefined') {
        // PDF.js is typically loaded in the background script or manifest
        console.log('PDF.js library needs to be loaded');
      }

      // Import the PDF processor and query handler
      const PDFContentProcessor = await import('./pdf-content-processor.js')
        .then(module => module.default)
        .catch(error => {
          console.error('Error importing PDF content processor:', error);
          return null;
        });

      const PDFQueryHandler = await import('./pdf-query-handler.js')
        .then(module => module.default)
        .catch(error => {
          console.error('Error importing PDF query handler:', error);
          return null;
        });

      this.PDFContentProcessor = PDFContentProcessor;
      this.PDFQueryHandler = PDFQueryHandler;

      return true;
    } catch (error) {
      console.error('Error importing dependencies:', error);
      return false;
    }
  }

  /**
   * Initialize the PDF processor and query handler
   */
  initializeProcessors() {
    if (this.PDFContentProcessor && this.PDFQueryHandler && this.featureManager.apiManager) {
      this.pdfProcessor = new this.PDFContentProcessor();
      this.queryHandler = new this.PDFQueryHandler(this.featureManager.apiManager);
    }
  }

  /**
   * Create the PDF tools dialog
   */
  createDialog() {
    // Create the dialog element if it doesn't exist
    if (!this.dialogElement) {
      this.dialogElement = document.createElement('div');
      this.dialogElement.className = 'pdf-tools-dialog';
      this.dialogElement.style.display = 'none';

      // Add the dialog content
      this.dialogElement.innerHTML = `
        <div class="pdf-tools-dialog-content">
          <div class="pdf-tools-dialog-header">
            <h3><i class="fas fa-file-pdf"></i> PDF Tools</h3>
            <button class="pdf-tools-close-btn"><i class="fas fa-times"></i></button>
          </div>
          <div class="pdf-tools-dialog-body">
            <div class="pdf-tools-section">
              <h4><i class="fas fa-file-alt"></i> Summarize PDF</h4>
              <p class="pdf-tools-description">Generate different types of summaries from your PDF document.</p>
              <div class="pdf-tools-buttons">
                <button class="pdf-tool-btn" data-action="summarize-executive">
                  <div class="btn-header">
                    <i class="fas fa-list"></i>
                    <span>Executive Summary</span>
                  </div>
                  <span class="btn-description">Brief overview of key points</span>
                </button>
                <button class="pdf-tool-btn" data-action="summarize-detailed">
                  <div class="btn-header">
                    <i class="fas fa-list-alt"></i>
                    <span>Detailed Summary</span>
                  </div>
                  <span class="btn-description">Comprehensive summary with main ideas</span>
                </button>
                <button class="pdf-tool-btn" data-action="summarize-chapter">
                  <div class="btn-header">
                    <i class="fas fa-book-open"></i>
                    <span>Chapter-by-Chapter</span>
                  </div>
                  <span class="btn-description">Summarize by chapters or sections</span>
                </button>
              </div>
            </div>

            <div class="pdf-tools-section">
              <h4><i class="fas fa-question-circle"></i> Ask Questions</h4>
              <p class="pdf-tools-description">Ask specific questions about the content of your PDF.</p>
              <div class="pdf-query-input-container">
                <input type="text" class="pdf-query-input" placeholder="Type your question about the PDF..." id="pdfQueryInput">
                <button class="pdf-query-submit-btn" id="pdfQuerySubmitBtn">
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
              <div class="pdf-query-examples">
                <p>Example questions:</p>
                <div class="pdf-query-example-chips">
                  <span class="pdf-query-example-chip" data-query="What is the main topic of this PDF?">Main topic?</span>
                  <span class="pdf-query-example-chip" data-query="Summarize the key points in this document">Key points?</span>
                  <span class="pdf-query-example-chip" data-query="What are the conclusions in this paper?">Conclusions?</span>
                  <span class="pdf-query-example-chip" data-query="Explain the methodology used in this research">Methodology?</span>
                </div>
              </div>
              <div id="pdfQueryResult"></div>
              <div id="pdfQueryHistory" style="display: none;">
                <h5><i class="fas fa-history"></i> Previous Questions</h5>
                <div id="pdfQueryHistoryItems"></div>
              </div>
            </div>

            <div class="pdf-tools-section">
              <h4><i class="fas fa-search-plus"></i> Extract Information</h4>
              <p class="pdf-tools-description">Extract specific types of information from your PDF.</p>
              <div class="pdf-tools-buttons">
                <button class="pdf-tool-btn" data-action="extract-all">
                  <div class="btn-header">
                    <i class="fas fa-clipboard-list"></i>
                    <span>All Information</span>
                  </div>
                  <span class="btn-description">Comprehensive extraction of all key information</span>
                </button>
                <button class="pdf-tool-btn" data-action="extract-topics">
                  <div class="btn-header">
                    <i class="fas fa-tags"></i>
                    <span>Topics & Themes</span>
                  </div>
                  <span class="btn-description">Identify main topics and themes</span>
                </button>
                <button class="pdf-tool-btn" data-action="extract-entities">
                  <div class="btn-header">
                    <i class="fas fa-user"></i>
                    <span>People & Organizations</span>
                  </div>
                  <span class="btn-description">Find people, organizations, and locations</span>
                </button>
                <button class="pdf-tool-btn" data-action="extract-dates">
                  <div class="btn-header">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Dates & Timeline</span>
                  </div>
                  <span class="btn-description">Identify dates and time references</span>
                </button>
                <button class="pdf-tool-btn" data-action="extract-statistics">
                  <div class="btn-header">
                    <i class="fas fa-chart-bar"></i>
                    <span>Statistics & Data</span>
                  </div>
                  <span class="btn-description">Find numbers, percentages, and statistics</span>
                </button>
                <button class="pdf-tool-btn" data-action="extract-citations">
                  <div class="btn-header">
                    <i class="fas fa-quote-right"></i>
                    <span>Citations & References</span>
                  </div>
                  <span class="btn-description">Identify references and citations</span>
                </button>
              </div>
            </div>

            <div class="pdf-tools-section">
              <h4><i class="fas fa-book"></i> Other Tools</h4>
              <p class="pdf-tools-description">Additional tools to help you work with your PDF.</p>
              <div class="pdf-tools-buttons">
                <button class="pdf-tool-btn" data-action="create-toc">
                  <div class="btn-header">
                    <i class="fas fa-list"></i>
                    <span>Create Table of Contents</span>
                  </div>
                  <span class="btn-description">Generate a structured table of contents</span>
                </button>
                <button class="pdf-tool-btn" data-action="generate-study-guide">
                  <div class="btn-header">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Generate Study Guide</span>
                  </div>
                  <span class="btn-description">Create a comprehensive study guide with key points</span>
                </button>
                <button class="pdf-tool-btn" data-action="compare-pdfs">
                  <div class="btn-header">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Compare with Another PDF</span>
                  </div>
                  <span class="btn-description">Compare content between two PDF documents</span>
                </button>
              </div>
            </div>
          </div>
          <div class="pdf-tools-dialog-footer">
            <div class="pdf-status-message" id="pdfStatusMessage">
              <i class="fas fa-info-circle"></i> Open a PDF document to use these tools
            </div>
          </div>
        </div>
      `;

      // Add the dialog to the document
      document.body.appendChild(this.dialogElement);

      // Add event listeners
      this.addEventListeners();

      // Add styles
      this.addStyles();
    }
  }

  /**
   * Add event listeners to the dialog
   */
  addEventListeners() {
    // Close button
    const closeButton = this.dialogElement.querySelector('.pdf-tools-close-btn');
    closeButton.addEventListener('click', () => {
      this.hideDialog();
    });

    // Tool buttons
    const toolButtons = this.dialogElement.querySelectorAll('.pdf-tool-btn');
    toolButtons.forEach(button => {
      button.addEventListener('click', () => {
        const action = button.getAttribute('data-action');
        this.handleAction(action);
      });
    });

    // PDF Query input and submit button
    const queryInput = this.dialogElement.querySelector('#pdfQueryInput');
    const querySubmitBtn = this.dialogElement.querySelector('#pdfQuerySubmitBtn');

    if (queryInput && querySubmitBtn) {
      // Submit query on button click
      querySubmitBtn.addEventListener('click', () => {
        const query = queryInput.value.trim();
        if (query) {
          this.handlePdfQuery(query);
          queryInput.value = '';
        }
      });

      // Submit query on Enter key
      queryInput.addEventListener('keydown', (event) => {
        if (event.key === 'Enter') {
          const query = queryInput.value.trim();
          if (query) {
            this.handlePdfQuery(query);
            queryInput.value = '';
          }
        }
      });
    }

    // Example query chips
    const exampleChips = this.dialogElement.querySelectorAll('.pdf-query-example-chip');
    exampleChips.forEach(chip => {
      chip.addEventListener('click', () => {
        const query = chip.getAttribute('data-query');
        if (query) {
          // Fill the input with the example query
          if (queryInput) {
            queryInput.value = query;
            queryInput.focus();
          }

          // Optionally, submit the query automatically
          this.handlePdfQuery(query);
        }
      });
    });

    // Close dialog when clicking outside
    this.dialogElement.addEventListener('click', (event) => {
      if (event.target === this.dialogElement) {
        this.hideDialog();
      }
    });

    // Close dialog on Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && this.dialogElement.style.display === 'flex') {
        this.hideDialog();
      }
    });
  }

  /**
   * Add styles for the dialog
   * Note: Most styles are now in the external pdf-tools-ui.css file
   */
  addStyles() {
    // Create a style element if it doesn't exist
    let styleElement = document.getElementById('pdf-dialog-styles');
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'pdf-dialog-styles';
      styleElement.textContent = `
        /* Any additional dynamic styles can be added here */

        .pdf-tools-dialog-content {
          background-color: #283b48;
          border-radius: 12px;
          width: 90%;
          max-width: 650px;
          max-height: 90vh;
          overflow-y: auto;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
          display: flex;
          flex-direction: column;
          border: 1px solid rgba(255, 255, 255, 0.1);
          animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
          from { transform: translateY(20px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }

        .pdf-tools-dialog-header {
          background: linear-gradient(135deg, #00a6c0, #48d7ce);
          padding: 15px 20px;
          border-radius: 12px 12px 0 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: white;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .pdf-tools-dialog-header h3 {
          margin: 0;
          font-size: 18px;
          display: flex;
          align-items: center;
          font-weight: 600;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .pdf-tools-dialog-header h3 i {
          margin-right: 10px;
          font-size: 20px;
        }

        .pdf-tools-close-btn {
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          font-size: 16px;
          cursor: pointer;
          padding: 8px;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
        }

        .pdf-tools-close-btn:hover {
          background-color: rgba(255, 255, 255, 0.3);
          transform: rotate(90deg);
        }

        .pdf-tools-dialog-body {
          padding: 20px;
          overflow-y: auto;
        }

        .pdf-tools-section {
          margin-bottom: 25px;
          animation: fadeIn 0.5s ease;
        }

        .pdf-tools-section h4 {
          margin: 0 0 8px 0;
          color: #48d7ce;
          font-size: 16px;
          display: flex;
          align-items: center;
          font-weight: 600;
        }

        .pdf-tools-section h4 i {
          margin-right: 10px;
          color: #00a6c0;
        }

        .pdf-tools-description {
          color: #ccc;
          font-size: 14px;
          margin: 0 0 15px 0;
          padding-left: 26px;
        }

        .pdf-tools-buttons {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .pdf-tool-btn {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 12px 15px;
          color: white;
          cursor: pointer;
          text-align: left;
          display: flex;
          flex-direction: column;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .pdf-tool-btn::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 4px;
          height: 100%;
          background: linear-gradient(135deg, #00a6c0, #48d7ce);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .pdf-tool-btn:hover::before {
          opacity: 1;
        }

        .btn-header {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
        }

        .btn-header i {
          margin-right: 10px;
          color: #00a6c0;
          width: 16px;
          text-align: center;
          font-size: 16px;
        }

        .btn-header span {
          font-weight: 500;
        }

        .btn-description {
          font-size: 12px;
          color: #aaa;
          padding-left: 26px;
        }

        .pdf-tool-btn:hover {
          background: rgba(255, 255, 255, 0.12);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 166, 192, 0.2);
        }

        .pdf-tool-btn:active {
          transform: translateY(0);
          box-shadow: 0 2px 6px rgba(0, 166, 192, 0.1);
        }

        .pdf-tools-dialog-footer {
          padding: 15px 20px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          background: rgba(0, 0, 0, 0.2);
          border-radius: 0 0 12px 12px;
        }

        .pdf-status-message {
          font-size: 14px;
          display: flex;
          align-items: center;
          padding: 8px 12px;
          border-radius: 6px;
          background: rgba(0, 0, 0, 0.2);
        }

        .pdf-status-message i {
          margin-right: 8px;
          font-size: 16px;
        }

        .pdf-status-success {
          color: #4CAF50;
          border-left: 3px solid #4CAF50;
        }

        .pdf-status-error {
          color: #F44336;
          border-left: 3px solid #F44336;
        }

        /* Disabled state for buttons */
        .pdf-tool-btn.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          pointer-events: none;
        }

        .pdf-tool-btn.disabled:hover {
          transform: none;
          box-shadow: none;
        }

        /* Loading state for buttons */
        .pdf-tool-btn.loading {
          background: linear-gradient(135deg, rgba(0, 166, 192, 0.2), rgba(72, 215, 206, 0.2));
          pointer-events: none;
        }

        .btn-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #48d7ce;
        }

        .btn-loading i {
          margin-right: 8px;
          color: #00a6c0;
        }

        /* PDF Query Input Styles */
        .pdf-query-input-container {
          display: flex;
          margin-bottom: 15px;
          position: relative;
        }

        .pdf-query-input {
          flex: 1;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          padding: 12px 15px;
          color: white;
          font-size: 14px;
          outline: none;
          transition: all 0.3s ease;
        }

        .pdf-query-input:focus {
          border-color: #00a6c0;
          box-shadow: 0 0 0 2px rgba(0, 166, 192, 0.3);
        }

        .pdf-query-input::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        .pdf-query-submit-btn {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          background: linear-gradient(135deg, #00a6c0, #48d7ce);
          border: none;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .pdf-query-submit-btn:hover {
          transform: translateY(-50%) scale(1.05);
          box-shadow: 0 0 10px rgba(0, 166, 192, 0.5);
        }

        .pdf-query-examples {
          margin-bottom: 15px;
        }

        .pdf-query-examples p {
          color: #aaa;
          font-size: 12px;
          margin: 0 0 8px 0;
        }

        .pdf-query-example-chips {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .pdf-query-example-chip {
          background: rgba(0, 166, 192, 0.2);
          border: 1px solid rgba(0, 166, 192, 0.3);
          border-radius: 16px;
          padding: 5px 12px;
          font-size: 12px;
          color: #48d7ce;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .pdf-query-example-chip:hover {
          background: rgba(0, 166, 192, 0.3);
          transform: translateY(-2px);
        }

        .pdf-query-history {
          margin-top: 20px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          padding-top: 15px;
        }

        .pdf-query-history h5 {
          color: #ccc;
          font-size: 14px;
          margin: 0 0 10px 0;
          font-weight: normal;
        }

        .pdf-query-history-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .pdf-query-history-item {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          padding: 10px;
        }

        .pdf-query-history-question {
          color: #48d7ce;
          font-size: 13px;
          margin-bottom: 5px;
          font-weight: 500;
        }

        .pdf-query-history-answer {
          color: #ddd;
          font-size: 12px;
        }

        /* PDF Query Result Styles */
        .pdf-query-result {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 8px;
          padding: 15px;
          margin-top: 15px;
          border-left: 3px solid #00a6c0;
          max-height: 300px;
          overflow-y: auto;
        }

        .pdf-query-result-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #48d7ce;
        }

        .pdf-query-result-loading i {
          margin-right: 10px;
        }

        .pdf-query-result-content {
          color: #fff;
          font-size: 14px;
          line-height: 1.5;
        }
      `;
      document.head.appendChild(styleElement);
    }
  }

  /**
   * Show the PDF tools dialog
   */
  async showDialog() {
    // Check if we're on a PDF page
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.isPdfPage = tab.url.toLowerCase().endsWith('.pdf') ||
                      tab.url.toLowerCase().includes('pdf') ||
                      tab.url.toLowerCase().includes('viewer.html');

      // Update the status message
      const statusMessage = document.getElementById('pdfStatusMessage');
      if (statusMessage) {
        if (this.isPdfPage) {
          statusMessage.innerHTML = '<i class="fas fa-check-circle"></i> PDF detected. All tools are available.';
          statusMessage.className = 'pdf-status-message pdf-status-success';
        } else {
          statusMessage.innerHTML = '<i class="fas fa-exclamation-triangle"></i> No PDF detected. Please open a PDF to use these tools.';
          statusMessage.className = 'pdf-status-message pdf-status-warning';
        }
      }

      // Disable buttons if not on a PDF page
      const toolButtons = this.dialogElement.querySelectorAll('.pdf-tool-btn');
      toolButtons.forEach(button => {
        if (this.isPdfPage) {
          button.classList.remove('disabled');
        } else {
          button.classList.add('disabled');
        }
      });
    } catch (error) {
      console.error('Error checking if on PDF page:', error);
      this.isPdfPage = false;
    }

    // Show the dialog
    this.dialogElement.style.display = 'flex';
  }

  /**
   * Hide the PDF tools dialog
   */
  hideDialog() {
    this.dialogElement.style.display = 'none';
  }

  /**
   * Handle a PDF tool action
   * @param {string} action - The action to handle
   */
  async handleAction(action) {
    // If not on a PDF page, show an error
    if (!this.isPdfPage) {
      alert('Please open a PDF document to use this tool.');
      return;
    }

    // Show loading state on the button
    const button = this.dialogElement.querySelector(`[data-action="${action}"]`);
    if (button) {
      const originalContent = button.innerHTML;
      button.innerHTML = `<div class="btn-loading"><i class="fas fa-spinner fa-spin"></i> Processing...</div>`;
      button.classList.add('loading');

      // Add a small delay to show the loading state
      await new Promise(resolve => setTimeout(resolve, 300));

      // Hide the dialog
      this.hideDialog();

      // Reset button state after dialog is hidden
      setTimeout(() => {
        button.innerHTML = originalContent;
        button.classList.remove('loading');
      }, 500);
    } else {
      // Hide the dialog if button not found
      this.hideDialog();
    }

    try {
      // Handle different actions
      switch (action) {
        case 'summarize-executive':
          await this.featureManager.summarizePDF('executive');
          break;
        case 'summarize-detailed':
          await this.featureManager.summarizePDF('detailed');
          break;
        case 'summarize-chapter':
          await this.featureManager.summarizePDF('chapter');
          break;
        case 'ask-question':
          await this.featureManager.askPDFQuestion();
          break;
        case 'extract-all':
          await this.featureManager.extractPDFInfo('all');
          break;
        case 'extract-topics':
          await this.featureManager.extractPDFInfo('topics');
          break;
        case 'extract-entities':
          await this.featureManager.extractPDFInfo('entities');
          break;
        case 'extract-dates':
          await this.featureManager.extractPDFInfo('dates');
          break;
        case 'extract-statistics':
          await this.featureManager.extractPDFInfo('statistics');
          break;
        case 'extract-citations':
          await this.featureManager.extractPDFInfo('citations');
          break;
        case 'create-toc':
          await this.featureManager.createPDFTableOfContents();
          break;
        case 'generate-study-guide':
          await this.featureManager.generatePDFStudyGuide();
          break;
        case 'compare-pdfs':
          this.promptForSecondPDF();
          break;
      }
    } catch (error) {
      console.error('Error handling PDF action:', error);
      alert(`Error: ${error.message}`);
    }
  }

  /**
   * Prompt for a second PDF to compare
   */
  async promptForSecondPDF() {
    try {
      // Prompt for the URL of the second PDF
      const secondPdfUrl = prompt('Enter the URL of the second PDF to compare:');

      if (!secondPdfUrl) {
        return; // User cancelled
      }

      // Validate the URL
      if (!secondPdfUrl.toLowerCase().endsWith('.pdf')) {
        throw new Error('The provided URL does not appear to be a PDF document. Please provide a URL ending with .pdf');
      }

      // Compare the PDFs
      await this.featureManager.comparePDFs(secondPdfUrl);
    } catch (error) {
      console.error('Error comparing PDFs:', error);
      alert(`Error: ${error.message}`);
    }
  }

  /**
   * Handle a PDF query
   * @param {string} query - The user's query about the PDF
   */
  async handlePdfQuery(query) {
    if (!this.isPdfPage) {
      alert('Please open a PDF document to ask questions about it.');
      return;
    }

    try {
      // Get the current tab to extract the PDF URL
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const pdfUrl = tab.url;

      // Get the query result container
      let queryResultContainer = this.dialogElement.querySelector('#pdfQueryResult');
      if (!queryResultContainer) {
        console.error('Query result container not found');
        return;
      }

      // Clear any existing content and add the result container
      queryResultContainer.innerHTML = '';
      const resultElement = document.createElement('div');
      resultElement.className = 'pdf-query-result';
      queryResultContainer.appendChild(resultElement);
      queryResultContainer = resultElement;

      // Show loading state
      queryResultContainer.innerHTML = `
        <div class="pdf-query-result-loading">
          <i class="fas fa-spinner fa-spin"></i> Processing your query...
        </div>
      `;

      // Process the query
      let response;

      // Check if we have the PDF processor and query handler initialized
      if (this.featureManager.apiManager && (!this.pdfProcessor || !this.queryHandler)) {
        // Initialize if needed
        await this.importDependencies();
        this.initializeProcessors();
      }

      if (this.pdfProcessor && this.queryHandler) {
        // Extract PDF content if not already done
        if (!this.pdfProcessor.pdfContent) {
          queryResultContainer.innerHTML = `
            <div class="pdf-query-result-loading">
              <i class="fas fa-spinner fa-spin"></i> Extracting PDF content...
            </div>
          `;

          const extractionSuccess = await this.pdfProcessor.extractContent(pdfUrl);
          if (!extractionSuccess) {
            throw new Error('Failed to extract PDF content. Please try again.');
          }

          // Set the content in the query handler
          this.queryHandler.setPdfContent(this.pdfProcessor.pdfContent, pdfUrl);
        }

        // Process the query
        queryResultContainer.innerHTML = `
          <div class="pdf-query-result-loading">
            <i class="fas fa-spinner fa-spin"></i> Analyzing your query...
          </div>
        `;

        response = await this.queryHandler.processQuery(query, {
          metadata: this.pdfProcessor.pdfMetadata,
          structure: this.pdfProcessor.pdfStructure
        });
      } else {
        // Fall back to the feature manager if the processor is not available
        response = await this.featureManager.processCustomPDFQuery(query);
      }

      // Display the response
      if (response && response.success) {
        queryResultContainer.innerHTML = `
          <div class="pdf-query-result-content">
            ${response.answer}
          </div>
        `;

        // Add to query history
        this.addToQueryHistory(query, response.answer);
      } else {
        const errorMessage = response?.error || 'Failed to process your query. Please try again.';
        queryResultContainer.innerHTML = `
          <div class="pdf-query-result-content" style="color: #ff6b6b;">
            <i class="fas fa-exclamation-circle"></i> Error: ${errorMessage}
          </div>
        `;
      }
    } catch (error) {
      console.error('Error processing PDF query:', error);

      // Show error in the result container
      const queryResultContainer = this.dialogElement.querySelector('.pdf-query-result');
      if (queryResultContainer) {
        queryResultContainer.innerHTML = `
          <div class="pdf-query-result-content" style="color: #ff6b6b;">
            <i class="fas fa-exclamation-circle"></i> Error: ${error.message}
          </div>
        `;
      } else {
        alert(`Error: ${error.message}`);
      }
    }
  }

  /**
   * Add a query and its answer to the query history
   * @param {string} query - The user's query
   * @param {string} answer - The answer to the query
   */
  addToQueryHistory(query, answer) {
    // Add to chat history array
    this.chatHistory.unshift({
      query,
      answer,
      timestamp: new Date().toISOString()
    });

    // Limit history to 5 items
    if (this.chatHistory.length > 5) {
      this.chatHistory.pop();
    }

    // Update the history UI
    const historyContainer = this.dialogElement.querySelector('#pdfQueryHistory');
    const historyList = this.dialogElement.querySelector('#pdfQueryHistoryItems');

    if (historyContainer && historyList) {
      // Show the history container
      historyContainer.style.display = 'block';

      // Clear the list
      historyList.innerHTML = '';

      // Add history items
      this.chatHistory.forEach(item => {
        const historyItem = document.createElement('div');
        historyItem.className = 'pdf-query-history-item';

        // Truncate answer if too long
        const truncatedAnswer = item.answer.length > 150
          ? item.answer.substring(0, 150) + '...'
          : item.answer;

        historyItem.innerHTML = `
          <div class="pdf-query-history-question">
            <i class="fas fa-question-circle"></i> ${item.query}
          </div>
          <div class="pdf-query-history-answer">
            ${truncatedAnswer}
          </div>
        `;

        // Add click event to re-run the query
        historyItem.addEventListener('click', () => {
          const queryInput = this.dialogElement.querySelector('#pdfQueryInput');
          if (queryInput) {
            queryInput.value = item.query;
            queryInput.focus();
          }
        });

        historyList.appendChild(historyItem);
      });
    }
  }
}
