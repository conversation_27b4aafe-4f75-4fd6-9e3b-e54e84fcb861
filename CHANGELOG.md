# Changelog

All notable changes to BrowzyAI will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.1] - 2024-12-19

### Fixed
- **Gemini Model Deprecation**: Fixed deprecated Gemini Pro and Gemini Pro Vision models that were returning errors since July 12, 2024
  - Replaced deprecated `gemini-pro` and `gemini-pro-vision` models
  - Added new working models: `gemini-1.5-flash-8b` and `gemini-2.0-flash-exp`
  - Updated model mapping to automatically handle deprecated models
  - All Gemini models now use current, supported versions

- **API Key Independence**: Fixed issue where extension required all API keys even when using only one provider
  - Users can now use only Gemini API key without needing OpenRouter key
  - Each provider (OpenAI, Gemini, OpenRouter) now works independently
  - Removed fallback logic that was causing cross-provider dependencies
  - Simplified validation to only check for the specific provider being used

- **Backend URL Configuration**: Updated backend and frontend requests to use browzyai.com instead of localhost
  - Dashboard URL: `https://browzyai.com`
  - API URL: `https://api.browzyai.com`
  - Updated CORS and CSP configurations
  - Added browzyai.com domains to host permissions

### Added
- **Docker Support**: Comprehensive Docker implementation for development and production
  - Multi-stage Dockerfiles for API and Dashboard
  - Docker Compose configurations for development and production
  - Environment-specific configurations
  - Database initialization scripts
  - Nginx configuration for dashboard
  - Setup scripts for easy deployment

### Changed
- **Gemini Model Options**: Updated available Gemini models in the UI
  - Removed: `gemini-1.5-pro`, `gemini-1.5-pro-latest`, `gemini-pro`, `gemini-pro-vision`
  - Added: `gemini-1.5-flash-8b`, `gemini-2.0-flash-exp`
  - Kept: `gemini-1.5-flash`, `gemini-1.5-flash-latest`

- **Error Handling**: Improved error messages for API key validation
  - Provider-specific error messages
  - Removed confusing fallback warnings
  - Clear instructions for required API keys

### Technical Improvements
- Enhanced model mapping logic for better compatibility
- Simplified API key validation flow
- Removed complex fallback mechanisms
- Updated environment configurations
- Improved Docker containerization
- Better separation of concerns between providers

### Documentation
- Added comprehensive Docker setup documentation
- Created API key independence fix documentation
- Updated Gemini model fix documentation
- Added changelog for version tracking

## [1.0.0] - 2024-12-18

### Added
- Initial release of BrowzyAI Chrome Extension
- Multi-provider AI support (OpenAI, Gemini, OpenRouter, Anthropic)
- Chrome Side Panel integration
- Smart Search functionality
- PDF processing capabilities
- Video analysis features
- Creative Studio tools
- Destress mode
- Chat history management
- Settings and API key management
- Usage statistics tracking
- Dashboard connectivity
- Multiple UI themes and customization options

### Features
- **AI Chat**: Conversational AI with multiple model options
- **Web Analysis**: Analyze and summarize web pages
- **PDF Tools**: Extract and analyze PDF content
- **Video Processing**: Analyze video content and timestamps
- **Smart Search**: Intelligent web search with AI enhancement
- **Creative Suite**: Drawing and annotation tools
- **Settings Management**: Comprehensive configuration options
- **Statistics**: Usage tracking and analytics
- **Dashboard Integration**: Sync with external dashboard
- **Multi-browser Support**: Works in Chrome and Brave browsers
