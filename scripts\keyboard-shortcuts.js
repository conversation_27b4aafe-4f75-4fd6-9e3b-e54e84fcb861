/**
 * Keyboard Shortcuts Manager
 * Handles keyboard shortcuts and the shortcuts dialog
 */
class KeyboardShortcutsManager {
  /**
   * Initialize the keyboard shortcuts manager
   */
  constructor() {
    this.initializeShortcutsDialog();
    this.registerAdditionalShortcuts();
    console.log('Keyboard shortcuts manager initialized');
  }

  /**
   * Initialize the keyboard shortcuts dialog
   */
  initializeShortcutsDialog() {
    // Get dialog elements
    this.shortcutsDialog = document.getElementById('keyboardShortcutsDialog');
    this.closeShortcutsDialogBtn = document.getElementById('closeShortcutsDialog');
    
    // Add event listeners
    if (this.closeShortcutsDialogBtn) {
      this.closeShortcutsDialogBtn.addEventListener('click', () => {
        this.hideShortcutsDialog();
      });
    }
    
    // Close dialog when clicking outside
    if (this.shortcutsDialog) {
      this.shortcutsDialog.addEventListener('click', (e) => {
        if (e.target === this.shortcutsDialog) {
          this.hideShortcutsDialog();
        }
      });
    }
    
    // Close dialog with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.shortcutsDialog && this.shortcutsDialog.classList.contains('active')) {
        this.hideShortcutsDialog();
      }
    });
    
    // Add event listener to the "View All" button in settings
    const showKeyboardShortcutsBtn = document.getElementById('showKeyboardShortcutsBtn');
    if (showKeyboardShortcutsBtn) {
      showKeyboardShortcutsBtn.addEventListener('click', () => {
        this.showShortcutsDialog();
      });
    }
  }

  /**
   * Show the keyboard shortcuts dialog
   */
  showShortcutsDialog() {
    if (this.shortcutsDialog) {
      this.shortcutsDialog.classList.add('active');
      // Add performance metrics
      const startTime = performance.now();
      console.log(`Shortcuts dialog opened in ${Math.round(performance.now() - startTime)}ms`);
    }
  }

  /**
   * Hide the keyboard shortcuts dialog
   */
  hideShortcutsDialog() {
    if (this.shortcutsDialog) {
      this.shortcutsDialog.classList.remove('active');
    }
  }

  /**
   * Register additional keyboard shortcuts
   */
  registerAdditionalShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Skip if in input or textarea
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
      }
      
      // Website Analyzer (Alt+W)
      if (e.altKey && e.key === 'w') {
        e.preventDefault();
        const websiteAnalyzerBtn = document.getElementById('websiteAnalyzerButton');
        if (websiteAnalyzerBtn) {
          websiteAnalyzerBtn.click();
        }
      }
      
      // Smart Search (Alt+S)
      if (e.altKey && e.key === 's') {
        e.preventDefault();
        const smartSearchBtn = document.getElementById('toggleFloatingChat');
        if (smartSearchBtn) {
          smartSearchBtn.click();
        }
      }
      
      // PDF Tools (Alt+P)
      if (e.altKey && e.key === 'p') {
        e.preventDefault();
        const pdfToolsBtn = document.getElementById('pdfToolsButton');
        if (pdfToolsBtn) {
          pdfToolsBtn.click();
        }
      }
      
      // Destress Mode (Alt+D)
      if (e.altKey && e.key === 'd') {
        e.preventDefault();
        const destressBtn = document.getElementById('destressBtn');
        if (destressBtn) {
          destressBtn.click();
        }
      }
      
      // Keyboard Shortcuts Dialog (Alt+K)
      if (e.altKey && e.key === 'k') {
        e.preventDefault();
        this.showShortcutsDialog();
      }
    });
  }
}

// Initialize the keyboard shortcuts manager when the document is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create the keyboard shortcuts manager
  window.keyboardShortcutsManager = new KeyboardShortcutsManager();
});
