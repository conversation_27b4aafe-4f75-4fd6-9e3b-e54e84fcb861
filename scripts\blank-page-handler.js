/**
 * Special handler for blank pages and new tabs
 * This script directly attaches event handlers to all interactive elements
 * to ensure they work properly on blank pages and new tabs
 */

class BlankPageHandler {
  constructor() {
    this.initialized = false;
    this.initAttempts = 0;
    this.maxInitAttempts = 10;
    this.initInterval = null;

    // Initialize immediately
    this.init();

    // Also set up an interval to try multiple times
    this.initInterval = setInterval(() => {
      this.initAttempts++;
      this.init();

      // Stop trying after max attempts
      if (this.initAttempts >= this.maxInitAttempts) {
        clearInterval(this.initInterval);
        console.log('BlankPageHandler: Max initialization attempts reached');
      }
    }, 500);
  }

  init() {
    console.log('BlankPageHandler: Initializing blank page handler');

    try {
      // Add a class to the body to indicate this is a blank page
      document.body.classList.add('blank-page-sidebar');
      document.body.classList.add('new-tab-sidebar');

      // Load critical scripts if they're not already loaded
      this.loadCriticalScripts().then(() => {
        // Make sure all manager objects are available in the global scope
        this.exposeGlobalObjects();

        // Initialize all interactive elements
        this.initializeButtons();
        this.initializeInputs();
        this.initializeTabs();
        this.initializeDropdowns();

        // Focus the input field
        this.focusInput();

        // Activate the chat tab
        this.activateChatTab();

        // Add a global flag to indicate the handler is initialized
        window.blankPageHandlerInitialized = true;

        // Mark as initialized
        this.initialized = true;
        console.log('BlankPageHandler: Initialization complete');
      });

      // Schedule additional initialization attempts to ensure everything works
      [500, 1000, 2000].forEach(delay => {
        setTimeout(() => {
          console.log(`BlankPageHandler: Scheduled re-initialization (delay: ${delay}ms)`);
          // Reload critical scripts
          this.loadCriticalScripts().then(() => {
            // Re-expose global objects
            this.exposeGlobalObjects();

            // Re-initialize UI elements
            this.initializeButtons();
            this.initializeInputs();
            this.initializeTabs();
            this.initializeDropdowns();
            this.focusInput();
          });
        }, delay);
      });
    } catch (error) {
      console.error('BlankPageHandler: Error during initialization', error);
    }
  }

  loadCriticalScripts() {
    return new Promise((resolve) => {
      const criticalScripts = [
        'scripts/sidebar-manager.js',
        'scripts/chat-manager.js',
        'scripts/api-manager.js',
        'scripts/ui-manager.js'
      ];

      // Function to load a script and return a promise
      const loadScript = (src) => {
        return new Promise((resolve, reject) => {
          if (document.querySelector(`script[src*="${src.split('/').pop()}"]`)) {
            console.log(`BlankPageHandler: Script ${src} already loaded, skipping`);
            resolve();
            return;
          }

          console.log(`BlankPageHandler: Loading ${src}`);
          const scriptElement = document.createElement('script');
          scriptElement.src = chrome.runtime.getURL(src);
          scriptElement.onload = () => {
            console.log(`BlankPageHandler: ${src} loaded successfully`);
            resolve();
          };
          scriptElement.onerror = (error) => {
            console.error(`BlankPageHandler: Error loading ${src}:`, error);
            // Resolve anyway to continue with other scripts
            resolve();
          };
          document.head.appendChild(scriptElement);
        });
      };

      // Load scripts in sequence
      const loadScriptsSequentially = async () => {
        for (const script of criticalScripts) {
          try {
            await loadScript(script);
          } catch (error) {
            console.error(`BlankPageHandler: Error loading ${script}:`, error);
          }
        }
        console.log('BlankPageHandler: All critical scripts loaded');
        resolve();
      };

      loadScriptsSequentially();
    });
  }

  exposeGlobalObjects() {
    // Make sure all manager objects are available in the global scope
    if (typeof chatManager !== 'undefined') window.chatManager = chatManager;
    if (typeof historyManager !== 'undefined') window.historyManager = historyManager;
    if (typeof exportManager !== 'undefined') window.exportManager = exportManager;
    if (typeof featureManager !== 'undefined') window.featureManager = featureManager;
    if (typeof uiManager !== 'undefined') window.uiManager = uiManager;
    if (typeof apiManager !== 'undefined') window.apiManager = apiManager;
    if (typeof sidebarManager !== 'undefined') window.sidebarManager = sidebarManager;

    // Make updateStatsDisplay available globally
    if (typeof updateStatsDisplay === 'function') {
      window.updateStatsDisplay = updateStatsDisplay;
    }

    // Initialize SidebarManager if it exists as a class but not as an instance
    if (typeof SidebarManager === 'function' && !window.sidebarManager) {
      console.log('BlankPageHandler: Creating SidebarManager instance');
      window.sidebarManager = new SidebarManager();
      if (typeof window.sidebarManager.init === 'function') {
        window.sidebarManager.init();
      }
    }

    // Initialize ChatManager if it exists as a class but not as an instance
    if (typeof ChatManager === 'function' && !window.chatManager) {
      console.log('BlankPageHandler: Creating ChatManager instance');
      window.chatManager = new ChatManager();
      if (typeof window.chatManager.init === 'function') {
        window.chatManager.init();
      }
    }

    // Initialize APIManager if it exists as a class but not as an instance
    if (typeof APIManager === 'function' && !window.apiManager) {
      console.log('BlankPageHandler: Creating APIManager instance');
      window.apiManager = new APIManager();
      if (typeof window.apiManager.init === 'function') {
        window.apiManager.init();
      }
    }

    // Initialize UIManager if it exists as a class but not as an instance
    if (typeof UIManager === 'function' && !window.uiManager) {
      console.log('BlankPageHandler: Creating UIManager instance');
      window.uiManager = new UIManager();
      if (typeof window.uiManager.init === 'function') {
        window.uiManager.init();
      }
    }

    console.log('BlankPageHandler: Global objects exposed and managers initialized');
  }

  initializeButtons() {
    const buttons = document.querySelectorAll('button');
    console.log(`BlankPageHandler: Found ${buttons.length} buttons to initialize`);

    buttons.forEach(button => {
      // Skip already initialized buttons
      if (button.hasAttribute('data-blank-page-initialized')) {
        return;
      }

      // Mark as initialized
      button.setAttribute('data-blank-page-initialized', 'true');

      // Add a direct click handler
      button.addEventListener('click', (event) => {
        console.log('BlankPageHandler: Button clicked', button.id || button.className);

        // Special handling for send button
        if (button.id === 'sendMessage') {
          this.handleSendButton(event);
        }

        // Special handling for tab buttons
        if (button.classList.contains('tab-btn')) {
          this.handleTabButton(button);
        }

        // Special handling for clear chat button
        if (button.id === 'clearChat') {
          this.handleClearChat();
        }
      });

      // Add special styling to ensure clickability
      button.style.pointerEvents = 'auto';
      button.style.cursor = 'pointer';
      button.style.zIndex = '9999';
      button.style.position = 'relative';

      console.log('BlankPageHandler: Initialized button', button.id || button.className);
    });
  }

  initializeInputs() {
    const inputs = document.querySelectorAll('input, textarea, select');
    console.log(`BlankPageHandler: Found ${inputs.length} inputs to initialize`);

    inputs.forEach(input => {
      // Skip already initialized inputs
      if (input.hasAttribute('data-blank-page-initialized')) {
        return;
      }

      // Mark as initialized
      input.setAttribute('data-blank-page-initialized', 'true');

      // Special handling for user input
      if (input.id === 'userInput') {
        this.initializeUserInput(input);
      }

      // Special handling for provider selector
      if (input.id === 'providerSelector') {
        this.initializeProviderSelector(input);
      }

      // Add special styling to ensure clickability
      input.style.pointerEvents = 'auto';
      input.style.zIndex = '9999';
      input.style.position = 'relative';

      console.log('BlankPageHandler: Initialized input', input.id || input.className);
    });
  }

  initializeUserInput(input) {
    // Add input event listener for auto-resize
    input.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });

    // Add keydown event listener for Enter key
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        console.log('BlankPageHandler: Enter key pressed in user input');
        this.handleSendButton();
      }
    });

    console.log('BlankPageHandler: Initialized user input');
  }

  initializeProviderSelector(select) {
    select.addEventListener('change', () => {
      console.log('BlankPageHandler: Provider changed', select.value);

      // Try to call the API manager directly
      if (window.apiManager && typeof window.apiManager.setProvider === 'function') {
        window.apiManager.setProvider(select.value);
      }

      // Also dispatch a custom event
      const event = new CustomEvent('providerChanged', {
        detail: { provider: select.value }
      });
      document.dispatchEvent(event);
    });

    console.log('BlankPageHandler: Initialized provider selector');
  }

  initializeTabs() {
    const tabs = document.querySelectorAll('.tab-btn');
    console.log(`BlankPageHandler: Found ${tabs.length} tabs to initialize`);

    tabs.forEach(tab => {
      // Skip already initialized tabs
      if (tab.hasAttribute('data-blank-page-initialized')) {
        return;
      }

      // Mark as initialized
      tab.setAttribute('data-blank-page-initialized', 'true');

      // Add a direct click handler
      tab.addEventListener('click', () => {
        this.handleTabButton(tab);
      });

      // Add special styling to ensure clickability
      tab.style.pointerEvents = 'auto';
      tab.style.cursor = 'pointer';
      tab.style.zIndex = '9999';
      tab.style.position = 'relative';

      console.log('BlankPageHandler: Initialized tab', tab.id || tab.className);
    });
  }

  initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown, .actions-dropdown-container');
    console.log(`BlankPageHandler: Found ${dropdowns.length} dropdowns to initialize`);

    dropdowns.forEach(dropdown => {
      // Skip already initialized dropdowns
      if (dropdown.hasAttribute('data-blank-page-initialized')) {
        return;
      }

      // Mark as initialized
      dropdown.setAttribute('data-blank-page-initialized', 'true');

      // Add special styling to ensure clickability
      dropdown.style.pointerEvents = 'auto';
      dropdown.style.zIndex = '9999';
      dropdown.style.position = 'relative';

      console.log('BlankPageHandler: Initialized dropdown', dropdown.id || dropdown.className);
    });
  }

  handleSendButton() {
    console.log('BlankPageHandler: Handling send button click');

    const userInput = document.getElementById('userInput');
    if (userInput && userInput.value.trim()) {
      console.log('BlankPageHandler: Sending message', userInput.value.trim());

      // Store the message text in case we need to use it directly
      const messageText = userInput.value.trim();

      // Try multiple methods to send the message

      // Method 1: Direct call to chat manager
      if (window.chatManager && typeof window.chatManager.sendMessage === 'function') {
        try {
          console.log('BlankPageHandler: Using direct chat manager call');
          window.chatManager.sendMessage();
        } catch (error) {
          console.error('BlankPageHandler: Error calling chatManager.sendMessage', error);
        }
      }

      // Method 2: Custom event
      try {
        console.log('BlankPageHandler: Dispatching sendMessage event');
        const event = new CustomEvent('sendMessage', {
          detail: { message: messageText }
        });
        document.dispatchEvent(event);
      } catch (error) {
        console.error('BlankPageHandler: Error dispatching sendMessage event', error);
      }

      // Method 3: Simulate Enter key press
      try {
        console.log('BlankPageHandler: Simulating Enter key press');
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true
        });
        userInput.dispatchEvent(enterEvent);
      } catch (error) {
        console.error('BlankPageHandler: Error simulating Enter key press', error);
      }

      // Method 4: Direct manipulation of chat UI as a last resort
      try {
        if (!window.chatManager && document.getElementById('chatMessages')) {
          console.log('BlankPageHandler: Using direct UI manipulation as fallback');

          // Add user message to chat
          const chatMessages = document.getElementById('chatMessages');
          const userMessageDiv = document.createElement('div');
          userMessageDiv.className = 'message user-message';
          userMessageDiv.innerHTML = `<div class="message-content">${messageText}</div>`;
          chatMessages.appendChild(userMessageDiv);

          // Clear input
          userInput.value = '';

          // Add AI response
          const aiMessageDiv = document.createElement('div');
          aiMessageDiv.className = 'message ai-message';
          aiMessageDiv.innerHTML = `<div class="message-content">I'm sorry, but the chat functionality isn't fully initialized yet. Please try again in a moment.</div>`;
          chatMessages.appendChild(aiMessageDiv);

          // Scroll to bottom
          chatMessages.scrollTop = chatMessages.scrollHeight;
        }
      } catch (error) {
        console.error('BlankPageHandler: Error with direct UI manipulation', error);
      }
    }
  }

  handleTabButton(tab) {
    console.log('BlankPageHandler: Handling tab button click', tab.id);

    // Remove active class from all tabs and contents
    document.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

    // Add active class to clicked tab
    tab.classList.add('active');

    // Show corresponding content
    const contentId = tab.id.replace('Tab', 'Content');
    const content = document.getElementById(contentId);
    if (content) {
      content.classList.add('active');

      // Special handling for history tab
      if (tab.id === 'historyTab' && window.historyManager) {
        window.historyManager.loadHistoryList();
      }

      // Special handling for stats tab
      if (tab.id === 'statsTab' && window.updateStatsDisplay) {
        window.updateStatsDisplay();
      }
    }
  }

  handleClearChat() {
    console.log('BlankPageHandler: Handling clear chat button click');

    if (window.chatManager && typeof window.chatManager.clearChat === 'function') {
      window.chatManager.clearChat();
    }
  }

  focusInput() {
    const userInput = document.getElementById('userInput');
    if (userInput) {
      userInput.focus();
      console.log('BlankPageHandler: Focused input field');
    }
  }

  activateChatTab() {
    const chatTab = document.getElementById('chatTab');
    if (chatTab) {
      chatTab.click();
      console.log('BlankPageHandler: Activated chat tab');
    }
  }
}

// Initialize the blank page handler
const isBlankPage = window.location.href === 'about:blank' ||
                   window.location.href === 'about:newtab' ||
                   window.location.href === 'chrome://newtab/' ||
                   window.location.href === 'brave://newtab/' ||
                   window.location.href.includes('/_/chrome/newtab') ||
                   window.location.href.includes('/_/brave/newtab') ||
                   window.location.href.includes('chrome://new-tab-page') ||
                   window.location.href.includes('brave://new-tab-page') ||
                   (window.location.pathname === '/' && document.title === 'New Tab') ||
                   (window.location.href === window.location.origin + '/' && document.title === 'New Tab') ||
                   document.documentElement.textContent.trim() === '';

// Check if we're in a sidebar or popup
const urlParams = new URLSearchParams(window.location.search);
const isSidebar = urlParams.get('sidebar') === 'true';
const isBlankPageParam = urlParams.get('blankPage') === 'true';
const isNewTabParam = urlParams.get('newTab') === 'true';
const isDirectParam = urlParams.get('direct') === 'true';

// Check if we have a global flag indicating this is a blank page
const hasBlankPageFlag = typeof window.isBlankPage !== 'undefined' && window.isBlankPage === true;

// Check if the body has the blank page classes
const hasBlankPageClass = document.body && (
  document.body.classList.contains('blank-page-sidebar') ||
  document.body.classList.contains('new-tab-sidebar')
);

// Initialize if any of the blank page conditions are met
const shouldInitialize = isBlankPageParam ||
                         hasBlankPageFlag ||
                         hasBlankPageClass ||
                         (isSidebar && isBlankPage) ||
                         (isSidebar && isNewTabParam) ||
                         (isSidebar && isDirectParam);

if (shouldInitialize) {
  console.log('BlankPageHandler: Detected blank page conditions, initializing handler', {
    isBlankPageParam,
    hasBlankPageFlag,
    hasBlankPageClass,
    isSidebar,
    isBlankPage,
    isNewTabParam,
    isDirectParam
  });

  // Force the body to have the blank page classes
  if (document.body) {
    document.body.classList.add('blank-page-sidebar');
    if (isNewTabParam) {
      document.body.classList.add('new-tab-sidebar');
    }
  }

  // Create the handler
  window.blankPageHandler = new BlankPageHandler();

  // Set global flag
  window.isBlankPage = true;
} else {
  console.log('BlankPageHandler: Not a blank page in sidebar, skipping initialization');
}

// Also initialize when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
  // Re-check all conditions in case they've changed
  const hasBlankPageClass = document.body && (
    document.body.classList.contains('blank-page-sidebar') ||
    document.body.classList.contains('new-tab-sidebar')
  );

  const hasBlankPageFlag = typeof window.isBlankPage !== 'undefined' && window.isBlankPage === true;

  // Check if we should initialize
  if (shouldInitialize || hasBlankPageClass || hasBlankPageFlag || isBlankPageParam) {
    console.log('BlankPageHandler: DOMContentLoaded - Initializing handler');

    // Force the body to have the blank page classes
    if (document.body) {
      document.body.classList.add('blank-page-sidebar');
      if (isNewTabParam) {
        document.body.classList.add('new-tab-sidebar');
      }
    }

    if (!window.blankPageHandler) {
      window.blankPageHandler = new BlankPageHandler();
    } else {
      window.blankPageHandler.init();
    }

    // Set global flag
    window.isBlankPage = true;
  }
});

// Add a direct event listener for the load event as well
window.addEventListener('load', () => {
  // Re-check all conditions in case they've changed
  const hasBlankPageClass = document.body && (
    document.body.classList.contains('blank-page-sidebar') ||
    document.body.classList.contains('new-tab-sidebar')
  );

  const hasBlankPageFlag = typeof window.isBlankPage !== 'undefined' && window.isBlankPage === true;

  // Check if we should initialize
  if (shouldInitialize || hasBlankPageClass || hasBlankPageFlag || isBlankPageParam) {
    console.log('BlankPageHandler: window.load - Ensuring handler is initialized');

    // Force the body to have the blank page classes
    if (document.body) {
      document.body.classList.add('blank-page-sidebar');
      if (isNewTabParam) {
        document.body.classList.add('new-tab-sidebar');
      }
    }

    if (!window.blankPageHandler) {
      window.blankPageHandler = new BlankPageHandler();
    } else {
      window.blankPageHandler.init();
    }

    // Force re-initialization of all buttons and inputs
    if (window.blankPageHandler) {
      window.blankPageHandler.initializeButtons();
      window.blankPageHandler.initializeInputs();
      window.blankPageHandler.initializeTabs();
      window.blankPageHandler.initializeDropdowns();
      window.blankPageHandler.focusInput();
    }

    // Set global flag
    window.isBlankPage = true;
  }
});
