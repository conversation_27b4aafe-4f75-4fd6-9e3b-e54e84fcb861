/**
 * Saved Links Manager
 * Handles saving, retrieving, and displaying saved links
 */
class SavedLinksManager {
  constructor(uiManager) {
    this.uiManager = uiManager;
    this.STORAGE_KEY = 'browzy_saved_links';
    this.dialogElement = null;
    
    // Initialize the dialog
    this.initializeDialog();
  }

  /**
   * Initialize the saved links dialog
   */
  initializeDialog() {
    // Create dialog if it doesn't exist
    if (!document.getElementById('savedLinksDialog')) {
      const dialog = document.createElement('div');
      dialog.id = 'savedLinksDialog';
      dialog.className = 'saved-links-dialog';
      dialog.innerHTML = `
        <div class="saved-links-dialog-content">
          <div class="saved-links-dialog-header">
            <h3><i class="fas fa-link"></i> Saved Links</h3>
            <button class="saved-links-close-btn"><i class="fas fa-times"></i></button>
          </div>
          <div class="saved-links-dialog-body">
            <div class="saved-links-search">
              <input type="text" id="savedLinksSearch" placeholder="Search saved links...">
              <i class="fas fa-search"></i>
            </div>
            <div class="saved-links-list" id="savedLinksList">
              <!-- Links will be added here -->
            </div>
            <div class="saved-links-empty" id="savedLinksEmpty">
              <i class="fas fa-link"></i>
              <p>No saved links yet</p>
              <p class="saved-links-empty-hint">Save links from webpages to access them later</p>
            </div>
          </div>
          <div class="saved-links-dialog-footer">
            <button class="saved-links-add-btn"><i class="fas fa-plus"></i> Add Current Page</button>
            <button class="saved-links-clear-btn"><i class="fas fa-trash"></i> Clear All</button>
          </div>
        </div>
      `;
      
      document.body.appendChild(dialog);
      this.dialogElement = dialog;
      
      // Add event listeners
      this.addEventListeners();
    } else {
      this.dialogElement = document.getElementById('savedLinksDialog');
    }
  }

  /**
   * Add event listeners to the dialog
   */
  addEventListeners() {
    // Close button
    const closeButton = this.dialogElement.querySelector('.saved-links-close-btn');
    closeButton.addEventListener('click', () => {
      this.hideDialog();
    });
    
    // Add current page button
    const addButton = this.dialogElement.querySelector('.saved-links-add-btn');
    addButton.addEventListener('click', async () => {
      await this.saveCurrentPage();
    });
    
    // Clear all button
    const clearButton = this.dialogElement.querySelector('.saved-links-clear-btn');
    clearButton.addEventListener('click', () => {
      if (confirm('Are you sure you want to clear all saved links?')) {
        this.clearAllLinks();
      }
    });
    
    // Search input
    const searchInput = this.dialogElement.querySelector('#savedLinksSearch');
    searchInput.addEventListener('input', () => {
      this.filterLinks(searchInput.value);
    });
    
    // Close dialog when clicking outside
    this.dialogElement.addEventListener('click', (event) => {
      if (event.target === this.dialogElement) {
        this.hideDialog();
      }
    });
  }

  /**
   * Show the saved links dialog
   */
  async showDialog() {
    // Load saved links
    await this.loadSavedLinks();
    
    // Show the dialog
    this.dialogElement.style.display = 'flex';
    setTimeout(() => {
      this.dialogElement.classList.add('active');
    }, 10);
    
    // Focus on search input
    setTimeout(() => {
      const searchInput = this.dialogElement.querySelector('#savedLinksSearch');
      if (searchInput) {
        searchInput.focus();
      }
    }, 300);
  }

  /**
   * Hide the saved links dialog
   */
  hideDialog() {
    this.dialogElement.classList.remove('active');
    setTimeout(() => {
      this.dialogElement.style.display = 'none';
    }, 300);
  }

  /**
   * Load saved links from storage
   */
  async loadSavedLinks() {
    try {
      const { [this.STORAGE_KEY]: savedLinks = [] } = await chrome.storage.local.get(this.STORAGE_KEY);
      
      // Update the UI
      this.updateLinksList(savedLinks);
      
      return savedLinks;
    } catch (error) {
      console.error('Error loading saved links:', error);
      this.uiManager.showStatus('Error loading saved links', true);
      return [];
    }
  }

  /**
   * Update the links list in the UI
   * @param {Array} links - Array of saved link objects
   */
  updateLinksList(links) {
    const linksList = this.dialogElement.querySelector('#savedLinksList');
    const emptyState = this.dialogElement.querySelector('#savedLinksEmpty');
    
    // Clear the list
    linksList.innerHTML = '';
    
    if (links.length === 0) {
      // Show empty state
      emptyState.style.display = 'flex';
      linksList.style.display = 'none';
      return;
    }
    
    // Hide empty state
    emptyState.style.display = 'none';
    linksList.style.display = 'block';
    
    // Add links to the list
    links.forEach(link => {
      const linkElement = document.createElement('div');
      linkElement.className = 'saved-link-item';
      
      // Create favicon element
      const faviconUrl = `https://www.google.com/s2/favicons?domain=${new URL(link.url).hostname}&sz=32`;
      
      linkElement.innerHTML = `
        <div class="saved-link-icon">
          <img src="${faviconUrl}" alt="favicon" onerror="this.src='../images/globe-icon.png';">
        </div>
        <div class="saved-link-content">
          <div class="saved-link-title">${link.title}</div>
          <div class="saved-link-url">${link.url}</div>
          <div class="saved-link-date">${new Date(link.timestamp).toLocaleDateString()}</div>
        </div>
        <div class="saved-link-actions">
          <button class="saved-link-open" title="Open link"><i class="fas fa-external-link-alt"></i></button>
          <button class="saved-link-delete" title="Delete link"><i class="fas fa-trash"></i></button>
        </div>
      `;
      
      // Add event listeners
      const openButton = linkElement.querySelector('.saved-link-open');
      openButton.addEventListener('click', () => {
        this.openLink(link.url);
      });
      
      const deleteButton = linkElement.querySelector('.saved-link-delete');
      deleteButton.addEventListener('click', () => {
        this.deleteLink(link.id);
      });
      
      // Add click event to the whole item (except buttons)
      linkElement.addEventListener('click', (event) => {
        if (!event.target.closest('button')) {
          this.openLink(link.url);
        }
      });
      
      linksList.appendChild(linkElement);
    });
  }

  /**
   * Filter links based on search query
   * @param {string} query - Search query
   */
  async filterLinks(query) {
    const links = await this.loadSavedLinks();
    
    if (!query) {
      this.updateLinksList(links);
      return;
    }
    
    const filteredLinks = links.filter(link => {
      const searchText = `${link.title} ${link.url}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    });
    
    this.updateLinksList(filteredLinks);
  }

  /**
   * Save the current page as a link
   */
  async saveCurrentPage() {
    try {
      // Get the active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        this.uiManager.showStatus('No active tab found', true);
        return;
      }
      
      // Create link object
      const link = {
        id: `link_${Date.now()}`,
        title: tab.title || 'Untitled Page',
        url: tab.url,
        favicon: tab.favIconUrl || null,
        timestamp: Date.now()
      };
      
      // Get existing links
      const { [this.STORAGE_KEY]: savedLinks = [] } = await chrome.storage.local.get(this.STORAGE_KEY);
      
      // Check if link already exists
      const linkExists = savedLinks.some(existingLink => existingLink.url === link.url);
      
      if (linkExists) {
        this.uiManager.showStatus('This link is already saved', true);
        return;
      }
      
      // Add new link
      savedLinks.unshift(link);
      
      // Save to storage
      await chrome.storage.local.set({ [this.STORAGE_KEY]: savedLinks });
      
      // Update UI
      this.updateLinksList(savedLinks);
      
      // Show success message
      this.uiManager.showStatus('Link saved successfully', false);
    } catch (error) {
      console.error('Error saving link:', error);
      this.uiManager.showStatus('Error saving link', true);
    }
  }

  /**
   * Delete a link by ID
   * @param {string} id - Link ID
   */
  async deleteLink(id) {
    try {
      // Get existing links
      const { [this.STORAGE_KEY]: savedLinks = [] } = await chrome.storage.local.get(this.STORAGE_KEY);
      
      // Filter out the link to delete
      const updatedLinks = savedLinks.filter(link => link.id !== id);
      
      // Save to storage
      await chrome.storage.local.set({ [this.STORAGE_KEY]: updatedLinks });
      
      // Update UI
      this.updateLinksList(updatedLinks);
      
      // Show success message
      this.uiManager.showStatus('Link deleted', false);
    } catch (error) {
      console.error('Error deleting link:', error);
      this.uiManager.showStatus('Error deleting link', true);
    }
  }

  /**
   * Clear all saved links
   */
  async clearAllLinks() {
    try {
      // Clear from storage
      await chrome.storage.local.remove(this.STORAGE_KEY);
      
      // Update UI
      this.updateLinksList([]);
      
      // Show success message
      this.uiManager.showStatus('All links cleared', false);
    } catch (error) {
      console.error('Error clearing links:', error);
      this.uiManager.showStatus('Error clearing links', true);
    }
  }

  /**
   * Open a link in a new tab
   * @param {string} url - URL to open
   */
  openLink(url) {
    chrome.tabs.create({ url });
  }
}
