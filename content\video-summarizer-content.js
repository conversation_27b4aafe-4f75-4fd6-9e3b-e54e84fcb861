'use strict';

/**
 * Video Summarizer Content Script
 * Injects the Video Summarizer into YouTube pages
 */

// Wait for the page to be fully loaded
window.addEventListener('load', () => {
  // Check if we're on YouTube
  if (window.location.hostname.includes('youtube.com')) {
    console.log('Video Summarizer: YouTube detected, initializing...');
    initVideoSummarizer();
  }
});

// Also listen for navigation events (for YouTube's SPA behavior)
let lastUrl = location.href;
new MutationObserver(() => {
  const url = location.href;
  if (url !== lastUrl) {
    lastUrl = url;
    console.log('Video Summarizer: URL changed, checking if YouTube video page...');
    
    // Small delay to ensure the page has loaded
    setTimeout(() => {
      if (window.location.hostname.includes('youtube.com') && 
          window.location.pathname.includes('/watch')) {
        console.log('Video Summarizer: YouTube video page detected after navigation');
        initVideoSummarizer();
      }
    }, 1000);
  }
}).observe(document, { subtree: true, childList: true });

/**
 * Initialize the Video Summarizer
 */
function initVideoSummarizer() {
  // Check if the summarizer is already initialized
  if (window.browzyVideoSummarizer) {
    console.log('Video Summarizer: Already initialized, reinitializing...');
    window.browzyVideoSummarizer.init();
    return;
  }
  
  // Load the Font Awesome for icons if not already loaded
  if (!document.querySelector('link[href*="font-awesome"]')) {
    const fontAwesome = document.createElement('link');
    fontAwesome.rel = 'stylesheet';
    fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
    document.head.appendChild(fontAwesome);
  }
  
  // Create and initialize the Video Summarizer
  console.log('Video Summarizer: Creating new instance...');
  
  // Check if VideoSummarizer class is available
  if (typeof VideoSummarizer === 'undefined') {
    console.log('Video Summarizer: Class not found, injecting script...');
    
    // Inject the Video Summarizer script
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('scripts/video-summarizer.js');
    script.onload = function() {
      console.log('Video Summarizer: Script loaded, initializing...');
      createSummarizer();
    };
    document.head.appendChild(script);
  } else {
    console.log('Video Summarizer: Class found, initializing directly...');
    createSummarizer();
  }
}

/**
 * Create and initialize the Video Summarizer instance
 */
function createSummarizer() {
  // Create the summarizer instance
  const initScript = document.createElement('script');
  initScript.textContent = `
    if (!window.browzyVideoSummarizer) {
      console.log('Video Summarizer: Creating instance...');
      window.browzyVideoSummarizer = new VideoSummarizer();
      window.browzyVideoSummarizer.init();
    } else {
      console.log('Video Summarizer: Reinitializing existing instance...');
      window.browzyVideoSummarizer.init();
    }
  `;
  document.head.appendChild(initScript);
  initScript.remove();
}

/**
 * Extract YouTube transcript using YouTube's API
 * This function will be called from the Video Summarizer
 */
async function extractYouTubeTranscript() {
  console.log('Video Summarizer: Extracting transcript...');
  
  // Get the video ID from the URL
  const url = window.location.href;
  const match = url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
  const videoId = match ? match[1] : null;
  
  if (!videoId) {
    throw new Error('Could not extract video ID from URL');
  }
  
  try {
    // First, try to get the transcript from YouTube's API
    const response = await fetch(`https://www.youtube.com/api/timedtext?lang=en&v=${videoId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch transcript from YouTube API');
    }
    
    const xml = await response.text();
    
    // Parse the XML to extract the transcript
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xml, 'text/xml');
    
    // Extract text from the XML
    const textNodes = xmlDoc.getElementsByTagName('text');
    let transcript = '';
    
    for (let i = 0; i < textNodes.length; i++) {
      transcript += textNodes[i].textContent + ' ';
    }
    
    // Clean up the transcript
    transcript = transcript.replace(/\s+/g, ' ').trim();
    
    return transcript;
  } catch (error) {
    console.error('Error extracting transcript:', error);
    
    // Fallback: Try to extract from the page itself
    // This is more complex and less reliable, but can work as a backup
    
    // Look for the transcript button
    const transcriptButton = Array.from(document.querySelectorAll('button'))
      .find(button => button.textContent.toLowerCase().includes('transcript'));
    
    if (transcriptButton) {
      // Click the transcript button to open the panel
      transcriptButton.click();
      
      // Wait for the transcript panel to load
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Extract text from the transcript panel
      const transcriptItems = document.querySelectorAll('yt-formatted-string.segment-text');
      
      if (transcriptItems.length > 0) {
        let transcript = '';
        
        transcriptItems.forEach(item => {
          transcript += item.textContent + ' ';
        });
        
        // Clean up the transcript
        transcript = transcript.replace(/\s+/g, ' ').trim();
        
        return transcript;
      }
    }
    
    // If all methods fail, throw an error
    throw new Error('Could not extract transcript from this video');
  }
}

// Make the function available to the page
window.extractYouTubeTranscript = extractYouTubeTranscript;
