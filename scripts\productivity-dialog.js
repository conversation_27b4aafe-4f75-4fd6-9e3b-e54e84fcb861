'use strict';

/**
 * Productivity Dialog class for handling productivity tools in a simple dialog
 */
class ProductivityDialog {
  /**
   * Initialize the Productivity Dialog
   * @param {FeatureManager} featureManager - The feature manager instance
   */
  constructor(featureManager) {
    this.featureManager = featureManager;
    this.dialogElement = null;

    // Create the dialog element
    this.createDialog();
  }

  /**
   * Create the productivity tools dialog
   */
  createDialog() {
    // Create the dialog element if it doesn't exist
    if (!this.dialogElement) {
      this.dialogElement = document.createElement('div');
      this.dialogElement.className = 'productivity-tools-dialog';
      this.dialogElement.style.display = 'none';

      // Add the dialog content
      this.dialogElement.innerHTML = `
        <div class="productivity-tools-dialog-content">
          <div class="productivity-tools-dialog-header">
            <h3><i class="fas fa-tasks"></i> Productivity Tools</h3>
            <button class="productivity-tools-close-btn"><i class="fas fa-times"></i></button>
          </div>
          <div class="productivity-tools-dialog-body">
            <div class="productivity-tools-section">
              <h4><i class="fas fa-search"></i> Research Assistant</h4>
              <div class="productivity-tools-buttons">
                <button class="productivity-tool-btn" data-action="research-topic">
                  <i class="fas fa-book"></i> Research Topic
                </button>
                <button class="productivity-tool-btn" data-action="find-sources">
                  <i class="fas fa-link"></i> Find Sources
                </button>
                <button class="productivity-tool-btn" data-action="summarize-article">
                  <i class="fas fa-file-alt"></i> Summarize Article
                </button>
              </div>
            </div>

            <div class="productivity-tools-section">
              <h4><i class="fas fa-pencil-alt"></i> Writing Assistant</h4>
              <div class="productivity-tools-buttons">
                <button class="productivity-tool-btn" data-action="improve-writing">
                  <i class="fas fa-magic"></i> Improve Writing
                </button>
                <button class="productivity-tool-btn" data-action="grammar-check">
                  <i class="fas fa-check"></i> Grammar Check
                </button>
                <button class="productivity-tool-btn" data-action="paraphrase">
                  <i class="fas fa-sync-alt"></i> Paraphrase Text
                </button>
              </div>
            </div>

            <div class="productivity-tools-section">
              <h4><i class="fas fa-code"></i> Coding Assistant</h4>
              <div class="productivity-tools-buttons">
                <button class="productivity-tool-btn" data-action="explain-code">
                  <i class="fas fa-question-circle"></i> Explain Code
                </button>
                <button class="productivity-tool-btn" data-action="optimize-code">
                  <i class="fas fa-bolt"></i> Optimize Code
                </button>
                <button class="productivity-tool-btn" data-action="debug-code">
                  <i class="fas fa-bug"></i> Debug Code
                </button>
              </div>
            </div>

            <div class="productivity-tools-section">
              <h4><i class="fas fa-chart-line"></i> Data Analysis</h4>
              <div class="productivity-tools-buttons">
                <button class="productivity-tool-btn" data-action="analyze-data">
                  <i class="fas fa-table"></i> Analyze Data
                </button>
                <button class="productivity-tool-btn" data-action="create-chart">
                  <i class="fas fa-chart-bar"></i> Create Chart
                </button>
                <button class="productivity-tool-btn" data-action="extract-data">
                  <i class="fas fa-file-export"></i> Extract Data
                </button>
              </div>
            </div>
          </div>
          <div class="productivity-tools-dialog-footer">
            <div class="productivity-status-message" id="productivityStatusMessage"></div>
          </div>
        </div>
      `;

      // Add the dialog to the document
      document.body.appendChild(this.dialogElement);

      // Add event listeners
      this.addEventListeners();

      // Add styles
      this.addStyles();
    }
  }

  /**
   * Add event listeners to the dialog
   */
  addEventListeners() {
    // Close button
    const closeButton = this.dialogElement.querySelector('.productivity-tools-close-btn');
    closeButton.addEventListener('click', () => {
      this.hideDialog();
    });

    // Tool buttons
    const toolButtons = this.dialogElement.querySelectorAll('.productivity-tool-btn');
    toolButtons.forEach(button => {
      button.addEventListener('click', () => {
        const action = button.getAttribute('data-action');
        this.handleAction(action);
      });
    });

    // Close dialog when clicking outside
    this.dialogElement.addEventListener('click', (event) => {
      if (event.target === this.dialogElement) {
        this.hideDialog();
      }
    });

    // Close dialog on Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && this.dialogElement.style.display === 'flex') {
        this.hideDialog();
      }
    });
  }

  /**
   * Add styles for the dialog
   */
  addStyles() {
    // Create a style element if it doesn't exist
    let styleElement = document.getElementById('productivity-dialog-styles');
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'productivity-dialog-styles';
      styleElement.textContent = `
        .productivity-tools-dialog {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        }

        .productivity-tools-dialog-content {
          background-color: #2D2D2D;
          border-radius: 8px;
          width: 90%;
          max-width: 600px;
          max-height: 90vh;
          overflow-y: auto;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          display: flex;
          flex-direction: column;
        }

        .productivity-tools-dialog-header {
          padding: 15px;
          border-bottom: 1px solid #444;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .productivity-tools-dialog-header h3 {
          margin: 0;
          color: #00b4d8;
          font-size: 18px;
          display: flex;
          align-items: center;
        }

        .productivity-tools-dialog-header h3 i {
          margin-right: 8px;
        }

        .productivity-tools-close-btn {
          background: none;
          border: none;
          color: #aaa;
          font-size: 16px;
          cursor: pointer;
          padding: 5px;
          border-radius: 4px;
        }

        .productivity-tools-close-btn:hover {
          color: #fff;
          background-color: rgba(255, 255, 255, 0.1);
        }

        .productivity-tools-dialog-body {
          padding: 15px;
          overflow-y: auto;
        }

        .productivity-tools-section {
          margin-bottom: 20px;
        }

        .productivity-tools-section h4 {
          margin: 0 0 10px 0;
          color: #eee;
          font-size: 16px;
          display: flex;
          align-items: center;
        }

        .productivity-tools-section h4 i {
          margin-right: 8px;
          color: #00b4d8;
        }

        .productivity-tools-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          gap: 10px;
        }

        .productivity-tool-btn {
          background-color: #3a3a3a;
          border: none;
          color: #eee;
          padding: 10px;
          border-radius: 4px;
          cursor: pointer;
          text-align: left;
          display: flex;
          align-items: center;
          transition: background-color 0.2s;
        }

        .productivity-tool-btn i {
          margin-right: 8px;
          color: #00b4d8;
          width: 16px;
          text-align: center;
        }

        .productivity-tool-btn:hover {
          background-color: #4a4a4a;
        }

        .productivity-tool-btn:active {
          background-color: #555;
        }

        .productivity-tools-dialog-footer {
          padding: 10px 15px;
          border-top: 1px solid #444;
        }

        .productivity-status-message {
          color: #00b4d8;
          font-size: 14px;
          text-align: center;
          padding: 5px;
          font-weight: bold;
          min-height: 20px;
        }
      `;
      document.head.appendChild(styleElement);
    }
  }

  /**
   * Show the productivity tools dialog
   */
  showDialog() {
    // Show the dialog
    this.dialogElement.style.display = 'flex';

    // Update the status message
    const statusMessage = document.getElementById('productivityStatusMessage');
    if (statusMessage) {
      statusMessage.textContent = 'Select a productivity tool to enhance your workflow.';
      statusMessage.style.color = '#aaa';
    }
  }

  /**
   * Hide the productivity tools dialog
   */
  hideDialog() {
    this.dialogElement.style.display = 'none';
  }

  /**
   * Handle a productivity tool action
   * @param {string} action - The action to handle
   */
  async handleAction(action) {
    // Hide the dialog
    this.hideDialog();

    try {
      // Check if the feature manager has the required methods
      if (!this.featureManager) {
        throw new Error('Feature manager is not available');
      }

      // Show status message in the UI
      const statusElement = document.getElementById('productivityStatusMessage');
      if (statusElement) {
        statusElement.textContent = `Processing ${action.replace(/-/g, ' ')}...`;
      }

      // Handle different actions with method existence checks
      switch (action) {
        case 'research-topic':
          if (typeof this.featureManager.researchTopic !== 'function') {
            throw new Error('Research topic feature is not available');
          }
          await this.featureManager.researchTopic();
          break;
        case 'find-sources':
          if (typeof this.featureManager.findSources !== 'function') {
            throw new Error('Find sources feature is not available');
          }
          await this.featureManager.findSources();
          break;
        case 'summarize-article':
          if (typeof this.featureManager.summarizeArticle !== 'function') {
            throw new Error('Summarize article feature is not available');
          }
          await this.featureManager.summarizeArticle();
          break;
        case 'improve-writing':
          if (typeof this.featureManager.improveWriting !== 'function') {
            throw new Error('Improve writing feature is not available');
          }
          await this.featureManager.improveWriting();
          break;
        case 'grammar-check':
          if (typeof this.featureManager.checkGrammar !== 'function') {
            throw new Error('Grammar check feature is not available');
          }
          await this.featureManager.checkGrammar();
          break;
        case 'paraphrase':
          if (typeof this.featureManager.paraphraseText !== 'function') {
            throw new Error('Paraphrase feature is not available');
          }
          await this.featureManager.paraphraseText();
          break;
        case 'explain-code':
          if (typeof this.featureManager.explainCode !== 'function') {
            throw new Error('Explain code feature is not available');
          }
          await this.featureManager.explainCode();
          break;
        case 'optimize-code':
          if (typeof this.featureManager.optimizeCode !== 'function') {
            throw new Error('Optimize code feature is not available');
          }
          await this.featureManager.optimizeCode();
          break;
        case 'debug-code':
          if (typeof this.featureManager.debugCode !== 'function') {
            throw new Error('Debug code feature is not available');
          }
          await this.featureManager.debugCode();
          break;
        case 'analyze-data':
          if (typeof this.featureManager.analyzeData !== 'function') {
            throw new Error('Analyze data feature is not available');
          }
          await this.featureManager.analyzeData();
          break;
        case 'create-chart':
          if (typeof this.featureManager.createChart !== 'function') {
            throw new Error('Create chart feature is not available');
          }
          await this.featureManager.createChart();
          break;
        case 'extract-data':
          if (typeof this.featureManager.extractData !== 'function') {
            throw new Error('Extract data feature is not available');
          }
          await this.featureManager.extractData();
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      // Clear status message
      if (statusElement) {
        statusElement.textContent = 'Action completed successfully';
        statusElement.style.color = '#00b4d8';
        setTimeout(() => {
          statusElement.textContent = '';
          statusElement.style.color = '';
        }, 3000);
      }
    } catch (error) {
      console.error('Error handling productivity action:', error);

      // Show error in UI instead of alert
      const statusElement = document.getElementById('productivityStatusMessage');
      if (statusElement) {
        statusElement.textContent = `Error: ${error.message}`;
        statusElement.style.color = 'red';
        setTimeout(() => {
          statusElement.textContent = '';
          statusElement.style.color = '';
        }, 5000);
      } else {
        // Fallback to alert if status element not found
        alert(`Error: ${error.message}`);
      }
    }
  }
}
