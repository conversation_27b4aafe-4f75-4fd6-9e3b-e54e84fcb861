/* Editable Messages Styles */
.message-edit-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: transparent;
  color: rgba(255, 255, 255, 0.5);
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
  z-index: 10;
}

.message:hover .message-edit-btn {
  opacity: 1;
}

.message-edit-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--primary-color);
}

.message-edit-btn i {
  font-size: 14px;
}

.message.editing .message-content {
  padding-bottom: 40px;
}

.message-edit-textarea {
  width: 100%;
  min-height: 80px;
  background-color: var(--bg-light);
  color: var(--text-color);
  border: 1px solid var(--primary-color);
  border-radius: 8px;
  padding: 10px;
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  resize: vertical;
  margin-top: 5px;
  transition: all 0.2s ease;
  box-shadow: 0 0 0 2px rgba(0, 180, 216, 0.2);
}

.message-edit-textarea:focus {
  outline: none;
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(0, 180, 216, 0.3);
}

.message-edit-controls {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.edit-save-btn, .edit-cancel-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.edit-save-btn {
  background-color: var(--primary-color);
  color: white;
}

.edit-save-btn:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
}

.edit-cancel-btn {
  background-color: var(--surface-color);
  color: var(--text-color);
}

.edit-cancel-btn:hover {
  background-color: var(--bg-light);
  transform: translateY(-1px);
}

/* Context indicator styles */
.context-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.75rem;
  color: var(--text-light);
  margin-top: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: rgba(0, 180, 216, 0.1);
  width: fit-content;
}

.context-indicator i {
  font-size: 0.7rem;
  color: var(--primary-color);
}

/* Follow-up message styles */
.follow-up-message {
  position: relative;
}

.follow-up-message::before {
  content: '';
  position: absolute;
  left: 19px;
  top: -22px;
  width: 2px;
  height: 22px;
  background: linear-gradient(to bottom, transparent, var(--primary-color));
  opacity: 0.5;
}

.user-message.follow-up-message::before {
  left: auto;
  right: 19px;
  background: linear-gradient(to bottom, transparent, var(--primary-color));
}

/* Original message indicator when edited */
.message-edited-indicator {
  font-size: 0.7rem;
  color: var(--text-light);
  margin-top: 4px;
  font-style: italic;
  text-align: right;
}
