'use strict';

// Function to extract page content
function extractPageContent() {
  try {
    // Performance measurement
    const startTime = performance.now();

    // Get the page title
    const title = document.title || 'Untitled Page';
    const url = window.location.href;

    // Extract main content with priority-based approach
    let mainContent = '';
    let contentSource = 'fallback';

    // Define content selectors in priority order
    const contentSelectors = [
      // High-priority content containers
      'article', 'main', '[role="main"]',
      // Medium-priority content containers
      '.content', '#content', '.article', '#article', '.post-content', '.entry-content',
      // Low-priority content containers
      '.main', '#main', '.body', '#body'
    ];

    // Try selectors in order until content is found
    for (const selector of contentSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        // Use the largest content element by text length
        let bestElement = elements[0];
        let maxLength = bestElement.innerText?.length || 0;

        for (let i = 1; i < Math.min(elements.length, 5); i++) {
          const length = elements[i].innerText?.length || 0;
          if (length > maxLength) {
            maxLength = length;
            bestElement = elements[i];
          }
        }

        mainContent = bestElement.innerText || '';
        contentSource = selector;
        break;
      }
    }

    // If no content found, use optimized body extraction
    if (!mainContent) {
      // Use a more efficient approach than cloning the entire body
      const textNodes = [];
      const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: function(node) {
            // Skip text in scripts, styles, and other non-content elements
            const parent = node.parentNode;
            if (!parent ||
                parent.nodeName === 'SCRIPT' ||
                parent.nodeName === 'STYLE' ||
                parent.nodeName === 'NOSCRIPT' ||
                parent.nodeName === 'SVG' ||
                parent.nodeName === 'META') {
              return NodeFilter.FILTER_REJECT;
            }

            // Skip empty text nodes
            if (node.textContent.trim() === '') {
              return NodeFilter.FILTER_REJECT;
            }

            return NodeFilter.FILTER_ACCEPT;
          }
        }
      );

      // Collect text nodes up to a reasonable limit
      const MAX_NODES = 1000;
      let nodeCount = 0;

      while (walker.nextNode() && nodeCount < MAX_NODES) {
        textNodes.push(walker.currentNode.textContent);
        nodeCount++;
      }

      mainContent = textNodes.join(' ');
      contentSource = 'treeWalker';
    }

    // Trim and normalize whitespace more efficiently
    mainContent = mainContent.replace(/\s+/g, ' ').trim();



    // Get any code blocks that might be present (useful for leetcode)
    let codeBlocks = [];
    try {
      const preElements = document.querySelectorAll('pre, code');
      preElements.forEach(el => {
        const code = el.innerText.trim();
        if (code.length > 0) {
          codeBlocks.push(code);
        }
      });
    } catch (codeError) {
      console.error('Error extracting code blocks:', codeError);
    }

    // Check for chess-related content
    let chessContent = null;

    try {
      // Enhanced chess detection
      const url = window.location.href.toLowerCase();
      const isChessSite = url.includes('chess.com') ||
                         url.includes('lichess.org') ||
                         url.includes('chess24.com') ||
                         url.includes('chessbase.com') ||
                         url.includes('chesstempo.com');

      const hasChessTerms = mainContent.toLowerCase().includes('chess') ||
                          title.toLowerCase().includes('chess');

      // More aggressive chess board detection
      const chessBoardSelectors = [
        '.chess-board', '.chessboard', '[class*="chess"]', '.board', '.game-board',
        '.cg-wrap', '.chessboard-component', '.board-layout-chessboard',
        '[class*="board"]', '[class*="chess"]', '.main-board', '#board-layout-main',
        '[id*="chess"]', '[id*="board"]', '.board-container', '.chess-container'
      ];

      let hasChessBoard = false;
      for (const selector of chessBoardSelectors) {
        if (document.querySelector(selector)) {
          hasChessBoard = true;
          break;
        }
      }

      // More aggressive chess piece detection
      const chessPieceSelectors = [
        '[class*="pawn"]', '[class*="knight"]', '[class*="bishop"]',
        '[class*="rook"]', '[class*="queen"]', '[class*="king"]',
        '.piece', '[class*="piece"]', '[class*="chess-piece"]',
        'img[src*="chess"]', 'img[src*="piece"]', 'svg[class*="piece"]',
        '.wp', '.bp', '.wn', '.bn', '.wb', '.bb', '.wr', '.br', '.wq', '.bq', '.wk', '.bk'
      ];

      let hasChessPieces = false;
      for (const selector of chessPieceSelectors) {
        if (document.querySelectorAll(selector).length > 0) {
          hasChessPieces = true;
          break;
        }
      }

      // Check for chess squares
      const hasChessSquares = document.querySelectorAll('[class*="square"]').length >= 16 || // At least 16 squares
                             document.querySelectorAll('[data-square]').length >= 16;

      // If we're on a chess site or have detected chess elements, create chess content
      if (isChessSite || hasChessBoard || hasChessPieces || hasChessSquares || hasChessTerms) {
        // Try to extract FEN and PGN
        const fen = extractChessFEN();
        const pgn = extractChessPGN();

        // If we couldn't extract FEN or PGN, create a mock position for analysis
        if (!fen && !pgn && (isChessSite || hasChessBoard || hasChessPieces || hasChessSquares)) {
          // Create a mock FEN for the current position based on visual board state
          const mockFen = createMockPositionFromVisualBoard();

          chessContent = {
            fen: mockFen || 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1', // Default starting position
            pgn: pgn,
            isVisuallyDetected: true
          };
        } else {
          chessContent = {
            fen: fen,
            pgn: pgn
          };
        }
      }
    } catch (chessError) {
      console.error('Error detecting chess content:', chessError);
    }

    // Check if this is a coding problem page and extract detailed information
    let codingProblemData = {};
    try {
      const url = window.location.href.toLowerCase();
      if (url.includes('leetcode.com') ||
          url.includes('hackerrank.com') ||
          url.includes('codewars.com') ||
          url.includes('codeforces.com') ||
          url.includes('atcoder.jp') ||
          url.includes('topcoder.com') ||
          url.includes('geeksforgeeks.org')) {

        // Get detailed coding problem data
        codingProblemData = extractCodeBlocks();
      }
    } catch (codingError) {
      console.error('Error extracting coding problem data:', codingError);
    }

    // Calculate performance metrics
    const endTime = performance.now();
    const extractionTime = Math.round(endTime - startTime);

    // Compile the page data, merging with coding problem data if available
    const pageData = {
      url,
      title,
      content: mainContent || 'No content available',
      contentSource,
      codeBlocks: codingProblemData.codeBlocks || codeBlocks || [],
      chess: chessContent,
      timestamp: new Date().toISOString(),

      // Add performance metrics
      performance: {
        extractionTime,
        contentLength: mainContent.length,
        contentSource,
        codeBlocksCount: (codingProblemData.codeBlocks || codeBlocks || []).length
      },

      // Add coding problem specific fields if available
      ...(codingProblemData.platform ? {
        platform: codingProblemData.platform,
        problemTitle: codingProblemData.problemTitle,
        problemDescription: codingProblemData.problemDescription,
        examples: codingProblemData.examples,
        constraints: codingProblemData.constraints,
        codeTemplate: codingProblemData.codeTemplate
      } : {})
    };

    console.log(`Content extraction completed in ${extractionTime}ms (source: ${contentSource}, length: ${mainContent.length} chars)`);
    return pageData;
  } catch (error) {
    console.error('Error extracting page content:', error);
    // Return minimal fallback data with error information
    const endTime = performance.now();
    const extractionTime = Math.round(endTime - startTime || 0);

    return {
      url: window.location.href || '',
      title: document.title || 'Untitled Page',
      content: 'Content extraction failed. Please refresh the page.',
      contentSource: 'error',
      codeBlocks: [],
      chess: null,
      timestamp: new Date().toISOString(),
      performance: {
        extractionTime,
        error: error.message || 'Unknown error',
        contentLength: 0,
        contentSource: 'error'
      }
    };
  }
}

// Function to extract selected text
function getSelectedText() {
  return window.getSelection().toString().trim();
}

/**
 * Extract chess FEN notation from the page
 * @returns {string|null} - FEN string or null if not found
 */
function extractChessFEN() {
  try {
    // Method 1: Look for FEN in the DOM
    // Check for elements with FEN in data attributes
    const fenElements = document.querySelectorAll('[data-fen], [data-position], [data-chess-position], [data-board-position]');
    for (const el of fenElements) {
      const fen = el.getAttribute('data-fen') || el.getAttribute('data-position') ||
                 el.getAttribute('data-chess-position') || el.getAttribute('data-board-position');
      if (fen && fen.includes('/') && /[KQRBNPkqrbnp1-8]+\/[KQRBNPkqrbnp1-8]+/.test(fen)) {
        return fen;
      }
    }

    // Method 2: Look for FEN in the page content
    const pageText = document.body.innerText;
    const fenRegex = /\b([rnbqkpRNBQKP1-8]+\/[rnbqkpRNBQKP1-8]+\/[rnbqkpRNBQKP1-8]+\/[rnbqkpRNBQKP1-8]+\/[rnbqkpRNBQKP1-8]+\/[rnbqkpRNBQKP1-8]+\/[rnbqkpRNBQKP1-8]+\/[rnbqkpRNBQKP1-8]+)\s+([wb])\s+(K?Q?k?q?|-)\s+([a-h][36]|-)(\s+\d+\s+\d+)?\b/g;
    const matches = pageText.match(fenRegex);
    if (matches && matches.length > 0) {
      return matches[0];
    }

    // Method 3: Chess.com specific
    if (window.location.href.includes('chess.com')) {
      // Try to access Chess.com's game object
      if (window.chessboard && window.chessboard.getFen) {
        return window.chessboard.getFen();
      }
      if (window.chess && window.chess.getFen) {
        return window.chess.getFen();
      }
    }

    // Method 4: Lichess specific
    if (window.location.href.includes('lichess.org')) {
      // Try to access Lichess's game object
      if (window.LichessBoard && window.LichessBoard.getFen) {
        return window.LichessBoard.getFen();
      }
    }

    return null;
  } catch (error) {
    console.error('Error extracting chess FEN:', error);
    return null;
  }
}

/**
 * Extract chess PGN notation from the page
 * @returns {string|null} - PGN string or null if not found
 */
function extractChessPGN() {
  try {
    // Method 1: Look for PGN in the DOM
    // Check for elements with PGN in data attributes or class names
    const pgnElements = document.querySelectorAll('[data-pgn], .pgn, .chess-pgn, pre.pgn, code.pgn');
    for (const el of pgnElements) {
      const pgn = el.getAttribute('data-pgn') || el.innerText;
      if (pgn && pgn.includes('1.') && /\d+\.\s*[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8]/.test(pgn)) {
        return pgn;
      }
    }

    // Method 2: Look for PGN in the page content
    const pageText = document.body.innerText;
    // Look for a section that looks like PGN notation
    const pgnRegex = /\[Event\s+"[^"]*"\][\s\S]*?\[Site\s+"[^"]*"\][\s\S]*?\[Date\s+"[^"]*"\][\s\S]*?\[Round\s+"[^"]*"\][\s\S]*?\[White\s+"[^"]*"\][\s\S]*?\[Black\s+"[^"]*"\][\s\S]*?\[Result\s+"[^"]*"\][\s\S]*?\d+\.\s*[NBRQK]?[a-h]?[1-8]?x?[a-h][1-8]/;
    const match = pageText.match(pgnRegex);
    if (match) {
      // Extract a reasonable amount of text that might contain the full PGN
      const startIndex = match.index;
      const endIndex = pageText.indexOf('\n\n', startIndex + 100);
      return pageText.substring(startIndex, endIndex > startIndex ? endIndex : startIndex + 1000);
    }

    // Method 3: Chess.com specific
    if (window.location.href.includes('chess.com')) {
      // Try to access Chess.com's game object
      if (window.chess && window.chess.getPgn) {
        return window.chess.getPgn();
      }
    }

    // Method 4: Lichess specific
    if (window.location.href.includes('lichess.org')) {
      // Try to access Lichess's game object
      if (window.LichessBoard && window.LichessBoard.getPgn) {
        return window.LichessBoard.getPgn();
      }
    }

    return null;
  } catch (error) {
    console.error('Error extracting chess PGN:', error);
    return null;
  }
}

/**
 * Create a mock FEN position from the visual board state
 * @returns {string|null} - FEN string or null if couldn't create
 */
function createMockPositionFromVisualBoard() {
  try {
    // This is a simplified approach to create a FEN from visual elements
    // It won't work for all chess sites but provides a fallback

    // Default starting position
    const defaultFen = 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1';

    // Try to find the chess board
    const boardSelectors = [
      '.chess-board', '.chessboard', '.board', '.game-board',
      '.cg-wrap', '.chessboard-component', '.board-layout-chessboard'
    ];

    let board = null;
    for (const selector of boardSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        board = element;
        break;
      }
    }

    if (!board) {
      return null;
    }

    // At this point, we found a board but creating a FEN from visual elements
    // is complex and site-specific. For simplicity, we'll return the default position.
    // A more sophisticated implementation would analyze the board's DOM structure
    // and piece positions, which varies greatly between chess sites.

    return defaultFen;
  } catch (error) {
    console.error('Error creating mock position:', error);
    return null;
  }
}

// Function to highlight text on the page
function highlightText(textToHighlight) {
  if (!textToHighlight) return;

  // Create a regex to find the text, escape special characters first
  const escapedText = textToHighlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(`(${escapedText})`, 'gi');

  // Walk through all text nodes and replace matches with highlighted versions
  const walker = document.createTreeWalker(
    document.body,
    NodeFilter.SHOW_TEXT,
    null,
    false
  );

  const nodesToReplace = [];

  while (walker.nextNode()) {
    const node = walker.currentNode;
    if (node.nodeValue.trim() === '') continue;
    if (regex.test(node.nodeValue)) {
      nodesToReplace.push(node);
    }
  }

  nodesToReplace.forEach(node => {
    const parent = node.parentNode;
    if (parent.nodeName === 'SCRIPT' || parent.nodeName === 'STYLE') return;

    const fragment = document.createDocumentFragment();
    const parts = node.nodeValue.split(regex);

    parts.forEach(part => {
      if (part.match(regex)) {
        const mark = document.createElement('mark');
        mark.style.backgroundColor = '#ffeb3b';
        mark.style.color = 'black';
        mark.textContent = part;
        fragment.appendChild(mark);
      } else if (part) {
        fragment.appendChild(document.createTextNode(part));
      }
    });

    parent.replaceChild(fragment, node);
  });
}

// Listen for messages from popup or background script
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  try {
    if (!message || !message.action) {
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }

    // Simple ping to check if content script is loaded
    if (message.action === 'ping') {
      sendResponse({ success: true, status: 'content_script_active' });
      return true;
    }

    if (message.action === 'extractPageContent') {
      try {
        // Extract content and send it to the background script for caching
        const content = extractPageContent();
        chrome.runtime.sendMessage({
          action: 'cachePageContent',
          content
        }).catch(err => console.error('Error caching content:', err));

        sendResponse({ success: true, content: content });
      } catch (error) {
        console.error('Error in extractPageContent handler:', error);
        sendResponse({
          success: false,
          error: 'Content extraction failed',
          content: createFallbackContent()
        });
      }
      return true;
    }

    if (message.action === 'getSelectedText') {
      try {
        sendResponse({ success: true, text: getSelectedText() });
      } catch (error) {
        console.error('Error getting selected text:', error);
        sendResponse({ success: false, text: '' });
      }
      return true;
    }

    if (message.action === 'highlightText') {
      try {
        highlightText(message.text);
        sendResponse({ success: true });
      } catch (error) {
        console.error('Error highlighting text:', error);
        sendResponse({ success: false, error: error.message });
      }
      return true;
    }

    if (message.action === 'extractSpecificInfo') {
      try {
        // Extract specific information based on the type requested
        let extractedInfo = {};

        switch (message.infoType) {
          case 'dates':
            extractedInfo = extractDates();
            break;
          case 'names':
            extractedInfo = extractNames();
            break;
          case 'organizations':
            extractedInfo = extractOrganizations();
            break;
          case 'code':
            extractedInfo = { codeBlocks: extractCodeBlocks() };
            break;
          case 'all':
            extractedInfo = {
              dates: extractDates(),
              names: extractNames(),
              organizations: extractOrganizations(),
              codeBlocks: extractCodeBlocks()
            };
            break;
          default:
            extractedInfo = { error: 'Unknown info type' };
        }

        sendResponse({ success: true, data: extractedInfo });
      } catch (error) {
        console.error('Error extracting specific info:', error);
        sendResponse({ success: false, error: error.message });
      }
      return true;
    }

    if (message.action === 'getVideoInfo') {
      try {
        const videoInfo = extractVideoInfo();
        sendResponse({ success: true, videos: videoInfo });
      } catch (error) {
        console.error('Error extracting video info:', error);
        sendResponse({ success: false, error: error.message });
      }
      return true;
    }

    if (message.action === 'checkForVideoElements') {
      try {
        const hasVideoElements = document.querySelectorAll('video').length > 0 ||
                                document.querySelectorAll('iframe[src*="youtube"]').length > 0 ||
                                document.querySelectorAll('iframe[src*="vimeo"]').length > 0 ||
                                document.querySelectorAll('iframe[src*="dailymotion"]').length > 0 ||
                                document.querySelectorAll('iframe[src*="twitch"]').length > 0;
        sendResponse({ success: true, hasVideoElements });
      } catch (error) {
        console.error('Error checking for video elements:', error);
        sendResponse({ success: false, error: error.message });
      }
      return true;
    }


    // If we get here, it's an unknown action
    sendResponse({ success: false, error: 'Unknown action' });
    return true;
  } catch (error) {
    console.error('Critical error in message listener:', error);
    sendResponse({ success: false, error: 'Internal extension error' });
    return true;
  }
});

/**
 * Create fallback content when extraction fails
 * @returns {object} - Minimal content object
 */
function createFallbackContent() {
  // Try to detect if this is a coding problem page
  let platform = 'unknown';
  const url = window.location.href.toLowerCase() || '';

  if (url.includes('leetcode.com')) {
    platform = 'leetcode';
  } else if (url.includes('hackerrank.com')) {
    platform = 'hackerrank';
  } else if (url.includes('codewars.com')) {
    platform = 'codewars';
  } else if (url.includes('codeforces.com')) {
    platform = 'codeforces';
  } else if (url.includes('atcoder.jp')) {
    platform = 'atcoder';
  } else if (url.includes('topcoder.com')) {
    platform = 'topcoder';
  } else if (url.includes('geeksforgeeks.org')) {
    platform = 'geeksforgeeks';
  }

  const isCodingProblemPage = platform !== 'unknown';

  return {
    url: window.location.href || '',
    title: document.title || 'Untitled Page',
    content: 'Content extraction failed. Please refresh the page.',
    codeBlocks: [],
    timestamp: new Date().toISOString(),
    // Add coding problem fields if this appears to be a coding problem page
    ...(isCodingProblemPage ? {
      platform,
      problemTitle: document.title || 'Unknown Problem',
      problemDescription: 'Problem description extraction failed. Please refresh the page.',
      examples: [],
      constraints: '',
      codeTemplate: ''
    } : {})
  };
}

// Helper function to extract dates from the page
function extractDates() {
  const dateRegex = /\b\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\b|\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b/gi;
  const pageText = document.body.innerText;
  return [...new Set(pageText.match(dateRegex) || [])];
}

// Helper function to extract potential names from the page
function extractNames() {
  // Simple regex for potential names (capitalized words)
  const nameRegex = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)+\b/g;
  const pageText = document.body.innerText;
  return [...new Set(pageText.match(nameRegex) || [])];
}

// Helper function to extract potential organization names
function extractOrganizations() {
  // Look for capitalized phrases that might be organizations
  const orgRegex = /\b(?:[A-Z][a-z]*\s*){2,}(?:Inc|Corp|LLC|Ltd|Company|Organization|Association|University)\b/g;
  const pageText = document.body.innerText;
  return [...new Set(pageText.match(orgRegex) || [])];
}

// Helper function to extract code blocks and problem details
function extractCodeBlocks() {
  const codeData = {
    codeBlocks: [],
    problemTitle: '',
    problemDescription: '',
    examples: [],
    constraints: '',
    codeTemplate: '',
    platform: 'unknown'
  };

  // Detect coding platform
  const url = window.location.href.toLowerCase();
  if (url.includes('leetcode.com')) {
    codeData.platform = 'leetcode';
  } else if (url.includes('hackerrank.com')) {
    codeData.platform = 'hackerrank';
  } else if (url.includes('codewars.com')) {
    codeData.platform = 'codewars';
  } else if (url.includes('codeforces.com')) {
    codeData.platform = 'codeforces';
  } else if (url.includes('atcoder.jp')) {
    codeData.platform = 'atcoder';
  } else if (url.includes('topcoder.com')) {
    codeData.platform = 'topcoder';
  } else if (url.includes('geeksforgeeks.org')) {
    codeData.platform = 'geeksforgeeks';
  }

  // Generic code extraction (works on any site)
  const preElements = document.querySelectorAll('pre, code, .syntax-highlighted, [class*="code"], [class*="CodeMirror"]');
  preElements.forEach(el => {
    const code = el.innerText.trim();
    if (code.length > 0) {
      codeData.codeBlocks.push(code);
    }
  });

  // Platform-specific extraction
  try {
    switch (codeData.platform) {
      case 'leetcode':
        extractLeetCodeData(codeData);
        break;
      case 'hackerrank':
        extractHackerRankData(codeData);
        break;
      case 'codewars':
        extractCodewarsData(codeData);
        break;
      case 'codeforces':
        extractCodeforcesData(codeData);
        break;
      case 'geeksforgeeks':
        extractGeeksForGeeksData(codeData);
        break;
      default:
        // Try generic extraction for unknown platforms
        extractGenericProblemData(codeData);
    }
  } catch (error) {
    console.error(`Error extracting problem data for ${codeData.platform}:`, error);
  }

  return codeData;
}

// Extract LeetCode specific data
function extractLeetCodeData(codeData) {
  // Problem title
  const titleElement = document.querySelector('[data-cy="question-title"], .question-title, .content__title');
  if (titleElement) {
    codeData.problemTitle = titleElement.innerText.trim();
  }

  // Problem description
  const descriptionElement = document.querySelector('[data-cy="question-content"], .question-content, .content__description');
  if (descriptionElement) {
    codeData.problemDescription = descriptionElement.innerText.trim();
  }

  // Examples
  const exampleElements = document.querySelectorAll('.example, [class*="example"]');
  exampleElements.forEach((example, index) => {
    codeData.examples.push({
      index: index + 1,
      text: example.innerText.trim()
    });
  });

  // Constraints
  const constraintsElement = document.querySelector('[data-cy="constraints"], .constraints, [class*="constraint"]');
  if (constraintsElement) {
    codeData.constraints = constraintsElement.innerText.trim();
  }

  // Code template
  const codeEditor = document.querySelector('.CodeMirror, [class*="editor"]');
  if (codeEditor) {
    const editorText = codeEditor.innerText.trim();
    if (editorText) {
      codeData.codeTemplate = editorText;
      codeData.codeBlocks.push(editorText);
    }
  }

  // Difficulty
  const difficultyElement = document.querySelector('[diff], .difficulty, [class*="difficulty"]');
  if (difficultyElement) {
    codeData.difficulty = difficultyElement.innerText.trim();
  }
}

// Extract HackerRank specific data
function extractHackerRankData(codeData) {
  // Problem title
  const titleElement = document.querySelector('.challenge-title, .title, h1.title');
  if (titleElement) {
    codeData.problemTitle = titleElement.innerText.trim();
  }

  // Problem description
  const descriptionElement = document.querySelector('.challenge-text, .problem-statement, .description');
  if (descriptionElement) {
    codeData.problemDescription = descriptionElement.innerText.trim();
  }

  // Examples and Input/Output
  const sampleElements = document.querySelectorAll('.challenge-sample-input, .challenge-sample-output, .input-output, .sample-tests');
  sampleElements.forEach((sample, index) => {
    codeData.examples.push({
      index: index + 1,
      text: sample.innerText.trim()
    });
  });

  // Code template
  const codeEditor = document.querySelector('.CodeMirror, .monaco-editor, [class*="editor"]');
  if (codeEditor) {
    const editorText = codeEditor.innerText.trim();
    if (editorText) {
      codeData.codeTemplate = editorText;
      codeData.codeBlocks.push(editorText);
    }
  }
}

// Extract Codewars specific data
function extractCodewarsData(codeData) {
  // Problem title
  const titleElement = document.querySelector('.content-header h4, .kata-title, h1.ml-2, h4');
  if (titleElement) {
    codeData.problemTitle = titleElement.innerText.trim();
  }

  // Problem description
  const descriptionElement = document.querySelector('#description, .description, .markdown');
  if (descriptionElement) {
    codeData.problemDescription = descriptionElement.innerText.trim();
  }

  // Examples are usually part of the description in Codewars
  // Try to extract examples from description using regex
  const exampleRegex = /Example[s]?:|Sample[s]?:|Test[s]?:|For example:/i;
  if (codeData.problemDescription && exampleRegex.test(codeData.problemDescription)) {
    const parts = codeData.problemDescription.split(exampleRegex);
    if (parts.length > 1) {
      // Extract the part after "Example:" and before the next section
      let exampleText = parts[1];
      const nextSectionMatch = exampleText.match(/\n\s*[A-Z][\w\s]+:/); // Look for next section header
      if (nextSectionMatch) {
        exampleText = exampleText.substring(0, nextSectionMatch.index);
      }
      codeData.examples.push({
        index: 1,
        text: exampleText.trim()
      });
    }
  }

  // Code template
  const codeEditor = document.querySelector('.CodeMirror, .editor, [class*="editor"]');
  if (codeEditor) {
    const editorText = codeEditor.innerText.trim();
    if (editorText) {
      codeData.codeTemplate = editorText;
      codeData.codeBlocks.push(editorText);
    }
  }

  // Difficulty (kyu in Codewars)
  const difficultyElement = document.querySelector('.rank, .kyu, [data-difficulty]');
  if (difficultyElement) {
    codeData.difficulty = difficultyElement.innerText.trim();
  }
}

// Extract Codeforces specific data
function extractCodeforcesData(codeData) {
  // Problem title
  const titleElement = document.querySelector('.problem-statement .title, .header .title');
  if (titleElement) {
    codeData.problemTitle = titleElement.innerText.trim();
  }

  // Problem description
  const descriptionElement = document.querySelector('.problem-statement');
  if (descriptionElement) {
    // Remove the title, input, output, and example sections
    const clone = descriptionElement.cloneNode(true);
    const toRemove = clone.querySelectorAll('.header, .input-specification, .output-specification, .sample-tests');
    toRemove.forEach(el => el.remove());
    codeData.problemDescription = clone.innerText.trim();
  }

  // Input/Output specifications
  const inputSpecElement = document.querySelector('.input-specification');
  const outputSpecElement = document.querySelector('.output-specification');
  let constraints = '';
  if (inputSpecElement) {
    constraints += 'Input Specification:\n' + inputSpecElement.innerText.trim() + '\n\n';
  }
  if (outputSpecElement) {
    constraints += 'Output Specification:\n' + outputSpecElement.innerText.trim();
  }
  codeData.constraints = constraints.trim();

  // Examples
  const sampleTestsElement = document.querySelector('.sample-tests');
  if (sampleTestsElement) {
    const inputs = sampleTestsElement.querySelectorAll('.input pre');
    const outputs = sampleTestsElement.querySelectorAll('.output pre');

    for (let i = 0; i < Math.max(inputs.length, outputs.length); i++) {
      let exampleText = '';
      if (i < inputs.length) {
        exampleText += 'Input:\n' + inputs[i].innerText.trim() + '\n\n';
      }
      if (i < outputs.length) {
        exampleText += 'Output:\n' + outputs[i].innerText.trim();
      }

      codeData.examples.push({
        index: i + 1,
        text: exampleText.trim()
      });
    }
  }

  // Code template (Codeforces doesn't usually provide templates)
  const codeEditor = document.querySelector('.CodeMirror, [class*="editor"]');
  if (codeEditor) {
    const editorText = codeEditor.innerText.trim();
    if (editorText) {
      codeData.codeTemplate = editorText;
      codeData.codeBlocks.push(editorText);
    }
  }
}

// Extract GeeksForGeeks specific data
function extractGeeksForGeeksData(codeData) {
  // Problem title
  const titleElement = document.querySelector('.problem-statement h1, .problemTitle, .title');
  if (titleElement) {
    codeData.problemTitle = titleElement.innerText.trim();
  }

  // Problem description
  const descriptionElement = document.querySelector('.problem-statement, .problemQuestion');
  if (descriptionElement) {
    codeData.problemDescription = descriptionElement.innerText.trim();
  }

  // Examples
  const exampleElements = document.querySelectorAll('.example, [class*="example"], .sampleTestCase');
  exampleElements.forEach((example, index) => {
    codeData.examples.push({
      index: index + 1,
      text: example.innerText.trim()
    });
  });

  // Code template
  const codeEditor = document.querySelector('.CodeMirror, [class*="editor"]');
  if (codeEditor) {
    const editorText = codeEditor.innerText.trim();
    if (editorText) {
      codeData.codeTemplate = editorText;
      codeData.codeBlocks.push(editorText);
    }
  }
}

/**
 * Extract information about videos on the page
 * @returns {Array} - Array of video information objects
 */
function extractVideoInfo() {
  const videos = [];

  // Extract HTML5 video elements
  const videoElements = document.querySelectorAll('video');
  videoElements.forEach(video => {
    try {
      const videoInfo = {
        type: 'html5',
        src: video.src || video.currentSrc || '',
        duration: formatDuration(video.duration),
        width: video.videoWidth || video.clientWidth || 0,
        height: video.videoHeight || video.clientHeight || 0,
        poster: video.poster || '',
        controls: video.controls,
        autoplay: video.autoplay,
        muted: video.muted,
        loop: video.loop
      };

      // If the video element doesn't have a direct src, check for source elements
      if (!videoInfo.src) {
        const sources = video.querySelectorAll('source');
        if (sources.length > 0) {
          videoInfo.src = sources[0].src || '';
          videoInfo.type = sources[0].type || 'html5';
        }
      }

      videos.push(videoInfo);
    } catch (error) {
      console.error('Error extracting HTML5 video info:', error);
    }
  });

  // Extract YouTube iframes
  const youtubeIframes = document.querySelectorAll('iframe[src*="youtube"], iframe[src*="youtu.be"]');
  youtubeIframes.forEach(iframe => {
    try {
      const src = iframe.src || '';
      // Extract video ID from YouTube URL
      let videoId = '';
      if (src.includes('youtube.com/embed/')) {
        videoId = src.split('youtube.com/embed/')[1].split('?')[0];
      } else if (src.includes('youtube.com/watch?v=')) {
        videoId = src.split('v=')[1].split('&')[0];
      } else if (src.includes('youtu.be/')) {
        videoId = src.split('youtu.be/')[1].split('?')[0];
      }

      const videoInfo = {
        type: 'youtube',
        src: src,
        videoId: videoId,
        width: iframe.width || iframe.clientWidth || 0,
        height: iframe.height || iframe.clientHeight || 0,
        thumbnail: videoId ? `https://img.youtube.com/vi/${videoId}/0.jpg` : ''
      };

      videos.push(videoInfo);
    } catch (error) {
      console.error('Error extracting YouTube video info:', error);
    }
  });

  // Extract Vimeo iframes
  const vimeoIframes = document.querySelectorAll('iframe[src*="vimeo"]');
  vimeoIframes.forEach(iframe => {
    try {
      const src = iframe.src || '';
      // Extract video ID from Vimeo URL
      let videoId = '';
      if (src.includes('vimeo.com/video/')) {
        videoId = src.split('vimeo.com/video/')[1].split('?')[0];
      } else if (src.includes('vimeo.com/')) {
        videoId = src.split('vimeo.com/')[1].split('?')[0];
      }

      const videoInfo = {
        type: 'vimeo',
        src: src,
        videoId: videoId,
        width: iframe.width || iframe.clientWidth || 0,
        height: iframe.height || iframe.clientHeight || 0
      };

      videos.push(videoInfo);
    } catch (error) {
      console.error('Error extracting Vimeo video info:', error);
    }
  });

  // Extract other video platform iframes (Dailymotion, Twitch, etc.)
  const otherVideoIframes = document.querySelectorAll('iframe[src*="dailymotion"], iframe[src*="twitch"], iframe[src*="tiktok"]');
  otherVideoIframes.forEach(iframe => {
    try {
      const src = iframe.src || '';
      let type = 'unknown';

      if (src.includes('dailymotion')) {
        type = 'dailymotion';
      } else if (src.includes('twitch')) {
        type = 'twitch';
      } else if (src.includes('tiktok')) {
        type = 'tiktok';
      }

      const videoInfo = {
        type: type,
        src: src,
        width: iframe.width || iframe.clientWidth || 0,
        height: iframe.height || iframe.clientHeight || 0
      };

      videos.push(videoInfo);
    } catch (error) {
      console.error('Error extracting other video platform info:', error);
    }
  });

  return videos;
}

/**
 * Format video duration in MM:SS format
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration
 */
function formatDuration(seconds) {
  if (!seconds || isNaN(seconds)) return 'Unknown';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// Generic extraction for unknown platforms
function extractGenericProblemData(codeData) {
  // Try to find problem title (usually the first h1 or h2)
  const titleElement = document.querySelector('h1, h2, .problem-title, .title');
  if (titleElement) {
    codeData.problemTitle = titleElement.innerText.trim();
  }

  // Try to find problem description
  const descriptionSelectors = [
    '.problem-description', '.description', '.statement',
    '#problem-statement', '#description',
    'section:has(h2:contains("Problem"))',
    'div:has(h3:contains("Description"))'
  ];

  for (const selector of descriptionSelectors) {
    try {
      const element = document.querySelector(selector);
      if (element) {
        codeData.problemDescription = element.innerText.trim();
        break;
      }
    } catch (e) {
      // Some complex selectors might not be supported, continue to next
    }
  }

  // Try to find examples
  const exampleSelectors = [
    '.example', '.sample', '.test-case', '.input-output',
    'section:has(h3:contains("Example"))',
    'div:has(h4:contains("Sample"))'
  ];

  for (const selector of exampleSelectors) {
    try {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        elements.forEach((example, index) => {
          codeData.examples.push({
            index: index + 1,
            text: example.innerText.trim()
          });
        });
        break;
      }
    } catch (e) {
      // Continue to next selector
    }
  }

  // If we still don't have examples, try to extract from the description
  if (codeData.examples.length === 0 && codeData.problemDescription) {
    const exampleRegex = /Example[s]?:|Sample[s]?:|Test[s]?:|For example:/i;
    if (exampleRegex.test(codeData.problemDescription)) {
      const parts = codeData.problemDescription.split(exampleRegex);
      if (parts.length > 1) {
        let exampleText = parts[1];
        const nextSectionMatch = exampleText.match(/\n\s*[A-Z][\w\s]+:/); // Look for next section header
        if (nextSectionMatch) {
          exampleText = exampleText.substring(0, nextSectionMatch.index);
        }
        codeData.examples.push({
          index: 1,
          text: exampleText.trim()
        });
      }
    }
  }

  // Try to find constraints
  const constraintSelectors = [
    '.constraints', '.limitation', '.restriction',
    'section:has(h3:contains("Constraint"))',
    'div:has(h4:contains("Limitation"))'
  ];

  for (const selector of constraintSelectors) {
    try {
      const element = document.querySelector(selector);
      if (element) {
        codeData.constraints = element.innerText.trim();
        break;
      }
    } catch (e) {
      // Continue to next selector
    }
  }
}

/**
 * Extract YouTube-specific content
 * @returns {object} YouTube video data
 */
function extractYouTubeContent() {
  const youtubeData = {
    videoTitle: '',
    videoDescription: '',
    channelName: '',
    viewCount: '',
    uploadDate: '',
    likes: '',
    comments: [],
    relatedVideos: [],
    isShort: window.location.href && window.location.href.includes('/shorts/')
  };

  try {
    // Video title
    const titleElement = document.querySelector('h1.title, h1.ytd-video-primary-info-renderer, #title h1, #video-title');
    if (titleElement && titleElement.innerText) {
      youtubeData.videoTitle = titleElement.innerText.trim();
    }

    // Video description
    const descriptionElement = document.querySelector('#description, #description-text, .ytd-video-secondary-info-renderer #description');
    if (descriptionElement && descriptionElement.innerText) {
      youtubeData.videoDescription = descriptionElement.innerText.trim();
    }

    // Channel name
    const channelElement = document.querySelector('#channel-name, #owner-name a, .ytd-channel-name a, #upload-info a');
    if (channelElement && channelElement.innerText) {
      youtubeData.channelName = channelElement.innerText.trim();
    }

    // View count
    const viewCountElement = document.querySelector('.view-count, #count .ytd-video-view-count-renderer, #info-text .ytd-video-view-count-renderer');
    if (viewCountElement && viewCountElement.innerText) {
      youtubeData.viewCount = viewCountElement.innerText.trim();
    }

    // Upload date
    const dateElement = document.querySelector('#info-strings yt-formatted-string, #upload-info .ytd-video-primary-info-renderer, #metadata-line span:nth-child(2)');
    if (dateElement && dateElement.innerText) {
      youtubeData.uploadDate = dateElement.innerText.trim();
    }

    // Likes
    const likesElement = document.querySelector('#top-level-buttons-computed yt-formatted-string, .like-button-renderer-like-button .yt-simple-endpoint span');
    if (likesElement && likesElement.innerText) {
      youtubeData.likes = likesElement.innerText.trim();
    }

    // Comments (top few)
    const commentElements = document.querySelectorAll('ytd-comment-renderer #content-text, #comments #content-text');
    if (commentElements && commentElements.length > 0) {
      commentElements.forEach((comment, index) => {
        if (index < 5 && comment && comment.innerText) { // Limit to 5 comments
          youtubeData.comments.push(comment.innerText.trim());
        }
      });
    }

    // Related videos (titles)
    const relatedElements = document.querySelectorAll('#related #video-title, .ytd-compact-video-renderer #video-title');
    if (relatedElements && relatedElements.length > 0) {
      relatedElements.forEach((video, index) => {
        if (index < 5 && video && video.innerText) { // Limit to 5 related videos
          youtubeData.relatedVideos.push(video.innerText.trim());
        }
      });
    }

    return youtubeData;
  } catch (error) {
    console.error('Error extracting YouTube content:', error);
    return youtubeData;
  }
}

// Extract and cache the page content on initial load
window.addEventListener('load', () => {
  try {
    const content = extractPageContent();

    // Add YouTube-specific data if on YouTube
    if (window.location.href && window.location.href.includes('youtube.com')) {
      try {
        const youtubeData = extractYouTubeContent();
        content.youtube = youtubeData;
      } catch (youtubeError) {
        console.error('Error extracting YouTube data:', youtubeError);
        // Add empty YouTube data structure
        content.youtube = {
          videoTitle: '',
          videoDescription: '',
          channelName: '',
          viewCount: '',
          uploadDate: '',
          likes: '',
          comments: [],
          relatedVideos: [],
          isShort: false
        };
      }
    }

    chrome.runtime.sendMessage({
      action: 'cachePageContent',
      content
    }).catch(error => {
      console.error('Error caching page content on load:', error);
    });
  } catch (error) {
    console.error('Error extracting page content on load:', error);
  }
});

// Also register a DOMContentLoaded event as a backup
document.addEventListener('DOMContentLoaded', () => {
  // Wait a short time to ensure the page is fully rendered
  setTimeout(() => {
    try {
      // Only extract if we haven't already done so in the load event
      const content = extractPageContent();

      // Add YouTube-specific data if on YouTube
      if (window.location.href && window.location.href.includes('youtube.com')) {
        try {
          const youtubeData = extractYouTubeContent();
          content.youtube = youtubeData;
        } catch (youtubeError) {
          console.error('Error extracting YouTube data:', youtubeError);
          // Add empty YouTube data structure
          content.youtube = {
            videoTitle: '',
            videoDescription: '',
            channelName: '',
            viewCount: '',
            uploadDate: '',
            likes: '',
            comments: [],
            relatedVideos: [],
            isShort: false
          };
        }
      }

      chrome.runtime.sendMessage({
        action: 'cachePageContent',
        content
      }).catch(error => {
        console.error('Error caching page content on DOMContentLoaded:', error);
      });
    } catch (error) {
      console.error('Error extracting page content on DOMContentLoaded:', error);
    }
  }, 500);
});


