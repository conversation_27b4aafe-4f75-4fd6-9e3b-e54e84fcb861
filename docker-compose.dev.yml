version: '3.8'

services:
  # API Development Service
  browzyai-api-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: browzyai-api-dev
    ports:
      - "5000:5000"
      - "9229:9229"  # Node.js debugging port
    environment:
      - NODE_ENV=development
      - API_PORT=5000
      - DEBUG=browzyai:*
      - CORS_ORIGIN=http://localhost:5173,chrome-extension://*
    volumes:
      - .:/app
      - /app/node_modules
      - api_dev_node_modules:/app/api/node_modules
    networks:
      - browzyai-dev-network
    restart: unless-stopped
    command: ["npm", "run", "dev:api"]

  # Dashboard Development Service
  browzyai-dashboard-dev:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
      target: development
    container_name: browzyai-dashboard-dev
    ports:
      - "5173:5173"
      - "24678:24678"  # Vite HMR port
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:5000
      - VITE_APP_NAME=BrowzyAI Dashboard (Dev)
      - VITE_HMR_PORT=24678
    volumes:
      - .:/app
      - /app/node_modules
      - dashboard_dev_node_modules:/app/dashboard/node_modules
    networks:
      - browzyai-dev-network
    depends_on:
      - browzyai-api-dev
    restart: unless-stopped
    command: ["npm", "run", "dev:dashboard"]

  # Extension Development Watcher
  browzyai-extension-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: browzyai-extension-dev
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - browzyai-dev-network
    restart: unless-stopped
    command: ["npm", "run", "watch:extension"]

  # Development Database
  postgres-dev:
    image: postgres:15-alpine
    container_name: browzyai-postgres-dev
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=browzyai_dev
      - POSTGRES_USER=browzyai_dev
      - POSTGRES_PASSWORD=dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init-dev.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - browzyai-dev-network
    restart: unless-stopped

  # Development Redis
  redis-dev:
    image: redis:7-alpine
    container_name: browzyai-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - browzyai-dev-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Mailhog for email testing (development only)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: browzyai-mailhog
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - browzyai-dev-network
    restart: unless-stopped

volumes:
  api_dev_node_modules:
  dashboard_dev_node_modules:
  postgres_dev_data:
  redis_dev_data:

networks:
  browzyai-dev-network:
    driver: bridge
