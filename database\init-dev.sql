-- BrowzyAI Development Database Initialization Script

-- Include the main initialization
\i /docker-entrypoint-initdb.d/init.sql

-- Insert sample data for development
INSERT INTO users (email, password_hash, name, plan) VALUES
    ('<EMAIL>', '$2a$10$example.hash.for.development.only', 'Test User 1', 'free'),
    ('<EMAIL>', '$2a$10$example.hash.for.development.only', 'Test User 2', 'premium'),
    ('<EMAIL>', '$2a$10$example.hash.for.development.only', 'Developer', 'premium')
ON CONFLICT (email) DO NOTHING;

-- Insert sample chat history
INSERT INTO chat_history (user_id, session_id, message, response, provider, model, tokens_used, response_time) VALUES
    (1, 'session_1', 'Hello, how are you?', 'I am doing well, thank you for asking!', 'openai', 'gpt-3.5-turbo', 25, 1.2),
    (1, 'session_1', 'What can you help me with?', 'I can help you with various tasks including writing, analysis, and answering questions.', 'openai', 'gpt-3.5-turbo', 35, 1.5),
    (2, 'session_2', 'Summarize this article for me', 'Here is a summary of the article you provided...', 'anthropic', 'claude-3-haiku', 150, 2.1),
    (2, 'session_2', 'Translate this to Spanish', 'Aquí está la traducción al español...', 'google', 'gemini-pro', 45, 0.8);

-- Insert sample analytics events
INSERT INTO analytics_events (user_id, event_name, properties, session_id) VALUES
    (1, 'chat_message_sent', '{"provider": "openai", "model": "gpt-3.5-turbo"}', 'session_1'),
    (1, 'extension_opened', '{"source": "popup"}', 'session_1'),
    (2, 'chat_message_sent', '{"provider": "anthropic", "model": "claude-3-haiku"}', 'session_2'),
    (2, 'feature_used', '{"feature": "summarization"}', 'session_2');

-- Insert sample usage stats
INSERT INTO usage_stats (user_id, date, requests_count, tokens_used, providers_used, models_used) VALUES
    (1, CURRENT_DATE, 15, 2500, '{"openai": 12, "google": 3}', '{"gpt-3.5-turbo": 12, "gemini-pro": 3}'),
    (1, CURRENT_DATE - INTERVAL '1 day', 20, 3200, '{"openai": 15, "anthropic": 5}', '{"gpt-3.5-turbo": 15, "claude-3-haiku": 5}'),
    (2, CURRENT_DATE, 8, 1800, '{"anthropic": 5, "google": 3}', '{"claude-3-haiku": 5, "gemini-pro": 3}'),
    (2, CURRENT_DATE - INTERVAL '1 day', 12, 2100, '{"openai": 8, "anthropic": 4}', '{"gpt-4": 8, "claude-3-sonnet": 4}');

-- Insert sample feedback
INSERT INTO feedback (user_id, rating, comment, category) VALUES
    (1, 5, 'Great extension! Very helpful for my daily work.', 'general'),
    (2, 4, 'Love the AI features, but could use more customization options.', 'features'),
    (1, 5, 'The summarization feature is amazing!', 'features');

-- Create a view for user statistics
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    u.id,
    u.email,
    u.name,
    u.plan,
    u.created_at,
    COALESCE(SUM(us.requests_count), 0) as total_requests,
    COALESCE(SUM(us.tokens_used), 0) as total_tokens,
    COUNT(DISTINCT ch.id) as total_chats,
    MAX(u.last_login) as last_login
FROM users u
LEFT JOIN usage_stats us ON u.id = us.user_id
LEFT JOIN chat_history ch ON u.id = ch.user_id
GROUP BY u.id, u.email, u.name, u.plan, u.created_at;

-- Grant permissions on the view
GRANT SELECT ON user_stats TO browzyai_dev;
