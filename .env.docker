# Docker Environment Configuration for BrowzyAI

# Application
NODE_ENV=development
APP_NAME=BrowzyAI
APP_VERSION=1.0.1

# API Configuration
API_PORT=5000
API_HOST=0.0.0.0
API_BASE_URL=https://api.browzyai.com

# Dashboard Configuration
DASHBOARD_PORT=5173
DASHBOARD_HOST=0.0.0.0
VITE_API_URL=https://api.browzyai.com
VITE_APP_NAME=BrowzyAI Dashboard (Docker)
VITE_HMR_PORT=24678

# Database Configuration (Docker services)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=browzyai
DB_USER=browzyai
DB_PASSWORD=browzyai_password
DATABASE_URL=*****************************************************/browzyai

# Redis Configuration (Docker service)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,chrome-extension://*

# Development Email (Mailhog)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=<EMAIL>

# Logging
LOG_LEVEL=debug
LOG_FILE=/app/logs/app.log

# Development Tools
DEBUG=browzyai:*
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true

# Health Check
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Feature Flags (Development)
ENABLE_ANALYTICS=false
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=false
ENABLE_COMPRESSION=false
ENABLE_HOT_RELOAD=true
