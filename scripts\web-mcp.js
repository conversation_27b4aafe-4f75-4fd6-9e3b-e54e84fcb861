/**
 * Web MCP - Master Control Program for Web Content
 * Allows users to scan web pages and ask questions about the content
 */
class WebMCP {
  /**
   * Initialize the Web MCP
   * @param {Object} apiManager - The API manager for AI interactions
   * @param {Object} uiManager - The UI manager for interface updates
   */
  constructor(apiManager, uiManager) {
    this.apiManager = apiManager;
    this.uiManager = uiManager;
    this.pageContent = null;
    this.pageMetadata = {
      title: '',
      url: '',
      contentSize: 0,
      paragraphs: 0,
      images: 0,
      extractionMethod: '',
      crawledPages: 0,
      links: []
    };
    this.isScanning = false;
    this.isInitialized = false;
    this.chatHistory = [];
    this.crawlingStatusInterval = null;

    // Initialize the dialog if it exists in the DOM
    this.initializeDialog();
  }

  /**
   * Initialize the dialog and event listeners
   */
  initializeDialog() {
    // Check if dialog already exists
    const dialog = document.getElementById('webMcpDialog');
    if (!dialog) {
      console.error('Web MCP Dialog not found in the DOM');
      return;
    }

    // Add event listeners
    this.addEventListeners();
    this.isInitialized = true;
  }

  /**
   * Add event listeners to dialog elements
   */
  addEventListeners() {
    // Close dialog button
    const closeBtn = document.getElementById('closeWebMcpDialog');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideDialog());
    }

    // Scan page button
    const scanBtn = document.getElementById('scanPageBtn');
    if (scanBtn) {
      scanBtn.addEventListener('click', () => this.scanCurrentPage());
    }

    // Clear chat button
    const clearChatBtn = document.getElementById('mcpClearChatBtn');
    if (clearChatBtn) {
      clearChatBtn.addEventListener('click', () => this.clearChat());
    }

    // Send message button
    const sendBtn = document.getElementById('mcpSendBtn');
    if (sendBtn) {
      sendBtn.addEventListener('click', () => this.handleUserQuestion());
    }

    // User input enter key and auto-resize
    const userInput = document.getElementById('mcpUserInput');
    if (userInput) {
      // Initialize textarea height
      this.autoResizeTextarea(userInput);

      // Add input event for auto-resize
      userInput.addEventListener('input', () => {
        this.autoResizeTextarea(userInput);
      });

      // Add keydown event for sending message
      userInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.handleUserQuestion();
        }
      });
    }

    // Close dialog when clicking outside
    const dialog = document.getElementById('webMcpDialog');
    if (dialog) {
      dialog.addEventListener('click', (e) => {
        if (e.target === dialog) {
          this.hideDialog();
        }
      });
    }

    // Add escape key listener to close dialog
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const dialog = document.getElementById('webMcpDialog');
        if (dialog && dialog.style.display === 'flex') {
          this.hideDialog();
        }
      }
    });
  }

  /**
   * Clear chat history and messages
   */
  clearChat() {
    // Clear chat messages
    const chatMessages = document.getElementById('mcpChatMessages');
    if (chatMessages) {
      // Keep only the first AI message (welcome message)
      const firstMessage = chatMessages.querySelector('.mcp-message.ai');
      chatMessages.innerHTML = '';

      if (firstMessage) {
        chatMessages.appendChild(firstMessage);
      }
    }

    // Clear chat history
    this.chatHistory = [];

    // Save empty chat history
    this.saveChatHistory();

    // Show status message
    this.uiManager.showStatus('Chat history cleared', false, 3000);
  }

  /**
   * Show the Web MCP dialog
   */
  showDialog() {
    const dialog = document.getElementById('webMcpDialog');
    if (dialog) {
      dialog.style.display = 'flex';
      this.uiManager.showStatus('Web MCP opened', false, 3000);

      // Check if we need to scan the page
      if (!this.pageContent) {
        // Auto-scan the page when opening dialog
        setTimeout(() => {
          this.scanCurrentPage();
        }, 500);
      }

      // Focus on input
      setTimeout(() => {
        const input = document.getElementById('mcpUserInput');
        if (input) {
          input.focus();
          this.autoResizeTextarea(input);
        }
      }, 300);
    }
  }

  /**
   * Hide the Web MCP dialog
   */
  hideDialog() {
    const dialog = document.getElementById('webMcpDialog');
    if (dialog) {
      dialog.style.display = 'none';
    }
  }

  /**
   * Scan the current page for content using crawling technique
   */
  async scanCurrentPage() {
    if (this.isScanning) return;

    this.isScanning = true;
    this.updateStatus('scanning', 'Initializing web crawler...');

    try {
      // Get the active tab
      chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
        const tab = tabs[0];

        // Update metadata
        this.pageMetadata.title = tab.title || 'Unknown Page';
        this.pageMetadata.url = tab.url || 'Unknown URL';

        // Clear chat history for new scan
        this.chatHistory = [];

        // Set enhanced crawling options
        const crawlOptions = {
          maxDepth: 2,          // Increased to depth 2 for better content coverage
          maxPages: 8,          // Increased to max 8 pages for more comprehensive results
          stayOnDomain: true,   // Stay on the same domain for security and relevance
          timeout: 15000,       // 15 second timeout for fetching pages
          retryCount: 2,        // Retry failed requests twice
        };

        // Start the crawling process
        this.updateStatus('scanning', 'Starting web crawler...');

        // Send message to content script to crawl page content
        chrome.tabs.sendMessage(tab.id, {
          action: 'extractPageContent',
          options: crawlOptions
        }, async (response) => {
          if (chrome.runtime.lastError) {
            console.error('Error sending message:', chrome.runtime.lastError);
            this.handleScanError('Could not access page content. Make sure you are on a webpage.');
            return;
          }

          if (!response || !response.success) {
            this.handleScanError('Failed to extract page content');
            return;
          }

          // Process the extracted content
          this.pageContent = response.content;
          this.pageMetadata.contentSize = this.formatSize(response.contentSize || 0);
          this.pageMetadata.paragraphs = response.paragraphs || 0;
          this.pageMetadata.images = response.images || 0;
          this.pageMetadata.extractionMethod = response.extractionMethod || 'standard';
          this.pageMetadata.crawledPages = response.crawledPages || 1;
          this.pageMetadata.links = response.links || [];

          // Update UI with metadata
          this.updateMetadataUI();

          // Update status based on extraction method
          if (this.pageMetadata.extractionMethod === 'crawling') {
            this.updateStatus('scanned', `Crawled ${this.pageMetadata.crawledPages} pages successfully`);
            console.log(`Web MCP: Crawled ${this.pageMetadata.crawledPages} pages`);
          } else if (this.pageMetadata.extractionMethod === 'fallback') {
            this.updateStatus('scanned', 'Page scanned using fallback methods');
            console.log('Web MCP: Used fallback extraction methods');
          } else {
            this.updateStatus('scanned', 'Page scanned successfully');
          }

          // Try to load previous chat history for this URL
          this.loadChatHistory();

          // If no chat history was loaded, add welcome message
          if (this.chatHistory.length === 0) {
            // Create welcome message based on extraction method
            let welcomeMessage = '';

            if (this.pageMetadata.extractionMethod === 'crawling') {
              welcomeMessage = `🕸️ I've crawled and analyzed ${this.pageMetadata.crawledPages} pages starting from "${this.pageMetadata.title}".`;

              if (this.pageMetadata.crawledPages > 1) {
                welcomeMessage += `\n\nI've gathered information from the current page and ${this.pageMetadata.crawledPages - 1} linked pages to provide more comprehensive answers. This gives me a better understanding of the content and context.`;
              }

              // Add information about links if available
              if (this.pageMetadata.links && this.pageMetadata.links.length > 0) {
                welcomeMessage += `\n\nI've discovered ${this.pageMetadata.links.length} links on the page${this.pageMetadata.crawledPages > 1 ? 's' : ''} I crawled.`;
              }
            } else {
              // This should not happen anymore since we're always using crawling,
              // but keeping as a fallback just in case
              welcomeMessage = `📄 I've analyzed the page: "${this.pageMetadata.title}" using web crawling.`;
            }

            welcomeMessage += `\n\nI'm ready to help you understand the content of this webpage. You can ask me about specific information, request a summary, or ask questions about what you see on the page.

What would you like to know? 😊`;

            this.addAIMessage(welcomeMessage);

            // Add to chat history
            this.chatHistory.push({ role: 'assistant', content: welcomeMessage });
            this.saveChatHistory();
          }

          this.isScanning = false;
        });

        // Start polling for crawling status updates
        this.startCrawlingStatusPolling(tab.id);
      });
    } catch (error) {
      console.error('Error scanning page:', error);
      this.handleScanError('Error scanning page: ' + error.message);
    }
  }

  /**
   * Start polling for crawling status updates
   * @param {number} tabId - The ID of the tab being crawled
   */
  startCrawlingStatusPolling(tabId) {
    // Clear any existing polling interval
    if (this.crawlingStatusInterval) {
      clearInterval(this.crawlingStatusInterval);
    }

    // Set up polling interval
    this.crawlingStatusInterval = setInterval(() => {
      // Check if we're still scanning
      if (!this.isScanning) {
        clearInterval(this.crawlingStatusInterval);
        return;
      }

      // Request status update from content script
      chrome.tabs.sendMessage(tabId, { action: 'getCrawlingStatus' }, (response) => {
        if (chrome.runtime.lastError || !response || !response.success) {
          return;
        }

        const status = response.crawlingStatus;

        // Update UI with crawling status
        if (status.inProgress) {
          const progress = status.totalPages > 0
            ? Math.round((status.pagesProcessed / status.totalPages) * 100)
            : 0;

          this.updateStatus('scanning', `Crawling pages: ${status.pagesProcessed}/${status.totalPages} (${progress}%)`);
        }
      });
    }, 500); // Poll every 500ms
  }

  /**
   * Handle scan error
   * @param {string} errorMessage - The error message
   */
  handleScanError(errorMessage) {
    console.error('Web MCP: Scan error:', errorMessage);
    this.updateStatus('error', errorMessage);
    this.isScanning = false;

    // Add error message to chat
    const chatMessages = document.getElementById('mcpChatMessages');
    if (chatMessages) {
      // Clear existing messages
      chatMessages.innerHTML = '';

      // Determine if it's a content protection issue
      const isContentProtection =
        errorMessage.includes('protection') ||
        errorMessage.includes('access') ||
        errorMessage.includes('permission') ||
        errorMessage.includes('blocked') ||
        errorMessage.includes('security') ||
        errorMessage.includes('CORS');

      // Create appropriate error message
      let errorMsg = '';

      if (isContentProtection) {
        errorMsg = `⚠️ I encountered an error while scanning this page: Content protection detected

This website appears to have content protection mechanisms that prevent me from crawling its content properly.

Here are some things you can try:
1. Try scanning a different page on this website that might have less protection
2. Try a different website altogether
3. For websites with login walls, try logging in first and then scanning

I'll continue trying to extract whatever content I can access, but my understanding of the page might be limited.`;
      } else {
        errorMsg = `⚠️ I encountered an error while scanning this page: Failed to extract page content

Here are some things you can try:
1. Refresh the page and try scanning again
2. Try scanning a different page
3. Check if the page has content protection that might be blocking extraction
4. For complex web applications, try scanning a simpler page first

If the problem persists, the page might be using techniques that prevent content extraction.`;
      }

      this.addAIMessage(errorMsg);

      // Add to chat history
      this.chatHistory = [{ role: 'assistant', content: errorMsg }];
      this.saveChatHistory();

      // Try to extract at least some content even after error
      this.tryEmergencyExtraction();
    }
  }

  /**
   * Try emergency extraction when normal extraction fails
   * This uses a more aggressive approach to get at least some content
   */
  tryEmergencyExtraction() {
    try {
      // Get the current tab
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (!tabs || tabs.length === 0) return;

        const tab = tabs[0];

        // Send message to content script to try emergency extraction
        chrome.tabs.sendMessage(tab.id, {
          action: 'emergencyExtraction'
        }, (response) => {
          if (chrome.runtime.lastError || !response || !response.success) {
            console.error('Emergency extraction failed:', chrome.runtime.lastError);
            return;
          }

          // If we got some content, store it
          if (response.content && response.content.length > 100) {
            console.log('Web MCP: Emergency extraction successful, got', response.content.length, 'chars');
            this.pageContent = response.content;
            this.pageMetadata.contentSize = this.formatSize(response.content.length);
            this.pageMetadata.extractionMethod = 'emergency';

            // Update status
            this.updateStatus('scanned', 'Limited content extracted in emergency mode');

            // Add follow-up message
            this.addAIMessage("I managed to extract some basic content from the page. While my understanding is limited, you can still ask me questions and I'll do my best to help! 🔍");
          }
        });
      });
    } catch (error) {
      console.error('Error in emergency extraction:', error);
    }
  }

  /**
   * Update the status indicator
   * @param {string} status - The status type (scanning, scanned, error)
   * @param {string} message - The status message
   */
  updateStatus(status, message) {
    const statusIndicator = document.getElementById('mcpStatusIndicator');
    const statusText = document.getElementById('mcpStatusText');

    if (statusIndicator && statusText) {
      // Remove all status classes
      statusIndicator.classList.remove('scanning', 'scanned', 'error');

      // Add the current status class
      statusIndicator.classList.add(status);

      // Update icon based on status
      const statusIcon = statusIndicator.querySelector('.status-icon i');
      if (statusIcon) {
        // Clear existing classes
        statusIcon.className = '';

        // Add appropriate icon class based on status
        switch (status) {
          case 'scanning':
            statusIcon.className = 'fas fa-circle-notch fa-spin';
            break;
          case 'scanned':
            statusIcon.className = 'fas fa-check-circle';
            break;
          case 'error':
            statusIcon.className = 'fas fa-exclamation-circle';
            break;
          default:
            statusIcon.className = 'fas fa-info-circle';
        }
      }

      // Update text
      statusText.textContent = message;
    }
  }

  /**
   * Update the metadata UI with page information
   * Note: Statistics cards have been removed from the UI, but we keep this method
   * to avoid breaking existing code. It now only logs the metadata to console.
   */
  updateMetadataUI() {
    // Log metadata to console for debugging purposes
    console.log('Page metadata:', {
      title: this.pageMetadata.title,
      url: this.pageMetadata.url,
      contentSize: this.pageMetadata.contentSize,
      paragraphs: this.pageMetadata.paragraphs,
      images: this.pageMetadata.images,
      extractionMethod: this.pageMetadata.extractionMethod || 'standard',
      crawledPages: this.pageMetadata.crawledPages || 1,
      links: (this.pageMetadata.links || []).length
    });

    // No UI elements to update anymore
  }

  /**
   * Format file size in KB, MB, etc.
   * @param {number} bytes - The size in bytes
   * @returns {string} - Formatted size string
   */
  formatSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  }

  /**
   * Handle user question submission
   */
  async handleUserQuestion() {
    const userInput = document.getElementById('mcpUserInput');
    if (!userInput || !userInput.value.trim()) return;

    const question = userInput.value.trim();
    userInput.value = '';

    // Auto-resize the input field
    this.autoResizeTextarea(userInput);

    // Add user message to chat
    this.addUserMessage(question);

    // Check if page has been scanned
    if (!this.pageContent) {
      this.addAIMessage("I need to scan the page first before I can answer questions about it. Please click the 'Scan Page' button. 🔍");
      return;
    }

    // Show thinking indicator
    this.addAIThinkingMessage();

    try {
      // Prepare context for AI
      const context = this.prepareContextForAI(question);

      // Get AI response - use the correct method name
      const response = await this.apiManager.sendMessage(context, {
        model: 'openai/gpt-3.5-turbo', // Explicitly use gpt-3.5-turbo, not the 16k version
        temperature: 0.7,
        max_tokens: 1000,
        systemPrompt: "You are Web MCP (Master Control Program), an AI assistant that helps users understand web page content. You are friendly, helpful, and include emojis in your responses to make the conversation more engaging."
      });

      // Remove thinking indicator and add AI response
      this.removeThinkingIndicator();

      // Get the response text
      const responseText = response.text || response;

      // Add AI response
      this.addAIMessage(responseText);

      // Add to chat history
      this.chatHistory.push({ role: 'user', content: question });
      this.chatHistory.push({ role: 'assistant', content: responseText });

      // Save chat history to local storage
      this.saveChatHistory();

    } catch (error) {
      console.error('Error getting AI response:', error);
      this.removeThinkingIndicator();
      this.addAIMessage("I'm sorry, I encountered an error while processing your question. Please try again. 😔");
    }
  }

  /**
   * Auto-resize textarea based on content
   * @param {HTMLTextAreaElement} textarea - The textarea element
   */
  autoResizeTextarea(textarea) {
    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';

    // Set the height to match content (with a max height)
    const newHeight = Math.min(textarea.scrollHeight, 100);
    textarea.style.height = newHeight + 'px';
  }

  /**
   * Save chat history to local storage
   */
  saveChatHistory() {
    try {
      // Create a storage object with URL as key
      const storageKey = `webMcpChat_${this.pageMetadata.url.substring(0, 100)}`;

      // Create storage object
      const storageObj = {
        url: this.pageMetadata.url,
        title: this.pageMetadata.title,
        timestamp: new Date().toISOString(),
        chatHistory: this.chatHistory
      };

      // Save to local storage
      chrome.storage.local.set({ [storageKey]: storageObj }, () => {
        console.log('Web MCP chat history saved');
      });
    } catch (error) {
      console.error('Error saving Web MCP chat history:', error);
    }
  }

  /**
   * Load chat history from local storage
   */
  loadChatHistory() {
    try {
      // Create a storage key with URL as key
      const storageKey = `webMcpChat_${this.pageMetadata.url.substring(0, 100)}`;

      // Load from local storage
      chrome.storage.local.get([storageKey], (result) => {
        const savedChat = result[storageKey];

        if (savedChat && savedChat.chatHistory && savedChat.chatHistory.length > 0) {
          // Restore chat history
          this.chatHistory = savedChat.chatHistory;

          // Clear chat messages
          const chatMessages = document.getElementById('mcpChatMessages');
          if (chatMessages) {
            chatMessages.innerHTML = '';
          }

          // Add welcome message
          this.addAIMessage(`I've loaded our previous conversation about "${savedChat.title}". How can I help you with this page today? 📄`);

          // Add last 5 messages from history
          const recentMessages = this.chatHistory.slice(-10);
          recentMessages.forEach(msg => {
            if (msg.role === 'user') {
              this.addUserMessage(msg.content, false);
            } else if (msg.role === 'assistant') {
              this.addAIMessage(msg.content, false);
            }
          });
        }
      });
    } catch (error) {
      console.error('Error loading Web MCP chat history:', error);
    }
  }

  /**
   * Prepare context for AI based on page content and question
   * @param {string} question - The user's question
   * @returns {string} - The formatted context for AI
   */
  prepareContextForAI(question) {
    // Truncate page content if too large
    let truncatedContent = this.pageContent;
    if (truncatedContent.length > 15000) {
      truncatedContent = truncatedContent.substring(0, 15000) + '... [content truncated]';
    }

    // Include chat history for context
    let chatHistoryContext = '';
    if (this.chatHistory.length > 0) {
      // Get the last 10 messages or fewer if there aren't that many
      const recentMessages = this.chatHistory.slice(-10);
      chatHistoryContext = '\nRECENT CONVERSATION:\n';

      recentMessages.forEach(msg => {
        chatHistoryContext += `${msg.role.toUpperCase()}: ${msg.content}\n`;
      });

      chatHistoryContext += '\n';
    }

    // Add extraction method information
    let extractionInfo = '';
    if (this.pageMetadata.extractionMethod === 'crawling') {
      extractionInfo = `EXTRACTION METHOD: Web crawling
CRAWLED PAGES: ${this.pageMetadata.crawledPages || 1}
CONTENT SOURCE: This content includes information from multiple linked pages
`;
    } else {
      extractionInfo = `EXTRACTION METHOD: ${this.pageMetadata.extractionMethod || 'standard'}
CONTENT SOURCE: Current page only
`;
    }

    // Format the context
    return `
You are Web MCP (Master Control Program), an AI assistant that helps users understand web page content.
You are friendly, helpful, and include emojis in your responses to make the conversation more engaging.

PAGE TITLE: ${this.pageMetadata.title}
PAGE URL: ${this.pageMetadata.url}
${extractionInfo}
${chatHistoryContext}
PAGE CONTENT:
${truncatedContent}

USER QUESTION: ${question}

Please answer the user's question based on the page content provided above.
If the answer is not in the content, say so clearly but try to be helpful anyway.
Keep your response concise and focused on the question.
Use markdown formatting for better readability when appropriate.
If you reference specific parts of the page, quote them exactly using > blockquotes.
Include relevant emojis in your response to make it more engaging.

${this.pageMetadata.extractionMethod === 'crawling'
  ? 'The content includes information from multiple pages that were crawled. When referencing information, mention which page it came from if that context is available.'
  : 'If the user asks about something that\'s not on the page, politely explain that you can only analyze the current page content.'}
`;
  }

  /**
   * Add user message to chat
   * @param {string} message - The user message
   * @param {boolean} scroll - Whether to scroll to bottom (default: true)
   */
  addUserMessage(message, scroll = true) {
    const chatMessages = document.getElementById('mcpChatMessages');
    if (!chatMessages) return;

    const messageEl = document.createElement('div');
    messageEl.className = 'mcp-message user';
    messageEl.innerHTML = `
      <div class="message-avatar">
        <i class="fas fa-user"></i>
      </div>
      <div class="message-content">
        <p>${this.escapeHTML(message)}</p>
      </div>
    `;

    chatMessages.appendChild(messageEl);
    if (scroll) this.scrollToBottom();
  }

  /**
   * Add AI message to chat
   * @param {string} message - The AI message
   * @param {boolean} scroll - Whether to scroll to bottom (default: true)
   */
  addAIMessage(message, scroll = true) {
    const chatMessages = document.getElementById('mcpChatMessages');
    if (!chatMessages) return;

    const messageEl = document.createElement('div');
    messageEl.className = 'mcp-message ai';
    messageEl.innerHTML = `
      <div class="message-avatar">
        <img src="../images/icon.png" alt="Browzy AI">
      </div>
      <div class="message-content">
        <p>${this.formatMessage(message)}</p>
      </div>
    `;

    // Add copy button
    const copyBtn = document.createElement('button');
    copyBtn.className = 'copy-message-btn';
    copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
    copyBtn.title = 'Copy to clipboard';
    copyBtn.addEventListener('click', () => this.copyMessageToClipboard(message));

    messageEl.querySelector('.message-content').appendChild(copyBtn);

    chatMessages.appendChild(messageEl);
    if (scroll) this.scrollToBottom();
  }

  /**
   * Copy message to clipboard
   * @param {string} message - The message to copy
   */
  copyMessageToClipboard(message) {
    navigator.clipboard.writeText(message)
      .then(() => {
        this.uiManager.showStatus('Message copied to clipboard', false, 2000);
      })
      .catch(err => {
        console.error('Could not copy text: ', err);
        this.uiManager.showStatus('Failed to copy message', true, 2000);
      });
  }

  /**
   * Add AI thinking indicator
   */
  addAIThinkingMessage() {
    const chatMessages = document.getElementById('mcpChatMessages');
    if (!chatMessages) return;

    const messageEl = document.createElement('div');
    messageEl.className = 'mcp-message ai thinking';
    messageEl.innerHTML = `
      <div class="message-avatar">
        <img src="../images/icon.png" alt="Browzy AI">
      </div>
      <div class="message-content">
        <p><i class="fas fa-circle-notch fa-spin"></i> Analyzing page content...</p>
      </div>
    `;

    chatMessages.appendChild(messageEl);
    this.scrollToBottom();
  }

  /**
   * Remove thinking indicator
   */
  removeThinkingIndicator() {
    const thinkingEl = document.querySelector('.mcp-message.ai.thinking');
    if (thinkingEl) {
      thinkingEl.remove();
    }
  }

  /**
   * Format message with markdown and highlight
   * @param {string} message - The message to format
   * @returns {string} - Formatted message
   */
  formatMessage(message) {
    // Simple markdown-like formatting
    let formatted = this.escapeHTML(message);

    // Bold
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Code
    formatted = formatted.replace(/`(.*?)`/g, '<code>$1</code>');

    // Quotes
    formatted = formatted.replace(/>(.*?)(\n|$)/g, '<blockquote>$1</blockquote>');

    // Headers (h1, h2, h3)
    formatted = formatted.replace(/^# (.*?)$/gm, '<h3>$1</h3>');
    formatted = formatted.replace(/^## (.*?)$/gm, '<h4>$1</h4>');
    formatted = formatted.replace(/^### (.*?)$/gm, '<h5>$1</h5>');

    // Lists
    formatted = formatted.replace(/^- (.*?)$/gm, '• $1<br>');
    formatted = formatted.replace(/^\d+\. (.*?)$/gm, '$&<br>');

    // Links
    formatted = formatted.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>');

    // New lines
    formatted = formatted.replace(/\n/g, '<br>');

    return formatted;
  }

  /**
   * Escape HTML special characters
   * @param {string} text - The text to escape
   * @returns {string} - Escaped text
   */
  escapeHTML(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Scroll chat to bottom
   */
  scrollToBottom() {
    const chatMessages = document.getElementById('mcpChatMessages');
    if (chatMessages) {
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }
  }
}
