/* Webpage Access Dialog Styles */
.webpage-access-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.webpage-access-content {
  background-color: var(--surface-color);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.webpage-access-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.webpage-access-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
  display: flex;
  align-items: center;
}

.webpage-access-header h3 i {
  margin-right: 10px;
  color: var(--primary-color);
}

.webpage-access-close-btn {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.webpage-access-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.webpage-access-body {
  padding: 20px;
}

.webpage-access-body p {
  margin: 0 0 15px 0;
  color: var(--text-color);
}

.webpage-access-input-container {
  display: flex;
  margin-bottom: 15px;
}

.webpage-url-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 6px 0 0 6px;
  background-color: var(--input-bg-color);
  color: var(--text-color);
  font-family: var(--font-secondary);
  font-size: 14px;
}

.webpage-url-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.fetch-webpage-btn {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 6px 6px 0;
  cursor: pointer;
  font-family: var(--font-primary);
  font-weight: 500;
  transition: background-color 0.2s;
}

.fetch-webpage-btn:hover {
  background-color: #0095b3;
}

.webpage-access-options {
  margin-bottom: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.option-label {
  display: flex;
  align-items: center;
  color: var(--text-color);
  font-size: 14px;
  margin-right: 15px;
  cursor: pointer;
}

.option-label input {
  margin-right: 8px;
}

.webpage-access-status {
  font-size: 14px;
  color: var(--text-color);
  min-height: 20px;
}

.webpage-access-status.error {
  color: #ff5252;
}
