/**
 * Special fix for copy functionality in Brave browser
 */

// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('Brave copy fix loaded');

  // Add a global click handler for copy buttons
  document.body.addEventListener('click', function(event) {
    // Check if the clicked element is a copy button
    if (event.target.closest('.copy-button') || event.target.closest('.brave-copy-btn')) {
      // Get the button that was clicked
      const button = event.target.closest('.copy-button') || event.target.closest('.brave-copy-btn');
      const codeId = button.getAttribute('data-target');

      if (codeId) {
        const codeElement = document.getElementById(codeId);
        if (codeElement) {
          console.log('Brave copy fix: Found code element', codeId);

          // Get the code text
          const codeText = codeElement.textContent;

          // Create a simple dialog
          const dialog = document.createElement('div');
          dialog.style.position = 'fixed';
          dialog.style.top = '0';
          dialog.style.left = '0';
          dialog.style.width = '100%';
          dialog.style.height = '100%';
          dialog.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
          dialog.style.zIndex = '9999';
          dialog.style.display = 'flex';
          dialog.style.justifyContent = 'center';
          dialog.style.alignItems = 'center';
          dialog.style.fontFamily = 'Arial, sans-serif';

          // Create dialog content
          dialog.innerHTML = `
            <div style="background-color: #252836; padding: 20px; border-radius: 8px; width: 80%; max-width: 500px; text-align: center; position: relative;">
              <h3 style="color: white; margin-top: 0;">Copy Code</h3>
              <p style="color: white;">Click the button below to copy the code to your clipboard.</p>
              <button id="simpleCopyBtn" style="background-color: #4CAF50; color: white; border: none; padding: 12px 24px; border-radius: 4px; font-size: 16px; cursor: pointer; margin: 20px 0; font-weight: bold;">Copy & Close</button>
            </div>
          `;

          // Add to document
          document.body.appendChild(dialog);

          // Get the copy button
          const copyBtn = dialog.querySelector('#simpleCopyBtn');

          // Add click event to the copy button
          copyBtn.addEventListener('click', function() {
            // Create a visible textarea (important for Brave)
            const tempInput = document.createElement('textarea');
            tempInput.style.position = 'fixed';
            tempInput.style.top = '50%';
            tempInput.style.left = '50%';
            tempInput.style.transform = 'translate(-50%, -50%)';
            tempInput.style.width = '1px';
            tempInput.style.height = '1px';
            tempInput.style.padding = '0';
            tempInput.style.border = 'none';
            tempInput.style.outline = 'none';
            tempInput.style.boxShadow = 'none';
            tempInput.style.background = 'transparent';
            tempInput.style.opacity = '0';
            tempInput.value = codeText;
            document.body.appendChild(tempInput);

            // Select and copy
            tempInput.focus();
            tempInput.select();
            tempInput.setSelectionRange(0, codeText.length);

            try {
              // Try to copy using execCommand first
              let success = document.execCommand('copy');
              console.log('execCommand copy result:', success);

              // If that fails, try the Clipboard API
              if (!success) {
                console.log('Trying Clipboard API...');
                navigator.clipboard.writeText(codeText)
                  .then(() => {
                    console.log('Clipboard API success');
                    success = true;
                  })
                  .catch(err => {
                    console.error('Clipboard API failed:', err);
                  });
              }

              // Try a third method - create a visible button that triggers a copy
              if (!success) {
                console.log('Trying direct copy method...');
                const directCopyBtn = document.createElement('button');
                directCopyBtn.textContent = 'Click to Copy';
                directCopyBtn.style.position = 'fixed';
                directCopyBtn.style.top = '50%';
                directCopyBtn.style.left = '50%';
                directCopyBtn.style.transform = 'translate(-50%, -50%)';
                directCopyBtn.style.zIndex = '10000';
                directCopyBtn.style.padding = '20px';
                directCopyBtn.style.fontSize = '24px';
                directCopyBtn.style.backgroundColor = '#ff0000';
                directCopyBtn.style.color = 'white';
                directCopyBtn.style.border = 'none';
                directCopyBtn.style.borderRadius = '8px';
                directCopyBtn.style.cursor = 'pointer';

                directCopyBtn.onclick = function() {
                  const newInput = document.createElement('textarea');
                  newInput.value = codeText;
                  document.body.appendChild(newInput);
                  newInput.select();
                  document.execCommand('copy');
                  document.body.removeChild(newInput);
                  document.body.removeChild(directCopyBtn);
                };

                document.body.appendChild(directCopyBtn);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                  if (document.body.contains(directCopyBtn)) {
                    document.body.removeChild(directCopyBtn);
                  }
                }, 5000);
              }

              // Update the original button regardless (we can't reliably detect failure in Brave)
              button.innerHTML = '<i class="fas fa-check"></i> Copied!';
              setTimeout(() => {
                button.innerHTML = '<i class="fas fa-copy"></i> Copy';
              }, 2000);

              // Show status message
              const statusMessage = document.getElementById('statusMessage');
              if (statusMessage) {
                statusMessage.innerHTML = '<i class="fas fa-info-circle"></i> Code copied to clipboard!';
                statusMessage.style.display = 'block';
                setTimeout(() => {
                  statusMessage.innerHTML = '<i class="fas fa-info-circle"></i> Ready to help with anything';
                }, 3000);
              }
            } catch (err) {
              console.error('Copy failed:', err);
            }

            // Clean up
            document.body.removeChild(tempInput);
            document.body.removeChild(dialog);
          });

          // Prevent default action
          event.preventDefault();
          event.stopPropagation();
        }
      }
    }
  });
});
