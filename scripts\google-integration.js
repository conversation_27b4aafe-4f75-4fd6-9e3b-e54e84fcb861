'use strict';

/**
 * Google Workspace Integration class for connecting with Google APIs
 */
class GoogleIntegration {
  /**
   * Initialize the Google Workspace Integration
   * @param {StorageManager} storageManager - The storage manager instance
   */
  constructor(storageManager) {
    this.storageManager = storageManager;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.authWindowId = null;

    // Google API client ID for the extension
    this.clientId = ''; // You need to set your own Google client ID in the settings

    // Google API scopes
    this.scopes = [
      'https://www.googleapis.com/auth/documents',
      'https://www.googleapis.com/auth/spreadsheets',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/drive.file'
    ];

    // Initialize
    this.init();
  }

  /**
   * Initialize the integration
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // Load tokens from storage
      const tokens = await this.storageManager.get('google_tokens');
      if (tokens) {
        const parsed = JSON.parse(tokens);
        this.accessToken = parsed.accessToken;
        this.refreshToken = parsed.refreshToken;
        this.tokenExpiry = parsed.tokenExpiry;
        console.log('Google tokens loaded from storage');
      }
    } catch (error) {
      console.error('Error initializing Google integration:', error);
    }
  }

  /**
   * Check if authenticated with Google
   * @returns {Promise<boolean>} - Whether authenticated
   */
  async isAuthenticated() {
    try {
      if (!this.accessToken || !this.refreshToken) {
        return false;
      }

      // Check if token is expired
      if (this.tokenExpiry && new Date(this.tokenExpiry) <= new Date()) {
        // Try to refresh the token
        const refreshed = await this.refreshAccessToken();
        if (!refreshed) {
          return false;
        }
      }

      // Verify the token by making a test request
      const response = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Error checking Google authentication:', error);
      return false;
    }
  }

  /**
   * Authenticate with Google
   * @returns {Promise<boolean>} - Whether authentication was successful
   */
  async authenticate() {
    try {
      // For Google, we need to use OAuth
      const authUrl = `https://accounts.google.com/o/oauth2/auth?client_id=${this.clientId}&redirect_uri=${encodeURIComponent(chrome.runtime.getURL('popup/auth-callback.html'))}&response_type=code&scope=${encodeURIComponent(this.scopes.join(' '))}&access_type=offline&prompt=consent`;

      // Open the auth window
      const authWindow = await chrome.windows.create({
        url: authUrl,
        type: 'popup',
        width: 800,
        height: 600
      });

      this.authWindowId = authWindow.id;

      // Wait for the auth callback
      return new Promise((resolve, reject) => {
        const handleAuthCallback = (message, sender, sendResponse) => {
          if (message.action === 'google_auth_callback' && message.code) {
            // Exchange the code for tokens
            this.exchangeCodeForTokens(message.code)
              .then(tokens => {
                // Save the tokens
                this.accessToken = tokens.access_token;
                this.refreshToken = tokens.refresh_token;
                this.tokenExpiry = new Date(Date.now() + tokens.expires_in * 1000).toISOString();

                // Save to storage
                this.storageManager.set('google_tokens', JSON.stringify({
                  accessToken: this.accessToken,
                  refreshToken: this.refreshToken,
                  tokenExpiry: this.tokenExpiry
                }));

                // Close the auth window
                if (this.authWindowId) {
                  chrome.windows.remove(this.authWindowId);
                  this.authWindowId = null;
                }

                // Remove the listener
                chrome.runtime.onMessage.removeListener(handleAuthCallback);

                resolve(true);
              })
              .catch(error => {
                // Close the auth window
                if (this.authWindowId) {
                  chrome.windows.remove(this.authWindowId);
                  this.authWindowId = null;
                }

                // Remove the listener
                chrome.runtime.onMessage.removeListener(handleAuthCallback);

                reject(error);
              });
          } else if (message.action === 'google_auth_error') {
            // Close the auth window
            if (this.authWindowId) {
              chrome.windows.remove(this.authWindowId);
              this.authWindowId = null;
            }

            // Remove the listener
            chrome.runtime.onMessage.removeListener(handleAuthCallback);

            reject(new Error(message.error || 'Authentication failed'));
          }
        };

        // Listen for the auth callback
        chrome.runtime.onMessage.addListener(handleAuthCallback);

        // Set a timeout
        setTimeout(() => {
          // Remove the listener
          chrome.runtime.onMessage.removeListener(handleAuthCallback);

          // Close the auth window
          if (this.authWindowId) {
            chrome.windows.remove(this.authWindowId);
            this.authWindowId = null;
          }

          reject(new Error('Authentication timed out'));
        }, 300000); // 5 minutes
      });
    } catch (error) {
      console.error('Error authenticating with Google:', error);
      throw error;
    }
  }

  /**
   * Exchange an authorization code for access and refresh tokens
   * @param {string} code - The authorization code
   * @returns {Promise<Object>} - The tokens
   */
  async exchangeCodeForTokens(code) {
    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          code: code,
          client_id: this.clientId,
          redirect_uri: chrome.runtime.getURL('popup/auth-callback.html'),
          grant_type: 'authorization_code'
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to exchange code for tokens: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error exchanging code for tokens:', error);
      throw error;
    }
  }

  /**
   * Refresh the access token
   * @returns {Promise<boolean>} - Whether the token was refreshed successfully
   */
  async refreshAccessToken() {
    try {
      if (!this.refreshToken) {
        return false;
      }

      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          refresh_token: this.refreshToken,
          client_id: this.clientId,
          grant_type: 'refresh_token'
        })
      });

      if (!response.ok) {
        return false;
      }

      const tokens = await response.json();

      // Update the access token and expiry
      this.accessToken = tokens.access_token;
      this.tokenExpiry = new Date(Date.now() + tokens.expires_in * 1000).toISOString();

      // Save to storage
      await this.storageManager.set('google_tokens', JSON.stringify({
        accessToken: this.accessToken,
        refreshToken: this.refreshToken,
        tokenExpiry: this.tokenExpiry
      }));

      return true;
    } catch (error) {
      console.error('Error refreshing access token:', error);
      return false;
    }
  }

  /**
   * Disconnect from Google
   * @returns {Promise<boolean>} - Whether disconnection was successful
   */
  async disconnect() {
    try {
      // Revoke the token if possible
      if (this.accessToken) {
        try {
          await fetch(`https://oauth2.googleapis.com/revoke?token=${this.accessToken}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          });
        } catch (error) {
          console.error('Error revoking token:', error);
        }
      }

      // Clear the tokens
      this.accessToken = null;
      this.refreshToken = null;
      this.tokenExpiry = null;

      // Remove from storage
      await this.storageManager.remove('google_tokens');

      return true;
    } catch (error) {
      console.error('Error disconnecting from Google:', error);
      throw error;
    }
  }

  /**
   * Make a request to a Google API
   * @param {string} url - The API URL
   * @param {string} method - The HTTP method
   * @param {Object} data - The request data
   * @returns {Promise<Object>} - The response data
   */
  async makeRequest(url, method = 'GET', data = null) {
    try {
      // Check if authenticated
      if (!await this.isAuthenticated()) {
        throw new Error('Not authenticated with Google');
      }

      const options = {
        method: method,
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      };

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`Google API error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error making Google API request:', error);
      throw error;
    }
  }

  /**
   * Create a new item in Google Workspace
   * @param {string} itemType - The type of item to create (document, spreadsheet, event)
   * @param {Object} content - The content of the item
   * @param {Object} options - Additional options for the item
   * @returns {Promise<Object>} - The created item
   */
  async createItem(itemType, content, options = {}) {
    try {
      switch (itemType.toLowerCase()) {
        case 'document':
          return await this.createDocument(content, options);
        case 'spreadsheet':
          return await this.createSpreadsheet(content, options);
        case 'event':
          return await this.createCalendarEvent(content, options);
        default:
          throw new Error(`Unknown item type: ${itemType}`);
      }
    } catch (error) {
      console.error(`Error creating ${itemType} in Google Workspace:`, error);
      throw error;
    }
  }

  /**
   * Create a new Google Document
   * @param {Object} content - The content of the document
   * @param {Object} options - Additional options for the document
   * @returns {Promise<Object>} - The created document
   */
  async createDocument(content, options = {}) {
    try {
      // Create an empty document
      const document = await this.makeRequest('https://docs.googleapis.com/v1/documents', 'POST', {
        title: content.title || 'Untitled Document'
      });

      // If content is provided, insert it into the document
      if (content.content) {
        await this.makeRequest(`https://docs.googleapis.com/v1/documents/${document.documentId}:batchUpdate`, 'POST', {
          requests: [
            {
              insertText: {
                location: {
                  index: 1
                },
                text: content.content
              }
            }
          ]
        });
      }

      return document;
    } catch (error) {
      console.error('Error creating Google Document:', error);
      throw error;
    }
  }

  /**
   * Create a new Google Spreadsheet
   * @param {Object} content - The content of the spreadsheet
   * @param {Object} options - Additional options for the spreadsheet
   * @returns {Promise<Object>} - The created spreadsheet
   */
  async createSpreadsheet(content, options = {}) {
    try {
      // Prepare the spreadsheet data
      const spreadsheetData = {
        properties: {
          title: content.title || 'Untitled Spreadsheet'
        },
        sheets: [
          {
            properties: {
              title: 'Sheet1'
            }
          }
        ]
      };

      // Create the spreadsheet
      const spreadsheet = await this.makeRequest('https://sheets.googleapis.com/v4/spreadsheets', 'POST', spreadsheetData);

      // If content is provided, update the spreadsheet
      if (content.data) {
        await this.makeRequest(`https://sheets.googleapis.com/v4/spreadsheets/${spreadsheet.spreadsheetId}/values/Sheet1!A1:Z1000:append?valueInputOption=USER_ENTERED`, 'POST', {
          values: content.data
        });
      }

      return spreadsheet;
    } catch (error) {
      console.error('Error creating Google Spreadsheet:', error);
      throw error;
    }
  }

  /**
   * Create a new Google Calendar event
   * @param {Object} content - The content of the event
   * @param {Object} options - Additional options for the event
   * @returns {Promise<Object>} - The created event
   */
  async createCalendarEvent(content, options = {}) {
    try {
      // Prepare the event data
      const eventData = {
        summary: content.summary || 'Untitled Event',
        description: content.description || '',
        start: content.start || {
          dateTime: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
        },
        end: content.end || {
          dateTime: new Date(Date.now() + 90000000).toISOString(), // Tomorrow + 1 hour
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
        },
        attendees: content.attendees || [],
        reminders: content.reminders || {
          useDefault: true
        }
      };

      // Create the event
      const event = await this.makeRequest('https://www.googleapis.com/calendar/v3/calendars/primary/events', 'POST', eventData);

      return event;
    } catch (error) {
      console.error('Error creating Google Calendar event:', error);
      throw error;
    }
  }

  /**
   * Get items from Google Workspace
   * @param {string} itemType - The type of items to get (documents, spreadsheets, events)
   * @param {Object} options - Options for filtering and sorting items
   * @returns {Promise<Array>} - The retrieved items
   */
  async getItems(itemType, options = {}) {
    try {
      switch (itemType.toLowerCase()) {
        case 'documents':
          return await this.getDocuments(options);
        case 'spreadsheets':
          return await this.getSpreadsheets(options);
        case 'events':
          return await this.getCalendarEvents(options);
        default:
          throw new Error(`Unknown item type: ${itemType}`);
      }
    } catch (error) {
      console.error(`Error getting ${itemType} from Google Workspace:`, error);
      throw error;
    }
  }

  /**
   * Get Google Documents
   * @param {Object} options - Options for filtering and sorting documents
   * @returns {Promise<Array>} - The retrieved documents
   */
  async getDocuments(options = {}) {
    try {
      // Use the Drive API to search for Google Documents
      const query = "mimeType='application/vnd.google-apps.document'";

      const response = await this.makeRequest(`https://www.googleapis.com/drive/v3/files?q=${encodeURIComponent(query)}&fields=files(id,name,createdTime,modifiedTime)`);

      return response.files;
    } catch (error) {
      console.error('Error getting Google Documents:', error);
      throw error;
    }
  }

  /**
   * Get Google Spreadsheets
   * @param {Object} options - Options for filtering and sorting spreadsheets
   * @returns {Promise<Array>} - The retrieved spreadsheets
   */
  async getSpreadsheets(options = {}) {
    try {
      // Use the Drive API to search for Google Spreadsheets
      const query = "mimeType='application/vnd.google-apps.spreadsheet'";

      const response = await this.makeRequest(`https://www.googleapis.com/drive/v3/files?q=${encodeURIComponent(query)}&fields=files(id,name,createdTime,modifiedTime)`);

      return response.files;
    } catch (error) {
      console.error('Error getting Google Spreadsheets:', error);
      throw error;
    }
  }

  /**
   * Get Google Calendar events
   * @param {Object} options - Options for filtering and sorting events
   * @returns {Promise<Array>} - The retrieved events
   */
  async getCalendarEvents(options = {}) {
    try {
      // Prepare the query parameters
      const params = new URLSearchParams();

      if (options.timeMin) {
        params.append('timeMin', options.timeMin);
      } else {
        params.append('timeMin', new Date().toISOString());
      }

      if (options.timeMax) {
        params.append('timeMax', options.timeMax);
      }

      if (options.maxResults) {
        params.append('maxResults', options.maxResults);
      } else {
        params.append('maxResults', '10');
      }

      params.append('singleEvents', 'true');
      params.append('orderBy', 'startTime');

      // Get the events
      const response = await this.makeRequest(`https://www.googleapis.com/calendar/v3/calendars/primary/events?${params.toString()}`);

      return response.items;
    } catch (error) {
      console.error('Error getting Google Calendar events:', error);
      throw error;
    }
  }
}
