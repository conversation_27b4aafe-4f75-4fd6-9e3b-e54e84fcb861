'use strict';

console.log('Video content script loaded');

/**
 * Extract information about videos on the page
 * @returns {Object} - Object containing video information
 */
function getVideoInfo() {
  try {
    const videos = [];
    const videoElements = document.querySelectorAll('video');

    videoElements.forEach((video, index) => {
      try {
        // Get basic video information
        const videoInfo = {
          index,
          src: video.src || video.currentSrc || '',
          duration: video.duration ? formatDuration(video.duration) : 'Unknown',
          durationSeconds: video.duration || 0,
          width: video.videoWidth || video.width || 0,
          height: video.videoHeight || video.height || 0,
          type: video.type || '',
          controls: video.controls,
          autoplay: video.autoplay,
          muted: video.muted,
          loop: video.loop,
          poster: video.poster || '',
          currentTime: video.currentTime || 0,
          currentTimeFormatted: formatDuration(video.currentTime || 0),
          playbackRate: video.playbackRate || 1,
          timestamps: extractVideoTimestamps(video),
          chapters: extractVideoChapters(),
          progress: video.duration ? Math.round((video.currentTime / video.duration) * 100) : 0
        };

        // Try to get video source from source elements
        if (!videoInfo.src) {
          const sources = video.querySelectorAll('source');
          if (sources.length > 0) {
            videoInfo.src = sources[0].src || '';
            videoInfo.type = sources[0].type || '';
          }
        }

        // Try to get video title
        videoInfo.title = getVideoTitle(video);

        // Set up event listener to track timestamp changes
        setupTimestampTracking(video, index);

        // Add to videos array
        videos.push(videoInfo);
      } catch (videoError) {
        console.error(`Error extracting info for video ${index}:`, videoError);
      }
    });

    // Check for iframe embedded videos
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach((iframe, index) => {
      try {
        const src = iframe.src || '';
        if (isVideoEmbed(src)) {
          videos.push({
            index: videos.length,
            src,
            type: 'iframe',
            width: iframe.width || 0,
            height: iframe.height || 0,
            title: iframe.title || getVideoTitleFromIframe(iframe),
            isEmbed: true,
            embedType: getEmbedType(src)
          });
        }
      } catch (iframeError) {
        console.error(`Error extracting info for iframe ${index}:`, iframeError);
      }
    });

    return {
      success: true,
      videos,
      count: videos.length,
      pageUrl: window.location.href,
      pageTitle: document.title
    };
  } catch (error) {
    console.error('Error getting video info:', error);
    return {
      success: false,
      error: error.message,
      videos: [],
      count: 0
    };
  }
}

/**
 * Check if there are any video elements on the page
 * @returns {Object} - Object containing check result
 */
function checkForVideoElements() {
  try {
    const videoElements = document.querySelectorAll('video');
    const iframes = document.querySelectorAll('iframe');

    let videoIframes = 0;
    iframes.forEach(iframe => {
      const src = iframe.src || '';
      if (isVideoEmbed(src)) {
        videoIframes++;
      }
    });

    return {
      success: true,
      hasVideos: videoElements.length > 0 || videoIframes > 0,
      videoCount: videoElements.length,
      videoIframeCount: videoIframes
    };
  } catch (error) {
    console.error('Error checking for video elements:', error);
    return {
      success: false,
      error: error.message,
      hasVideos: false
    };
  }
}

/**
 * Format video duration in MM:SS or HH:MM:SS format
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration
 */
function formatDuration(seconds) {
  if (!seconds || isNaN(seconds)) return 'Unknown';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * Try to get the title of a video
 * @param {HTMLVideoElement} video - Video element
 * @returns {string} - Video title or empty string
 */
function getVideoTitle(video) {
  // Try various ways to get the video title

  // 1. Check for title attribute
  if (video.title) return video.title;

  // 2. Check for aria-label
  if (video.getAttribute('aria-label')) return video.getAttribute('aria-label');

  // 3. Check for parent elements with title or heading
  let parent = video.parentElement;
  let depth = 0;
  while (parent && depth < 3) {
    // Check for heading elements
    const heading = parent.querySelector('h1, h2, h3, h4, h5, h6');
    if (heading && heading.textContent.trim()) {
      return heading.textContent.trim();
    }

    // Check for title attribute
    if (parent.title) return parent.title;

    // Move up the DOM
    parent = parent.parentElement;
    depth++;
  }

  return '';
}

/**
 * Try to get the title of a video from an iframe
 * @param {HTMLIFrameElement} iframe - Iframe element
 * @returns {string} - Video title or empty string
 */
function getVideoTitleFromIframe(iframe) {
  // Try various ways to get the iframe video title

  // 1. Check for title attribute
  if (iframe.title) return iframe.title;

  // 2. Check for aria-label
  if (iframe.getAttribute('aria-label')) return iframe.getAttribute('aria-label');

  // 3. Check for parent elements with title or heading
  let parent = iframe.parentElement;
  let depth = 0;
  while (parent && depth < 3) {
    // Check for heading elements
    const heading = parent.querySelector('h1, h2, h3, h4, h5, h6');
    if (heading && heading.textContent.trim()) {
      return heading.textContent.trim();
    }

    // Check for title attribute
    if (parent.title) return parent.title;

    // Move up the DOM
    parent = parent.parentElement;
    depth++;
  }

  return '';
}

/**
 * Check if an iframe src is a video embed
 * @param {string} src - Iframe src URL
 * @returns {boolean} - True if it's a video embed
 */
function isVideoEmbed(src) {
  if (!src) return false;

  const videoEmbedPatterns = [
    'youtube.com/embed',
    'youtube-nocookie.com/embed',
    'player.vimeo.com',
    'dailymotion.com/embed',
    'facebook.com/plugins/video',
    'twitch.tv/embed',
    'player.twitch.tv',
    'tiktok.com/embed',
    'instagram.com/p/',
    'streamable.com/e/',
    'ted.com/talks',
    'brightcove.net',
    'jwplayer.com',
    'vidyard.com',
    'wistia.com',
    'loom.com/embed',
    'videopress.com/embed',
    'rumble.com/embed',
    'vevo.com',
    'hulu.com/embed',
    'video'
  ];

  return videoEmbedPatterns.some(pattern => src.includes(pattern));
}

/**
 * Get the type of video embed
 * @param {string} src - Iframe src URL
 * @returns {string} - Embed type (youtube, vimeo, etc.)
 */
function getEmbedType(src) {
  if (!src) return 'unknown';

  if (src.includes('youtube.com') || src.includes('youtube-nocookie.com')) {
    return 'youtube';
  } else if (src.includes('vimeo.com')) {
    return 'vimeo';
  } else if (src.includes('dailymotion.com')) {
    return 'dailymotion';
  } else if (src.includes('facebook.com')) {
    return 'facebook';
  } else if (src.includes('twitch.tv')) {
    return 'twitch';
  } else if (src.includes('tiktok.com')) {
    return 'tiktok';
  } else if (src.includes('instagram.com')) {
    return 'instagram';
  } else {
    return 'other';
  }
}

// Listen for messages from popup or background script
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  try {
    if (!message || !message.action) {
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }

    // Handle getVideoInfo action
    if (message.action === 'getVideoInfo') {
      try {
        const videoInfo = getVideoInfo();
        sendResponse(videoInfo);
      } catch (error) {
        console.error('Error in getVideoInfo handler:', error);
        sendResponse({
          success: false,
          error: error.message,
          videos: [],
          count: 0
        });
      }
      return true;
    }

    // Handle checkForVideoElements action
    if (message.action === 'checkForVideoElements') {
      try {
        const result = checkForVideoElements();
        sendResponse(result);
      } catch (error) {
        console.error('Error in checkForVideoElements handler:', error);
        sendResponse({
          success: false,
          error: error.message,
          hasVideos: false
        });
      }
      return true;
    }

    // If we get here, it's an unknown action
    sendResponse({ success: false, error: 'Unknown action' });
    return true;
  } catch (error) {
    console.error('Critical error in message listener:', error);
    sendResponse({ success: false, error: 'Internal extension error' });
    return true;
  }
});

/**
 * Extract timestamps from video description or surrounding content
 * @param {HTMLVideoElement} video - Video element
 * @returns {Array} - Array of timestamp objects
 */
function extractVideoTimestamps(video) {
  try {
    const timestamps = [];

    // Check if we're on YouTube
    const isYouTube = window.location.href.includes('youtube.com') || window.location.href.includes('youtu.be');

    if (isYouTube) {
      // Extract timestamps from YouTube description
      const descriptionElement = document.querySelector('#description, #description-text, .ytd-video-secondary-info-renderer #description');
      if (descriptionElement) {
        const descriptionText = descriptionElement.innerText;

        // Look for timestamp patterns like "1:23" or "01:23" or "1:23:45"
        const timestampRegex = /(?:^|\s)((?:\d{1,2}:)?\d{1,2}:\d{2})(?:\s|$)/gm;
        let match;

        while ((match = timestampRegex.exec(descriptionText)) !== null) {
          const timestampText = match[1];
          const timestampSeconds = convertTimestampToSeconds(timestampText);

          // Get the context (text around the timestamp)
          const startIndex = Math.max(0, match.index - 40);
          const endIndex = Math.min(descriptionText.length, match.index + timestampText.length + 40);
          const context = descriptionText.substring(startIndex, endIndex).trim();

          timestamps.push({
            text: timestampText,
            seconds: timestampSeconds,
            context: context
          });
        }
      }
    } else {
      // For non-YouTube videos, look for timestamps in surrounding text
      const parentElement = video.parentElement;
      if (parentElement) {
        // Look for elements that might contain timestamps
        const potentialTimestampContainers = parentElement.querySelectorAll('div, span, p, ul, ol, li');

        potentialTimestampContainers.forEach(container => {
          const text = container.innerText;

          // Look for timestamp patterns
          const timestampRegex = /(?:^|\s)((?:\d{1,2}:)?\d{1,2}:\d{2})(?:\s|$)/gm;
          let match;

          while ((match = timestampRegex.exec(text)) !== null) {
            const timestampText = match[1];
            const timestampSeconds = convertTimestampToSeconds(timestampText);

            // Get the context (text around the timestamp)
            const startIndex = Math.max(0, match.index - 30);
            const endIndex = Math.min(text.length, match.index + timestampText.length + 30);
            const context = text.substring(startIndex, endIndex).trim();

            timestamps.push({
              text: timestampText,
              seconds: timestampSeconds,
              context: context
            });
          }
        });
      }
    }

    return timestamps;
  } catch (error) {
    console.error('Error extracting video timestamps:', error);
    return [];
  }
}

/**
 * Extract video chapters if available
 * @returns {Array} - Array of chapter objects
 */
function extractVideoChapters() {
  try {
    const chapters = [];

    // Check if we're on YouTube
    const isYouTube = window.location.href.includes('youtube.com') || window.location.href.includes('youtu.be');

    if (isYouTube) {
      // YouTube chapters are in the description or in chapter markers
      const chapterElements = document.querySelectorAll('.ytp-chapter-title-content');

      if (chapterElements && chapterElements.length > 0) {
        // Extract chapters from chapter markers
        chapterElements.forEach(element => {
          const chapterText = element.innerText.trim();
          if (chapterText) {
            // Try to extract timestamp from chapter text
            const timestampMatch = chapterText.match(/^((?:\d{1,2}:)?\d{1,2}:\d{2})/);
            if (timestampMatch) {
              const timestampText = timestampMatch[1];
              const timestampSeconds = convertTimestampToSeconds(timestampText);
              const title = chapterText.replace(timestampMatch[0], '').trim();

              chapters.push({
                title: title || 'Chapter',
                time: timestampText,
                seconds: timestampSeconds
              });
            }
          }
        });
      } else {
        // Try to extract chapters from description
        const descriptionElement = document.querySelector('#description, #description-text, .ytd-video-secondary-info-renderer #description');
        if (descriptionElement) {
          const descriptionText = descriptionElement.innerText;
          const lines = descriptionText.split('\n');

          // Look for patterns like "0:00 Introduction" or "00:00 - Introduction"
          const chapterRegex = /^((?:\d{1,2}:)?\d{1,2}:\d{2})(?:\s*[-:]\s*|\s+)(.+)$/;

          lines.forEach(line => {
            const match = line.match(chapterRegex);
            if (match) {
              const timestampText = match[1];
              const title = match[2].trim();
              const timestampSeconds = convertTimestampToSeconds(timestampText);

              chapters.push({
                title: title,
                time: timestampText,
                seconds: timestampSeconds
              });
            }
          });
        }
      }
    }

    return chapters;
  } catch (error) {
    console.error('Error extracting video chapters:', error);
    return [];
  }
}

/**
 * Convert timestamp text to seconds
 * @param {string} timestamp - Timestamp text (e.g., "1:23" or "1:23:45")
 * @returns {number} - Seconds
 */
function convertTimestampToSeconds(timestamp) {
  try {
    const parts = timestamp.split(':').map(part => parseInt(part, 10));

    if (parts.length === 3) {
      // Format: hours:minutes:seconds
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) {
      // Format: minutes:seconds
      return parts[0] * 60 + parts[1];
    } else {
      // Invalid format
      return 0;
    }
  } catch (error) {
    console.error('Error converting timestamp to seconds:', error);
    return 0;
  }
}

/**
 * Set up event listeners to track video timestamp changes
 * @param {HTMLVideoElement} video - Video element
 * @param {number} videoIndex - Index of the video
 */
function setupTimestampTracking(video, videoIndex) {
  try {
    // Store the last reported time to avoid excessive updates
    let lastReportedTime = video.currentTime || 0;

    // Listen for timeupdate events
    video.addEventListener('timeupdate', () => {
      // Only report significant changes (more than 5 seconds)
      if (Math.abs(video.currentTime - lastReportedTime) >= 5) {
        lastReportedTime = video.currentTime;

        // Send message to background script with updated timestamp
        chrome.runtime.sendMessage({
          action: 'videoTimestampUpdate',
          videoIndex: videoIndex,
          currentTime: video.currentTime,
          currentTimeFormatted: formatDuration(video.currentTime),
          progress: video.duration ? Math.round((video.currentTime / video.duration) * 100) : 0
        }).catch(err => console.error('Error sending timestamp update:', err));
      }
    });

    // Listen for seeking events
    video.addEventListener('seeking', () => {
      // Report when user manually seeks
      lastReportedTime = video.currentTime;

      // Send message to background script with updated timestamp
      chrome.runtime.sendMessage({
        action: 'videoTimestampUpdate',
        videoIndex: videoIndex,
        currentTime: video.currentTime,
        currentTimeFormatted: formatDuration(video.currentTime),
        progress: video.duration ? Math.round((video.currentTime / video.duration) * 100) : 0,
        isSeeking: true
      }).catch(err => console.error('Error sending seeking update:', err));
    });
  } catch (error) {
    console.error('Error setting up timestamp tracking:', error);
  }
}

console.log('Video content script initialization complete');
