'use strict';

/**
 * Enhanced Website NLP Manager
 * Provides advanced natural language understanding for website detection
 * Uses the top 100 most visited websites data for better platform detection
 */
class EnhancedWebsiteNLPManager {
  /**
   * Create a new Enhanced Website NLP Manager
   * @param {APIManager} apiManager - The API Manager instance
   */
  constructor(apiManager) {
    this.apiManager = apiManager;

    // Load the top websites data
    this.topWebsites = window.TOP_WEBSITES || [];

    // Create a map of domains to website objects for quick lookup
    this.domainMap = {};
    this.topWebsites.forEach(website => {
      this.domainMap[website.domain] = website;
    });

    // Create a map of categories to websites for category-based searches
    this.categoryMap = {};
    this.topWebsites.forEach(website => {
      if (!this.categoryMap[website.category]) {
        this.categoryMap[website.category] = [];
      }
      this.categoryMap[website.category].push(website);
    });

    // Initialize intent patterns with expanded keywords
    this.intentPatterns = {
      browse: ['browse', 'go to', 'visit', 'open', 'navigate to', 'take me to', 'show me', 'website', 'check out', 'access', 'view', 'load', 'bring up', 'display', 'get to', 'head to', 'direct me to', 'lead me to'],
      search: ['search', 'find', 'look for', 'where is', 'can you find', 'search for', 'find me', 'look up', 'locate', 'discover', 'hunt for', 'seek', 'query for', 'google', 'research', 'investigate', 'explore', 'scout for', 'track down'],
      shop: ['buy', 'purchase', 'shop', 'order', 'shopping', 'price', 'deal', 'discount', 'product', 'item', 'merchandise', 'goods', 'sale', 'bargain', 'cheap', 'affordable', 'cost', 'retail', 'marketplace', 'store', 'shop for', 'get me', 'want to buy', 'looking to purchase'],
      watch: ['watch', 'video', 'stream', 'streaming', 'movie', 'show', 'episode', 'film', 'youtube', 'clip', 'footage', 'recording', 'broadcast', 'live', 'trailer', 'series', 'documentary', 'vlog', 'tutorial', 'review', 'gameplay', 'highlights', 'see a video', 'watch a video about', 'streaming video', 'play video'],
      listen: ['listen', 'music', 'song', 'album', 'artist', 'playlist', 'track', 'audio', 'spotify', 'podcast', 'radio', 'tune', 'melody', 'sound', 'band', 'singer', 'concert', 'mp3', 'streaming music', 'hear', 'play music', 'play song', 'play a track by'],
      social: ['post', 'profile', 'friend', 'follow', 'follower', 'social', 'share', 'comment', 'like', 'tweet', 'status', 'update', 'feed', 'timeline', 'network', 'connection', 'contact', 'community', 'group', 'page', 'account', 'social media', 'online presence'],
      news: ['news', 'article', 'report', 'headline', 'latest', 'breaking', 'update', 'story', 'coverage', 'press', 'media', 'journalist', 'current events', 'today\'s news', 'recent developments', 'bulletin', 'broadcast', 'publication', 'newspaper', 'magazine', 'blog post'],
      weather: ['weather', 'forecast', 'temperature', 'rain', 'snow', 'sunny', 'cloudy', 'storm', 'climate', 'humidity', 'precipitation', 'meteorology', 'conditions', 'atmosphere', 'barometer', 'thermometer', 'wind', 'cold', 'hot', 'warm', 'chilly', 'freezing', 'weather report', 'weather update'],
      travel: ['travel', 'flight', 'hotel', 'booking', 'reservation', 'trip', 'vacation', 'destination', 'accommodation', 'tourism', 'tourist', 'journey', 'tour', 'visit', 'holiday', 'getaway', 'expedition', 'voyage', 'cruise', 'resort', 'lodge', 'motel', 'hostel', 'airbnb', 'plane ticket', 'airline'],
      job: ['job', 'career', 'employment', 'hiring', 'resume', 'cv', 'interview', 'salary', 'position', 'work', 'occupation', 'profession', 'vocation', 'role', 'opening', 'vacancy', 'opportunity', 'application', 'recruit', 'headhunter', 'employer', 'employee', 'staff', 'personnel', 'human resources', 'job search', 'job hunting'],
      reference: ['wikipedia', 'wiki', 'encyclopedia', 'definition', 'meaning', 'what is', 'who is', 'where is', 'when was', 'why is', 'how to', 'information about', 'facts about', 'history of', 'biography of', 'research', 'learn about', 'explain', 'clarify', 'describe', 'elaborate on', 'tell me about', 'give me info on', 'details about', 'specifics of', 'knowledge about', 'data on', 'background of', 'origin of', 'source of', 'reference for']
    };

    // Expanded conversational patterns for better natural language understanding
    this.conversationalPatterns = {
      browse: [
        'i want to visit',
        'i want to go to',
        'i need to check',
        'can you take me to',
        'open the website',
        'navigate to',
        'i want to see the website',
        'show me the site',
        'i want to browse',
        'i need to access',
        'could you open',
        'please take me to',
        'i would like to visit',
        'i wish to see',
        'i need to look at',
        'can i see',
        'let me check',
        'help me access',
        'direct me to',
        'i want to check out',
        'please show me',
        'can you help me find',
        'i need to get to',
        'take me to the website for',
        'i want to view',
        'let me see',
        'i need to view',
        'please navigate to',
        'can we go to',
        'i would like to see'
      ],
      bored: [
        'i am bored',
        'i\'m bored',
        'nothing to do',
        'entertain me',
        'something fun',
        'something interesting',
        'waste time',
        'kill time',
        'i need entertainment',
        'i need something to do',
        'i have free time',
        'i\'m feeling bored',
        'looking for entertainment',
        'need to pass time',
        'show me something interesting',
        'i need a distraction',
        'i\'m tired of doing nothing',
        'suggest something fun',
        'what can i do for fun',
        'i need amusement',
        'i want to be entertained',
        'i\'m looking for something to do',
        'help me find something interesting',
        'i\'m so bored right now',
        'i need to kill some time',
        'i have nothing to do'
      ],
      question: [
        'what is',
        'who is',
        'where is',
        'when was',
        'why is',
        'how do',
        'how can',
        'how does',
        'can you tell me',
        'i want to know',
        'tell me about',
        'explain',
        'describe',
        'define',
        'what does',
        'what are',
        'who are',
        'where can',
        'when is',
        'why does',
        'how would',
        'could you explain',
        'i need information about',
        'i\'m curious about',
        'i wonder',
        'do you know',
        'can you help me understand'
      ],
      shopping: [
        'i want to buy',
        'i need to purchase',
        'i\'m looking to buy',
        'where can i buy',
        'i want to shop for',
        'i need to find',
        'i\'m shopping for',
        'i want to get',
        'i need to order',
        'help me find',
        'i\'m looking for',
        'i want to purchase',
        'i need to get',
        'where can i purchase',
        'i want to find',
        'i need a new',
        'i\'m in the market for',
        'i want to compare prices for',
        'i need to compare',
        'best place to buy',
        'cheapest place to get',
        'i want the best deal on',
        'where should i buy',
        'i need recommendations for buying'
      ],
      video: [
        'i want to watch',
        'show me videos of',
        'i need to see a video about',
        'find me videos on',
        'i want to see',
        'show me how to',
        'i want to learn how to',
        'tutorial for',
        'videos about',
        'show me a movie about',
        'i want to stream',
        'help me find videos of',
        'i\'m looking for videos on',
        'i want to find videos about',
        'show me some videos of',
        'i need video tutorials on',
        'i want to watch a documentary about',
        'find me a good video on',
        'i want to see footage of',
        'show me clips of',
        'i want to watch reviews of',
        'find me trailers for'
      ],
      music: [
        'i want to listen to',
        'play music by',
        'i want to hear',
        'find songs by',
        'play me some',
        'i want to listen to songs from',
        'find music like',
        'play the song',
        'i need a playlist for',
        'find me music for',
        'i want to discover new music',
        'recommend me songs like',
        'i want to find similar artists to',
        'play the latest album by',
        'i want to hear the top songs from',
        'find me the soundtrack from',
        'i want to listen to the radio station',
        'play me something relaxing',
        'i want to hear upbeat music',
        'find me workout music'
      ]
    };

    // Add common synonyms for better matching
    this.synonyms = {
      'youtube': ['yt', 'tube', 'video site', 'video platform', 'video sharing'],
      'facebook': ['fb', 'meta', 'social network', 'facebook app'],
      'instagram': ['ig', 'insta', 'gram', 'instagram app'],
      'twitter': ['tweet', 'x', 'twitter app', 'tweets', 'x app'],
      'amazon': ['amzn', 'amazon store', 'amazon shop', 'amazon site'],
      'google': ['search engine', 'google search', 'google it', 'look up online'],
      'netflix': ['netflix app', 'streaming service', 'netflix shows', 'netflix movies'],
      'spotify': ['spotify app', 'music app', 'music streaming', 'spotify music'],
      'wikipedia': ['wiki', 'encyclopedia', 'information site', 'knowledge base'],
      'linkedin': ['linked in', 'professional network', 'job site', 'career network'],
      'reddit': ['subreddit', 'reddit thread', 'reddit post', 'reddit community'],
      'tiktok': ['tik tok', 'tiktok app', 'short videos', 'tiktok videos'],
      'github': ['git', 'code repository', 'source code', 'git hub'],
      'stackoverflow': ['stack overflow', 'coding help', 'programming questions', 'developer forum'],
      'pinterest': ['pin', 'pins', 'pinterest board', 'pinterest app'],
      'weather': ['forecast', 'temperature', 'climate', 'weather report', 'meteorology'],
      'news': ['current events', 'headlines', 'breaking news', 'latest news', 'news report'],
      'maps': ['directions', 'navigation', 'location', 'places', 'map app']
    };
  }

  /**
   * Process a search query to determine the best website and extract the actual search terms
   * @param {string} query - The user's search query
   * @returns {object} - Website information and processed query
   */
  processQuery(query) {
    if (!query || typeof query !== 'string') {
      return {
        platform: 'google',
        processedQuery: query,
        url: `https://www.google.com/search?q=${encodeURIComponent(query)}`,
        confidence: 0.5
      };
    }

    // Normalize the query
    const normalizedQuery = query.trim().toLowerCase();

    // Log for debugging
    console.log('Smart Search - Processing query:', normalizedQuery);

    // Check for direct URL
    if (this.isUrl(normalizedQuery)) {
      const url = this.ensureHttps(normalizedQuery);
      return {
        platform: 'direct',
        processedQuery: normalizedQuery,
        url,
        confidence: 1.0
      };
    }

    // Check for browse intent
    if (this.hasKeywords(normalizedQuery, this.intentPatterns.browse)) {
      const websiteInfo = this.extractWebsiteFromBrowseQuery(normalizedQuery);
      if (websiteInfo) {
        return websiteInfo;
      }
    }

    // Check for conversational patterns
    const conversationalMatch = this.matchConversationalPattern(normalizedQuery);
    if (conversationalMatch) {
      return conversationalMatch;
    }

    // Check for specific content type intents (video, music, shopping, etc.)
    const contentTypeMatch = this.matchContentTypeIntent(normalizedQuery);
    if (contentTypeMatch) {
      return contentTypeMatch;
    }

    // Check for question patterns that might indicate reference needs
    if (this.isQuestionPattern(normalizedQuery)) {
      // For question patterns, prefer reference sites like Wikipedia
      const referenceMatch = this.findWebsiteForIntent('reference', normalizedQuery);
      if (referenceMatch && referenceMatch.confidence > 0.6) {
        return referenceMatch;
      }
    }

    // Try fuzzy matching for website names and keywords
    const fuzzyMatch = this.findFuzzyMatch(normalizedQuery);
    if (fuzzyMatch && fuzzyMatch.confidence > 0.5) {
      return fuzzyMatch;
    }

    // Find the best matching website based on keywords
    const websiteMatch = this.findBestMatchingWebsite(normalizedQuery);
    if (websiteMatch && websiteMatch.confidence > 0.4) {
      return websiteMatch;
    }

    // If no good match found, try to extract intent from query
    const intent = this.extractIntentFromQuery(normalizedQuery);
    if (intent) {
      const websiteForIntent = this.findWebsiteForIntent(intent, normalizedQuery);
      if (websiteForIntent) {
        return websiteForIntent;
      }
    }

    // Check for synonym matches as a last resort
    const synonymMatch = this.findSynonymMatch(normalizedQuery);
    if (synonymMatch && synonymMatch.confidence > 0.4) {
      return synonymMatch;
    }

    // Default to Google search
    return {
      platform: 'google',
      processedQuery: normalizedQuery,
      url: `https://www.google.com/search?q=${encodeURIComponent(normalizedQuery)}`,
      confidence: 0.5,
      isConversational: this.isConversationalQuery(normalizedQuery)
    };
  }

  /**
   * Check if a query is conversational in nature
   * @param {string} query - The query to check
   * @returns {boolean} - Whether the query is conversational
   */
  isConversationalQuery(query) {
    // Check for first-person pronouns and question words
    const conversationalIndicators = [
      'i ', 'me ', 'my ', 'we ', 'our ', 'us ',
      'can you', 'could you', 'would you', 'will you',
      'please', 'help me', 'show me', 'tell me',
      'what', 'where', 'when', 'why', 'how', 'who', 'which'
    ];

    return conversationalIndicators.some(indicator => query.includes(indicator));
  }

  /**
   * Match a query against conversational patterns
   * @param {string} query - The query to match
   * @returns {object|null} - The matched result or null if no match
   */
  matchConversationalPattern(query) {
    // Check for browse intent in conversational patterns
    for (const pattern of this.conversationalPatterns.browse) {
      if (query.includes(pattern)) {
        const websiteInfo = this.extractWebsiteFromConversation(query);
        if (websiteInfo) {
          websiteInfo.isConversational = true;
          return websiteInfo;
        }
      }
    }

    // Check for boredom indicators
    for (const pattern of this.conversationalPatterns.bored) {
      if (query.includes(pattern)) {
        // Default to YouTube for entertainment
        const youtubeWebsite = this.topWebsites.find(site => site.domain === 'youtube.com');
        if (youtubeWebsite) {
          return {
            platform: 'youtube',
            processedQuery: 'entertaining videos',
            url: 'https://www.youtube.com/results?search_query=entertaining+videos',
            confidence: 0.7,
            website: youtubeWebsite,
            isConversational: true
          };
        }
      }
    }

    // Check for shopping intent
    for (const pattern of this.conversationalPatterns.shopping) {
      if (query.includes(pattern)) {
        // Extract the product being searched for
        const productMatch = query.split(pattern)[1]?.trim();
        if (productMatch) {
          // Default to Amazon for shopping
          const amazonWebsite = this.topWebsites.find(site => site.domain === 'amazon.com');
          if (amazonWebsite) {
            return {
              platform: 'amazon',
              processedQuery: productMatch,
              url: `https://www.amazon.com/s?k=${encodeURIComponent(productMatch)}`,
              confidence: 0.7,
              website: amazonWebsite,
              isConversational: true
            };
          }
        }
      }
    }

    // Check for video intent
    for (const pattern of this.conversationalPatterns.video) {
      if (query.includes(pattern)) {
        // Extract the video topic
        const videoMatch = query.split(pattern)[1]?.trim();
        if (videoMatch) {
          // Default to YouTube for videos
          const youtubeWebsite = this.topWebsites.find(site => site.domain === 'youtube.com');
          if (youtubeWebsite) {
            return {
              platform: 'youtube',
              processedQuery: videoMatch,
              url: `https://www.youtube.com/results?search_query=${encodeURIComponent(videoMatch)}`,
              confidence: 0.8,
              website: youtubeWebsite,
              isConversational: true
            };
          }
        }
      }
    }

    // Check for music intent
    for (const pattern of this.conversationalPatterns.music) {
      if (query.includes(pattern)) {
        // Extract the music topic
        const musicMatch = query.split(pattern)[1]?.trim();
        if (musicMatch) {
          // Default to Spotify for music
          const spotifyWebsite = this.topWebsites.find(site => site.domain === 'spotify.com');
          if (spotifyWebsite) {
            return {
              platform: 'spotify',
              processedQuery: musicMatch,
              url: `https://open.spotify.com/search/${encodeURIComponent(musicMatch)}`,
              confidence: 0.8,
              website: spotifyWebsite,
              isConversational: true
            };
          }
        }
      }
    }

    return null;
  }

  /**
   * Match a query against content type intents (video, music, shopping, etc.)
   * @param {string} query - The query to match
   * @returns {object|null} - The matched result or null if no match
   */
  matchContentTypeIntent(query) {
    // Check for video intent
    if (this.hasKeywords(query, this.intentPatterns.watch)) {
      // Extract the video topic by removing the intent keywords
      let processedQuery = query;
      for (const keyword of this.intentPatterns.watch) {
        processedQuery = processedQuery.replace(keyword, '').trim();
      }

      if (processedQuery) {
        // Default to YouTube for videos
        const youtubeWebsite = this.topWebsites.find(site => site.domain === 'youtube.com');
        if (youtubeWebsite) {
          return {
            platform: 'youtube',
            processedQuery: processedQuery,
            url: `https://www.youtube.com/results?search_query=${encodeURIComponent(processedQuery)}`,
            confidence: 0.7,
            website: youtubeWebsite
          };
        }
      }
    }

    // Check for music intent
    if (this.hasKeywords(query, this.intentPatterns.listen)) {
      // Extract the music topic by removing the intent keywords
      let processedQuery = query;
      for (const keyword of this.intentPatterns.listen) {
        processedQuery = processedQuery.replace(keyword, '').trim();
      }

      if (processedQuery) {
        // Default to Spotify for music
        const spotifyWebsite = this.topWebsites.find(site => site.domain === 'spotify.com');
        if (spotifyWebsite) {
          return {
            platform: 'spotify',
            processedQuery: processedQuery,
            url: `https://open.spotify.com/search/${encodeURIComponent(processedQuery)}`,
            confidence: 0.7,
            website: spotifyWebsite
          };
        }
      }
    }

    // Check for shopping intent
    if (this.hasKeywords(query, this.intentPatterns.shop)) {
      // Extract the product by removing the intent keywords
      let processedQuery = query;
      for (const keyword of this.intentPatterns.shop) {
        processedQuery = processedQuery.replace(keyword, '').trim();
      }

      if (processedQuery) {
        // Default to Amazon for shopping
        const amazonWebsite = this.topWebsites.find(site => site.domain === 'amazon.com');
        if (amazonWebsite) {
          return {
            platform: 'amazon',
            processedQuery: processedQuery,
            url: `https://www.amazon.com/s?k=${encodeURIComponent(processedQuery)}`,
            confidence: 0.7,
            website: amazonWebsite
          };
        }
      }
    }

    return null;
  }

  /**
   * Check if a query matches question patterns
   * @param {string} query - The query to check
   * @returns {boolean} - Whether the query is a question
   */
  isQuestionPattern(query) {
    return this.conversationalPatterns.question.some(pattern => query.includes(pattern));
  }

  /**
   * Find a fuzzy match for a website based on the query
   * @param {string} query - The query to match
   * @returns {object|null} - The matched website or null if no match
   */
  findFuzzyMatch(query) {
    // Simple fuzzy matching by checking for partial matches
    // This could be enhanced with a proper fuzzy matching algorithm

    let bestMatch = { website: null, confidence: 0, searchQuery: query };

    for (const website of this.topWebsites) {
      // Check for partial domain match
      const domainParts = website.domain.split('.');
      const domainName = domainParts[0];

      if (query.includes(domainName) && domainName.length > 2) {
        const confidence = 0.6 + (domainName.length / 20); // Longer domain names get higher confidence
        if (confidence > bestMatch.confidence) {
          bestMatch = {
            website,
            confidence,
            searchQuery: this.extractSearchQuery(query, website, domainName)
          };
        }
      }

      // Check for partial name match
      const nameParts = website.name.toLowerCase().split(' ');
      for (const part of nameParts) {
        if (part.length > 2 && query.includes(part)) {
          const confidence = 0.5 + (part.length / 20); // Longer name parts get higher confidence
          if (confidence > bestMatch.confidence) {
            bestMatch = {
              website,
              confidence,
              searchQuery: this.extractSearchQuery(query, website, part)
            };
          }
        }
      }
    }

    if (bestMatch.website) {
      const platform = bestMatch.website.domain.split('.')[0];
      const processedQuery = bestMatch.searchQuery;

      // Build the search URL
      let url;
      if (bestMatch.website.category === 'search') {
        url = `https://${bestMatch.website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
      } else if (bestMatch.website.category === 'video') {
        url = `https://${bestMatch.website.domain}/results?search_query=${encodeURIComponent(processedQuery)}`;
      } else if (bestMatch.website.category === 'shopping') {
        url = `https://${bestMatch.website.domain}/s?k=${encodeURIComponent(processedQuery)}`;
      } else if (bestMatch.website.category === 'reference' && bestMatch.website.domain.includes('wikipedia.org')) {
        // Special case for Wikipedia
        if (processedQuery) {
          url = `https://${bestMatch.website.domain}/w/index.php?search=${encodeURIComponent(processedQuery)}`;
        } else {
          url = `https://${bestMatch.website.domain}`;
        }
      } else {
        url = `https://${bestMatch.website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
      }

      return {
        platform,
        processedQuery,
        url,
        confidence: bestMatch.confidence,
        website: bestMatch.website
      };
    }

    return null;
  }

  /**
   * Find a match based on synonyms
   * @param {string} query - The query to match
   * @returns {object|null} - The matched website or null if no match
   */
  findSynonymMatch(query) {
    for (const [term, synonyms] of Object.entries(this.synonyms)) {
      for (const synonym of synonyms) {
        if (query.includes(synonym)) {
          // Find the website that matches this term
          for (const website of this.topWebsites) {
            if (website.domain.includes(term) || website.name.toLowerCase().includes(term)) {
              // Extract the search query by removing the synonym
              const processedQuery = query.replace(synonym, '').trim();

              // Build the search URL
              let url;
              if (website.category === 'search') {
                url = `https://${website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
              } else if (website.category === 'video') {
                url = `https://${website.domain}/results?search_query=${encodeURIComponent(processedQuery)}`;
              } else if (website.category === 'shopping') {
                url = `https://${website.domain}/s?k=${encodeURIComponent(processedQuery)}`;
              } else if (website.category === 'reference' && website.domain.includes('wikipedia.org')) {
                if (processedQuery) {
                  url = `https://${website.domain}/w/index.php?search=${encodeURIComponent(processedQuery)}`;
                } else {
                  url = `https://${website.domain}`;
                }
              } else {
                url = `https://${website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
              }

              return {
                platform: website.domain.split('.')[0],
                processedQuery,
                url,
                confidence: 0.6,
                website
              };
            }
          }
        }
      }
    }

    return null;
  }

  /**
   * Check if a query contains any of the specified keywords
   * @param {string} query - The query to check
   * @param {string[]} keywords - The keywords to look for
   * @returns {boolean} - Whether any keyword was found
   */
  hasKeywords(query, keywords) {
    for (const keyword of keywords) {
      if (query.includes(keyword)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if a string is a URL
   * @param {string} str - The string to check
   * @returns {boolean} - Whether the string is a URL
   */
  isUrl(str) {
    // Simple URL check
    return (
      str.includes('.') &&
      !str.includes(' ') &&
      (str.startsWith('http') || !str.includes('/'))
    );
  }

  /**
   * Ensure a URL has the HTTPS protocol
   * @param {string} url - The URL to check
   * @returns {string} - The URL with HTTPS protocol
   */
  ensureHttps(url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    return 'https://' + url;
  }

  /**
   * Extract a website from a browse query
   * @param {string} query - The browse query
   * @returns {object|null} - The extracted website information or null if not found
   */
  extractWebsiteFromBrowseQuery(query) {
    // Remove browse keywords
    let processedQuery = query;
    for (const keyword of this.intentPatterns.browse) {
      processedQuery = processedQuery.replace(keyword, '').trim();
    }

    // Check if the remaining query contains a known website
    for (const website of this.topWebsites) {
      // Check domain
      if (processedQuery.includes(website.domain)) {
        return {
          platform: website.domain.split('.')[0],
          processedQuery: '',
          url: this.ensureHttps(website.domain),
          confidence: 0.9,
          website: website
        };
      }

      // Check name
      if (processedQuery.includes(website.name.toLowerCase())) {
        return {
          platform: website.domain.split('.')[0],
          processedQuery: '',
          url: this.ensureHttps(website.domain),
          confidence: 0.8,
          website: website
        };
      }

      // Check keywords
      for (const keyword of website.keywords) {
        if (processedQuery.includes(keyword)) {
          return {
            platform: website.domain.split('.')[0],
            processedQuery: '',
            url: this.ensureHttps(website.domain),
            confidence: 0.7,
            website: website
          };
        }
      }
    }

    // Check if the remaining query is a URL
    if (this.isUrl(processedQuery)) {
      return {
        platform: 'direct',
        processedQuery: processedQuery,
        url: this.ensureHttps(processedQuery),
        confidence: 0.9
      };
    }

    // Try to extract a domain
    const domainMatch = processedQuery.match(/([a-zA-Z0-9][-a-zA-Z0-9]*\.)+[a-zA-Z]{2,}/);
    if (domainMatch) {
      return {
        platform: 'direct',
        processedQuery: domainMatch[0],
        url: this.ensureHttps(domainMatch[0]),
        confidence: 0.8
      };
    }

    return null;
  }

  /**
   * Extract a website from a conversational query
   * @param {string} query - The conversational query
   * @returns {object|null} - The extracted website information or null if not found
   */
  extractWebsiteFromConversation(query) {
    // Remove conversational starters
    let processedQuery = query;
    for (const pattern of this.conversationalPatterns.browse) {
      processedQuery = processedQuery.replace(pattern, '').trim();
    }

    // Check if the remaining query contains a known website
    for (const website of this.topWebsites) {
      // Check domain
      if (processedQuery.includes(website.domain)) {
        return {
          platform: website.domain.split('.')[0],
          processedQuery: '',
          url: this.ensureHttps(website.domain),
          confidence: 0.9,
          website: website
        };
      }

      // Check name
      if (processedQuery.includes(website.name.toLowerCase())) {
        return {
          platform: website.domain.split('.')[0],
          processedQuery: '',
          url: this.ensureHttps(website.domain),
          confidence: 0.8,
          website: website
        };
      }

      // Check keywords
      for (const keyword of website.keywords) {
        if (processedQuery.includes(keyword)) {
          return {
            platform: website.domain.split('.')[0],
            processedQuery: '',
            url: this.ensureHttps(website.domain),
            confidence: 0.7,
            website: website
          };
        }
      }
    }

    return null;
  }

  /**
   * Find the best matching website based on keywords
   * @param {string} query - The query to match
   * @returns {object|null} - The best matching website information or null if not found
   */
  findBestMatchingWebsite(query) {
    let bestMatch = { website: null, confidence: 0, searchQuery: query };

    for (const website of this.topWebsites) {
      // Check domain
      if (query.includes(website.domain)) {
        const confidence = 0.9;
        if (confidence > bestMatch.confidence) {
          bestMatch = {
            website,
            confidence,
            searchQuery: this.extractSearchQuery(query, website)
          };
        }
        continue;
      }

      // Check name
      if (query.includes(website.name.toLowerCase())) {
        const confidence = 0.8;
        if (confidence > bestMatch.confidence) {
          bestMatch = {
            website,
            confidence,
            searchQuery: this.extractSearchQuery(query, website)
          };
        }
        continue;
      }

      // Check keywords
      for (const keyword of website.keywords) {
        if (query.includes(keyword)) {
          // Calculate confidence based on keyword length and position
          const keywordLength = keyword.length;
          const queryLength = query.length;
          const position = query.indexOf(keyword);

          // Patterns at the beginning get higher scores
          const positionFactor = 1 - (position / queryLength);

          // Longer patterns get higher scores
          const lengthFactor = keywordLength / queryLength;

          // Calculate final confidence
          const confidence = 0.5 + (0.2 * positionFactor) + (0.2 * lengthFactor);

          if (confidence > bestMatch.confidence) {
            bestMatch = {
              website,
              confidence,
              searchQuery: this.extractSearchQuery(query, website, keyword)
            };
          }
          break;
        }
      }
    }

    if (bestMatch.website) {
      const platform = bestMatch.website.domain.split('.')[0];
      const processedQuery = bestMatch.searchQuery;

      // Build the search URL
      let url;
      if (bestMatch.website.category === 'search') {
        url = `https://${bestMatch.website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
      } else if (bestMatch.website.category === 'video') {
        url = `https://${bestMatch.website.domain}/results?search_query=${encodeURIComponent(processedQuery)}`;
      } else if (bestMatch.website.category === 'shopping') {
        url = `https://${bestMatch.website.domain}/s?k=${encodeURIComponent(processedQuery)}`;
      } else if (bestMatch.website.category === 'reference' && bestMatch.website.domain.includes('wikipedia.org')) {
        // Special case for Wikipedia
        if (processedQuery) {
          // If there's a search query, use the search functionality
          url = `https://${bestMatch.website.domain}/w/index.php?search=${encodeURIComponent(processedQuery)}`;
        } else {
          // If no search query, just go to the main page
          url = `https://${bestMatch.website.domain}`;
        }
      } else {
        url = `https://${bestMatch.website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
      }

      return {
        platform,
        processedQuery,
        url,
        confidence: bestMatch.confidence,
        website: bestMatch.website
      };
    }

    return null;
  }

  /**
   * Extract a search query from a query containing a website reference
   * @param {string} query - The full query
   * @param {object} website - The website object
   * @param {string} [matchedKeyword] - The keyword that matched, if any
   * @returns {string} - The extracted search query
   */
  extractSearchQuery(query, website, matchedKeyword) {
    let processedQuery = query;

    // Remove domain if present
    if (query.includes(website.domain)) {
      processedQuery = processedQuery.replace(website.domain, '').trim();
    }

    // Remove name if present
    if (query.includes(website.name.toLowerCase())) {
      processedQuery = processedQuery.replace(website.name.toLowerCase(), '').trim();
    }

    // Remove matched keyword if provided
    if (matchedKeyword) {
      processedQuery = processedQuery.replace(matchedKeyword, '').trim();
    }

    // Remove common connectors with more precise pattern matching
    const connectors = [
      'on', 'in', 'at', 'from', 'using', 'with', 'for', 'about',
      'of', 'to', 'by', 'through', 'via', 'regarding', 'concerning',
      'related to', 'pertaining to', 'associated with', 'connected to'
    ];

    // First try to remove connector phrases at the beginning of the query
    for (const connector of connectors) {
      const startPattern = new RegExp(`^${connector}\\s+`, 'i');
      processedQuery = processedQuery.replace(startPattern, '').trim();
    }

    // Then try to remove connector words between terms
    for (const connector of connectors) {
      // Match the connector with spaces on both sides or at the beginning/end
      const pattern = new RegExp(`(^|\\s)${connector}(\\s|$)`, 'gi');
      processedQuery = processedQuery.replace(pattern, ' ').trim();
    }

    // Remove search intent keywords
    for (const keyword of this.intentPatterns.search) {
      const keywordPattern = new RegExp(`(^|\\s)${this.escapeRegExp(keyword)}(\\s|$)`, 'gi');
      processedQuery = processedQuery.replace(keywordPattern, ' ').trim();
    }

    // Remove browse intent keywords
    for (const keyword of this.intentPatterns.browse) {
      const keywordPattern = new RegExp(`(^|\\s)${this.escapeRegExp(keyword)}(\\s|$)`, 'gi');
      processedQuery = processedQuery.replace(keywordPattern, ' ').trim();
    }

    // Remove common filler words
    const fillerWords = ['please', 'can you', 'could you', 'would you', 'i want', 'i need', 'i would like'];
    for (const filler of fillerWords) {
      const fillerPattern = new RegExp(`(^|\\s)${this.escapeRegExp(filler)}(\\s|$)`, 'gi');
      processedQuery = processedQuery.replace(fillerPattern, ' ').trim();
    }

    // Clean up multiple spaces
    processedQuery = processedQuery.replace(/\s+/g, ' ').trim();

    return processedQuery || query; // Return original query if processed query is empty
  }

  /**
   * Escape special characters in a string for use in a regular expression
   * @param {string} string - The string to escape
   * @returns {string} - The escaped string
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Extract intent from a query
   * @param {string} query - The query to analyze
   * @returns {string|null} - The extracted intent or null if not found
   */
  extractIntentFromQuery(query) {
    // Check each intent pattern
    for (const [intent, keywords] of Object.entries(this.intentPatterns)) {
      for (const keyword of keywords) {
        if (query.includes(keyword)) {
          return intent;
        }
      }
    }

    return null;
  }

  /**
   * Find a website for a specific intent
   * @param {string} intent - The intent to match
   * @param {string} query - The original query
   * @returns {object|null} - The website information or null if not found
   */
  findWebsiteForIntent(intent, query) {
    // Map intents to categories
    const intentCategoryMap = {
      browse: null, // Special case handled elsewhere
      search: 'search',
      shop: 'shopping',
      watch: 'video',
      listen: 'music',
      social: 'social',
      news: 'news',
      weather: 'weather',
      travel: 'travel',
      job: 'jobs',
      reference: 'reference'
    };

    const category = intentCategoryMap[intent];
    if (!category) {
      return null;
    }

    // Get websites in this category
    const websites = this.categoryMap[category] || [];
    if (websites.length === 0) {
      return null;
    }

    // Use the first website in the category
    const website = websites[0];
    const platform = website.domain.split('.')[0];
    const processedQuery = this.extractSearchQuery(query, website);

    // Build the search URL
    let url;
    if (website.category === 'search') {
      url = `https://${website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
    } else if (website.category === 'video') {
      url = `https://${website.domain}/results?search_query=${encodeURIComponent(processedQuery)}`;
    } else if (website.category === 'shopping') {
      url = `https://${website.domain}/s?k=${encodeURIComponent(processedQuery)}`;
    } else if (website.category === 'reference' && website.domain.includes('wikipedia.org')) {
      // Special case for Wikipedia
      if (processedQuery) {
        // If there's a search query, use the search functionality
        url = `https://${website.domain}/w/index.php?search=${encodeURIComponent(processedQuery)}`;
      } else {
        // If no search query, just go to the main page
        url = `https://${website.domain}`;
      }
    } else {
      url = `https://${website.domain}/search?q=${encodeURIComponent(processedQuery)}`;
    }

    return {
      platform,
      processedQuery,
      url,
      confidence: 0.6,
      website
    };
  }

  /**
   * Detect platform and search query from user input
   * @param {string} userInput - The user's message
   * @returns {object} - The detected platform and search query
   */
  detectPlatformAndSearch(userInput) {
    if (!userInput || typeof userInput !== 'string') {
      return { platform: null, query: null, confidence: 0 };
    }

    try {
      // Log the original input for debugging
      console.log('Smart Search Beta NLP processing input:', userInput);

      // Process the query to determine the best website and extract the search terms
      const result = this.processQuery(userInput);

      console.log('Smart Search Beta NLP result:', result);

      return result;
    } catch (error) {
      console.error('Error in detectPlatformAndSearch:', error);
      return {
        platform: 'google',
        query: userInput,
        confidence: 0.5,
        url: `https://www.google.com/search?q=${encodeURIComponent(userInput)}`
      };
    }
  }
}

// Export the class
window.EnhancedWebsiteNLPManager = EnhancedWebsiteNLPManager;