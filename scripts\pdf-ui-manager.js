'use strict';

/**
 * PDF UI Manager class for handling the message-based UI for PDF tools
 */
class PDFUIManager {
  /**
   * Initialize the PDF UI Manager
   * @param {FeatureManager} featureManager - The feature manager instance
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(featureManager, uiManager) {
    this.featureManager = featureManager;
    this.uiManager = uiManager;
    
    // Initialize event listeners
    this.initEventListeners();
  }
  
  /**
   * Initialize event listeners for chat interactions
   */
  initEventListeners() {
    // Add event listener for chat link clicks
    document.getElementById('chatMessages').addEventListener('click', (event) => {
      // Check if the clicked element is a link
      if (event.target.tagName === 'A') {
        const action = event.target.getAttribute('href');
        
        // Handle PDF tool actions
        if (action && action.startsWith('#pdf-')) {
          event.preventDefault();
          this.handlePDFAction(action);
        }
      }
    });
  }
  
  /**
   * Handle PDF tool actions from chat links
   * @param {string} action - The action to handle
   */
  async handlePDFAction(action) {
    try {
      // Check if we're on a PDF page
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const isPdf = tab.url.toLowerCase().endsWith('.pdf') || 
                   tab.url.toLowerCase().includes('pdf') || 
                   tab.url.toLowerCase().includes('viewer.html');
      
      if (!isPdf && action !== '#pdf-main') {
        this.uiManager.addMessageToChat(
          `⚠️ **PDF Tools require a PDF document**\n\nPlease open a PDF file in your browser and try again.`,
          'ai',
          'error-message'
        );
        return;
      }
      
      // Handle different PDF actions
      if (action === '#pdf-main') {
        this.showPDFToolsOptions();
      } else if (action === '#pdf-summarize') {
        this.showSummarizeOptions();
      } else if (action === '#pdf-summarize-executive') {
        await this.featureManager.summarizePDF('executive');
      } else if (action === '#pdf-summarize-detailed') {
        await this.featureManager.summarizePDF('detailed');
      } else if (action === '#pdf-summarize-chapter') {
        await this.featureManager.summarizePDF('chapter');
      } else if (action === '#pdf-ask') {
        await this.featureManager.askPDFQuestion();
      } else if (action === '#pdf-extract') {
        this.showExtractOptions();
      } else if (action === '#pdf-extract-all') {
        await this.featureManager.extractPDFInfo('all');
      } else if (action === '#pdf-extract-topics') {
        await this.featureManager.extractPDFInfo('topics');
      } else if (action === '#pdf-extract-entities') {
        await this.featureManager.extractPDFInfo('entities');
      } else if (action === '#pdf-extract-dates') {
        await this.featureManager.extractPDFInfo('dates');
      } else if (action === '#pdf-extract-statistics') {
        await this.featureManager.extractPDFInfo('statistics');
      } else if (action === '#pdf-extract-citations') {
        await this.featureManager.extractPDFInfo('citations');
      } else if (action === '#pdf-toc') {
        await this.featureManager.createPDFTableOfContents();
      } else if (action === '#pdf-study-guide') {
        await this.featureManager.generatePDFStudyGuide();
      } else if (action === '#pdf-compare') {
        this.promptForSecondPDF();
      }
    } catch (error) {
      console.error('Error handling PDF action:', error);
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
    }
  }
  
  /**
   * Show the main PDF tools options
   */
  showPDFToolsOptions() {
    const message = `# PDF Tools

What would you like to do with the current PDF?

- [Summarize PDF](#pdf-summarize)
- [Ask a Question](#pdf-ask)
- [Extract Information](#pdf-extract)
- [Create Table of Contents](#pdf-toc)
- [Generate Study Guide](#pdf-study-guide)
- [Compare with Another PDF](#pdf-compare)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show PDF summarization options
   */
  showSummarizeOptions() {
    const message = `# Summarize PDF

What type of summary would you like?

- [Executive Summary](#pdf-summarize-executive) - Concise overview of key points
- [Detailed Summary](#pdf-summarize-detailed) - Comprehensive summary with all important details
- [Chapter-by-Chapter](#pdf-summarize-chapter) - Summary broken down by sections

Or [go back to PDF Tools](#pdf-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Show PDF information extraction options
   */
  showExtractOptions() {
    const message = `# Extract Information from PDF

What type of information would you like to extract?

- [All Information](#pdf-extract-all) - Extract all key information
- [Topics & Themes](#pdf-extract-topics) - Extract main topics and themes
- [People & Organizations](#pdf-extract-entities) - Extract named entities
- [Dates & Timeline](#pdf-extract-dates) - Extract chronological information
- [Statistics & Data](#pdf-extract-statistics) - Extract numerical data
- [Citations & References](#pdf-extract-citations) - Extract sources and references

Or [go back to PDF Tools](#pdf-main)`;

    this.uiManager.addMessageToChat(message, 'ai');
  }
  
  /**
   * Prompt for a second PDF to compare
   */
  async promptForSecondPDF() {
    try {
      // Prompt for the URL of the second PDF
      const secondPdfUrl = await this.uiManager.prompt('Enter the URL of the second PDF to compare:');
      
      if (!secondPdfUrl) {
        return; // User cancelled
      }
      
      // Validate the URL
      if (!secondPdfUrl.toLowerCase().endsWith('.pdf')) {
        throw new Error('The provided URL does not appear to be a PDF document. Please provide a URL ending with .pdf');
      }
      
      // Compare the PDFs
      await this.featureManager.comparePDFs(secondPdfUrl);
    } catch (error) {
      console.error('Error comparing PDFs:', error);
      this.uiManager.addMessageToChat(`Error: ${error.message}`, 'ai', 'error-message');
    }
  }
}
