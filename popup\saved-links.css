/* Saved Links Dialog Styles */
.saved-links-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.saved-links-dialog.active {
  opacity: 1;
}

.saved-links-dialog-content {
  background-color: var(--bg-color);
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
  overflow: hidden;
  animation: dialogFadeIn 0.3s ease;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.saved-links-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--header-bg);
}

.saved-links-dialog-header h3 {
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  color: var(--text-color);
}

.saved-links-dialog-header h3 i {
  margin-right: 8px;
  color: var(--primary-color);
}

.saved-links-close-btn {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 16px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.saved-links-close-btn:hover {
  background-color: var(--hover-color);
}

.saved-links-dialog-body {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.saved-links-search {
  position: relative;
  margin-bottom: 12px;
}

.saved-links-search input {
  width: 100%;
  padding: 10px 36px 10px 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 14px;
}

.saved-links-search i {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
}

.saved-links-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.saved-link-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.saved-link-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.saved-link-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.saved-link-icon img {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.saved-link-content {
  flex: 1;
  overflow: hidden;
}

.saved-link-title {
  font-weight: 600;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-color);
}

.saved-link-url {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.saved-link-date {
  font-size: 11px;
  color: var(--text-muted);
}

.saved-link-actions {
  display: flex;
  gap: 8px;
}

.saved-link-actions button {
  background: none;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.saved-link-actions button:hover {
  background-color: var(--hover-color);
  color: var(--text-color);
}

.saved-link-open:hover {
  color: var(--primary-color) !important;
}

.saved-link-delete:hover {
  color: var(--danger-color) !important;
}

.saved-links-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-muted);
  text-align: center;
}

.saved-links-empty i {
  font-size: 40px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.saved-links-empty p {
  margin: 4px 0;
}

.saved-links-empty-hint {
  font-size: 14px;
  opacity: 0.7;
}

.saved-links-dialog-footer {
  padding: 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.saved-links-add-btn,
.saved-links-clear-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.saved-links-add-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.saved-links-add-btn:hover {
  background-color: var(--primary-hover);
}

.saved-links-clear-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.saved-links-clear-btn:hover {
  background-color: var(--hover-color);
  color: var(--danger-color);
}
