'use strict';

/**
 * Video Summarizer Background Script
 * Handles transcript extraction and summarization
 */

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'extractTranscript') {
    extractTranscript(request.videoId)
      .then(transcript => sendResponse({ success: true, transcript }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Indicates we'll respond asynchronously
  }
  
  if (request.action === 'summarizeTranscript') {
    summarizeTranscript(request.transcript)
      .then(summary => sendResponse({ success: true, summary }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Indicates we'll respond asynchronously
  }
});

/**
 * Extract transcript for a YouTube video
 * @param {string} videoId - The YouTube video ID
 * @returns {Promise<string>} The transcript text
 */
async function extractTranscript(videoId) {
  try {
    // First try the official YouTube API
    const response = await fetch(`https://www.youtube.com/api/timedtext?lang=en&v=${videoId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch transcript from YouTube API');
    }
    
    const xml = await response.text();
    
    // Check if we got a valid XML response
    if (!xml || xml.trim() === '') {
      throw new Error('Empty transcript response from YouTube API');
    }
    
    // Parse the XML to extract the transcript
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xml, 'text/xml');
    
    // Extract text from the XML
    const textNodes = xmlDoc.getElementsByTagName('text');
    
    if (textNodes.length === 0) {
      throw new Error('No transcript found in YouTube API response');
    }
    
    let transcript = '';
    
    for (let i = 0; i < textNodes.length; i++) {
      transcript += textNodes[i].textContent + ' ';
    }
    
    // Clean up the transcript
    transcript = transcript.replace(/\s+/g, ' ').trim();
    
    return transcript;
  } catch (error) {
    console.error('Error extracting transcript:', error);
    
    // Fallback: Try a third-party API
    // Note: In a production environment, you would need to handle API keys and rate limits
    try {
      // This is a placeholder for a third-party API call
      // In a real implementation, you would use a service like YouTube Transcript API
      throw new Error('Third-party API not implemented yet');
    } catch (fallbackError) {
      console.error('Fallback extraction failed:', fallbackError);
      throw new Error('Could not extract transcript for this video');
    }
  }
}

/**
 * Summarize a transcript using NLP techniques
 * @param {string} transcript - The video transcript
 * @returns {Promise<object>} The summary object with text and key points
 */
async function summarizeTranscript(transcript) {
  try {
    // For the initial implementation, we'll use a simple extractive summarization approach
    // In a more advanced version, we could use an AI service like OpenAI's API
    
    // 1. Split the transcript into sentences
    const sentences = splitIntoSentences(transcript);
    
    if (sentences.length === 0) {
      throw new Error('Could not parse transcript into sentences');
    }
    
    // 2. Score each sentence based on importance
    const sentenceScores = scoreSentences(sentences);
    
    // 3. Select the top sentences for the summary
    const summaryLength = Math.max(3, Math.floor(sentences.length * 0.2)); // 20% of sentences or at least 3
    const topSentences = selectTopSentences(sentences, sentenceScores, summaryLength);
    
    // 4. Extract key points
    const keyPoints = extractKeyPoints(sentences, sentenceScores, 5);
    
    // 5. Assemble the summary
    const summary = topSentences.join(' ');
    
    return {
      summary,
      keyPoints
    };
  } catch (error) {
    console.error('Error summarizing transcript:', error);
    throw new Error('Failed to generate summary from transcript');
  }
}

/**
 * Split text into sentences
 * @param {string} text - The text to split
 * @returns {string[]} Array of sentences
 */
function splitIntoSentences(text) {
  // Simple sentence splitting - in a production environment, you would use a more sophisticated approach
  return text
    .replace(/([.!?])\s+/g, '$1|')
    .split('|')
    .filter(sentence => sentence.trim().length > 10); // Filter out very short sentences
}

/**
 * Score sentences based on importance
 * @param {string[]} sentences - Array of sentences
 * @returns {number[]} Array of scores
 */
function scoreSentences(sentences) {
  // Create a word frequency map
  const wordFrequency = {};
  
  // Count word frequencies across all sentences
  sentences.forEach(sentence => {
    const words = sentence.toLowerCase().split(/\s+/);
    
    words.forEach(word => {
      // Ignore very short words and common stop words
      if (word.length < 3 || isStopWord(word)) {
        return;
      }
      
      wordFrequency[word] = (wordFrequency[word] || 0) + 1;
    });
  });
  
  // Score each sentence based on the frequency of its words
  return sentences.map(sentence => {
    const words = sentence.toLowerCase().split(/\s+/);
    let score = 0;
    
    words.forEach(word => {
      if (word.length >= 3 && !isStopWord(word)) {
        score += wordFrequency[word] || 0;
      }
    });
    
    // Normalize by sentence length to avoid favoring very long sentences
    return score / Math.max(1, words.length);
  });
}

/**
 * Check if a word is a stop word (common words with little semantic value)
 * @param {string} word - The word to check
 * @returns {boolean} True if the word is a stop word
 */
function isStopWord(word) {
  const stopWords = new Set([
    'the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about',
    'as', 'of', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'shall', 'should',
    'can', 'could', 'may', 'might', 'must', 'that', 'this', 'these', 'those',
    'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
    'my', 'your', 'his', 'its', 'our', 'their', 'mine', 'yours', 'hers', 'ours', 'theirs'
  ]);
  
  return stopWords.has(word.toLowerCase());
}

/**
 * Select the top N sentences based on scores
 * @param {string[]} sentences - Array of sentences
 * @param {number[]} scores - Array of sentence scores
 * @param {number} count - Number of sentences to select
 * @returns {string[]} Array of selected sentences
 */
function selectTopSentences(sentences, scores, count) {
  // Create an array of sentence indices
  const indices = Array.from({ length: sentences.length }, (_, i) => i);
  
  // Sort indices by score (descending)
  indices.sort((a, b) => scores[b] - scores[a]);
  
  // Select the top N indices
  const topIndices = indices.slice(0, count);
  
  // Sort the indices to maintain the original sentence order
  topIndices.sort();
  
  // Return the selected sentences
  return topIndices.map(i => sentences[i]);
}

/**
 * Extract key points from the transcript
 * @param {string[]} sentences - Array of sentences
 * @param {number[]} scores - Array of sentence scores
 * @param {number} count - Number of key points to extract
 * @returns {string[]} Array of key points
 */
function extractKeyPoints(sentences, scores, count) {
  // Create an array of sentence indices
  const indices = Array.from({ length: sentences.length }, (_, i) => i);
  
  // Sort indices by score (descending)
  indices.sort((a, b) => scores[b] - scores[a]);
  
  // Select the top N indices
  const topIndices = indices.slice(0, count);
  
  // Return the selected sentences as key points
  return topIndices.map(i => {
    // Simplify the sentence if it's too long
    let sentence = sentences[i];
    if (sentence.length > 100) {
      sentence = sentence.substring(0, 97) + '...';
    }
    return sentence;
  });
}
