/* Web MCP Styles */
.web-mcp-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  color: var(--text-color);
}

.web-mcp-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(40, 59, 72, 0.5);
  padding: 12px 15px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-icon {
  color: var(--primary-color);
  font-size: 20px;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.mcp-action-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 15px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.mcp-action-btn:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
}

.mcp-action-btn i {
  font-size: 18px;
}

.web-mcp-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
  min-height: 0;
}

.web-mcp-info {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.info-card {
  flex: 1;
  background-color: rgba(40, 59, 72, 0.5);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-icon {
  width: 38px;
  height: 38px;
  background-color: rgba(0, 166, 192, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-light);
}

.info-details {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  opacity: 0.7;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
}

.web-mcp-chat {
  display: flex;
  flex-direction: column;
  gap: 15px;
  flex: 1;
  min-height: 0;
  background-color: rgba(40, 59, 72, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mcp-chat-messages {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-right: 5px;
  min-height: 200px;
}

.mcp-message {
  display: flex;
  gap: 10px;
  max-width: 100%;
}

.mcp-message.ai {
  align-self: flex-start;
}

.mcp-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.mcp-message .message-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.mcp-message .message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mcp-message.user .message-avatar {
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.mcp-message .message-content {
  background-color: rgba(40, 59, 72, 0.7);
  padding: 10px 15px;
  border-radius: 12px;
  border-bottom-left-radius: 4px;
  max-width: calc(100% - 50px);
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mcp-message.user .message-content {
  background-color: var(--primary-color);
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 4px;
}

.mcp-message .message-content p {
  margin: 0;
}

.mcp-chat-input {
  display: flex;
  gap: 10px;
}

#mcpUserInput {
  flex: 1;
  background-color: rgba(40, 59, 72, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px 15px;
  color: var(--text-color);
  font-size: 14px;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
}

#mcpUserInput:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 166, 192, 0.2);
}

.mcp-send-btn {
  width: 42px;
  height: 42px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-end;
}

.mcp-send-btn:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
}

/* Status indicators */
.web-mcp-status.scanning .status-icon {
  color: #ffc107;
}

.web-mcp-status.scanned .status-icon {
  color: #4caf50;
}

.web-mcp-status.error .status-icon {
  color: #f44336;
}

/* Highlight text in content */
.highlight-text {
  background-color: rgba(0, 166, 192, 0.3);
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}
