'use strict';

/**
 * Enhanced Translation UI Manager
 * Provides improved UI for translation features
 */
class TranslateUIManager {
  /**
   * Initialize the Translation UI Manager
   * @param {UIManager} uiManager - The main UI manager instance
   * @param {FeatureManager} featureManager - The feature manager instance
   */
  constructor(uiManager, featureManager) {
    this.uiManager = uiManager;
    this.featureManager = featureManager;
    
    // Language categories
    this.categories = [
      { name: 'All', filter: () => true },
      { name: 'Popular', filter: lang => ['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Russian', 'Arabic', 'Portuguese', 'Italian'].includes(lang) },
      { name: 'European', filter: lang => ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian', 'Dutch', 'Swedish', 'Greek', 'Polish', 'Czech', 'Hungarian', 'Romanian', 'Ukrainian', 'Bulgarian', 'Croatian', 'Danish', 'Finnish', 'Norwegian'].includes(lang) },
      { name: 'Asian', filter: lang => ['Chinese', 'Japanese', 'Korean', 'Vietnamese', 'Thai', 'Indonesian', 'Hindi'].includes(lang) },
      { name: 'Other', filter: lang => ['Arabic', 'Hebrew', 'Turkish'].includes(lang) }
    ];
    
    // Native language names for better UX
    this.nativeNames = {
      'English': 'English',
      'Spanish': 'Español',
      'French': 'Français',
      'German': 'Deutsch',
      'Italian': 'Italiano',
      'Portuguese': 'Português',
      'Russian': 'Русский',
      'Japanese': '日本語',
      'Chinese': '中文',
      'Korean': '한국어',
      'Arabic': 'العربية',
      'Hindi': 'हिन्दी',
      'Dutch': 'Nederlands',
      'Swedish': 'Svenska',
      'Greek': 'Ελληνικά',
      'Turkish': 'Türkçe',
      'Polish': 'Polski',
      'Vietnamese': 'Tiếng Việt',
      'Thai': 'ไทย',
      'Indonesian': 'Bahasa Indonesia',
      'Hebrew': 'עברית',
      'Danish': 'Dansk',
      'Finnish': 'Suomi',
      'Norwegian': 'Norsk',
      'Czech': 'Čeština',
      'Hungarian': 'Magyar',
      'Romanian': 'Română',
      'Ukrainian': 'Українська',
      'Bulgarian': 'Български',
      'Croatian': 'Hrvatski'
    };
    
    // Flag emoji codes for each language
    this.flagEmojis = {
      'English': '🇺🇸',
      'Spanish': '🇪🇸',
      'French': '🇫🇷',
      'German': '🇩🇪',
      'Italian': '🇮🇹',
      'Portuguese': '🇵🇹',
      'Russian': '🇷🇺',
      'Japanese': '🇯🇵',
      'Chinese': '🇨🇳',
      'Korean': '🇰🇷',
      'Arabic': '🇸🇦',
      'Hindi': '🇮🇳',
      'Dutch': '🇳🇱',
      'Swedish': '🇸🇪',
      'Greek': '🇬🇷',
      'Turkish': '🇹🇷',
      'Polish': '🇵🇱',
      'Vietnamese': '🇻🇳',
      'Thai': '🇹🇭',
      'Indonesian': '🇮🇩',
      'Hebrew': '🇮🇱',
      'Danish': '🇩🇰',
      'Finnish': '🇫🇮',
      'Norwegian': '🇳🇴',
      'Czech': '🇨🇿',
      'Hungarian': '🇭🇺',
      'Romanian': '🇷🇴',
      'Ukrainian': '🇺🇦',
      'Bulgarian': '🇧🇬',
      'Croatian': '🇭🇷'
    };
  }
  
  /**
   * Show enhanced language selection UI for translation
   * @param {string} message - The prompt message
   * @param {string[]} languages - Array of language options
   * @param {string} defaultLanguage - Default selected language
   * @returns {Promise<string|null>} - The selected language or null if canceled
   */
  async showLanguageSelectionUI(message, languages, defaultLanguage = 'English') {
    return new Promise(resolve => {
      // Create modal container
      const modal = document.createElement('div');
      modal.className = 'translate-modal';
      
      // Create modal content
      const modalContent = document.createElement('div');
      modalContent.className = 'translate-modal-content';
      
      // Add header
      const header = document.createElement('div');
      header.className = 'translate-header';
      header.innerHTML = `<h3><i class="fas fa-language"></i> ${message}</h3>`;
      modalContent.appendChild(header);
      
      // Add search container
      const searchContainer = document.createElement('div');
      searchContainer.className = 'translate-search-container';
      
      const searchInput = document.createElement('input');
      searchInput.type = 'text';
      searchInput.className = 'translate-search';
      searchInput.placeholder = 'Search languages...';
      
      const searchIcon = document.createElement('i');
      searchIcon.className = 'fas fa-search translate-search-icon';
      
      searchContainer.appendChild(searchInput);
      searchContainer.appendChild(searchIcon);
      modalContent.appendChild(searchContainer);
      
      // Add categories
      const categoriesContainer = document.createElement('div');
      categoriesContainer.className = 'translate-categories';
      
      this.categories.forEach((category, index) => {
        const categoryBtn = document.createElement('div');
        categoryBtn.className = 'translate-category';
        if (index === 0) categoryBtn.classList.add('active');
        categoryBtn.textContent = category.name;
        categoryBtn.addEventListener('click', () => {
          // Update active state
          document.querySelectorAll('.translate-category').forEach(btn => btn.classList.remove('active'));
          categoryBtn.classList.add('active');
          
          // Filter languages
          updateLanguagesList(searchInput.value, category.filter);
        });
        categoriesContainer.appendChild(categoryBtn);
      });
      
      modalContent.appendChild(categoriesContainer);
      
      // Add languages container
      const languagesContainer = document.createElement('div');
      languagesContainer.className = 'translate-languages';
      modalContent.appendChild(languagesContainer);
      
      // Add footer with cancel button
      const footer = document.createElement('div');
      footer.className = 'translate-footer';
      
      const cancelBtn = document.createElement('button');
      cancelBtn.className = 'translate-cancel';
      cancelBtn.textContent = 'Cancel';
      cancelBtn.addEventListener('click', () => {
        resolve(null);
        document.body.removeChild(modal);
      });
      
      footer.appendChild(cancelBtn);
      modalContent.appendChild(footer);
      
      // Function to update languages list based on search and category
      const updateLanguagesList = (searchText = '', categoryFilter = () => true) => {
        // Clear languages container
        languagesContainer.innerHTML = '';
        
        // Filter languages
        const filteredLanguages = languages.filter(lang => 
          lang.toLowerCase().includes(searchText.toLowerCase()) && categoryFilter(lang)
        );
        
        if (filteredLanguages.length === 0) {
          const noResults = document.createElement('div');
          noResults.className = 'no-results';
          noResults.textContent = 'No languages found';
          languagesContainer.appendChild(noResults);
          return;
        }
        
        // Add language options
        filteredLanguages.forEach(language => {
          const langOption = document.createElement('div');
          langOption.className = 'language-option';
          if (language === defaultLanguage) langOption.classList.add('selected');
          
          // Add flag
          const flagSpan = document.createElement('div');
          flagSpan.className = 'language-flag';
          flagSpan.textContent = this.flagEmojis[language] || '🌐';
          langOption.appendChild(flagSpan);
          
          // Add language name container
          const nameContainer = document.createElement('div');
          nameContainer.className = 'language-info';
          
          // Add language name
          const langName = document.createElement('div');
          langName.className = 'language-name';
          langName.textContent = language;
          nameContainer.appendChild(langName);
          
          // Add native name if available
          if (this.nativeNames[language] && this.nativeNames[language] !== language) {
            const nativeName = document.createElement('div');
            nativeName.className = 'language-native';
            nativeName.textContent = this.nativeNames[language];
            nameContainer.appendChild(nativeName);
          }
          
          langOption.appendChild(nameContainer);
          
          // Add click handler
          langOption.addEventListener('click', () => {
            resolve(language);
            document.body.removeChild(modal);
          });
          
          languagesContainer.appendChild(langOption);
        });
      };
      
      // Initialize languages list
      updateLanguagesList();
      
      // Add search functionality
      searchInput.addEventListener('input', () => {
        const activeCategory = document.querySelector('.translate-category.active');
        const categoryIndex = Array.from(document.querySelectorAll('.translate-category')).indexOf(activeCategory);
        updateLanguagesList(searchInput.value, this.categories[categoryIndex].filter);
      });
      
      // Add modal to body
      modal.appendChild(modalContent);
      document.body.appendChild(modal);
      
      // Focus search input
      setTimeout(() => searchInput.focus(), 100);
      
      // Close modal if clicked outside
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          resolve(null);
          document.body.removeChild(modal);
        }
      });
    });
  }
  
  /**
   * Enhance the translation result display
   * @param {string} content - The translated content
   * @param {string} targetLanguage - The language the content was translated to
   * @returns {HTMLElement} - The enhanced translation box element
   */
  createEnhancedTranslationBox(content, targetLanguage) {
    // Create translation box container
    const translationBox = document.createElement('div');
    translationBox.className = 'translation-box';
    
    // Create header
    const header = document.createElement('div');
    header.className = 'translation-header';
    header.innerHTML = `<i class="fas fa-language"></i> Translated to ${targetLanguage}`;
    translationBox.appendChild(header);
    
    // Create content area
    const contentDiv = document.createElement('div');
    contentDiv.className = 'translation-content';
    contentDiv.textContent = content;
    translationBox.appendChild(contentDiv);
    
    // Create actions footer
    const actions = document.createElement('div');
    actions.className = 'translation-actions';
    
    // Add copy button
    const copyBtn = document.createElement('button');
    copyBtn.className = 'translation-copy-btn';
    copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy Translation';
    
    // Add event listener for the copy button
    copyBtn.addEventListener('click', async () => {
      try {
        await navigator.clipboard.writeText(content);
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        copyBtn.classList.add('success');
        
        // Reset button after 2 seconds
        setTimeout(() => {
          copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy Translation';
          copyBtn.classList.remove('success');
        }, 2000);
      } catch (error) {
        console.error('Failed to copy translation:', error);
      }
    });
    
    actions.appendChild(copyBtn);
    translationBox.appendChild(actions);
    
    return translationBox;
  }
  
  /**
   * Show the translation dialog and handle the translation process
   */
  async showTranslateDialog() {
    try {
      const languages = [
        'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian', 'Japanese',
        'Chinese', 'Korean', 'Arabic', 'Hindi', 'Dutch', 'Swedish', 'Greek', 'Turkish',
        'Polish', 'Vietnamese', 'Thai', 'Indonesian', 'Hebrew', 'Danish', 'Finnish',
        'Norwegian', 'Czech', 'Hungarian', 'Romanian', 'Ukrainian', 'Bulgarian', 'Croatian'
      ];
      
      // Sort languages alphabetically
      languages.sort();
      
      // Add English at the top for translating back
      languages.unshift('English');
      
      // Show language selection UI
      const targetLanguage = await this.showLanguageSelectionUI(
        'Select target language for translation:',
        languages,
        'English'
      );
      
      if (!targetLanguage) return; // User cancelled
      
      // Call the feature manager's translate method
      await this.featureManager.translateText(targetLanguage);
    } catch (error) {
      console.error('Error showing translate dialog:', error);
      this.uiManager.showStatus(`Error: ${error.message}`, true);
    }
  }
}
