/* Destress Mode Enhanced Styles */

/* Main Dialog */
.destress-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.destress-content {
  background-color: var(--bg-dark);
  background-image:
    radial-gradient(circle at 15% 85%, rgba(0, 216, 224, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(0, 166, 192, 0.08) 0%, transparent 50%),
    linear-gradient(to bottom, rgba(10, 15, 20, 0.95), rgba(5, 10, 15, 0.98));
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 420px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 166, 192, 0.2);
  border: 1px solid rgba(0, 216, 224, 0.15);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.destress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
  background: linear-gradient(135deg, rgba(0, 166, 192, 0.9), rgba(0, 216, 224, 0.8));
  position: relative;
  overflow: hidden;
}

.destress-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  z-index: 0;
}

.destress-header h3 {
  font-size: 1.3rem;
  color: var(--white);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 600;
}

.destress-close-btn {
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: var(--white);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.destress-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.destress-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) var(--bg-dark);
}

/* Tabs */
.destress-tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
  margin-bottom: 16px;
  margin-top: -5px;
  padding-bottom: 2px;
}

.destress-tab {
  background: transparent;
  border: none;
  color: var(--text-color);
  padding: 10px 12px;
  cursor: pointer;
  font-size: 0.9rem;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  flex: 1;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.destress-tab.active {
  color: var(--primary-light);
  border-bottom: 2px solid var(--primary-light);
  font-weight: 500;
}

.destress-tab:hover {
  background-color: rgba(0, 216, 224, 0.05);
}

.destress-tab::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--primary-light);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.destress-tab:hover::before {
  width: 80%;
}

.destress-tab-content {
  display: none;
}

.destress-tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-out;
}

/* Technique Cards */
.technique-card {
  background-color: rgba(10, 15, 20, 0.6);
  border-radius: var(--border-radius);
  padding: 24px;
  margin-bottom: 18px;
  border: 1px solid rgba(0, 216, 224, 0.1);
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.technique-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-dark), var(--primary-light));
  opacity: 0.8;
}

.technique-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25), 0 0 15px rgba(0, 216, 224, 0.1);
  border-color: rgba(0, 216, 224, 0.2);
}

/* Type-specific styling */
.breathing-card::before {
  background: linear-gradient(90deg, #00a6c0, #48d7ce);
}

.meditation-card::before {
  background: linear-gradient(90deg, #9c27b0, #ba68c8);
}

.physical-card::before {
  background: linear-gradient(90deg, #f44336, #ff9800);
}

.mindfulness-card::before {
  background: linear-gradient(90deg, #4caf50, #8bc34a);
}

.technique-icon {
  font-size: 2.8rem;
  margin-bottom: 18px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
  background: rgba(0, 166, 192, 0.1);
  color: var(--primary-light);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15), 0 0 10px rgba(0, 216, 224, 0.2);
  animation: gentle-float 3s ease-in-out infinite alternate;
}

@keyframes gentle-float {
  0% { transform: translateY(0); }
  100% { transform: translateY(-5px); }
}

.breathing-card .technique-icon {
  color: #48d7ce;
  background: rgba(72, 215, 206, 0.1);
}

.meditation-card .technique-icon {
  color: #ba68c8;
  background: rgba(186, 104, 200, 0.1);
}

.physical-card .technique-icon {
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
}

.mindfulness-card .technique-icon {
  color: #8bc34a;
  background: rgba(139, 195, 74, 0.1);
}

.technique-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 0.7rem;
  text-transform: uppercase;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(0, 166, 192, 0.15);
  color: var(--primary-light);
  font-weight: 500;
  letter-spacing: 0.5px;
}

.breathing-card .technique-badge {
  background: rgba(72, 215, 206, 0.15);
  color: #48d7ce;
}

.meditation-card .technique-badge {
  background: rgba(186, 104, 200, 0.15);
  color: #ba68c8;
}

.physical-card .technique-badge {
  background: rgba(255, 152, 0, 0.15);
  color: #ff9800;
}

.mindfulness-card .technique-badge {
  background: rgba(139, 195, 74, 0.15);
  color: #8bc34a;
}

.technique-card h4 {
  font-size: 1.3rem;
  margin-bottom: 8px;
  color: var(--white);
  font-weight: 600;
}

.technique-duration {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.technique-description {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 20px;
}

/* Technique Filter */
.technique-filter {
  margin-top: 20px;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
}

.filter-btn {
  background: rgba(10, 15, 20, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: rgba(0, 166, 192, 0.1);
  border-color: rgba(0, 216, 224, 0.2);
  color: var(--primary-light);
}

.filter-btn.active {
  background: rgba(0, 166, 192, 0.15);
  border-color: rgba(0, 216, 224, 0.3);
  color: var(--primary-light);
  font-weight: 500;
}

/* Timer */
.technique-timer {
  background: rgba(0, 166, 192, 0.1);
  color: var(--primary-light);
  font-size: 1.5rem;
  font-weight: 600;
  padding: 10px 15px;
  border-radius: var(--border-radius-sm);
  margin: 15px auto;
  width: 100px;
  text-align: center;
  border: 1px solid rgba(0, 216, 224, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.timer-done {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border-color: rgba(76, 175, 80, 0.2);
  animation: pulse 1s;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Action Buttons */
.destress-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.next-technique-btn, .timer-btn, .exercise-btn {
  background-color: rgba(10, 15, 20, 0.6);
  color: var(--text-color);
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: var(--border-radius-sm);
  padding: 10px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.next-technique-btn:hover, .timer-btn:hover, .exercise-btn:hover {
  background-color: rgba(0, 166, 192, 0.15);
  color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.timer-btn {
  background-color: rgba(0, 166, 192, 0.1);
  color: var(--primary-light);
}

/* Guided Exercises */
.guided-title {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: var(--white);
  font-weight: 600;
}

.guided-subtitle {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-bottom: 20px;
}

.guided-exercises {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.guided-exercise-card {
  background-color: rgba(10, 15, 20, 0.6);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid rgba(0, 216, 224, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.guided-exercise-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25), 0 0 15px rgba(0, 216, 224, 0.1);
  border-color: rgba(0, 216, 224, 0.2);
}

.exercise-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border-bottom: 1px solid rgba(0, 216, 224, 0.1);
  background: rgba(0, 166, 192, 0.05);
}

.breathing-exercise .exercise-header {
  background: linear-gradient(to right, rgba(0, 166, 192, 0.1), rgba(72, 215, 206, 0.05));
}

.meditation-exercise .exercise-header {
  background: linear-gradient(to right, rgba(156, 39, 176, 0.1), rgba(186, 104, 200, 0.05));
}

.exercise-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  background: rgba(0, 166, 192, 0.1);
  color: var(--primary-light);
}

.breathing-exercise .exercise-icon {
  color: #48d7ce;
  background: rgba(72, 215, 206, 0.1);
}

.meditation-exercise .exercise-icon {
  color: #ba68c8;
  background: rgba(186, 104, 200, 0.1);
}

.exercise-header h4 {
  font-size: 1.1rem;
  margin: 0;
  color: var(--white);
  font-weight: 500;
}

.exercise-content {
  padding: 20px;
}

.exercise-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 20px;
}

.exercise-controls {
  display: flex;
  justify-content: center;
}

/* Breathing Animation */
.breathing-animation {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.breathing-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 166, 192, 0.05);
  border: 2px solid rgba(72, 215, 206, 0.3);
  position: relative;
  transition: all 0.5s ease;
  box-shadow: 0 0 20px rgba(0, 166, 192, 0.1);
}

.breathing-circle::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid rgba(72, 215, 206, 0.1);
  opacity: 0.5;
}

.breathing-circle.inhale {
  transform: scale(1.2);
  border-color: rgba(72, 215, 206, 0.6);
  box-shadow: 0 0 30px rgba(72, 215, 206, 0.2);
}

.breathing-circle.hold {
  border-color: rgba(255, 193, 7, 0.6);
  box-shadow: 0 0 30px rgba(255, 193, 7, 0.2);
}

.breathing-circle.exhale {
  transform: scale(0.9);
  border-color: rgba(0, 166, 192, 0.3);
  box-shadow: 0 0 15px rgba(0, 166, 192, 0.1);
}

.breathing-circle.complete {
  border-color: rgba(76, 175, 80, 0.6);
  box-shadow: 0 0 30px rgba(76, 175, 80, 0.2);
  background: rgba(76, 175, 80, 0.05);
}

.breathing-instruction {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 5px;
}

.breathing-count {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-light);
}

.breathing-circle.inhale .breathing-count {
  color: #48d7ce;
}

.breathing-circle.hold .breathing-count {
  color: #ffc107;
}

.breathing-circle.exhale .breathing-count {
  color: var(--primary-color);
}

.breathing-circle.complete .breathing-count {
  color: #4caf50;
}

/* Meditation Progress */
.meditation-progress {
  margin-bottom: 20px;
}

.progress-bar {
  height: 8px;
  background-color: rgba(10, 15, 20, 0.4);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #9c27b0, #ba68c8);
  width: 0%;
  transition: width 1s linear;
  border-radius: 4px;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 10px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 4px 4px 0;
  opacity: 0;
}

.progress-fill.pulse::after {
  animation: progressPulse 1s ease-out;
}

@keyframes progressPulse {
  0% { opacity: 0; width: 0; }
  50% { opacity: 0.7; width: 15px; }
  100% { opacity: 0; width: 0; }
}

.progress-time {
  text-align: center;
  font-size: 1rem;
  color: #ba68c8;
}

.active-meditation {
  border-color: rgba(186, 104, 200, 0.4) !important;
  box-shadow: 0 0 20px rgba(186, 104, 200, 0.2) !important;
}

.completed-meditation {
  border-color: rgba(76, 175, 80, 0.4) !important;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.2) !important;
}

.completed-meditation .progress-fill {
  background: linear-gradient(to right, #4caf50, #8bc34a);
}

/* Recommendations */
.recommendations-title {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: var(--white);
  font-weight: 600;
}

.recommendations-subtitle {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-bottom: 20px;
}

.recommendations-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.recommendation-card {
  display: flex;
  background-color: rgba(10, 15, 20, 0.6);
  border-radius: var(--border-radius-sm);
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 216, 224, 0.1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.recommendation-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
  opacity: 0.8;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 216, 224, 0.2);
  background-color: rgba(0, 166, 192, 0.05);
}

.recommendation-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(0, 166, 192, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-light);
  font-size: 1.2rem;
  margin-right: 15px;
  flex-shrink: 0;
}

#music-rec .recommendation-icon {
  background: rgba(30, 215, 96, 0.1);
  color: #1ed760; /* Spotify green */
}

#youtube-rec .recommendation-icon {
  background: rgba(255, 0, 0, 0.1);
  color: #ff0000; /* YouTube red */
}

#book-rec .recommendation-icon {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800; /* Orange */
}

.recommendation-content {
  flex: 1;
}

.recommendation-content h5 {
  font-size: 1rem;
  margin: 0 0 5px 0;
  color: var(--white);
  font-weight: 500;
}

.recommendation-content p {
  font-size: 0.9rem;
  color: var(--text-color);
  margin: 0 0 10px 0;
  line-height: 1.5;
}

.recommendation-platform {
  font-size: 0.8rem;
  color: var(--primary-light);
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 10px;
  background-color: rgba(0, 166, 192, 0.1);
  border-radius: 20px;
  transition: all 0.2s ease;
}

#music-rec .recommendation-platform {
  color: #1ed760;
  background-color: rgba(30, 215, 96, 0.1);
}

#youtube-rec .recommendation-platform {
  color: #ff0000;
  background-color: rgba(255, 0, 0, 0.1);
}

#book-rec .recommendation-platform {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.recommendation-card:hover .recommendation-platform {
  background-color: rgba(0, 166, 192, 0.2);
}
