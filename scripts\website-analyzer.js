'use strict';

/**
 * Website Analyzer class for analyzing websites and providing SEO, accessibility, and performance insights
 */
class WebsiteAnalyzer {
  /**
   * Initialize the Website Analyzer
   * @param {APIManager} apiManager - The API manager instance
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(apiManager, uiManager) {
    this.apiManager = apiManager;
    this.uiManager = uiManager;
    this.analysisResults = null;
  }

  /**
   * Analyze the current website
   * @param {Object} options - Analysis options
   * @param {Array<string>} [options.categories] - Specific categories to analyze (e.g., ['seo', 'accessibility'])
   * @param {boolean} [options.detailed] - Whether to perform a detailed analysis
   * @returns {Promise<Object>} - The analysis results
   */
  async analyzeWebsite(options = {}) {
    try {
      // Get the page content
      const pageContent = await this.apiManager.getPageContent();

      // Extract the DOM content using a content script
      const domContent = await this.extractDOMContent();

      // Determine which categories to analyze
      const categories = options.categories || ['seo', 'accessibility', 'performance', 'security', 'content'];
      const isDetailedAnalysis = options.detailed || false;

      // Initialize results object
      const results = {
        url: pageContent.url,
        title: pageContent.title,
        timestamp: new Date().toISOString(),
        analysisType: isDetailedAnalysis ? 'detailed' : 'standard',
        categories: categories
      };

      // Analyze each requested category
      if (categories.includes('seo')) {
        results.seo = await this.analyzeSEO(pageContent, domContent, isDetailedAnalysis);
      }

      if (categories.includes('accessibility')) {
        results.accessibility = await this.analyzeAccessibility(pageContent, domContent, isDetailedAnalysis);
      }

      if (categories.includes('performance')) {
        results.performance = await this.analyzePerformance(pageContent, domContent, isDetailedAnalysis);
      }

      if (categories.includes('security')) {
        results.security = await this.analyzeSecurity(pageContent, domContent, isDetailedAnalysis);
      }

      if (categories.includes('content')) {
        results.content = await this.analyzeContent(pageContent, domContent, isDetailedAnalysis);
      }

      // Add competitor analysis if detailed analysis is requested
      if (isDetailedAnalysis) {
        try {
          results.competitors = await this.analyzeCompetitors(pageContent, domContent);
        } catch (competitorError) {
          console.error('Competitor analysis error:', competitorError);
          // Continue without competitor analysis
        }
      }

      // Add mobile-friendliness analysis if detailed analysis is requested
      if (isDetailedAnalysis && categories.includes('seo')) {
        try {
          results.mobileFriendliness = await this.analyzeMobileFriendliness(pageContent, domContent);
        } catch (mobileError) {
          console.error('Mobile-friendliness analysis error:', mobileError);
          // Continue without mobile-friendliness analysis
        }
      }

      this.analysisResults = results;
      return results;
    } catch (error) {
      console.error('Website analysis error:', error);
      throw new Error(`Failed to analyze website: ${error.message}`);
    }
  }

  /**
   * Analyze competitors for the current website
   * @param {Object} pageContent - The page content
   * @param {Object} domContent - The DOM content
   * @returns {Promise<Object>} - Competitor analysis results
   */
  async analyzeCompetitors(pageContent, domContent) {
    const competitorResults = {
      score: 0,
      maxScore: 100,
      competitors: [],
      issues: [],
      recommendations: []
    };

    try {
      // Extract keywords from the page
      const keywords = this.extractKeywords(pageContent.content);

      if (keywords.length === 0) {
        competitorResults.issues.push({
          severity: 'medium',
          message: 'Could not extract keywords for competitor analysis'
        });
        competitorResults.recommendations.push({
          priority: 'medium',
          message: 'Add more specific keywords to your content to better identify competitors'
        });
        return competitorResults;
      }

      // Simulate finding competitors based on keywords
      // In a real implementation, this would use a search API or database
      competitorResults.competitors = [
        {
          name: 'Example Competitor 1',
          url: 'https://example.com/competitor1',
          keywordOverlap: '75%',
          strengths: ['Strong SEO', 'Good mobile experience'],
          weaknesses: ['Slow loading time', 'Poor accessibility']
        },
        {
          name: 'Example Competitor 2',
          url: 'https://example.com/competitor2',
          keywordOverlap: '60%',
          strengths: ['Fast loading time', 'Good content structure'],
          weaknesses: ['Poor SEO', 'Limited mobile support']
        }
      ];

      // Add recommendations based on competitor analysis
      competitorResults.recommendations.push({
        priority: 'medium',
        message: 'Analyze competitor websites to identify opportunities for improvement',
        implementation: 'Conduct a detailed competitor analysis focusing on their strengths and weaknesses'
      });

      competitorResults.score = 70; // Example score

      return competitorResults;
    } catch (error) {
      console.error('Competitor analysis error:', error);
      competitorResults.issues.push({
        severity: 'low',
        message: 'Error during competitor analysis: ' + error.message
      });
      competitorResults.score = 50;
      return competitorResults;
    }
  }

  /**
   * Analyze mobile-friendliness of the website
   * @param {Object} pageContent - The page content
   * @param {Object} domContent - The DOM content
   * @returns {Promise<Object>} - Mobile-friendliness analysis results
   */
  async analyzeMobileFriendliness(pageContent, domContent) {
    const mobileResults = {
      score: 0,
      maxScore: 100,
      issues: [],
      recommendations: []
    };

    try {
      // Check viewport meta tag
      const viewport = domContent.metaTags.find(tag =>
        tag.name === 'viewport'
      );

      if (!viewport || !viewport.content) {
        mobileResults.issues.push({
          severity: 'high',
          message: 'Missing viewport meta tag'
        });
        mobileResults.recommendations.push({
          priority: 'high',
          message: 'Add a viewport meta tag to make your page mobile-friendly',
          implementation: '<meta name="viewport" content="width=device-width, initial-scale=1.0">'
        });
      } else if (!viewport.content.includes('width=device-width') ||
                !viewport.content.includes('initial-scale=1')) {
        mobileResults.issues.push({
          severity: 'medium',
          message: 'Incomplete viewport meta tag'
        });
        mobileResults.recommendations.push({
          priority: 'medium',
          message: 'Update the viewport meta tag to include width=device-width and initial-scale=1',
          implementation: '<meta name="viewport" content="width=device-width, initial-scale=1.0">'
        });
      }

      // Check for mobile-specific CSS
      const hasMobileCSS = domContent.styles.some(style =>
        style.content && style.content.includes('@media') &&
        (style.content.includes('max-width') || style.content.includes('min-width'))
      );

      if (!hasMobileCSS) {
        mobileResults.issues.push({
          severity: 'medium',
          message: 'No mobile-specific CSS media queries detected'
        });
        mobileResults.recommendations.push({
          priority: 'medium',
          message: 'Add responsive design with CSS media queries',
          implementation: '@media (max-width: 768px) { /* Mobile styles */ }'
        });
      }

      // Check for touch-friendly elements
      const smallButtons = domContent.buttons && domContent.buttons.filter(button =>
        button.width < 40 || button.height < 40
      );

      if (smallButtons && smallButtons.length > 0) {
        mobileResults.issues.push({
          severity: 'medium',
          message: `${smallButtons.length} buttons may be too small for touch interaction`
        });
        mobileResults.recommendations.push({
          priority: 'medium',
          message: 'Ensure all interactive elements are at least 40px × 40px for touch screens',
          implementation: 'button, .clickable { min-width: 40px; min-height: 40px; }'
        });
      }

      // Calculate mobile score based on issues
      const highIssues = mobileResults.issues.filter(issue => issue.severity === 'high').length;
      const mediumIssues = mobileResults.issues.filter(issue => issue.severity === 'medium').length;
      const lowIssues = mobileResults.issues.filter(issue => issue.severity === 'low').length;

      mobileResults.score = Math.max(0, 100 - (highIssues * 15) - (mediumIssues * 10) - (lowIssues * 5));

      return mobileResults;
    } catch (error) {
      console.error('Mobile-friendliness analysis error:', error);
      mobileResults.issues.push({
        severity: 'low',
        message: 'Error during mobile-friendliness analysis: ' + error.message
      });
      mobileResults.score = 50;
      return mobileResults;
    }
  }

  /**
   * Extract keywords from content
   * @param {string} content - The content to extract keywords from
   * @returns {Array<string>} - Extracted keywords
   */
  extractKeywords(content) {
    if (!content) return [];

    // Simple keyword extraction (this is a basic implementation)
    const words = content.toLowerCase().split(/\W+/).filter(word =>
      word.length > 3 &&
      !['this', 'that', 'with', 'from', 'have', 'were', 'they', 'will', 'what', 'when', 'where', 'which'].includes(word)
    );

    // Count word frequency
    const wordFrequency = {};
    words.forEach(word => {
      wordFrequency[word] = (wordFrequency[word] || 0) + 1;
    });

    // Sort by frequency and get top keywords
    const sortedWords = Object.entries(wordFrequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);

    return sortedWords;
  }

  /**
   * Extract detailed DOM content using a content script
   * @returns {Promise<Object>} - The DOM content
   */
  async extractDOMContent() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // Execute a script to extract DOM information
      const result = await chrome.tabs.sendMessage(tab.id, {
        action: 'extractDOMContent'
      });

      if (!result || !result.success) {
        throw new Error('Failed to extract DOM content');
      }

      return result.content;
    } catch (error) {
      console.error('DOM extraction error:', error);

      // Try to inject the analyzer content script if it's not available
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content/analyzer-content.js']
        });

        // Wait a moment for the script to initialize
        await new Promise(resolve => setTimeout(resolve, 500));

        // Try again after injection
        const retryResult = await chrome.tabs.sendMessage(tab.id, {
          action: 'extractDOMContent'
        });

        if (!retryResult || !retryResult.success) {
          throw new Error('Failed to extract DOM content after script injection');
        }

        return retryResult.content;
      } catch (injectionError) {
        console.error('Analyzer script injection failed:', injectionError);
        // Return a minimal fallback object
        return {
          metaTags: [],
          headings: [],
          images: [],
          links: [],
          scripts: [],
          styles: []
        };
      }
    }
  }

  /**
   * Analyze SEO aspects of the website
   * @param {Object} pageContent - The page content
   * @param {Object} domContent - The DOM content
   * @param {boolean} [isDetailedAnalysis=false] - Whether to perform a detailed analysis
   * @returns {Object} - SEO analysis results
   */
  async analyzeSEO(pageContent, domContent, isDetailedAnalysis = false) {
    const seoResults = {
      score: 0,
      maxScore: 100,
      issues: [],
      recommendations: [],
      details: {
        titleAnalysis: {},
        metaTagsAnalysis: {},
        headingsAnalysis: {},
        contentAnalysis: {},
        linksAnalysis: {},
        imagesAnalysis: {},
        structuredDataAnalysis: {},
        keywordAnalysis: {},
        mobileAnalysis: {}
      }
    };

    // --- Title Analysis ---
    seoResults.details.titleAnalysis.value = pageContent.title || '';

    if (!pageContent.title || pageContent.title.length < 10) {
      seoResults.issues.push({
        severity: 'high',
        category: 'title',
        message: 'Page title is missing or too short (less than 10 characters)'
      });
      seoResults.recommendations.push({
        priority: 'high',
        category: 'title',
        message: 'Add a descriptive page title between 50-60 characters that includes your primary keyword',
        implementation: '<title>Your Primary Keyword - Your Brand Name</title>'
      });
      seoResults.details.titleAnalysis.status = 'missing_or_short';
      seoResults.details.titleAnalysis.length = pageContent.title ? pageContent.title.length : 0;
    } else if (pageContent.title.length > 60) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'title',
        message: 'Page title is too long (more than 60 characters)'
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'title',
        message: 'Shorten the page title to 50-60 characters while keeping your primary keyword near the beginning',
        implementation: '<title>Primary Keyword | Brand Name</title>'
      });
      seoResults.details.titleAnalysis.status = 'too_long';
      seoResults.details.titleAnalysis.length = pageContent.title.length;
    } else {
      seoResults.details.titleAnalysis.status = 'good';
      seoResults.details.titleAnalysis.length = pageContent.title.length;
    }

    // --- Meta Tags Analysis ---
    seoResults.details.metaTagsAnalysis.tags = domContent.metaTags || [];

    // Check meta description
    const metaDescription = domContent.metaTags.find(tag =>
      tag.name === 'description' || tag.property === 'og:description'
    );

    if (!metaDescription || !metaDescription.content) {
      seoResults.issues.push({
        severity: 'high',
        category: 'meta_tags',
        message: 'Meta description is missing'
      });
      seoResults.recommendations.push({
        priority: 'high',
        category: 'meta_tags',
        message: 'Add a meta description tag with a concise page summary that includes your primary keyword',
        implementation: '<meta name="description" content="Your compelling description with primary keyword (120-158 characters)">'
      });
      seoResults.details.metaTagsAnalysis.description = {
        status: 'missing'
      };
    } else if (metaDescription.content.length < 50) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'meta_tags',
        message: 'Meta description is too short (less than 50 characters)'
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'meta_tags',
        message: 'Expand the meta description to 120-158 characters and include your primary keyword',
        implementation: '<meta name="description" content="Your compelling description with primary keyword (120-158 characters)">'
      });
      seoResults.details.metaTagsAnalysis.description = {
        status: 'too_short',
        length: metaDescription.content.length,
        value: metaDescription.content
      };
    } else if (metaDescription.content.length > 158) {
      seoResults.issues.push({
        severity: 'low',
        category: 'meta_tags',
        message: 'Meta description is too long (more than 158 characters)'
      });
      seoResults.recommendations.push({
        priority: 'low',
        category: 'meta_tags',
        message: 'Shorten the meta description to 158 characters or less while keeping your primary keyword',
        implementation: '<meta name="description" content="Your compelling description with primary keyword (120-158 characters)">'
      });
      seoResults.details.metaTagsAnalysis.description = {
        status: 'too_long',
        length: metaDescription.content.length,
        value: metaDescription.content
      };
    } else {
      seoResults.details.metaTagsAnalysis.description = {
        status: 'good',
        length: metaDescription.content.length,
        value: metaDescription.content
      };
    }

    // Check for canonical URL
    const canonical = domContent.metaTags.find(tag =>
      tag.rel === 'canonical'
    );

    if (!canonical || !canonical.href) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'meta_tags',
        message: 'No canonical URL specified'
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'meta_tags',
        message: 'Add a canonical URL tag to prevent duplicate content issues',
        implementation: '<link rel="canonical" href="https://www.example.com/your-page">'
      });
      seoResults.details.metaTagsAnalysis.canonical = {
        status: 'missing'
      };
    } else {
      seoResults.details.metaTagsAnalysis.canonical = {
        status: 'good',
        value: canonical.href
      };
    }

    // Check for Open Graph tags
    const ogTags = domContent.metaTags.filter(tag =>
      tag.property && tag.property.startsWith('og:')
    );

    if (ogTags.length < 3) {
      seoResults.issues.push({
        severity: 'low',
        category: 'meta_tags',
        message: 'Missing or incomplete Open Graph tags for social sharing'
      });
      seoResults.recommendations.push({
        priority: 'low',
        category: 'meta_tags',
        message: 'Add Open Graph tags for better social media sharing',
        implementation: `<meta property="og:title" content="Your Page Title">
<meta property="og:description" content="Your page description">
<meta property="og:image" content="https://example.com/image.jpg">
<meta property="og:url" content="https://example.com/your-page">`
      });
      seoResults.details.metaTagsAnalysis.openGraph = {
        status: 'incomplete',
        count: ogTags.length,
        tags: ogTags
      };
    } else {
      seoResults.details.metaTagsAnalysis.openGraph = {
        status: 'good',
        count: ogTags.length,
        tags: ogTags
      };
    }

    // Check for Twitter Card tags
    const twitterTags = domContent.metaTags.filter(tag =>
      tag.name && tag.name.startsWith('twitter:')
    );

    if (twitterTags.length < 2) {
      seoResults.issues.push({
        severity: 'low',
        category: 'meta_tags',
        message: 'Missing or incomplete Twitter Card tags'
      });
      seoResults.recommendations.push({
        priority: 'low',
        category: 'meta_tags',
        message: 'Add Twitter Card tags for better Twitter sharing',
        implementation: `<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Your Page Title">
<meta name="twitter:description" content="Your page description">
<meta name="twitter:image" content="https://example.com/image.jpg">`
      });
      seoResults.details.metaTagsAnalysis.twitterCard = {
        status: 'incomplete',
        count: twitterTags.length,
        tags: twitterTags
      };
    } else {
      seoResults.details.metaTagsAnalysis.twitterCard = {
        status: 'good',
        count: twitterTags.length,
        tags: twitterTags
      };
    }

    // --- Headings Analysis ---
    seoResults.details.headingsAnalysis.headings = domContent.headings || [];

    // Check for H1 heading
    const h1Headings = domContent.headings.filter(h => h.level === 1 || h.level === 'h1');

    if (h1Headings.length === 0) {
      seoResults.issues.push({
        severity: 'high',
        category: 'headings',
        message: 'No H1 heading found on the page'
      });
      seoResults.recommendations.push({
        priority: 'high',
        category: 'headings',
        message: 'Add an H1 heading that includes your primary keyword',
        implementation: '<h1>Your Primary Keyword: Compelling Title</h1>'
      });
      seoResults.details.headingsAnalysis.h1 = {
        status: 'missing'
      };
    } else {
      seoResults.details.headingsAnalysis.h1 = {
        status: h1Headings.length === 1 ? 'good' : 'multiple',
        count: h1Headings.length,
        values: h1Headings.map(h => h.text)
      };

      // Check for multiple H1 headings
      if (h1Headings.length > 1) {
        seoResults.issues.push({
          severity: 'medium',
          category: 'headings',
          message: `Multiple H1 headings found (${h1Headings.length})`
        });
        seoResults.recommendations.push({
          priority: 'medium',
          category: 'headings',
          message: 'Use only one H1 heading per page that includes your primary keyword',
          implementation: 'Keep the most important H1 and change others to H2 or H3'
        });
      }
    }

    // Check heading distribution
    const h2Headings = domContent.headings.filter(h => h.level === 2 || h.level === 'h2');
    const h3Headings = domContent.headings.filter(h => h.level === 3 || h.level === 'h3');

    seoResults.details.headingsAnalysis.distribution = {
      h1: h1Headings.length,
      h2: h2Headings.length,
      h3: h3Headings.length,
      total: domContent.headings.length
    };

    if (h2Headings.length === 0 && domContent.headings.length > 1) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'headings',
        message: 'No H2 headings found on the page'
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'headings',
        message: 'Add H2 headings to structure your content and include secondary keywords',
        implementation: '<h2>Your Secondary Keyword: Section Title</h2>'
      });
    }

    // --- Links Analysis ---

    // Check for internal and external links
    const internalLinks = domContent.links.filter(link =>
      link.href && link.href.includes(new URL(pageContent.url).hostname)
    );

    const externalLinks = domContent.links.filter(link =>
      link.href && !link.href.includes(new URL(pageContent.url).hostname) &&
      link.href.startsWith('http')
    );

    seoResults.details.linksAnalysis = {
      internal: {
        count: internalLinks.length,
        links: internalLinks.slice(0, 10) // Limit to first 10 for brevity
      },
      external: {
        count: externalLinks.length,
        links: externalLinks.slice(0, 10) // Limit to first 10 for brevity
      }
    };

    if (internalLinks.length < 3 && domContent.links.length > 0) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'links',
        message: 'Few internal links (less than 3)'
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'links',
        message: 'Add more internal links to improve site structure and user navigation',
        implementation: 'Link to related pages on your website using descriptive anchor text'
      });
    }

    // Check for empty or generic anchor text
    const poorAnchorTextLinks = domContent.links.filter(link =>
      !link.text || link.text.trim() === '' ||
      ['click here', 'read more', 'learn more', 'more', 'link'].includes(link.text.toLowerCase().trim())
    );

    if (poorAnchorTextLinks.length > 0) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'links',
        message: `${poorAnchorTextLinks.length} links with empty or generic anchor text`
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'links',
        message: 'Use descriptive anchor text that includes relevant keywords',
        implementation: 'Instead of "Click here", use "Learn more about SEO best practices"'
      });
      seoResults.details.linksAnalysis.poorAnchorText = {
        count: poorAnchorTextLinks.length,
        examples: poorAnchorTextLinks.slice(0, 5).map(link => link.text || '[empty]')
      };
    }

    // --- Images Analysis ---

    // Check for image alt text (SEO perspective)
    const imagesWithoutAlt = domContent.images.filter(img => !img.alt);
    const imagesWithAlt = domContent.images.filter(img => img.alt);

    seoResults.details.imagesAnalysis = {
      total: domContent.images.length,
      withAlt: imagesWithAlt.length,
      withoutAlt: imagesWithoutAlt.length,
      images: domContent.images.slice(0, 10) // Limit to first 10 for brevity
    };

    if (imagesWithoutAlt.length > 0) {
      seoResults.issues.push({
        severity: 'high',
        category: 'images',
        message: `${imagesWithoutAlt.length} images missing alt text`
      });
      seoResults.recommendations.push({
        priority: 'high',
        category: 'images',
        message: 'Add descriptive alt text to all images for better SEO and accessibility',
        implementation: '<img src="image.jpg" alt="Descriptive text with keywords">'
      });
    }

    // --- Mobile Friendliness Analysis ---

    // Check for viewport meta tag
    const viewport = domContent.metaTags.find(tag =>
      tag.name === 'viewport'
    );

    if (!viewport || !viewport.content) {
      seoResults.issues.push({
        severity: 'high',
        category: 'mobile',
        message: 'No mobile viewport meta tag found'
      });
      seoResults.recommendations.push({
        priority: 'high',
        category: 'mobile',
        message: 'Add a viewport meta tag to make your page mobile-friendly',
        implementation: '<meta name="viewport" content="width=device-width, initial-scale=1.0">'
      });
      seoResults.details.mobileAnalysis = {
        viewport: {
          status: 'missing'
        }
      };
    } else if (!viewport.content.includes('width=device-width') ||
               !viewport.content.includes('initial-scale=1')) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'mobile',
        message: 'Incomplete viewport meta tag'
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'mobile',
        message: 'Update the viewport meta tag to include width=device-width and initial-scale=1',
        implementation: '<meta name="viewport" content="width=device-width, initial-scale=1.0">'
      });
      seoResults.details.mobileAnalysis = {
        viewport: {
          status: 'incomplete',
          value: viewport.content
        }
      };
    } else {
      seoResults.details.mobileAnalysis = {
        viewport: {
          status: 'good',
          value: viewport.content
        }
      };
    }

    // --- Structured Data Analysis ---

    // Check for structured data
    const structuredData = domContent.metaTags.filter(tag =>
      tag.type === 'application/ld+json'
    );

    seoResults.details.structuredDataAnalysis = {
      count: structuredData.length,
      items: structuredData
    };

    if (structuredData.length === 0) {
      seoResults.issues.push({
        severity: 'medium',
        category: 'structured_data',
        message: 'No structured data (Schema.org) found'
      });
      seoResults.recommendations.push({
        priority: 'medium',
        category: 'structured_data',
        message: 'Add structured data to enhance search results with rich snippets',
        implementation: `<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Page Title",
  "description": "Page description"
}
</script>`
      });
      seoResults.details.structuredDataAnalysis.status = 'missing';
    } else {
      seoResults.details.structuredDataAnalysis.status = 'found';
    }

    // --- Keyword Analysis ---

    // Simple keyword extraction
    const content = pageContent.content || '';
    const words = content.toLowerCase().split(/\W+/).filter(word =>
      word.length > 3 &&
      !['this', 'that', 'with', 'from', 'have', 'were', 'they', 'will', 'what', 'when', 'where', 'which'].includes(word)
    );

    // Count word frequency
    const wordFrequency = {};
    words.forEach(word => {
      wordFrequency[word] = (wordFrequency[word] || 0) + 1;
    });

    // Sort by frequency
    const sortedWords = Object.entries(wordFrequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    seoResults.details.keywordAnalysis = {
      potentialKeywords: sortedWords.map(([word, count]) => ({
        word,
        count,
        density: (count / words.length * 100).toFixed(2) + '%'
      }))
    };

    // Check if the most frequent words appear in title and headings
    if (sortedWords.length > 0) {
      const topKeywords = sortedWords.slice(0, 3).map(([word]) => word);
      const titleLower = pageContent.title ? pageContent.title.toLowerCase() : '';
      const h1Text = h1Headings.length > 0 ? h1Headings[0].text.toLowerCase() : '';

      const keywordsInTitle = topKeywords.filter(keyword => titleLower.includes(keyword));
      const keywordsInH1 = topKeywords.filter(keyword => h1Text.includes(keyword));

      seoResults.details.keywordAnalysis.keywordsInTitle = keywordsInTitle;
      seoResults.details.keywordAnalysis.keywordsInH1 = keywordsInH1;

      if (keywordsInTitle.length === 0 && pageContent.title) {
        seoResults.issues.push({
          severity: 'medium',
          category: 'keywords',
          message: 'Top keywords not found in page title'
        });
        seoResults.recommendations.push({
          priority: 'medium',
          category: 'keywords',
          message: `Include your primary keywords (${topKeywords.join(', ')}) in the page title`,
          implementation: `<title>Your Page Title with ${topKeywords[0]} and ${topKeywords[1]}</title>`
        });
      }

      if (keywordsInH1.length === 0 && h1Text) {
        seoResults.issues.push({
          severity: 'medium',
          category: 'keywords',
          message: 'Top keywords not found in H1 heading'
        });
        seoResults.recommendations.push({
          priority: 'medium',
          category: 'keywords',
          message: `Include your primary keywords (${topKeywords.join(', ')}) in the H1 heading`,
          implementation: `<h1>Your H1 Heading with ${topKeywords[0]} and ${topKeywords[1]}</h1>`
        });
      }
    }

    // Calculate SEO score based on issues
    const highIssues = seoResults.issues.filter(issue => issue.severity === 'high').length;
    const mediumIssues = seoResults.issues.filter(issue => issue.severity === 'medium').length;
    const lowIssues = seoResults.issues.filter(issue => issue.severity === 'low').length;

    seoResults.score = Math.max(0, 100 - (highIssues * 15) - (mediumIssues * 7) - (lowIssues * 3));

    return seoResults;
  }

  /**
   * Analyze accessibility aspects of the website
   * @param {Object} pageContent - The page content
   * @param {Object} domContent - The DOM content
   * @param {boolean} [isDetailedAnalysis=false] - Whether to perform a detailed analysis
   * @returns {Object} - Accessibility analysis results
   */
  async analyzeAccessibility(pageContent, domContent, isDetailedAnalysis = false) {
    const accessibilityResults = {
      score: 0,
      maxScore: 100,
      issues: [],
      recommendations: []
    };

    // Check images for alt text
    const imagesWithoutAlt = domContent.images.filter(img => !img.alt);
    if (imagesWithoutAlt.length > 0) {
      accessibilityResults.issues.push({
        severity: 'high',
        message: `${imagesWithoutAlt.length} images missing alt text`
      });
      accessibilityResults.recommendations.push('Add descriptive alt text to all images');
    }

    // Check for ARIA attributes
    const hasAriaAttributes = domContent.ariaAttributes && domContent.ariaAttributes.length > 0;
    if (!hasAriaAttributes) {
      accessibilityResults.issues.push({
        severity: 'medium',
        message: 'No ARIA attributes found on the page'
      });
      accessibilityResults.recommendations.push('Add appropriate ARIA attributes to improve accessibility');
    }

    // Check for form labels
    if (domContent.forms && domContent.forms.length > 0) {
      const formsWithoutLabels = domContent.forms.filter(form =>
        form.inputs.some(input => !input.hasLabel)
      );

      if (formsWithoutLabels.length > 0) {
        accessibilityResults.issues.push({
          severity: 'high',
          message: 'Form inputs found without associated labels'
        });
        accessibilityResults.recommendations.push('Add labels to all form inputs');
      }
    }

    // Check for color contrast (simplified check)
    if (domContent.contrastIssues && domContent.contrastIssues.length > 0) {
      accessibilityResults.issues.push({
        severity: 'high',
        message: `${domContent.contrastIssues.length} potential color contrast issues detected`
      });
      accessibilityResults.recommendations.push('Ensure sufficient color contrast between text and background');
    }

    // Check for keyboard navigation
    if (domContent.keyboardNavIssues && domContent.keyboardNavIssues.length > 0) {
      accessibilityResults.issues.push({
        severity: 'high',
        message: 'Potential keyboard navigation issues detected'
      });
      accessibilityResults.recommendations.push('Ensure all interactive elements are keyboard accessible');
    }

    // Calculate accessibility score based on issues
    const highIssues = accessibilityResults.issues.filter(issue => issue.severity === 'high').length;
    const mediumIssues = accessibilityResults.issues.filter(issue => issue.severity === 'medium').length;
    const lowIssues = accessibilityResults.issues.filter(issue => issue.severity === 'low').length;

    accessibilityResults.score = Math.max(0, 100 - (highIssues * 15) - (mediumIssues * 10) - (lowIssues * 5));

    return accessibilityResults;
  }

  /**
   * Analyze performance aspects of the website
   * @param {Object} pageContent - The page content
   * @param {Object} domContent - The DOM content
   * @param {boolean} [isDetailedAnalysis=false] - Whether to perform a detailed analysis
   * @returns {Object} - Performance analysis results
   */
  async analyzePerformance(pageContent, domContent, isDetailedAnalysis = false) {
    const performanceResults = {
      score: 0,
      maxScore: 100,
      issues: [],
      recommendations: []
    };

    // Check number of scripts
    if (domContent.scripts && domContent.scripts.length > 15) {
      performanceResults.issues.push({
        severity: 'medium',
        message: `High number of script files (${domContent.scripts.length})`
      });
      performanceResults.recommendations.push('Reduce the number of script files by combining them');
    }

    // Check number of stylesheets
    if (domContent.styles && domContent.styles.length > 10) {
      performanceResults.issues.push({
        severity: 'medium',
        message: `High number of stylesheet files (${domContent.styles.length})`
      });
      performanceResults.recommendations.push('Reduce the number of stylesheet files by combining them');
    }

    // Check image optimization
    const largeImages = domContent.images.filter(img =>
      img.size && img.size > 200 * 1024 // 200KB
    );

    if (largeImages.length > 0) {
      performanceResults.issues.push({
        severity: 'medium',
        message: `${largeImages.length} large images detected (>200KB)`
      });
      performanceResults.recommendations.push('Optimize large images to improve page load time');
    }

    // Check for render-blocking resources
    if (domContent.renderBlockingResources && domContent.renderBlockingResources.length > 0) {
      performanceResults.issues.push({
        severity: 'high',
        message: `${domContent.renderBlockingResources.length} render-blocking resources detected`
      });
      performanceResults.recommendations.push('Eliminate render-blocking resources or load them asynchronously');
    }

    // Check page size (if available)
    if (domContent.pageSize && domContent.pageSize > 3 * 1024 * 1024) { // 3MB
      performanceResults.issues.push({
        severity: 'high',
        message: `Large page size (${Math.round(domContent.pageSize / (1024 * 1024))}MB)`
      });
      performanceResults.recommendations.push('Reduce page size to improve load time');
    }

    // Calculate performance score based on issues
    const highIssues = performanceResults.issues.filter(issue => issue.severity === 'high').length;
    const mediumIssues = performanceResults.issues.filter(issue => issue.severity === 'medium').length;
    const lowIssues = performanceResults.issues.filter(issue => issue.severity === 'low').length;

    performanceResults.score = Math.max(0, 100 - (highIssues * 15) - (mediumIssues * 10) - (lowIssues * 5));

    return performanceResults;
  }

  /**
   * Analyze security aspects of the website
   * @param {Object} pageContent - The page content
   * @param {Object} domContent - The DOM content
   * @param {boolean} [isDetailedAnalysis=false] - Whether to perform a detailed analysis
   * @returns {Object} - Security analysis results
   */
  async analyzeSecurity(pageContent, domContent, isDetailedAnalysis = false) {
    const securityResults = {
      score: 0,
      maxScore: 100,
      issues: [],
      recommendations: []
    };

    // Check HTTPS
    if (pageContent.url && !pageContent.url.startsWith('https://')) {
      securityResults.issues.push({
        severity: 'high',
        message: 'Website not using HTTPS'
      });
      securityResults.recommendations.push('Migrate to HTTPS to secure user data and improve SEO');
    }

    // Check for security headers
    if (domContent.securityHeaders) {
      if (!domContent.securityHeaders.includes('Content-Security-Policy')) {
        securityResults.issues.push({
          severity: 'medium',
          message: 'Content Security Policy (CSP) header not found'
        });
        securityResults.recommendations.push('Implement a Content Security Policy to prevent XSS attacks');
      }

      if (!domContent.securityHeaders.includes('X-XSS-Protection')) {
        securityResults.issues.push({
          severity: 'medium',
          message: 'X-XSS-Protection header not found'
        });
        securityResults.recommendations.push('Add X-XSS-Protection header to mitigate XSS attacks');
      }

      if (!domContent.securityHeaders.includes('X-Frame-Options')) {
        securityResults.issues.push({
          severity: 'medium',
          message: 'X-Frame-Options header not found'
        });
        securityResults.recommendations.push('Add X-Frame-Options header to prevent clickjacking');
      }
    } else {
      securityResults.issues.push({
        severity: 'low',
        message: 'Security headers information not available'
      });
      securityResults.recommendations.push('Implement security headers to improve website security');
    }

    // Check for mixed content
    if (domContent.mixedContent && domContent.mixedContent.length > 0) {
      securityResults.issues.push({
        severity: 'high',
        message: `${domContent.mixedContent.length} mixed content issues detected`
      });
      securityResults.recommendations.push('Fix mixed content issues by ensuring all resources are loaded over HTTPS');
    }

    // Calculate security score based on issues
    const highIssues = securityResults.issues.filter(issue => issue.severity === 'high').length;
    const mediumIssues = securityResults.issues.filter(issue => issue.severity === 'medium').length;
    const lowIssues = securityResults.issues.filter(issue => issue.severity === 'low').length;

    securityResults.score = Math.max(0, 100 - (highIssues * 15) - (mediumIssues * 10) - (lowIssues * 5));

    return securityResults;
  }

  /**
   * Analyze content quality of the website
   * @param {Object} pageContent - The page content
   * @param {Object} domContent - The DOM content
   * @param {boolean} [isDetailedAnalysis=false] - Whether to perform a detailed analysis
   * @returns {Object} - Content analysis results
   */
  async analyzeContent(pageContent, domContent, isDetailedAnalysis = false) {
    const contentResults = {
      score: 0,
      maxScore: 100,
      issues: [],
      recommendations: []
    };

    // Check content length
    const contentLength = pageContent.content ? pageContent.content.length : 0;

    if (contentLength < 300) {
      contentResults.issues.push({
        severity: 'high',
        message: 'Very little content on the page (less than 300 characters)'
      });
      contentResults.recommendations.push('Add more valuable content to improve SEO and user experience');
    } else if (contentLength < 1000) {
      contentResults.issues.push({
        severity: 'medium',
        message: 'Limited content on the page (less than 1000 characters)'
      });
      contentResults.recommendations.push('Consider adding more comprehensive content');
    }

    // Check heading structure
    if (domContent.headings) {
      const headingLevels = domContent.headings.map(h => h.level);
      const hasProperStructure = headingLevels.every((level, index, arr) =>
        index === 0 || level >= arr[index - 1] || level <= arr[index - 1] + 1
      );

      if (!hasProperStructure) {
        contentResults.issues.push({
          severity: 'medium',
          message: 'Heading structure is not properly organized'
        });
        contentResults.recommendations.push('Organize headings in a logical hierarchy (H1 → H2 → H3)');
      }
    }

    // Check for broken links
    if (domContent.brokenLinks && domContent.brokenLinks.length > 0) {
      contentResults.issues.push({
        severity: 'high',
        message: `${domContent.brokenLinks.length} broken links detected`
      });
      contentResults.recommendations.push('Fix broken links to improve user experience and SEO');
    }

    // Check for duplicate content
    if (domContent.duplicateContent && domContent.duplicateContent.length > 0) {
      contentResults.issues.push({
        severity: 'medium',
        message: 'Duplicate content detected on the page'
      });
      contentResults.recommendations.push('Remove or consolidate duplicate content');
    }

    // Check readability (simplified)
    if (pageContent.content) {
      const longSentences = pageContent.content.split('.').filter(s => s.trim().length > 150).length;

      if (longSentences > 5) {
        contentResults.issues.push({
          severity: 'low',
          message: `${longSentences} potentially long sentences detected`
        });
        contentResults.recommendations.push('Improve readability by shortening long sentences');
      }
    }

    // Calculate content score based on issues
    const highIssues = contentResults.issues.filter(issue => issue.severity === 'high').length;
    const mediumIssues = contentResults.issues.filter(issue => issue.severity === 'medium').length;
    const lowIssues = contentResults.issues.filter(issue => issue.severity === 'low').length;

    contentResults.score = Math.max(0, 100 - (highIssues * 15) - (mediumIssues * 10) - (lowIssues * 5));

    return contentResults;
  }

  /**
   * Generate a comprehensive analysis report
   * @param {Object} results - The analysis results
   * @returns {string} - Formatted analysis report
   */
  generateReport(results = null) {
    const analysisResults = results || this.analysisResults;

    if (!analysisResults) {
      return 'No analysis results available. Please run the analysis first.';
    }

    // Calculate overall score
    const overallScore = Math.round(
      (analysisResults.seo.score +
       analysisResults.accessibility.score +
       analysisResults.performance.score +
       analysisResults.security.score +
       analysisResults.content.score) / 5
    );

    // Determine overall health status
    let healthStatus = '🟢 Excellent';
    if (overallScore < 50) {
      healthStatus = '🔴 Critical';
    } else if (overallScore < 70) {
      healthStatus = '🟠 Needs Improvement';
    } else if (overallScore < 90) {
      healthStatus = '🟡 Good';
    }

    // Format the report with visual elements
    let report = `# 📊 Website Analysis Report\n\n`;

    // Add visual score card
    report += `## 📈 Website Health: ${healthStatus}\n\n`;
    report += this.generateScoreCard(overallScore, analysisResults);

    // Add overview section
    report += `## 🔍 Overview\n\n`;
    report += `**URL:** ${analysisResults.url}\n`;
    report += `**Title:** ${analysisResults.title}\n`;
    report += `**Analysis Date:** ${new Date(analysisResults.timestamp).toLocaleString()}\n\n`;

    // Add priority issues section
    report += `## 🚨 Priority Issues\n\n`;

    // Get all high priority issues across categories
    const allHighIssues = [
      ...this.getIssuesWithCategory(analysisResults.seo.issues, 'SEO', 'high'),
      ...this.getIssuesWithCategory(analysisResults.accessibility.issues, 'Accessibility', 'high'),
      ...this.getIssuesWithCategory(analysisResults.performance.issues, 'Performance', 'high'),
      ...this.getIssuesWithCategory(analysisResults.security.issues, 'Security', 'high'),
      ...this.getIssuesWithCategory(analysisResults.content.issues, 'Content', 'high')
    ];

    if (allHighIssues.length > 0) {
      report += `### 🔴 Critical Issues\n\n`;
      allHighIssues.forEach(issue => {
        report += `- **[${issue.category}]** ${issue.message}\n`;
      });
      report += `\n`;
    } else {
      report += `### 🔴 Critical Issues\n\n`;
      report += `✅ No critical issues found. Great job!\n\n`;
    }

    // Add SEO section with visual elements
    report += `## 🔍 SEO Analysis (${this.getScoreEmoji(analysisResults.seo.score)} ${analysisResults.seo.score}/100)\n\n`;
    report += this.formatEnhancedCategoryReport(analysisResults.seo, analysisResults.seo.details);

    // Add Accessibility section with visual elements
    report += `## ♿ Accessibility Analysis (${this.getScoreEmoji(analysisResults.accessibility.score)} ${analysisResults.accessibility.score}/100)\n\n`;
    report += this.formatEnhancedCategoryReport(analysisResults.accessibility);

    // Add Performance section with visual elements
    report += `## ⚡ Performance Analysis (${this.getScoreEmoji(analysisResults.performance.score)} ${analysisResults.performance.score}/100)\n\n`;
    report += this.formatEnhancedCategoryReport(analysisResults.performance);

    // Add Security section with visual elements
    report += `## 🔒 Security Analysis (${this.getScoreEmoji(analysisResults.security.score)} ${analysisResults.security.score}/100)\n\n`;
    report += this.formatEnhancedCategoryReport(analysisResults.security);

    // Add Content section with visual elements
    report += `## 📝 Content Analysis (${this.getScoreEmoji(analysisResults.content.score)} ${analysisResults.content.score}/100)\n\n`;
    report += this.formatEnhancedCategoryReport(analysisResults.content);

    // Add actionable recommendations section
    report += `## 🚀 Action Plan\n\n`;

    // Get prioritized recommendations
    const prioritizedRecommendations = this.getPrioritizedRecommendations(analysisResults);

    if (prioritizedRecommendations.high.length > 0) {
      report += `### 🔴 High Priority Actions\n\n`;
      prioritizedRecommendations.high.forEach(rec => {
        report += `- **[${rec.category || 'General'}]** ${rec.message || rec}\n`;
        if (rec.implementation) {
          report += `  \`\`\`html\n  ${rec.implementation}\n  \`\`\`\n`;
        }
      });
      report += `\n`;
    }

    if (prioritizedRecommendations.medium.length > 0) {
      report += `### 🟠 Medium Priority Actions\n\n`;
      prioritizedRecommendations.medium.forEach(rec => {
        report += `- **[${rec.category || 'General'}]** ${rec.message || rec}\n`;
        if (rec.implementation) {
          report += `  \`\`\`html\n  ${rec.implementation}\n  \`\`\`\n`;
        }
      });
      report += `\n`;
    }

    if (prioritizedRecommendations.low.length > 0) {
      report += `### 🟡 Low Priority Actions\n\n`;
      prioritizedRecommendations.low.slice(0, 5).forEach(rec => {
        report += `- **[${rec.category || 'General'}]** ${rec.message || rec}\n`;
      });
      report += `\n`;
    }

    // Add keyword analysis section if available
    if (analysisResults.seo.details && analysisResults.seo.details.keywordAnalysis) {
      report += `## 🔑 Keyword Analysis\n\n`;

      const keywordAnalysis = analysisResults.seo.details.keywordAnalysis;

      if (keywordAnalysis.potentialKeywords && keywordAnalysis.potentialKeywords.length > 0) {
        report += `### Top Keywords Detected\n\n`;
        report += `| Keyword | Occurrences | Density |\n`;
        report += `|---------|-------------|--------|\n`;

        keywordAnalysis.potentialKeywords.slice(0, 5).forEach(keyword => {
          report += `| ${keyword.word} | ${keyword.count} | ${keyword.density} |\n`;
        });

        report += `\n`;
      }
    }

    // Add mobile-friendliness section if available
    if (analysisResults.seo.details && analysisResults.seo.details.mobileAnalysis) {
      report += `## 📱 Mobile-Friendliness\n\n`;

      const mobileAnalysis = analysisResults.seo.details.mobileAnalysis;

      if (mobileAnalysis.viewport) {
        if (mobileAnalysis.viewport.status === 'good') {
          report += `✅ **Viewport:** Properly configured for mobile devices\n\n`;
        } else if (mobileAnalysis.viewport.status === 'incomplete') {
          report += `⚠️ **Viewport:** Incomplete configuration - \`${mobileAnalysis.viewport.value}\`\n\n`;
        } else {
          report += `❌ **Viewport:** Missing viewport meta tag\n\n`;
        }
      }
    }

    // Add summary and next steps
    report += `## 📋 Summary\n\n`;

    // Add overall assessment
    if (overallScore >= 90) {
      report += `🌟 **Excellent website!** Your site is performing very well across all categories. Focus on the few minor improvements to achieve perfection.\n\n`;
    } else if (overallScore >= 70) {
      report += `👍 **Good website!** Your site is performing well in most areas, but there are some opportunities for improvement to enhance user experience and search rankings.\n\n`;
    } else if (overallScore >= 50) {
      report += `⚠️ **Needs improvement.** Your website has several issues that should be addressed to improve user experience and search rankings.\n\n`;
    } else {
      report += `🚨 **Critical issues detected.** Your website has significant problems that need immediate attention to improve functionality, user experience, and search rankings.\n\n`;
    }

    // Add export options note
    report += `---\n\n`;
    report += `*This report was generated by Browzy AI Website Analyzer. For a more detailed analysis, consider exporting this report or running specific category analyses.*\n\n`;

    return report;
  }

  /**
   * Format a category report section
   * @param {Object} categoryResults - The category results
   * @returns {string} - Formatted category report
   */
  formatCategoryReport(categoryResults) {
    return this.formatEnhancedCategoryReport(categoryResults);
  }

  /**
   * Generate a visual score card
   * @param {number} overallScore - The overall score
   * @param {Object} analysisResults - The analysis results
   * @returns {string} - Formatted score card
   */
  generateScoreCard(overallScore, analysisResults) {
    let scoreCard = `| Category | Score | Rating |\n`;
    scoreCard += `|----------|-------|--------|\n`;
    scoreCard += `| **Overall** | **${overallScore}/100** | ${this.getScoreEmoji(overallScore)} |\n`;
    scoreCard += `| SEO | ${analysisResults.seo.score}/100 | ${this.getScoreEmoji(analysisResults.seo.score)} |\n`;
    scoreCard += `| Accessibility | ${analysisResults.accessibility.score}/100 | ${this.getScoreEmoji(analysisResults.accessibility.score)} |\n`;
    scoreCard += `| Performance | ${analysisResults.performance.score}/100 | ${this.getScoreEmoji(analysisResults.performance.score)} |\n`;
    scoreCard += `| Security | ${analysisResults.security.score}/100 | ${this.getScoreEmoji(analysisResults.security.score)} |\n`;
    scoreCard += `| Content | ${analysisResults.content.score}/100 | ${this.getScoreEmoji(analysisResults.content.score)} |\n\n`;

    return scoreCard;
  }

  /**
   * Get an emoji representing the score
   * @param {number} score - The score
   * @returns {string} - Emoji representing the score
   */
  getScoreEmoji(score) {
    if (score >= 90) return '🟢';
    if (score >= 70) return '🟡';
    if (score >= 50) return '🟠';
    return '🔴';
  }

  /**
   * Get issues with category information
   * @param {Array} issues - The issues
   * @param {string} categoryName - The category name
   * @param {string} severity - The severity to filter by
   * @returns {Array} - Issues with category information
   */
  getIssuesWithCategory(issues, categoryName, severity) {
    return issues
      .filter(issue => issue.severity === severity)
      .map(issue => ({
        ...issue,
        category: issue.category || categoryName
      }));
  }

  /**
   * Get prioritized recommendations
   * @param {Object} analysisResults - The analysis results
   * @returns {Object} - Prioritized recommendations
   */
  getPrioritizedRecommendations(analysisResults) {
    // Get all recommendations with priority information
    const allRecommendations = [
      ...(analysisResults.seo.recommendations || []),
      ...(analysisResults.accessibility.recommendations || []),
      ...(analysisResults.performance.recommendations || []),
      ...(analysisResults.security.recommendations || []),
      ...(analysisResults.content.recommendations || [])
    ];

    // Filter recommendations by priority
    const highPriority = allRecommendations.filter(rec =>
      (typeof rec === 'object' && rec.priority === 'high') ||
      (typeof rec === 'string' &&
       allRecommendations.filter(r => typeof r === 'object' && r.priority === 'high').some(r => r.message === rec))
    );

    const mediumPriority = allRecommendations.filter(rec =>
      (typeof rec === 'object' && rec.priority === 'medium') ||
      (typeof rec === 'string' &&
       allRecommendations.filter(r => typeof r === 'object' && r.priority === 'medium').some(r => r.message === rec))
    );

    const lowPriority = allRecommendations.filter(rec =>
      (typeof rec === 'object' && rec.priority === 'low') ||
      (typeof rec === 'string' &&
       allRecommendations.filter(r => typeof r === 'object' && r.priority === 'low').some(r => r.message === rec))
    );

    // For string recommendations that don't match any priority, put them in medium
    const unclassifiedRecs = allRecommendations.filter(rec =>
      typeof rec === 'string' &&
      !highPriority.includes(rec) &&
      !mediumPriority.includes(rec) &&
      !lowPriority.includes(rec)
    );

    // Remove duplicates
    const uniqueHigh = this.getUniqueRecommendations(highPriority);
    const uniqueMedium = this.getUniqueRecommendations([...mediumPriority, ...unclassifiedRecs]);
    const uniqueLow = this.getUniqueRecommendations(lowPriority);

    return {
      high: uniqueHigh,
      medium: uniqueMedium,
      low: uniqueLow
    };
  }

  /**
   * Get unique recommendations
   * @param {Array} recommendations - The recommendations
   * @returns {Array} - Unique recommendations
   */
  getUniqueRecommendations(recommendations) {
    const seen = new Set();
    return recommendations.filter(rec => {
      const message = typeof rec === 'string' ? rec : rec.message;
      if (seen.has(message)) {
        return false;
      }
      seen.add(message);
      return true;
    });
  }

  /**
   * Format an enhanced category report
   * @param {Object} categoryResults - The category results
   * @param {Object} details - Optional detailed results
   * @returns {string} - Formatted category report
   */
  formatEnhancedCategoryReport(categoryResults, details = null) {
    let report = `**Score:** ${categoryResults.score}/${categoryResults.maxScore}\n\n`;

    if (categoryResults.issues.length === 0) {
      report += `✅ **No issues found in this category. Great job!**\n\n`;
      return report;
    }

    // Group issues by severity
    const highIssues = categoryResults.issues.filter(issue => issue.severity === 'high');
    const mediumIssues = categoryResults.issues.filter(issue => issue.severity === 'medium');
    const lowIssues = categoryResults.issues.filter(issue => issue.severity === 'low');

    // Add issue summary
    report += `Found **${categoryResults.issues.length}** issues: `;
    report += `${highIssues.length} critical, ${mediumIssues.length} important, ${lowIssues.length} minor\n\n`;

    // Add high severity issues
    if (highIssues.length > 0) {
      report += `### 🔴 Critical Issues\n\n`;
      highIssues.forEach(issue => {
        const category = issue.category ? `[${issue.category.toUpperCase()}] ` : '';
        report += `- **${category}${issue.message}**\n`;
      });
      report += `\n`;
    }

    // Add medium severity issues
    if (mediumIssues.length > 0) {
      report += `### 🟠 Important Issues\n\n`;
      mediumIssues.forEach(issue => {
        const category = issue.category ? `[${issue.category.toUpperCase()}] ` : '';
        report += `- **${category}${issue.message}**\n`;
      });
      report += `\n`;
    }

    // Add low severity issues (limited to 3 for brevity)
    if (lowIssues.length > 0) {
      report += `### 🟡 Minor Issues\n\n`;
      lowIssues.slice(0, 3).forEach(issue => {
        const category = issue.category ? `[${issue.category.toUpperCase()}] ` : '';
        report += `- ${category}${issue.message}\n`;
      });

      if (lowIssues.length > 3) {
        report += `- *...and ${lowIssues.length - 3} more minor issues*\n`;
      }

      report += `\n`;
    }

    // Add recommendations
    if (categoryResults.recommendations.length > 0) {
      report += `### 💡 Recommendations\n\n`;
      categoryResults.recommendations.forEach(rec => {
        const message = typeof rec === 'string' ? rec : rec.message;
        report += `- ${message}\n`;
      });
      report += `\n`;
    }

    // Add detailed analysis if available
    if (details) {
      // Add title analysis if available
      if (details.titleAnalysis && Object.keys(details.titleAnalysis).length > 0) {
        report += `### 📝 Title Analysis\n\n`;
        report += `- **Current Title:** "${details.titleAnalysis.value || 'None'}"\n`;
        report += `- **Length:** ${details.titleAnalysis.length || 0} characters\n`;
        report += `- **Status:** ${this.formatStatus(details.titleAnalysis.status)}\n\n`;
      }

      // Add meta tags analysis if available
      if (details.metaTagsAnalysis && details.metaTagsAnalysis.description) {
        report += `### 📋 Meta Description\n\n`;
        if (details.metaTagsAnalysis.description.status === 'missing') {
          report += `- ❌ **Missing meta description**\n\n`;
        } else {
          report += `- **Current Description:** "${details.metaTagsAnalysis.description.value || 'None'}"\n`;
          report += `- **Length:** ${details.metaTagsAnalysis.description.length || 0} characters\n`;
          report += `- **Status:** ${this.formatStatus(details.metaTagsAnalysis.description.status)}\n\n`;
        }
      }

      // Add headings analysis if available
      if (details.headingsAnalysis && details.headingsAnalysis.distribution) {
        report += `### 📊 Headings Structure\n\n`;
        report += `- **H1:** ${details.headingsAnalysis.distribution.h1 || 0}\n`;
        report += `- **H2:** ${details.headingsAnalysis.distribution.h2 || 0}\n`;
        report += `- **H3:** ${details.headingsAnalysis.distribution.h3 || 0}\n`;
        report += `- **Total Headings:** ${details.headingsAnalysis.distribution.total || 0}\n\n`;
      }
    }

    return report;
  }

  /**
   * Format a status string into a more readable format with emoji
   * @param {string} status - The status string
   * @returns {string} - Formatted status
   */
  formatStatus(status) {
    if (!status) return 'Unknown';

    const statusMap = {
      'good': '✅ Good',
      'missing': '❌ Missing',
      'missing_or_short': '❌ Missing or Too Short',
      'too_short': '⚠️ Too Short',
      'too_long': '⚠️ Too Long',
      'incomplete': '⚠️ Incomplete',
      'multiple': '⚠️ Multiple (should be only one)',
      'found': '✅ Found',
      'not_found': '❌ Not Found',
      'improper': '⚠️ Improper Structure'
    };

    return statusMap[status] || status.replace(/_/g, ' ');
  }
}
