/**
 * Sidebar functionality for Browzy AI
 * This file handles the sidebar close button and related functionality
 */

// Add the pulse animation for the close button
const addPulseAnimation = () => {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    /* Make sure the button is always visible */
    #superCloseBtn {
      pointer-events: auto !important;
    }

    #superCloseBtn > div {
      animation: pulse 2s infinite;
    }
  `;
  document.head.appendChild(style);
};

// Initialize the sidebar mode
const initSidebarMode = () => {
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('sidebar') === 'true') {
    console.log('Initializing sidebar mode in popup');

    // Add sidebar mode class to body
    document.body.classList.add('sidebar-mode');

    // Show and initialize the super close button
    const superCloseBtn = document.getElementById('superCloseBtn');
    if (superCloseBtn) {
      superCloseBtn.style.display = 'block';

      // Add click handler with multiple close methods
      superCloseBtn.addEventListener('click', handleSuperCloseBtnClick);
    }

    // Add the pulse animation
    addPulseAnimation();

    // Initialize all UI elements to ensure they work in sidebar mode
    initializeUIElements();

    // Listen for messages from the parent window with origin validation
    window.addEventListener('message', (event) => {
      console.log('Received message in popup:', event.data);

      // Validate the origin of the message
      const isExtensionOrigin = event.origin === 'chrome-extension://' + chrome.runtime.id;
      const isSameOrigin = event.origin === window.location.origin;

      // Only process messages from trusted sources
      if ((isExtensionOrigin || isSameOrigin) && event.data && event.data.source === 'brave-sidebar') {
        if (event.data.action === 'sidebarInitialized') {
          console.log('Sidebar initialized from trusted source, ensuring UI is ready');
          // Make sure UI is initialized
          initializeUIElements();
        } else if (event.data.action === 'sidebarClosed') {
          console.log('Sidebar closed message received from trusted source');
        }
      } else {
        console.warn('Received message from untrusted source:', event.origin);
      }
    });
  }
};

// Initialize all UI elements to ensure they work in sidebar mode
const initializeUIElements = () => {
  console.log('Initializing UI elements for sidebar mode');

  // Make sure all buttons and inputs are properly initialized
  setTimeout(() => {
    try {
      // Ensure the chat input is working
      const userInput = document.getElementById('userInput');
      if (userInput) {
        userInput.focus();
        userInput.blur();
      }

      // Ensure the send button is working
      const sendButton = document.getElementById('sendMessage');
      if (sendButton) {
        // Force a re-initialization of the click handler
        const oldSendButton = sendButton.cloneNode(true);
        sendButton.parentNode.replaceChild(oldSendButton, sendButton);
      }

      // Ensure the provider selector is working
      const providerSelector = document.getElementById('providerSelector');
      if (providerSelector) {
        // Force a re-initialization of the change handler
        const oldProviderSelector = providerSelector.cloneNode(true);
        providerSelector.parentNode.replaceChild(oldProviderSelector, providerSelector);
      }

      console.log('UI elements initialized for sidebar mode');
    } catch (error) {
      console.error('Error initializing UI elements:', error);
    }
  }, 500);
};

// Handle super close button click with multiple close methods
const handleSuperCloseBtnClick = () => {
  console.log('Super close button clicked');

  // Method 1: Direct window close
  window.close();

  // Method 2: Send message to parent (Brave sidebar) with specific target origin
  try {
    // Get the extension's origin for secure messaging
    const extensionOrigin = 'chrome-extension://' + chrome.runtime.id;

    window.parent.postMessage({
      source: 'browzy-ai-popup',
      action: 'closeSidebar'
    }, extensionOrigin);
  } catch (e) {
    console.error('Error sending message to parent:', e);
  }

  // Method 3: Send message to background script
  try {
    chrome.runtime.sendMessage({ action: 'closeSidePanel' })
      .catch(e => console.error('Error sending closeSidePanel message:', e));
  } catch (e) {
    console.error('Error sending message to background script:', e);
  }

  // Method 4: Force close after delay
  setTimeout(() => {
    try {
      window.close();
    } catch (e) {
      console.error('Error closing window:', e);
    }
  }, 100);
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initSidebarMode);
