// Enhanced Settings and History UI functionality

document.addEventListener('DOMContentLoaded', function() {
  // API Key Status Indicators
  updateApiKeyStatus('openrouterKey', 'openrouterStatus');
  updateApiKeyStatus('openaiKey', 'openaiStatus');
  updateApiKeyStatus('geminiKey', 'geminiStatus');
  // Hugging Face API key status indicator has been removed as requested

  // Toggle visibility for API keys
  document.querySelectorAll('.toggle-visibility').forEach(button => {
    button.addEventListener('click', function() {
      const input = this.previousElementSibling;
      const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
      input.setAttribute('type', type);

      // Change icon
      const icon = this.querySelector('i');
      if (type === 'text') {
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      } else {
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      }
    });
  });

  // Temperature slider value display
  const temperatureSlider = document.getElementById('temperature');
  const temperatureValue = document.getElementById('temperatureValue');

  if (temperatureSlider && temperatureValue) {
    temperatureSlider.addEventListener('input', function() {
      temperatureValue.textContent = this.value;
    });
  }

  // History search functionality
  const historySearch = document.getElementById('historySearch');
  const historyList = document.getElementById('historyList');

  if (historySearch && historyList) {
    historySearch.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      filterHistoryItems(searchTerm);
    });
  }

  // History filter functionality
  const historyFilter = document.getElementById('historyFilter');

  if (historyFilter && historyList) {
    historyFilter.addEventListener('change', function() {
      const filterValue = this.value;
      filterHistoryByModel(filterValue);
    });
  }

  // Save API Keys event
  const saveKeysBtn = document.getElementById('saveKeys');

  if (saveKeysBtn) {
    saveKeysBtn.addEventListener('click', function() {
      // Add animation to button
      this.classList.add('saving');

      // Update status indicators after saving
      setTimeout(() => {
        updateApiKeyStatus('openrouterKey', 'openrouterStatus');
        updateApiKeyStatus('openaiKey', 'openaiStatus');
        updateApiKeyStatus('geminiKey', 'geminiStatus');
        // Hugging Face API key status indicator has been removed as requested

        // Remove animation
        this.classList.remove('saving');
      }, 1000);
    });
  }
});

// Function to update API key status indicators
function updateApiKeyStatus(keyId, statusId) {
  const keyInput = document.getElementById(keyId);
  const statusElement = document.getElementById(statusId);

  if (keyInput && statusElement) {
    const hasValue = keyInput.value && keyInput.value.trim() !== '';

    if (hasValue) {
      statusElement.innerHTML = '<i class="fas fa-circle"></i> Set';
      statusElement.classList.add('set');
      statusElement.classList.remove('not-set');
    } else {
      statusElement.innerHTML = '<i class="fas fa-circle"></i> Not Set';
      statusElement.classList.add('not-set');
      statusElement.classList.remove('set');
    }
  }
}

// Function to filter history items by search term
function filterHistoryItems(searchTerm) {
  const historyItems = document.querySelectorAll('.history-item');

  if (historyItems.length === 0) {
    // No history items yet, nothing to filter
    return;
  }

  let hasResults = false;

  historyItems.forEach(item => {
    const title = item.querySelector('.history-item-title').textContent.toLowerCase();
    const preview = item.querySelector('.history-item-preview')?.textContent.toLowerCase() || '';

    if (title.includes(searchTerm) || preview.includes(searchTerm)) {
      item.style.display = 'block';
      hasResults = true;
    } else {
      item.style.display = 'none';
    }
  });

  // Show/hide no results message
  const emptyState = document.querySelector('.empty-history-state');
  const noResultsMessage = document.querySelector('.no-results-message') || createNoResultsMessage();

  if (!hasResults && historyItems.length > 0) {
    emptyState.style.display = 'none';
    noResultsMessage.style.display = 'flex';
  } else {
    if (historyItems.length > 0) {
      emptyState.style.display = 'none';
    } else {
      emptyState.style.display = 'flex';
    }
    noResultsMessage.style.display = 'none';
  }
}

// Function to create "no results" message
function createNoResultsMessage() {
  const noResults = document.createElement('div');
  noResults.className = 'no-results-message';
  noResults.style.display = 'none';
  noResults.innerHTML = `
    <div class="empty-history-icon">
      <i class="fas fa-search"></i>
    </div>
    <div class="empty-history-message">No matching conversations</div>
    <div class="empty-history-hint">Try a different search term</div>
  `;

  const historyList = document.getElementById('historyList');
  historyList.appendChild(noResults);

  return noResults;
}

// Function to filter history items by model
function filterHistoryByModel(modelFilter) {
  const historyItems = document.querySelectorAll('.history-item');

  if (historyItems.length === 0 || modelFilter === 'all') {
    // No history items yet or showing all, nothing to filter
    historyItems.forEach(item => {
      item.style.display = 'block';
    });
    return;
  }

  let hasResults = false;

  historyItems.forEach(item => {
    const modelElement = item.querySelector('.history-item-model');
    const modelClass = modelElement ? Array.from(modelElement.classList).find(cls => cls !== 'history-item-model') : null;

    if (modelClass === modelFilter) {
      item.style.display = 'block';
      hasResults = true;
    } else {
      item.style.display = 'none';
    }
  });

  // Show/hide no results message
  const emptyState = document.querySelector('.empty-history-state');
  const noResultsMessage = document.querySelector('.no-results-message') || createNoResultsMessage();

  if (!hasResults && historyItems.length > 0) {
    emptyState.style.display = 'none';
    noResultsMessage.style.display = 'flex';
    noResultsMessage.querySelector('.empty-history-message').textContent = 'No conversations with this model';
    noResultsMessage.querySelector('.empty-history-hint').textContent = 'Try selecting a different model filter';
  } else {
    if (historyItems.length > 0) {
      emptyState.style.display = 'none';
    } else {
      emptyState.style.display = 'flex';
    }
    noResultsMessage.style.display = 'none';
  }
}
