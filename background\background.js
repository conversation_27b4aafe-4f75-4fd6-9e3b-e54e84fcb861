'use strict';

// Cache for the current tab's content
let pageContentCache = {};

// Function to detect Brave browser
async function isBraveBrowser() {
  try {
    // <PERSON> has a specific navigator.brave object
    return navigator.brave && await navigator.brave.isBrave() || false;
  } catch (e) {
    // Check for <PERSON>'s fingerprinting protection
    return navigator.userAgent.includes('Brave');
  }
}

// Check if the browser supports the Side Panel API
const isSidePanelSupported = typeof chrome.sidePanel !== 'undefined';

// Detect if we're running in Brave (will be set asynchronously)
let isRunningInBrave = false;

// Immediately invoke async function to detect <PERSON>
(async function() {
  isRunningInBrave = await isBraveBrowser();
  console.log('Browser detection:', { isRunningInBrave, isSidePanelSupported });

  // If we're in Brave, we'll use a different approach for the sidebar
  if (isRunningInBrave) {
    console.log('Running in Brave browser - using custom sidebar approach');
    // Store this information for other parts of the extension
    chrome.storage.local.set({
      usingSidePanelFallback: true,
      isBraveBrowser: true
    });

    // Make sure the sidebar is properly initialized in all tabs
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        // We're not skipping any pages now to ensure sidebar is loaded everywhere
        console.log('Attempting to inject sidebar script for all pages:', tab.url);

        // Inject the sidebar script
        chrome.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content/brave-sidebar.js']
        }).catch(error => {
          console.log('Could not inject sidebar script into tab:', tab.url, error);
        });
      });
    });
  }
})();

// Set up the side panel behavior if supported
if (isSidePanelSupported) {
  // Set the side panel behavior to always open on action click
  chrome.sidePanel
    .setPanelBehavior({ openPanelOnActionClick: true })
    .catch((error) => console.error('Error setting side panel behavior:', error));

  // Set the side panel width to 400px (if the API supports it)
  try {
    if (chrome.sidePanel.setOptions) {
      chrome.sidePanel.setOptions({
        path: 'popup/popup.html?sidebar=true',
        width: 400
      }).catch(error => console.error('Error setting side panel width:', error));
    }
  } catch (error) {
    console.warn('Side Panel setOptions API not supported in this browser version:', error);
  }

  // Store this information for other parts of the extension
  chrome.storage.local.set({
    usingSidePanelFallback: false,
    isBraveBrowser: false,
    usingSidePanel: true
  });

  console.log('Chrome Side Panel API is supported and configured');
} else {
  console.warn('Side Panel API not supported in this browser. Falling back to popup-only mode.');

  // Store this information for other parts of the extension
  chrome.storage.local.set({
    usingSidePanelFallback: true,
    isBraveBrowser: false,
    usingSidePanel: false
  });
}

// Create a context menu item to open the side panel
chrome.runtime.onInstalled.addListener(() => {
  // Only create the context menu item if the Side Panel API is supported
  if (isSidePanelSupported) {
    chrome.contextMenus.create({
      id: 'openBrowzyAISidePanel',
      title: 'Open Browzy AI in side panel',
      contexts: ['all']
    });
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  console.log('Extension icon clicked');

  // Check if we're running in Brave
  if (isRunningInBrave) {
    console.log('Running in Brave, using custom sidebar approach');
    // Get the current tab
    chrome.tabs.query({ active: true, currentWindow: true })
      .then(tabs => {
        if (!tabs || tabs.length === 0) {
          console.error('No active tab found');
          return;
        }

        const tab = tabs[0];

        // We'll try to inject the sidebar script for all pages
        console.log('Attempting to inject sidebar for all pages:', tab.url);

        // Send a message to the content script to show the sidebar
        chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
          .then(response => {
            // Check if this is a blank page
            if (response && response.isBlankPage) {
              console.log('Blank page detected, showing notification');
              // Show a notification to the user
              chrome.notifications.create({
                type: 'basic',
                iconUrl: chrome.runtime.getURL('images/icon.png'),
                title: 'Browzy AI Sidebar',
                message: 'Sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.',
                priority: 2
              });
              return;
            }

            if (response && response.success) {
              console.log('Brave sidebar shown successfully');
            } else {
              console.log('Brave sidebar not initialized yet, injecting script');
              // Inject the Brave sidebar script
              chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content/brave-sidebar.js']
              })
              .then(() => {
                // Wait a moment for the script to initialize
                setTimeout(() => {
                  // Try to show the sidebar again
                  chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                    .then(response => {
                      // Check again if this is a blank page
                      if (response && response.isBlankPage) {
                        console.log('Blank page detected after script injection, showing notification');
                        // Show a notification to the user
                        chrome.notifications.create({
                          type: 'basic',
                          iconUrl: chrome.runtime.getURL('images/icon.png'),
                          title: 'Browzy AI Sidebar',
                          message: 'Sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.',
                          priority: 2
                        });
                        return;
                      }

                      if (response && response.success) {
                        console.log('Brave sidebar shown successfully after script injection');
                      } else {
                        throw new Error('Failed to show Brave sidebar');
                      }
                    })
                    .catch(error => {
                      console.error('Error showing Brave sidebar:', error);

                      // Check if this is a blank page error
                      if (error.message && (error.message.includes('blank page') || error.message.includes('Blank page'))) {
                        // Show a notification to the user
                        chrome.notifications.create({
                          type: 'basic',
                          iconUrl: chrome.runtime.getURL('images/icon.png'),
                          title: 'Browzy AI Sidebar',
                          message: 'Sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.',
                          priority: 2
                        });
                      } else {
                        // Fallback to opening the popup in a new tab
                        chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
                      }
                    });
                }, 500);
              })
              .catch(error => {
                console.error('Error injecting Brave sidebar script:', error);

                // Check if this is a blank page error
                if (error.message && (error.message.includes('blank page') || error.message.includes('Blank page'))) {
                  // Show a notification to the user
                  chrome.notifications.create({
                    type: 'basic',
                    iconUrl: chrome.runtime.getURL('images/icon.png'),
                    title: 'Browzy AI Sidebar',
                    message: 'Sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.',
                    priority: 2
                  });
                } else {
                  // Fallback to opening the popup in a new tab
                  chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
                }
              });
            }
          })
          .catch(error => {
            console.error('Error sending message to content script:', error);

            // Check if this is a blank page error
            if (error.message && (error.message.includes('blank page') || error.message.includes('Blank page'))) {
              // Show a notification to the user
              chrome.notifications.create({
                type: 'basic',
                iconUrl: chrome.runtime.getURL('images/icon.png'),
                title: 'Browzy AI Sidebar',
                message: 'Sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.',
                priority: 2
              });
            } else {
              // Fallback to opening the popup in a new tab
              chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
            }
          });
      })
      .catch(error => {
        console.error('Error getting active tab:', error);
      });
  } else if (isSidePanelSupported) {
    // In Chrome, use the Side Panel API
    console.log('Using Chrome Side Panel API');

    // Open the side panel in the current window
    chrome.sidePanel.open({ windowId: tab.windowId })
      .then(() => {
        console.log('Chrome Side Panel opened successfully');
      })
      .catch(error => {
        console.error('Error opening Chrome side panel:', error);
        // Fallback to opening the popup in a new tab
        chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
      });
  } else {
    // Fallback for browsers that don't support the Side Panel API
    console.log('Side Panel API not supported, opening popup in new tab');
    chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
  }
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'openBrowzyAISidePanel') {
    // Check if the Side Panel API is supported
    if (isSidePanelSupported) {
      // Open the side panel in the current window
      chrome.sidePanel.open({ windowId: tab.windowId })
        .catch(error => {
          console.error('Error opening side panel:', error);
          // Fallback to opening the popup in a new tab if side panel fails
          chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
        });
    } else {
      // Fallback to opening the popup in a new tab
      chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') });
    }
  }
});

// Shared analysis cache for cross-tab functionality
let sharedAnalysisCache = {
  lastAnalysis: null,
  timestamp: null,
  sourceUrl: null,
  sourceTabId: null
};

// Cache for browser history jokes
let browserHistoryCache = {
  history: [],
  timestamp: null,
  categories: {
    shopping: [],
    social: [],
    entertainment: [],
    tech: [],
    news: [],
    adult: [],
    gaming: [],
    food: [],
    travel: [],
    other: []
  }
};

// Listen for messages from content script or popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Make sure we respond asynchronously
  const sendAsyncResponse = (response) => {
    // Add a small delay to ensure the response is treated as async
    setTimeout(() => sendResponse(response), 0);
    return true;
  };

  switch (request.action) {    case 'testConnection':
      // Test connection to server with proper CORS headers
      fetch('https://api.unexpected-glynis-legendsumeet-c06bc78a.koyeb.app/test', {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Origin': chrome.runtime.getURL('/')
        }
      })
        .then(async response => {
          if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
          }
          try {
            const text = await response.text();
            const data = text.startsWith('{') ? JSON.parse(text) : { success: true, message: text };
            sendResponse({ success: true, data });
          } catch (e) {
            if (response.ok) {
              sendResponse({ success: true, data: { message: 'Connection successful' } });
            } else {
              throw e;
            }
          }
        })
        .catch(error => {
          console.error('Connection test error:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true; // Will respond asynchronouslycase 'fetchWithCORS':
      // Handle CORS request with proper headers
      const defaultOptions = {
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      };
      const fetchOptions = { ...defaultOptions, ...request.options };
      
      fetch(request.url, fetchOptions)
        .then(async response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          sendResponse({ success: true, data });
        })
        .catch(error => {
          console.error('CORS fetch error:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true; // Indicates we'll respond asynchronously

    case 'blankPageError':
      // Handle blank page error message from content script
      console.log('Received blank page error message:', request.message);
      // Show a notification to the user
      chrome.notifications.create({
        type: 'basic',
        iconUrl: chrome.runtime.getURL('images/icon.png'),
        title: 'Browzy AI Sidebar',
        message: 'Sidebar cannot be used on blank pages or new tabs. Please navigate to a website first.',
        priority: 2
      });
      return sendAsyncResponse({ success: true });

    case 'fetchWebpage':
      // Fetch and process an external webpage
      fetchExternalWebpage(request.url, request.includeImages, request.deepAnalysis)
        .then(content => sendResponse({ success: true, content }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Indicates we'll respond asynchronously

    case 'webSearch':
      // Perform a web search
      performWebSearch(request.query, request.numResults || 5)
        .then(results => sendResponse({ success: true, results }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Indicates we'll respond asynchronously

    case 'fetchPageContent':
      // Fetch content from a URL
      fetchPageContent(request.url)
        .then(content => sendResponse({ success: true, content }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Indicates we'll respond asynchronously

    case 'sendPDFTextToChat':
      // Store the selected PDF text for the popup to retrieve
      try {
        // Validate the PDF text
        if (!request.text || typeof request.text !== 'string') {
          return sendAsyncResponse({
            success: false,
            error: 'Invalid PDF text: ' + (request.text ? typeof request.text : 'empty')
          });
        }

        console.log(`Received PDF text (${request.text.length} chars) from content script`);

        // Store the PDF text in a special cache
        const pdfData = {
          text: request.text,
          metadata: request.metadata || {},
          timestamp: new Date().toISOString()
        };

        // Store in shared analysis cache for the popup to access
        sharedAnalysisCache = {
          lastAnalysis: pdfData,
          timestamp: new Date().getTime(),
          sourceType: 'pdf',
          sourceUrl: request.metadata?.url || 'PDF document'
        };

        // Notify any open popups that PDF text is available
        try {
          chrome.runtime.sendMessage({
            action: 'pdfTextAvailable',
            data: {
              length: request.text.length,
              source: request.metadata?.title || 'PDF document'
            }
          }).catch(() => {
            // Ignore errors when no popup is open
            console.log('Could not notify popup of PDF text (popup might be closed)');
          });
        } catch (notifyError) {
          console.log('Error notifying popup:', notifyError);
        }

        return sendAsyncResponse({ success: true });
      } catch (error) {
        console.error('Error handling PDF text:', error);
        return sendAsyncResponse({ success: false, error: error.message });
      }

    case 'getPDFSelection':
      // Return any stored PDF selection
      return sendAsyncResponse({
        success: true,
        hasSelection: sharedAnalysisCache.sourceType === 'pdf' && !!sharedAnalysisCache.lastAnalysis,
        data: sharedAnalysisCache.lastAnalysis
      });

    case 'getPageContent':
      // If we already have cached content for this tab, use it
      if (sender.tab && pageContentCache[sender.tab.id]) {
        return sendAsyncResponse({ content: pageContentCache[sender.tab.id] });
      } else {
        // Otherwise, we'll need to request it from the content script
        return sendAsyncResponse({ error: 'Content not available' });
      }



    case 'videoTimestampUpdate':
      // Handle video timestamp updates
      try {
        // Forward the timestamp update to any open popups
        chrome.runtime.sendMessage({
          action: 'videoTimestampUpdate',
          videoIndex: request.videoIndex,
          currentTime: request.currentTime,
          currentTimeFormatted: request.currentTimeFormatted,
          progress: request.progress,
          isSeeking: request.isSeeking || false
        }).catch(() => {
          // Ignore errors when no popup is open
          console.log('Could not forward video timestamp update (popup might be closed)');
        });

        return sendAsyncResponse({ success: true });
      } catch (error) {
        console.error('Error handling video timestamp update:', error);
        return sendAsyncResponse({ success: false, error: error.message });
      }

    case 'drawingToolsLoaded':
      // Handle drawing tools loaded message
      console.log('Drawing tools loaded in tab:', sender.tab ? sender.tab.id : 'unknown', 'URL:', request.url || 'unknown');
      return sendAsyncResponse({
        success: true,
        message: 'Drawing tools registered successfully',
        tabId: sender.tab ? sender.tab.id : 'unknown',
        timestamp: new Date().toISOString()
      });

    case 'ensureDrawingToolsLoaded':
      // Ensure drawing tools are loaded in the specified tab
      try {
        if (!request.tabId) {
          console.error('No tab ID provided for ensureDrawingToolsLoaded');
          return sendAsyncResponse({ success: false, error: 'No tab ID provided' });
        }

        console.log('Ensuring drawing tools are loaded in tab:', request.tabId);

        // Check if the tab exists first
        chrome.tabs.get(request.tabId, (tab) => {
          if (chrome.runtime.lastError) {
            console.error('Error getting tab:', chrome.runtime.lastError);
            return sendAsyncResponse({
              success: false,
              error: `Tab not found: ${chrome.runtime.lastError.message}`
            });
          }

          // We'll try to inject scripts into all tabs
          console.log('Attempting to inject drawing tools script for all pages:', tab.url);

          // Inject the drawing tools script
          chrome.scripting.executeScript({
            target: { tabId: request.tabId },
            files: ['content/drawing-tools.js']
          })
          .then((results) => {
            console.log('Drawing tools script injected into tab:', request.tabId, 'Results:', results);
            return sendAsyncResponse({
              success: true,
              message: 'Drawing tools script injected successfully'
            });
          })
          .catch(error => {
            console.error('Error injecting drawing tools script:', error);
            console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return sendAsyncResponse({
              success: false,
              error: error.message || 'Unknown error injecting script'
            });
          });
        });

        return true; // Will respond asynchronously
      } catch (error) {
        console.error('Error ensuring drawing tools loaded:', error);
        console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
        return sendAsyncResponse({
          success: false,
          error: error.message || 'Unknown error in ensureDrawingToolsLoaded'
        });
      }

    case 'cachePageContent':
      // Cache content from a specific tab
      if (sender.tab) {
        // Add context detection for specialized handling
        const content = request.content;
        let contextType = 'general';

        // Enhanced context detection
        // Check for chess content
        if (content.chess && (content.chess.fen || content.chess.pgn) ||
            content.url.toLowerCase().includes('chess') ||
            content.title.toLowerCase().includes('chess') ||
            content.content.toLowerCase().includes('chess') &&
            (/[a-h][1-8]/g.test(content.content.toLowerCase()) &&
             /(knight|bishop|rook|queen|king|pawn)/i.test(content.content.toLowerCase()))
           ) {
          contextType = 'chess';

          // Try to determine the specific chess context (opening, middlegame, endgame)
          if (content.content.toLowerCase().includes('opening') ||
              content.content.toLowerCase().includes('gambit') ||
              content.content.toLowerCase().includes('defense')) {
            contextType = 'chess-opening';
          } else if (content.content.toLowerCase().includes('endgame') ||
                    content.content.toLowerCase().includes('checkmate')) {
            contextType = 'chess-endgame';
          }
        }

        // Check for coding/programming content
        else if (content.url.includes('leetcode') ||
                content.url.includes('hackerrank') ||
                content.url.includes('codewars') ||
                content.url.includes('github') ||
                content.url.includes('stackoverflow') ||
                content.url.includes('codeforces') ||
                (content.codeBlocks && content.codeBlocks.length > 0) ||
                (/(function|class|algorithm|complexity|code|programming|solution|problem)/i.test(content.content) &&
                 /(for |while |if |else|return |import |def |public |private |void |int |string)/i.test(content.content))) {

          // Determine specific coding context
          if (content.url.includes('leetcode')) {
            contextType = 'leetcode';
          } else if (content.url.includes('github')) {
            contextType = 'github';
          } else if (content.url.includes('stackoverflow')) {
            contextType = 'stackoverflow';
          } else {
            contextType = 'coding';
          }
        }

        // Check for YouTube content
        else if (content.url.includes('youtube.com') ||
                content.url.includes('youtu.be') ||
                content.youtube) {
          contextType = 'youtube';

          // Determine specific YouTube context
          if (content.url.includes('/shorts/')) {
            contextType = 'youtube-shorts';
          } else if (content.url.includes('/watch')) {
            contextType = 'youtube-video';
          } else if (content.url.includes('/channel/') || content.url.includes('/c/')) {
            contextType = 'youtube-channel';
          }
        }

        // Check for news/article content
        else if (content.url.includes('news') ||
                content.url.includes('article') ||
                content.content.includes('author') ||
                content.content.includes('published')) {
          contextType = 'article';
        }

        // Check for shopping/product content
        else if (content.url.includes('product') ||
                content.url.includes('shop') ||
                content.url.includes('item') ||
                content.url.includes('store') ||
                content.content.includes('price') ||
                content.content.includes('buy now')) {
          contextType = 'shopping';
        }



        // Enhanced content with context type
        const enhancedContent = {
          ...content,
          contextType
        };

        pageContentCache[sender.tab.id] = enhancedContent;
        return sendAsyncResponse({ success: true });
      }
      return sendAsyncResponse({ error: 'Tab ID not available' });

    case 'makeAPIRequest':
      // Handle API requests
      handleAPIRequest(request.provider, request.endpoint, request.data)
        .then(response => sendResponse({ success: true, data: response }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Indicates we'll respond asynchronously

    case 'updateStats':
      // Update usage statistics with model and response time if available
      updateStats(request.provider, request.tokens, request.model, request.responseTime)
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Indicates we'll respond asynchronously

    case 'analyzePage':
      // Auto-analyze the current page and provide context-specific insights
      if (sender.tab && pageContentCache[sender.tab.id]) {
        const content = pageContentCache[sender.tab.id];
        const contextType = content.contextType || 'general';

        let insights = {
          type: contextType,
          features: []
        };

        // Add context-specific insights based on the more granular context types
        if (contextType.startsWith('chess')) {
          insights.features.push('Chess position analysis');
          insights.features.push('Move suggestions');

          if (contextType === 'chess-opening') {
            insights.features.push('Opening theory analysis');
            insights.features.push('Opening variation suggestions');
          } else if (contextType === 'chess-endgame') {
            insights.features.push('Endgame technique analysis');
            insights.features.push('Checkmate pattern detection');
          }

          if (content.chess?.fen) {
            insights.features.push('FEN position detected');
          }
          if (content.chess?.pgn) {
            insights.features.push('PGN game history detected');
          }
        }
        else if (contextType === 'leetcode') {
          insights.features.push('Algorithm optimization');
          insights.features.push('LeetCode solution strategies');
          insights.features.push('Time & space complexity analysis');
          insights.features.push('Edge case identification');
          if (content.codeBlocks && content.codeBlocks.length > 0) {
            insights.features.push(`${content.codeBlocks.length} code blocks detected`);
          }
        }
        else if (contextType === 'stackoverflow') {
          insights.features.push('Code debugging assistance');
          insights.features.push('Error explanation');
          insights.features.push('Alternative approaches');
          if (content.codeBlocks && content.codeBlocks.length > 0) {
            insights.features.push(`${content.codeBlocks.length} code blocks detected`);
          }
        }
        else if (contextType === 'github' || contextType === 'coding') {
          insights.features.push('Code explanation');
          insights.features.push('Code review suggestions');
          insights.features.push('Documentation assistance');
          if (content.codeBlocks && content.codeBlocks.length > 0) {
            insights.features.push(`${content.codeBlocks.length} code blocks detected`);
          }
        }
        else if (contextType === 'article') {
          insights.features.push('Article summarization');
          insights.features.push('Key points extraction');
          insights.features.push('Bias detection');
          insights.features.push('Related context');
        }
        else if (contextType === 'shopping') {
          insights.features.push('Product evaluation');
          insights.features.push('Alternative comparison');
          insights.features.push('Price analysis');
          insights.features.push('Review summarization');
        }
        else if (contextType.startsWith('youtube')) {
          insights.features.push('Video content analysis');
          insights.features.push('Comment summarization');

          if (contextType === 'youtube-video') {
            insights.features.push('Video summarization');
            insights.features.push('Key points extraction');
            if (content.youtube?.comments && content.youtube.comments.length > 0) {
              insights.features.push(`${content.youtube.comments.length} comments detected`);
            }
          }
          else if (contextType === 'youtube-shorts') {
            insights.features.push('Short video analysis');
            insights.features.push('Trend identification');
          }
          else if (contextType === 'youtube-channel') {
            insights.features.push('Channel content analysis');
            insights.features.push('Creator profile insights');
          }
        }
        else {
          insights.features.push('Page summarization');
          insights.features.push('Information extraction');
          insights.features.push('Question answering');
        }

        return sendAsyncResponse({ success: true, insights });
      }
      return sendAsyncResponse({ error: 'No page content available for analysis' });

    case 'saveSharedAnalysis':
      // Save analysis for cross-tab sharing
      if (sender.tab) {
        sharedAnalysisCache = {
          lastAnalysis: request.analysis,
          timestamp: new Date().getTime(),
          sourceUrl: sender.tab.url,
          sourceTabId: sender.tab.id
        };
        return sendAsyncResponse({ success: true });
      }
      return sendAsyncResponse({ error: 'Tab information not available' });

    case 'getSharedAnalysis':
      // Get the latest shared analysis
      return sendAsyncResponse({
        success: true,
        hasShared: !!sharedAnalysisCache.lastAnalysis,
        analysis: sharedAnalysisCache
      });

    case 'clearSharedAnalysis':
      // Clear the shared analysis cache
      sharedAnalysisCache = {
        lastAnalysis: null,
        timestamp: null,
        sourceUrl: null,
        sourceTabId: null
      };
      return sendAsyncResponse({ success: true });

    case 'openPopup':
      // Open the popup in a new tab
      try {
        let popupUrl = chrome.runtime.getURL('popup/popup.html');

        // Add tab parameter if specified
        if (request.tab) {
          popupUrl += `?tab=${request.tab}`;
        }

        // Open in a new tab
        chrome.tabs.create({ url: popupUrl })
          .then(() => sendAsyncResponse({ success: true }))
          .catch(error => sendAsyncResponse({ success: false, error: error.message }));
      } catch (error) {
        console.error('Error opening popup:', error);
        return sendAsyncResponse({ success: false, error: error.message });
      }
      return true; // Indicates we'll respond asynchronously

    case 'openSidePanel':
      // Open the side panel
      try {
        // Check if we're running in Brave browser
        if (isRunningInBrave) {
          console.log('Using Brave custom sidebar');
          // Get the current tab
          chrome.tabs.query({ active: true, currentWindow: true })
            .then(tabs => {
              if (!tabs || tabs.length === 0) {
                return sendAsyncResponse({ success: false, error: 'No active tab found' });
              }

              const tab = tabs[0];

              // Check for blank pages or unsupported URLs
              if (tab.url === 'about:blank' || tab.url === 'about:newtab' || tab.url === 'chrome://newtab/' || tab.url === 'brave://newtab/') {
                console.log('Blank page detected, using standard sidebar approach');

                // Use the same approach for blank pages as regular pages
                // Send a message to the content script to show the sidebar
                chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                  .then(response => {
                    if (response && response.success) {
                      console.log('Brave sidebar shown successfully on blank page');
                      sendAsyncResponse({ success: true, brave: true });
                    } else {
                      console.log('Brave sidebar not initialized yet on blank page, injecting script');
                      // Inject the Brave sidebar script
                      chrome.scripting.executeScript({
                        target: { tabId: tab.id },
                        files: ['content/brave-sidebar.js']
                      })
                      .then(() => {
                        // Wait a moment for the script to initialize
                        setTimeout(() => {
                          // Try to show the sidebar again
                          chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                            .then(response => {
                              if (response && response.success) {
                                console.log('Brave sidebar shown successfully on blank page after injection');
                                sendAsyncResponse({ success: true, brave: true });
                              } else {
                                throw new Error('Failed to show Brave sidebar on blank page');
                              }
                            })
                            .catch(error => {
                              console.error('Error showing Brave sidebar on blank page:', error);
                              // Fallback to opening the popup in a new tab
                              chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                                .then(() => sendAsyncResponse({ success: true, fallback: true }))
                                .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                            });
                        }, 500);
                      })
                      .catch(error => {
                        console.error('Error injecting Brave sidebar script on blank page:', error);
                        // Fallback to opening the popup in a new tab
                        chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                          .then(() => sendAsyncResponse({ success: true, fallback: true }))
                          .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                      });
                    }
                  })
                  .catch(error => {
                    console.error('Error sending message to blank page:', error);
                    // Inject the Brave sidebar script as fallback
                    chrome.scripting.executeScript({
                      target: { tabId: tab.id },
                      files: ['content/brave-sidebar.js']
                    })
                    .then(() => {
                      // Wait a moment for the script to initialize
                      setTimeout(() => {
                        // Try to show the sidebar
                        chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                          .then(response => {
                            if (response && response.success) {
                              console.log('Brave sidebar shown successfully on blank page after fallback');
                              sendAsyncResponse({ success: true, brave: true });
                            } else {
                              throw new Error('Failed to show Brave sidebar on blank page');
                            }
                          })
                          .catch(error => {
                            console.error('Error showing Brave sidebar on blank page:', error);
                            // Fallback to opening the popup in a new tab
                            chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                              .then(() => sendAsyncResponse({ success: true, fallback: true }))
                              .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                          });
                      }, 500);
                    })
                    .catch(error => {
                      console.error('Error injecting Brave sidebar script on blank page:', error);
                      // Fallback to opening the popup in a new tab
                      chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                        .then(() => sendAsyncResponse({ success: true, fallback: true }))
                        .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                    });
                  });
                return;
              }
              // Skip other unsupported URLs
              else if (tab.url.startsWith('chrome://') ||
                  tab.url.startsWith('brave://') ||
                  tab.url.startsWith('about:') ||
                  tab.url.startsWith('chrome-extension://') ||
                  tab.url.startsWith('brave-extension://')) {
                console.log('Cannot inject sidebar into browser internal page');
                // Fallback to opening the popup in a new tab
                chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                  .then(() => sendAsyncResponse({ success: true, fallback: true }))
                  .catch(error => sendAsyncResponse({ success: false, error: error.message }));
                return;
              }

              // Send a message to the content script to show the sidebar
              chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                .then(response => {
                  if (response && response.success) {
                    sendAsyncResponse({ success: true, brave: true });
                  } else {
                    console.log('Brave sidebar not initialized yet, injecting script');
                    // Inject the Brave sidebar script
                    chrome.scripting.executeScript({
                      target: { tabId: tab.id },
                      files: ['content/brave-sidebar.js']
                    })
                    .then(() => {
                      // Wait a moment for the script to initialize
                      setTimeout(() => {
                        // Try to show the sidebar again
                        chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                          .then(response => {
                            if (response && response.success) {
                              sendAsyncResponse({ success: true, brave: true });
                            } else {
                              throw new Error('Failed to show Brave sidebar');
                            }
                          })
                          .catch(error => {
                            console.error('Error showing Brave sidebar:', error);
                            // Fallback to opening the popup in a new tab
                            chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                              .then(() => sendAsyncResponse({ success: true, fallback: true }))
                              .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                          });
                      }, 500);
                    })
                    .catch(error => {
                      console.error('Error injecting Brave sidebar script:', error);
                      // Fallback to opening the popup in a new tab
                      chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                        .then(() => sendAsyncResponse({ success: true, fallback: true }))
                        .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                    });
                  }
                })
                .catch(error => {
                  console.error('Error sending message to content script:', error);
                  // Fallback to opening the popup in a new tab
                  chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                    .then(() => sendAsyncResponse({ success: true, fallback: true }))
                    .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                });
            })
            .catch(error => {
              console.error('Error getting active tab:', error);
              sendAsyncResponse({ success: false, error: error.message });
            });
          return true;
        }

        // Use our custom sidebar implementation for all browsers for consistency
        console.log('Using custom sidebar implementation for all browsers');

        // Get the current tab
        chrome.tabs.query({ active: true, currentWindow: true })
          .then(tabs => {
            if (!tabs || tabs.length === 0) {
              return sendAsyncResponse({ success: false, error: 'No active tab found' });
            }

            const tab = tabs[0];

            // Check for blank pages or unsupported URLs
            if (tab.url === 'about:blank' || tab.url === 'about:newtab' || tab.url === 'chrome://newtab/' || tab.url === 'brave://newtab/') {
              console.log('Blank page detected, using standard sidebar approach');

              // Use the same approach for blank pages as regular pages
              // Send a message to the content script to show the sidebar
              chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                .then(response => {
                  if (response && response.success) {
                    console.log('Custom sidebar shown successfully on blank page');
                    sendAsyncResponse({ success: true, custom: true });
                  } else {
                    console.log('Custom sidebar not initialized yet on blank page, injecting script');
                    // Inject the sidebar script
                    chrome.scripting.executeScript({
                      target: { tabId: tab.id },
                      files: ['content/brave-sidebar.js']
                    })
                    .then(() => {
                      // Wait a moment for the script to initialize
                      setTimeout(() => {
                        // Try to show the sidebar again
                        chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                          .then(response => {
                            if (response && response.success) {
                              console.log('Custom sidebar shown successfully on blank page after injection');
                              sendAsyncResponse({ success: true, custom: true });
                            } else {
                              throw new Error('Failed to show custom sidebar on blank page');
                            }
                          })
                          .catch(error => {
                            console.error('Error showing custom sidebar on blank page:', error);
                            // Fallback to opening the popup in a new tab
                            chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                              .then(() => sendAsyncResponse({ success: true, fallback: true }))
                              .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                          });
                      }, 500);
                    })
                    .catch(error => {
                      console.error('Error injecting custom sidebar script on blank page:', error);
                      // Fallback to opening the popup in a new tab
                      chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                        .then(() => sendAsyncResponse({ success: true, fallback: true }))
                        .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                    });
                  }
                })
                .catch(error => {
                  console.error('Error sending message to blank page:', error);
                  // Inject the sidebar script as fallback
                  chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content/brave-sidebar.js']
                  })
                  .then(() => {
                    // Wait a moment for the script to initialize
                    setTimeout(() => {
                      // Try to show the sidebar
                      chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                        .then(response => {
                          if (response && response.success) {
                            console.log('Custom sidebar shown successfully on blank page after fallback');
                            sendAsyncResponse({ success: true, custom: true });
                          } else {
                            throw new Error('Failed to show custom sidebar on blank page');
                          }
                        })
                        .catch(error => {
                          console.error('Error showing custom sidebar on blank page:', error);
                          // Fallback to opening the popup in a new tab
                          chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                            .then(() => sendAsyncResponse({ success: true, fallback: true }))
                            .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                        });
                    }, 500);
                  })
                  .catch(error => {
                    console.error('Error injecting custom sidebar script on blank page:', error);
                    // Fallback to opening the popup in a new tab
                    chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                      .then(() => sendAsyncResponse({ success: true, fallback: true }))
                      .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                  });
                });
              return;
            }
            // Skip other unsupported URLs
            else if (tab.url.startsWith('chrome://') ||
                tab.url.startsWith('brave://') ||
                tab.url.startsWith('about:') ||
                tab.url.startsWith('chrome-extension://') ||
                tab.url.startsWith('brave-extension://')) {
              console.log('Cannot inject sidebar into browser internal page');
              // Fallback to opening the popup in a new tab
              chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                .then(() => sendAsyncResponse({ success: true, fallback: true }))
                .catch(error => sendAsyncResponse({ success: false, error: error.message }));
              return;
            }

            // Send a message to the content script to show the sidebar
            chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
              .then(response => {
                if (response && response.success) {
                  sendAsyncResponse({ success: true, custom: true });
                } else {
                  console.log('Custom sidebar not initialized yet, injecting script');
                  // Inject the sidebar script
                  chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content/brave-sidebar.js']
                  })
                  .then(() => {
                    // Wait a moment for the script to initialize
                    setTimeout(() => {
                      // Try to show the sidebar again
                      chrome.tabs.sendMessage(tab.id, { action: 'showBraveSidebar' })
                        .then(response => {
                          if (response && response.success) {
                            sendAsyncResponse({ success: true, custom: true });
                          } else {
                            throw new Error('Failed to show custom sidebar');
                          }
                        })
                        .catch(error => {
                          console.error('Error showing custom sidebar:', error);
                          // Fallback to opening the popup in a new tab
                          chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                            .then(() => sendAsyncResponse({ success: true, fallback: true }))
                            .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                        });
                    }, 500);
                  })
                  .catch(error => {
                    console.error('Error injecting custom sidebar script:', error);
                    // Fallback to opening the popup in a new tab
                    chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                      .then(() => sendAsyncResponse({ success: true, fallback: true }))
                      .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
                  });
                }
              })
              .catch(error => {
                console.error('Error sending message to content script:', error);
                // Fallback to opening the popup in a new tab
                chrome.tabs.create({ url: chrome.runtime.getURL('popup/popup.html?sidebar=true') })
                  .then(() => sendAsyncResponse({ success: true, fallback: true }))
                  .catch(tabError => sendAsyncResponse({ success: false, error: tabError.message }));
              });
          })
          .catch(error => {
            console.error('Error getting active tab:', error);
            sendAsyncResponse({ success: false, error: error.message });
          });
      } catch (error) {
        console.error('Error opening side panel:', error);
        return sendAsyncResponse({ success: false, error: error.message });
      }
      return true; // Indicates we'll respond asynchronously

    case 'closeSidePanel':
      // Close the side panel
      try {
        console.log('Received closeSidePanel action');

        // Use our custom sidebar implementation for all browsers for consistency
        console.log('Using custom sidebar implementation for all browsers');

        // Get the current tab
        chrome.tabs.query({ active: true, currentWindow: true })
          .then(tabs => {
            if (!tabs || tabs.length === 0) {
              console.error('No active tab found');
              return sendAsyncResponse({ success: false, error: 'No active tab found' });
            }

            const tab = tabs[0];

            // Check for blank pages or unsupported URLs
            if (tab.url === 'about:blank' || tab.url === 'about:newtab' || tab.url === 'chrome://newtab/' || tab.url === 'brave://newtab/') {
              console.log('Blank page detected, using standard approach for closing');

              // Use the same approach for blank pages as regular pages, but with better error handling
              try {
                // First try to inject the content script if it's not already there
                chrome.scripting.executeScript({
                  target: { tabId: tab.id },
                  files: ['content/brave-sidebar.js']
                })
                .then(() => {
                  console.log('Injected Brave sidebar script before hiding');

                  // Wait a moment for the script to initialize
                  setTimeout(() => {
                    // Now try to hide the sidebar
                    chrome.tabs.sendMessage(tab.id, { action: 'hideBraveSidebar' })
                      .then(response => {
                        console.log('Custom sidebar hide response on blank page:', response);
                        sendAsyncResponse({ success: true, custom: true });
                      })
                      .catch(error => {
                        console.error('Error hiding custom sidebar on blank page after injection:', error);
                        // Still return success to allow UI to proceed
                        sendAsyncResponse({ success: true, warning: 'Could not close sidebar on blank page, but UI can proceed' });
                      });
                  }, 300);
                })
                .catch(error => {
                  console.error('Error injecting Brave sidebar script before hiding:', error);

                  // Try direct message anyway as a fallback
                  chrome.tabs.sendMessage(tab.id, { action: 'hideBraveSidebar' })
                    .then(response => {
                      console.log('Custom sidebar hide response on blank page (fallback):', response);
                      sendAsyncResponse({ success: true, custom: true });
                    })
                    .catch(error => {
                      console.error('Error hiding custom sidebar on blank page (fallback):', error);
                      // Still return success to allow UI to proceed
                      sendAsyncResponse({ success: true, warning: 'Could not close sidebar on blank page, but UI can proceed' });
                    });
                });
              } catch (error) {
                console.error('Error in try-catch block for hiding sidebar on blank page:', error);
                // Still return success to allow UI to proceed
                sendAsyncResponse({ success: true, warning: 'Could not close sidebar on blank page, but UI can proceed' });
              }
              return true;
            }
            // Skip other unsupported URLs
            else if (tab.url.startsWith('chrome://') ||
                tab.url.startsWith('brave://') ||
                tab.url.startsWith('about:') ||
                tab.url.startsWith('chrome-extension://') ||
                tab.url.startsWith('brave-extension://')) {
              console.log('Cannot interact with browser internal page');
              return sendAsyncResponse({ success: true, message: 'Internal page, no action needed' });
            }

            // Send a message to the content script to hide the sidebar with better error handling
            try {
              // Check if the URL is a protected URL that doesn't allow content script injection
              const isProtectedUrl = tab.url.startsWith('chrome://') ||
                                    tab.url.startsWith('brave://') ||
                                    tab.url.startsWith('edge://') ||
                                    tab.url.startsWith('about:') ||
                                    tab.url.startsWith('chrome-extension://') ||
                                    tab.url.startsWith('devtools://');

              if (isProtectedUrl) {
                console.log('Protected URL detected, skipping script injection:', tab.url);
                // For protected URLs, just return success without trying to inject scripts
                sendAsyncResponse({ success: true, warning: 'Protected URL detected, sidebar cannot be controlled on this page' });
                return;
              }

              // For regular URLs, try to inject the content script if it's not already there
              chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content/brave-sidebar.js']
              })
              .then(() => {
                console.log('Injected Brave sidebar script before hiding');

                // Wait a moment for the script to initialize
                setTimeout(() => {
                  // Now try to hide the sidebar
                  chrome.tabs.sendMessage(tab.id, { action: 'hideBraveSidebar' })
                    .then(response => {
                      console.log('Custom sidebar hide response:', response);
                      sendAsyncResponse({ success: true, custom: true });
                    })
                    .catch(error => {
                      console.error('Error hiding custom sidebar after injection:', error);
                      // Still return success to allow UI to proceed
                      sendAsyncResponse({ success: true, warning: 'Could not close sidebar, but UI can proceed' });
                    });
                }, 300);
              })
              .catch(error => {
                console.error('Error injecting Brave sidebar script before hiding:', error);

                // Try direct message anyway as a fallback
                chrome.tabs.sendMessage(tab.id, { action: 'hideBraveSidebar' })
                  .then(response => {
                    console.log('Custom sidebar hide response (fallback):', response);
                    sendAsyncResponse({ success: true, custom: true });
                  })
                  .catch(error => {
                    console.error('Error hiding custom sidebar (fallback):', error);
                    // Still return success to allow UI to proceed
                    sendAsyncResponse({ success: true, warning: 'Could not close sidebar, but UI can proceed' });
                  });
              });
            } catch (error) {
              console.error('Error in try-catch block for hiding sidebar:', error);
              // Still return success to allow UI to proceed
              sendAsyncResponse({ success: true, warning: 'Could not close sidebar, but UI can proceed' });
            }
          })
          .catch(error => {
            console.error('Error getting active tab:', error);
            sendAsyncResponse({ success: false, error: error.message });
          });
      } catch (error) {
        console.error('Error closing side panel:', error);
        // Return success anyway to allow UI to proceed
        return sendAsyncResponse({
          success: true,
          warning: 'Could not close side panel, but UI can proceed'
        });
      }
      return true; // Indicates we'll respond asynchronously

    case 'configureSidePanel':
      // Configure the side panel behavior
      try {
        // For our custom sidebar implementation, no special configuration is needed
        console.log('Using custom sidebar implementation, no special configuration needed');
        sendAsyncResponse({
          success: true,
          custom: true,
          message: 'Using custom sidebar implementation for all browsers'
        });
      } catch (error) {
        console.error('Error configuring side panel:', error);
        sendAsyncResponse({ success: false, error: error.message });
      }
      return true; // Indicates we'll respond asynchronously

    case 'analyzePage':
      // Analyze the current page from the sidebar
      try {
        // Get the current tab
        chrome.tabs.query({ active: true, currentWindow: true })
          .then(tabs => {
            if (!tabs || tabs.length === 0) {
              return sendAsyncResponse({ success: false, error: 'No active tab found' });
            }

            const tab = tabs[0];

            // Skip unsupported URLs
            if (tab.url.startsWith('chrome://') ||
                tab.url.startsWith('edge://') ||
                tab.url.startsWith('about:') ||
                tab.url.startsWith('chrome-extension://')) {
              return sendAsyncResponse({
                success: false,
                error: 'Cannot analyze this type of page (browser internal page)'
              });
            }

            // Check if we already have cached content for this tab
            if (pageContentCache[tab.id]) {
              // Open the popup with the analysis
              let popupUrl = chrome.runtime.getURL('popup/popup.html?analyze=true');
              chrome.tabs.create({ url: popupUrl })
                .then(() => sendAsyncResponse({ success: true }))
                .catch(error => sendAsyncResponse({ success: false, error: error.message }));
            } else {
              // Scan the tab first, then open the popup
              chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content/scanner.js']
              })
              .then(() => {
                // Wait a moment for the script to initialize
                setTimeout(() => {
                  // Try to extract content
                  chrome.tabs.sendMessage(tab.id, { action: 'extractPageContent' })
                    .then(response => {
                      if (response && response.success && response.content) {
                        // Cache the content
                        pageContentCache[tab.id] = response.content;

                        // Open the popup with the analysis
                        let popupUrl = chrome.runtime.getURL('popup/popup.html?analyze=true');
                        chrome.tabs.create({ url: popupUrl })
                          .then(() => sendAsyncResponse({ success: true }))
                          .catch(error => sendAsyncResponse({ success: false, error: error.message }));
                      } else {
                        throw new Error('Failed to extract content from tab');
                      }
                    })
                    .catch(error => {
                      console.error('Error during tab scanning:', error);
                      sendAsyncResponse({ success: false, error: error.message });
                    });
                }, 1000);
              })
              .catch(error => {
                console.error('Error injecting scanner script:', error);
                sendAsyncResponse({ success: false, error: error.message });
              });
            }
          })
          .catch(error => {
            console.error('Error getting active tab:', error);
            sendAsyncResponse({ success: false, error: error.message });
          });
      } catch (error) {
        console.error('Error analyzing page:', error);
        return sendAsyncResponse({ success: false, error: error.message });
      }
      return true; // Indicates we'll respond asynchronously

    case 'getBrowserHistoryJoke':
      // Generate a joke about the user's browser history
      generateBrowserHistoryJoke()
        .then(joke => sendResponse({ success: true, joke }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Indicates we'll respond asynchronously

    case 'scanTabContent':
      // Scan content from a specific tab
      const tabId = request.tabId;
      if (!tabId) {
        return sendAsyncResponse({ success: false, error: 'No tab ID provided' });
      }

      // Check if we already have cached content for this tab
      if (pageContentCache[tabId]) {
        return sendAsyncResponse({ success: true, content: pageContentCache[tabId] });
      }

      // Get tab info and process content
      chrome.tabs.get(tabId)
        .then(tab => {
          // Skip unsupported URLs
          if (tab.url.startsWith('chrome://') ||
              tab.url.startsWith('edge://') ||
              tab.url.startsWith('about:') ||
              tab.url.startsWith('chrome-extension://')) {
            return sendAsyncResponse({
              success: false,
              error: 'Cannot scan this type of page (browser internal page)'
            });
          }

          console.log('Scanning tab:', tab.title, tab.url);

          // Inject the scanner script
          chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['content/scanner.js']
          })
          .then(() => {
            // Wait a moment for the script to initialize
            setTimeout(() => {
              // Try to extract content
              chrome.tabs.sendMessage(tabId, { action: 'extractPageContent' })
                .then(response => {
                  if (response && response.success && response.content) {
                    // Cache the content
                    pageContentCache[tabId] = response.content;
                    sendAsyncResponse({ success: true, content: response.content });
                  } else {
                    throw new Error('Failed to extract content from tab: ' +
                                  (response?.error || 'Unknown error'));
                  }
                })
                .catch(error => {
                  console.error('Error during tab scanning:', error);

                  // Try one more time with a different approach
                  chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    func: () => {
                      return {
                        url: window.location.href,
                        title: document.title,
                        content: document.body.innerText,
                        timestamp: new Date().toISOString()
                      };
                    }
                  })
                  .then(result => {
                    if (result && result[0] && result[0].result) {
                      const content = result[0].result;
                      // Cache the content
                      pageContentCache[tabId] = content;
                      sendAsyncResponse({ success: true, content: content });
                    } else {
                      throw new Error('Failed to extract content using direct script execution');
                    }
                  })
                  .catch(directError => {
                    console.error('Error during direct script execution:', directError);
                    sendAsyncResponse({
                      success: false,
                      error: 'All content extraction methods failed'
                    });
                  });
                });
            }, 1000);
          })
          .catch(error => {
            console.error('Error injecting scanner script:', error);
            sendAsyncResponse({ success: false, error: error.message });
          });
        })
        .catch(error => {
          console.error('Error getting tab info:', error);
          sendAsyncResponse({ success: false, error: error.message });
        });

      return true; // Indicates we'll respond asynchronously
  }
});

/**
 * Fetch and categorize browser history for jokes
 * @returns {Promise<object>} - Categorized browser history
 */
function fetchBrowserHistory() {
  return new Promise((resolve) => {
    // Check if we have a recent cache (less than 1 hour old)
    const now = new Date().getTime();
    if (browserHistoryCache.timestamp && (now - browserHistoryCache.timestamp < 3600000)) {
      console.log('Using cached browser history');
      resolve(browserHistoryCache);
      return;
    }

    // Fetch the last 100 history items
    chrome.history.search({
      text: '',          // Search all history
      maxResults: 100,   // Limit to 100 results
      startTime: now - (7 * 24 * 60 * 60 * 1000) // Last 7 days
    }, (historyItems) => {
      console.log(`Fetched ${historyItems.length} history items`);

      // Reset categories
      browserHistoryCache.categories = {
        shopping: [],
        social: [],
        entertainment: [],
        tech: [],
        news: [],
        adult: [],
        gaming: [],
        food: [],
        travel: [],
        other: []
      };

      // Categorize each history item
      historyItems.forEach(item => {
        const url = item.url.toLowerCase();
        // Use both URL and title for better categorization
        const title = (item.title || '').toLowerCase();

        // Function to check if either URL or title contains any of the keywords
        const containsAny = (keywords) => {
          return keywords.some(keyword =>
            url.includes(keyword) || title.includes(keyword)
          );
        };

        // Shopping sites
        if (containsAny(['amazon', 'ebay', 'walmart', 'etsy', 'shop', 'store', 'buy',
                         'price', 'cart', 'checkout', 'order', 'purchase', 'product'])) {
          browserHistoryCache.categories.shopping.push(item);
        }
        // Social media
        else if (containsAny(['facebook', 'twitter', 'instagram', 'tiktok', 'reddit',
                             'linkedin', 'social', 'forum', 'comment', 'post', 'share',
                             'like', 'follow', 'friend', 'message', 'chat'])) {
          browserHistoryCache.categories.social.push(item);
        }
        // Entertainment
        else if (containsAny(['youtube', 'netflix', 'hulu', 'disney', 'movie', 'tv',
                             'stream', 'watch', 'video', 'film', 'series', 'episode',
                             'show', 'entertainment', 'music', 'song', 'playlist'])) {
          browserHistoryCache.categories.entertainment.push(item);
        }
        // Tech
        else if (containsAny(['github', 'stackoverflow', 'code', 'tech', 'programming',
                             'developer', 'tutorial', 'software', 'hardware', 'computer',
                             'algorithm', 'function', 'class', 'api', 'framework'])) {
          browserHistoryCache.categories.tech.push(item);
        }
        // News
        else if (containsAny(['news', 'cnn', 'bbc', 'nytimes', 'article', 'blog',
                             'report', 'headline', 'journalist', 'media', 'press',
                             'breaking', 'politics', 'economy', 'world'])) {
          browserHistoryCache.categories.news.push(item);
        }
        // Adult content (for jokes)
        else if (containsAny(['adult', 'xxx', 'porn', 'sex', 'dating', 'match',
                             'tinder', 'bumble', 'hookup', 'nsfw', 'mature'])) {
          browserHistoryCache.categories.adult.push(item);
        }
        // Gaming
        else if (containsAny(['game', 'steam', 'epic', 'playstation', 'xbox', 'nintendo',
                             'gaming', 'player', 'level', 'score', 'achievement', 'quest',
                             'mission', 'character', 'rpg', 'fps', 'mmorpg'])) {
          browserHistoryCache.categories.gaming.push(item);
        }
        // Food
        else if (containsAny(['food', 'recipe', 'cook', 'restaurant', 'doordash', 'ubereats',
                             'meal', 'dinner', 'lunch', 'breakfast', 'ingredient', 'cuisine',
                             'dish', 'menu', 'chef', 'bake', 'grill'])) {
          browserHistoryCache.categories.food.push(item);
        }
        // Travel
        else if (containsAny(['travel', 'hotel', 'flight', 'booking', 'airbnb', 'vacation',
                             'trip', 'journey', 'tour', 'destination', 'resort', 'beach',
                             'mountain', 'cruise', 'passport', 'holiday'])) {
          browserHistoryCache.categories.travel.push(item);
        }
        // Other
        else {
          browserHistoryCache.categories.other.push(item);
        }
      });

      // Store all history items
      browserHistoryCache.history = historyItems;
      browserHistoryCache.timestamp = now;

      console.log('Browser history categorized:', {
        shopping: browserHistoryCache.categories.shopping.length,
        social: browserHistoryCache.categories.social.length,
        entertainment: browserHistoryCache.categories.entertainment.length,
        tech: browserHistoryCache.categories.tech.length,
        news: browserHistoryCache.categories.news.length,
        adult: browserHistoryCache.categories.adult.length,
        gaming: browserHistoryCache.categories.gaming.length,
        food: browserHistoryCache.categories.food.length,
        travel: browserHistoryCache.categories.travel.length,
        other: browserHistoryCache.categories.other.length
      });

      resolve(browserHistoryCache);
    });
  });
}

/**
 * Generate a joke about the user's browser history
 * @returns {Promise<string>} - A joke about the user's browser history
 */
function generateBrowserHistoryJoke() {
  return new Promise((resolve) => {
    fetchBrowserHistory().then(historyCache => {
      // Find the category with the most items
      let topCategory = 'other';
      let topCount = 0;

      Object.keys(historyCache.categories).forEach(category => {
        const count = historyCache.categories[category].length;
        if (count > topCount) {
          topCount = count;
          topCategory = category;
        }
      });

      // Generate a joke based on the top category
      let joke = '';

      switch (topCategory) {
        case 'shopping':
          joke = "I see you've been shopping a lot lately. Your credit card must be begging for mercy!";
          break;
        case 'social':
          joke = "Wow, you spend more time on social media than a teenager. No wonder you have no real friends!";
          break;
        case 'entertainment':
          joke = "Based on your browser history, you've watched so many videos that your eyes must be square by now!";
          break;
        case 'tech':
          joke = "Your browser history is full of tech sites. No wonder you need an AI to talk to - you've scared away all the humans with your nerd talk!";
          break;
        case 'news':
          joke = "You read a lot of news, huh? Trying to find something more depressing than your own life?";
          break;
        case 'adult':
          joke = "I'd comment on your 'adult' browsing habits, but I'm trying to keep this PG-13. Let's just say your incognito mode is getting a workout!";
          break;
        case 'gaming':
          joke = "Your browser history shows you're really into games. Is that why your productivity is at level 1 while your procrastination is at level 100?";
          break;
        case 'food':
          joke = "You look at a lot of food websites. Is that why your keyboard has more crumbs than a bakery floor?";
          break;
        case 'travel':
          joke = "Planning another trip? According to your browser history, you spend more time dreaming of vacations than actually working to afford them!";
          break;
        case 'other':
          // If we have some history but it's mostly uncategorized
          if (historyCache.history.length > 0) {
            joke = "Your browser history is so random, even Google's algorithms are confused about what to advertise to you!";
          } else {
            joke = "Your browser history is suspiciously empty. What are you hiding? Or do you just live in incognito mode?";
          }
          break;
      }

      // Add a second joke about a specific site if we have one
      if (historyCache.history.length > 0) {
        // Pick a random history item from the top category or any category if top is empty
        const items = historyCache.categories[topCategory].length > 0 ?
                      historyCache.categories[topCategory] :
                      historyCache.history;

        if (items.length > 0) {
          const randomItem = items[Math.floor(Math.random() * items.length)];
          const domain = new URL(randomItem.url).hostname.replace('www.', '');

          // Add a site-specific joke
          if (domain.includes('youtube')) {
            joke += " And seriously, how many cat videos does one person need to watch on YouTube?";
          } else if (domain.includes('amazon')) {
            joke += " Amazon should name a warehouse after you with all the shopping you do there!";
          } else if (domain.includes('facebook')) {
            joke += " Facebook knows more about your life than your own mother at this point!";
          } else if (domain.includes('reddit')) {
            joke += " You spend so much time on Reddit, you probably think 'touching grass' is a new subreddit!";
          } else if (domain.includes('netflix')) {
            joke += " Netflix is probably wondering if you ever leave your couch!";
          } else if (domain.includes('twitter') || domain.includes('x.com')) {
            joke += " Your Twitter/X addiction explains your short attention span and hot takes!";
          } else if (domain.includes('instagram')) {
            joke += " Instagram has you convinced everyone else's life is perfect while you scroll in your pajamas!";
          } else if (domain.includes('tiktok')) {
            joke += " TikTok has rotted your brain so much you probably can't focus for more than 15 seconds!";
          } else {
            joke += ` I see you visit ${domain} a lot. That explains... well, it explains a lot about you!`;
          }
        }
      }

      resolve(joke);
    }).catch(error => {
      console.error('Error generating browser history joke:', error);
      resolve("I tried to make a joke about your browser history, but it seems you're browsing history is as empty as your social calendar!");
    });
  });
}

// Handle tab updates to refresh content cache
chrome.tabs.onUpdated.addListener((tabId, changeInfo, _tabInfo) => {
  if (changeInfo.status === 'complete') {
    // Clear cache for this tab since it has been updated
    delete pageContentCache[tabId];

    // First check if the tab still exists
    chrome.tabs.get(tabId).then(tab => {
      // Skip unsupported URLs
      if (tab.url.startsWith('chrome://') ||
          tab.url.startsWith('edge://') ||
          tab.url.startsWith('about:') ||
          tab.url.startsWith('chrome-extension://')) {
        return; // Skip injection for browser internal pages
      }

      // Determine if this is a YouTube page or PDF
      const isYouTube = tab.url && (tab.url.includes('youtube.com') || tab.url.includes('youtu.be'));
      const isPDF = tab.url && (tab.url.toLowerCase().endsWith('.pdf') ||
                 tab.url.toLowerCase().includes('pdf') ||
                 tab.url.toLowerCase().includes('viewer.html'));

      let contentScriptFile = 'content/content.js';
      if (isYouTube) {
        contentScriptFile = 'content/youtube-content.js';
      } else if (isPDF) {
        contentScriptFile = 'content/pdf-content.js';
      }

      // First check if the content script is already injected
      chrome.tabs.sendMessage(tabId, { action: 'ping' })
        .then(response => {
          if (response && response.success) {
            // Content script is already injected, request content extraction
            return chrome.tabs.sendMessage(tabId, { action: 'extractPageContent' });
          } else {
            throw new Error('Content script not responding');
          }
        })
        .catch(error => {
          console.warn(`Content script not available for ${isYouTube ? 'YouTube' : 'regular'} page, injecting it:`, error);

          // Inject the appropriate content script manually
          chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: [contentScriptFile]
          })
          .then(() => {
            console.log(`${isYouTube ? 'YouTube' : 'Regular'} content script injected successfully`);
            // Wait a moment for the script to initialize
            setTimeout(() => {
              chrome.tabs.sendMessage(tabId, { action: 'extractPageContent' })
                .catch(err => console.warn(`Failed to extract content after injection for ${isYouTube ? 'YouTube' : 'regular'} page:`, err));
            }, 500);
          })
          .catch(injectError => {
            console.error(`Failed to inject ${isYouTube ? 'YouTube' : 'regular'} content script:`, injectError);
          });
        });
    }).catch(error => {
      // Tab no longer exists or cannot be accessed
      console.warn(`Tab ${tabId} no longer exists or cannot be accessed:`, error);
    });
  }
});

/**
 * Perform a web search using Google Custom Search API
 * @param {string} query - The search query
 * @param {number} numResults - Number of results to return
 * @returns {Promise<Array>} - Array of search results
 */
async function performWebSearch(query, numResults = 5) {
  try {
    console.log(`Performing web search for: ${query}`);

    // Skip the actual API call and go straight to simulated results
    // This ensures the feature works without requiring an API key
    return simulateSearchResults(query, numResults);

    /* Uncomment and configure this section if you want to use a real search API
    const response = await fetch(`https://serpapi.com/search.json?engine=google&q=${encodeURIComponent(query)}&api_key=YOUR_SERPAPI_KEY&num=${numResults}`);

    if (!response.ok) {
      throw new Error(`Search API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.organic_results || data.organic_results.length === 0) {
      // Fallback to simulated results if no real results are available
      return simulateSearchResults(query, numResults);
    }

    // Format the results
    return data.organic_results.map(result => ({
      title: result.title,
      url: result.link,
      snippet: result.snippet
    }));
    */
  } catch (error) {
    console.error('Web search error:', error);
    // Fallback to simulated results
    return simulateSearchResults(query, numResults);
  }
}

/**
 * Simulate search results when the API is not available
 * @param {string} query - The search query
 * @param {number} numResults - Number of results to return
 * @returns {Array} - Array of simulated search results
 */
function simulateSearchResults(query, numResults = 5) {
  // This is a fallback function that simulates search results
  // In a production environment, you would use a real search API
  console.log(`Simulating search results for: ${query}`);

  // Check for specific queries to provide more relevant results
  if (query.toLowerCase().includes('pinchofyum')) {
    // Custom results for Pinch of Yum (food blog)
    return [
      {
        title: `Pinch of Yum - Food Blog with Healthy and Delicious Recipes`,
        url: `https://pinchofyum.com/`,
        snippet: `Pinch of Yum is a food blog with hundreds of simple, healthy recipes and food blogging resources. Founded by Lindsay and Bjork Ostrom.`
      },
      {
        title: `About Pinch of Yum - Our Story`,
        url: `https://pinchofyum.com/about`,
        snippet: `Learn about Lindsay and Bjork Ostrom, the creators behind Pinch of Yum, a popular food blog featuring simple, tasty recipes and food photography.`
      },
      {
        title: `Pinch of Yum's Most Popular Recipes | Food Blog Success Story`,
        url: `https://pinchofyum.com/recipes`,
        snippet: `Discover the most popular recipes from Pinch of Yum, including easy dinner ideas, healthy meal prep options, and comfort food classics.`
      },
      {
        title: `How Pinch of Yum Became a Seven-Figure Food Blog | Case Study`,
        url: `https://www.entrepreneur.com/article/pinchofyum-success`,
        snippet: `Case study on how Lindsay and Bjork Ostrom turned their food blog Pinch of Yum into a seven-figure business through recipe development, food photography, and digital marketing.`
      },
      {
        title: `Food Blogger Pro - Founded by Pinch of Yum Creators`,
        url: `https://foodbloggerpro.com/`,
        snippet: `Food Blogger Pro is an online community and learning resource for food bloggers created by Bjork Ostrom, co-founder of Pinch of Yum.`
      }
    ].slice(0, numResults);
  }

  // Generic results for any other query
  const baseResults = [
    {
      title: `${query} - Wikipedia`,
      url: `https://en.wikipedia.org/wiki/${query.replace(/ /g, '_')}`,
      snippet: `Comprehensive information about ${query} from Wikipedia, the free encyclopedia.`
    },
    {
      title: `Latest Research on ${query} | ScienceDirect`,
      url: `https://www.sciencedirect.com/search?qs=${query.replace(/ /g, '%20')}`,
      snippet: `Find peer-reviewed articles and research papers about ${query} from leading academic journals.`
    },
    {
      title: `${query} News, Articles and Research | New York Times`,
      url: `https://www.nytimes.com/search?query=${query.replace(/ /g, '+')}`,
      snippet: `Latest news, analysis and opinion about ${query} from the New York Times.`
    },
    {
      title: `Understanding ${query} | National Geographic`,
      url: `https://www.nationalgeographic.com/search?q=${query.replace(/ /g, '+')}`,
      snippet: `Explore ${query} through National Geographic's lens - articles, photos, and videos.`
    },
    {
      title: `${query} Explained | MIT Technology Review`,
      url: `https://www.technologyreview.com/search/?s=${query.replace(/ /g, '+')}`,
      snippet: `MIT Technology Review's analysis and explanation of ${query} and its implications.`
    },
    {
      title: `${query} | Scientific American`,
      url: `https://www.scientificamerican.com/search/?q=${query.replace(/ /g, '+')}`,
      snippet: `Scientific American provides insights and research about ${query} from scientific perspective.`
    },
    {
      title: `${query} Research Papers | Google Scholar`,
      url: `https://scholar.google.com/scholar?q=${query.replace(/ /g, '+')}`,
      snippet: `Academic papers, citations and scholarly literature about ${query}.`
    }
  ];

  // Return the requested number of results
  return baseResults.slice(0, numResults);
}

// Import the ProxyService
import ProxyService from './proxy-service.js';

// Initialize the proxy service
const proxyService = new ProxyService();

/**
 * Fetch and process an external webpage
 * @param {string} url - The URL to fetch
 * @param {boolean} includeImages - Whether to include image descriptions
 * @param {boolean} deepAnalysis - Whether to perform deep analysis
 * @returns {Promise<string>} - The processed webpage content
 */
async function fetchExternalWebpage(url, includeImages = false, deepAnalysis = false) {
  try {
    console.log(`Fetching external webpage: ${url}`);
    console.log(`Options: includeImages=${includeImages}, deepAnalysis=${deepAnalysis}`);

    // Use our proxy service to fetch the webpage
    const response = await proxyService.fetchWithProxy(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.success) {
      throw new Error(`Failed to fetch page: ${response.status}`);
    }

    const html = response.content;

    // Process the HTML content
    let processedContent = '';

    if (deepAnalysis) {
      // Perform more detailed extraction for deep analysis
      processedContent = extractStructuredContent(html, includeImages);
    } else {
      // Standard text extraction
      processedContent = extractTextFromHTML(html, includeImages);
    }

    return processedContent;
  } catch (error) {
    console.error('Fetch external webpage error:', error);

    // Provide a more helpful error message
    if (error.message.includes('Failed to fetch')) {
      throw new Error(`Could not access the webpage. This might be due to CORS restrictions or the website blocking access. Try visiting the page directly in your browser.`);
    }

    // For social media sites, provide specific guidance
    if (url.includes('instagram.com') || url.includes('facebook.com') ||
        url.includes('twitter.com') || url.includes('linkedin.com')) {
      throw new Error(`This appears to be a social media site that requires authentication. The extension can only access public content. For better results, try accessing specific public pages or profiles.`);
    }

    // Fallback to simulated content in development environments
    if (process.env.NODE_ENV === 'development') {
      return simulatePageContent(url);
    } else {
      throw error;
    }
  }
}

/**
 * Extract structured content from HTML
 * @param {string} html - The HTML content
 * @param {boolean} includeImages - Whether to include image descriptions
 * @returns {string} - The structured content
 */
function extractStructuredContent(html, includeImages = false) {
  try {
    // Create a DOM parser
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Get the title
    const title = doc.title || '';

    // Get meta description
    let metaDescription = '';
    const metaDescEl = doc.querySelector('meta[name="description"]');
    if (metaDescEl) {
      metaDescription = metaDescEl.getAttribute('content') || '';
    }

    // Get meta keywords
    let metaKeywords = '';
    const metaKeywordsEl = doc.querySelector('meta[name="keywords"]');
    if (metaKeywordsEl) {
      metaKeywords = metaKeywordsEl.getAttribute('content') || '';
    }

    // Get Open Graph data
    let ogDescription = '';
    let ogImage = '';

    // Get Open Graph description
    const ogDescEl = doc.querySelector('meta[property="og:description"]');
    if (ogDescEl) {
      ogDescription = ogDescEl.getAttribute('content') || '';
    }

    // Get Open Graph image
    const ogImageEl = doc.querySelector('meta[property="og:image"]');
    if (ogImageEl) {
      ogImage = ogImageEl.getAttribute('content') || '';
    }

    // Check for social media specific content
    const isSocialMedia = html.includes('instagram') || html.includes('facebook') ||
                         html.includes('twitter') || html.includes('linkedin');

    // Extract main content
    let mainContent = '';

    // For social media, try to extract specific content
    if (isSocialMedia) {
      mainContent = extractSocialMediaContent(doc, html);
    } else {
      // Try to find the main content area
      const mainEl = doc.querySelector('main') ||
                    doc.querySelector('article') ||
                    doc.querySelector('#content') ||
                    doc.querySelector('.content') ||
                    doc.querySelector('.main');

      if (mainEl) {
        // Remove script, style, and other non-content elements
        const nonContentEls = mainEl.querySelectorAll('script, style, noscript, iframe, svg');
        nonContentEls.forEach(el => el.remove());

        mainContent = mainEl.textContent || '';
      } else {
        // Fallback to body content
        const bodyEl = doc.body;

        // Remove script, style, and other non-content elements
        const nonContentEls = bodyEl.querySelectorAll('script, style, noscript, iframe, svg');
        nonContentEls.forEach(el => el.remove());

        // Also try to remove navigation, header, footer, and sidebar elements
        const nonMainEls = bodyEl.querySelectorAll('nav, header, footer, aside');
        nonMainEls.forEach(el => el.remove());

        mainContent = bodyEl.textContent || '';
      }
    }

    // Clean up the text
    mainContent = mainContent.replace(/\s+/g, ' ').trim();

    // Extract images if requested
    let imageDescriptions = '';
    if (includeImages) {
      const images = doc.querySelectorAll('img[alt]');
      const imageDescs = [];

      images.forEach(img => {
        const alt = img.getAttribute('alt');
        const src = img.getAttribute('src');
        if (alt && alt.trim() && alt.length > 3) {
          imageDescs.push(`Image: ${alt} (${src})`);
        }
      });

      if (imageDescs.length > 0) {
        imageDescriptions = '\n\nImages on the page:\n' + imageDescs.join('\n');
      }

      // If we have OG image but no other images, include it
      if (imageDescs.length === 0 && ogImage) {
        imageDescriptions = '\n\nFeatured Image: ' + ogImage;
      }
    }

    // Extract links
    const links = doc.querySelectorAll('a[href]');
    let importantLinks = '';

    if (links.length > 0) {
      const linkArray = [];
      let linkCount = 0;

      links.forEach(link => {
        const href = link.getAttribute('href');
        const text = link.textContent.trim();

        // Only include links with text and that aren't just "#" or "javascript:void(0)"
        if (href && text && href !== '#' && !href.startsWith('javascript:') && linkCount < 10) {
          linkArray.push(`${text}: ${href}`);
          linkCount++;
        }
      });

      if (linkArray.length > 0) {
        importantLinks = '\n\nImportant Links:\n' + linkArray.join('\n');
      }
    }

    // Combine all the extracted content
    let structuredContent = `Title: ${title}\n\n`;

    if (metaDescription) {
      structuredContent += `Description: ${metaDescription}\n\n`;
    } else if (ogDescription) {
      structuredContent += `Description: ${ogDescription}\n\n`;
    }

    if (metaKeywords) {
      structuredContent += `Keywords: ${metaKeywords}\n\n`;
    }

    structuredContent += `Content: ${mainContent}`;
    structuredContent += imageDescriptions;
    structuredContent += importantLinks;

    return structuredContent;
  } catch (error) {
    console.error('Error extracting structured content:', error);
    return extractTextFromHTML(html, includeImages); // Fallback to simple extraction
  }
}

/**
 * Extract content specific to social media sites
 * @param {Document} doc - The parsed HTML document
 * @param {string} html - The raw HTML
 * @returns {string} - The extracted social media content
 */
function extractSocialMediaContent(doc, html) {
  let content = '';

  // Check for Instagram
  if (html.includes('instagram')) {
    // Try to extract profile information
    const profileNameEl = doc.querySelector('h1, h2');
    if (profileNameEl) {
      content += `Profile: ${profileNameEl.textContent.trim()}\n\n`;
    }

    // Try to extract bio
    const bioEl = doc.querySelector('.biography');
    if (bioEl) {
      content += `Bio: ${bioEl.textContent.trim()}\n\n`;
    }

    // Try to extract post content
    const posts = doc.querySelectorAll('article');
    if (posts.length > 0) {
      content += 'Recent Posts:\n';

      posts.forEach((post, index) => {
        if (index < 5) { // Limit to 5 posts
          const caption = post.querySelector('.caption');
          if (caption) {
            content += `Post ${index + 1}: ${caption.textContent.trim()}\n`;
          }
        }
      });

      content += '\n';
    }

    // Extract from JSON if available
    const jsonMatch = html.match(/<script type="text\/javascript">window\._sharedData = (.+?);<\/script>/);
    if (jsonMatch && jsonMatch[1]) {
      try {
        const data = JSON.parse(jsonMatch[1]);

        // Extract user data if available
        if (data.entry_data && data.entry_data.ProfilePage) {
          const user = data.entry_data.ProfilePage[0].graphql.user;
          content += `Username: ${user.username}\n`;
          content += `Full Name: ${user.full_name}\n`;
          content += `Followers: ${user.edge_followed_by.count}\n`;
          content += `Following: ${user.edge_follow.count}\n`;
          content += `Posts: ${user.edge_owner_to_timeline_media.count}\n\n`;

          if (user.biography) {
            content += `Biography: ${user.biography}\n\n`;
          }
        }
      } catch (e) {
        console.error('Error parsing Instagram JSON:', e);
      }
    }
  }

  // Check for Twitter/X
  else if (html.includes('twitter') || html.includes('x.com')) {
    // Try to extract profile information
    const profileNameEl = doc.querySelector('[data-testid="UserName"]');
    if (profileNameEl) {
      content += `Profile: ${profileNameEl.textContent.trim()}\n\n`;
    }

    // Try to extract bio
    const bioEl = doc.querySelector('[data-testid="UserDescription"]');
    if (bioEl) {
      content += `Bio: ${bioEl.textContent.trim()}\n\n`;
    }

    // Try to extract tweets
    const tweets = doc.querySelectorAll('[data-testid="tweet"]');
    if (tweets.length > 0) {
      content += 'Recent Tweets:\n';

      tweets.forEach((tweet, index) => {
        if (index < 5) { // Limit to 5 tweets
          const tweetText = tweet.querySelector('[data-testid="tweetText"]');
          if (tweetText) {
            content += `Tweet ${index + 1}: ${tweetText.textContent.trim()}\n`;
          }
        }
      });

      content += '\n';
    }
  }

  // Check for Facebook
  else if (html.includes('facebook')) {
    // Try to extract profile information
    const profileNameEl = doc.querySelector('h1');
    if (profileNameEl) {
      content += `Profile: ${profileNameEl.textContent.trim()}\n\n`;
    }

    // Try to extract posts
    const posts = doc.querySelectorAll('[data-testid="post_message"]');
    if (posts.length > 0) {
      content += 'Recent Posts:\n';

      posts.forEach((post, index) => {
        if (index < 5) { // Limit to 5 posts
          content += `Post ${index + 1}: ${post.textContent.trim()}\n`;
        }
      });

      content += '\n';
    }
  }

  // Check for LinkedIn
  else if (html.includes('linkedin')) {
    // Try to extract profile information
    const profileNameEl = doc.querySelector('.top-card-layout__title');
    if (profileNameEl) {
      content += `Profile: ${profileNameEl.textContent.trim()}\n\n`;
    }

    // Try to extract headline
    const headlineEl = doc.querySelector('.top-card-layout__headline');
    if (headlineEl) {
      content += `Headline: ${headlineEl.textContent.trim()}\n\n`;
    }

    // Try to extract about section
    const aboutEl = doc.querySelector('.about-section');
    if (aboutEl) {
      content += `About: ${aboutEl.textContent.trim()}\n\n`;
    }

    // Try to extract experience
    const experienceEls = doc.querySelectorAll('.experience-section .pv-entity__summary-info');
    if (experienceEls.length > 0) {
      content += 'Experience:\n';

      experienceEls.forEach((exp, index) => {
        if (index < 5) { // Limit to 5 experiences
          content += `- ${exp.textContent.trim()}\n`;
        }
      });

      content += '\n';
    }
  }

  // If we couldn't extract specific social media content, return empty string
  // and let the main function handle it with the generic approach
  return content;
}

/**
 * Fetch content from a URL
 * @param {string} url - The URL to fetch
 * @returns {Promise<string>} - The page content
 */
async function fetchPageContent(url) {
  try {
    // Use a CORS proxy if needed
    const corsProxy = '';
    const fetchUrl = corsProxy + url;

    const response = await fetch(fetchUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch page: ${response.status}`);
    }

    const html = await response.text();

    // Extract text content from HTML
    return extractTextFromHTML(html);
  } catch (error) {
    console.error('Fetch page content error:', error);

    // Fallback to simulated content
    return simulatePageContent(url);
  }
}

/**
 * Extract text content from HTML
 * @param {string} html - The HTML content
 * @param {boolean} includeImages - Whether to include image descriptions
 * @returns {string} - The extracted text
 */
function extractTextFromHTML(html, includeImages = false) {
  try {
    // Create a DOM parser
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Get the title
    const title = doc.title || '';

    // Remove script and style elements
    const scripts = doc.querySelectorAll('script, style, noscript, iframe, svg');
    scripts.forEach(script => script.remove());

    // Get text content
    let text = doc.body.textContent || '';

    // Clean up the text
    text = text.replace(/\s+/g, ' ').trim();

    // Extract images if requested
    let imageDescriptions = '';
    if (includeImages) {
      const images = doc.querySelectorAll('img[alt]');
      const imageDescs = [];

      images.forEach(img => {
        const alt = img.getAttribute('alt');
        if (alt && alt.trim() && alt.length > 3) {
          imageDescs.push(`Image: ${alt}`);
        }
      });

      if (imageDescs.length > 0) {
        imageDescriptions = '\n\nImages on the page:\n' + imageDescs.join('\n');
      }
    }

    return `Title: ${title}\n\nContent: ${text}${imageDescriptions}`;
  } catch (error) {
    console.error('Error extracting text from HTML:', error);
    return html; // Return the raw HTML if extraction fails
  }
}

/**
 * Simulate page content when fetching fails
 * @param {string} url - The URL that was attempted to fetch
 * @returns {string} - Simulated content
 */
function simulatePageContent(url) {
  // Extract domain and path from URL
  let domain = 'unknown';
  let path = '';

  try {
    const urlObj = new URL(url);
    domain = urlObj.hostname;
    path = urlObj.pathname;
  } catch (e) {
    console.error('Error parsing URL:', e);
  }

  // Check for specific domains to provide more realistic content
  if (domain === 'pinchofyum.com') {
    if (path === '/' || path === '') {
      return `Pinch of Yum is a food blog founded by Lindsay and Bjork Ostrom. The blog features hundreds of simple, healthy, and delicious recipes along with stunning food photography. Lindsay is the recipe developer, photographer, and main content creator, while Bjork handles the business and technical aspects of the blog. Pinch of Yum began as a hobby in 2010 and has since grown into a full-time business with millions of monthly visitors. The blog focuses on approachable recipes that use everyday ingredients and provides detailed instructions to help home cooks succeed. Popular recipe categories include quick dinner ideas, vegetarian meals, healthy meal prep options, and comfort food classics. In addition to recipes, Pinch of Yum offers resources for food bloggers, including photography tips and income reports. The Ostroms also founded Food Blogger Pro, an online community and learning platform for food bloggers.`;
    } else if (path.includes('/about')) {
      return `About Pinch of Yum: Lindsay and Bjork Ostrom are the husband-and-wife team behind Pinch of Yum. Lindsay, a former fourth-grade teacher, started the blog in 2010 as a hobby to document her cooking adventures. Bjork joined the business side of the blog in 2014 when they decided to pursue Pinch of Yum full-time. Lindsay develops and photographs all the recipes, bringing her passion for simple, delicious food to life through her approachable cooking style and stunning photography. Bjork manages the technical and business aspects of the blog, including their income reports which they published from 2011 to 2017 to help other food bloggers understand the business side of blogging. The couple is based in Minnesota and has built Pinch of Yum into one of the most successful food blogs online, with millions of monthly visitors and a strong social media presence. They're also the founders of Food Blogger Pro, a membership site that helps food bloggers grow their websites and businesses.`;
    } else if (path.includes('/recipes')) {
      return `Pinch of Yum Recipes: This page features a comprehensive collection of recipes organized by category. Popular categories include: Quick and Easy Dinners, Vegetarian Meals, Healthy Meal Prep, Comfort Food Classics, Breakfast and Brunch, Soups and Stews, Salads, Desserts, and Seasonal Recipes. Some of the most popular recipes on Pinch of Yum include: Creamy Garlic Pasta, The Best Soft Chocolate Chip Cookies, 5-Ingredient Green Curry, Instant Pot Wild Rice Soup, Cauliflower Walnut Taco Meat, Healthy Banana Bread, and Crockpot Chicken Wild Rice Soup. Each recipe includes detailed instructions, ingredient lists, nutritional information, preparation and cooking times, and stunning food photography. Many recipes also feature step-by-step photos to guide home cooks through the process. Recipes typically focus on using accessible ingredients and straightforward techniques to create delicious meals that appeal to a wide audience. The blog emphasizes flexible cooking, often providing suggestions for ingredient substitutions and variations to accommodate different dietary needs and preferences.`;
    } else {
      return `Pinch of Yum is a popular food blog created by Lindsay and Bjork Ostrom featuring simple, healthy, and delicious recipes. The blog began in 2010 as a hobby and has grown into a successful full-time business with millions of monthly visitors. Lindsay develops and photographs all recipes, focusing on approachable cooking with everyday ingredients. Popular content includes quick dinner ideas, vegetarian recipes, healthy meal prep options, and comfort food classics. Each recipe includes detailed instructions, nutritional information, and high-quality food photography. Beyond recipes, Pinch of Yum offers resources for food bloggers including photography tips and business advice. The Ostroms also founded Food Blogger Pro, an online community and learning platform for food bloggers. Their success story has made them influential figures in the food blogging industry, demonstrating how passion for food and photography can be transformed into a thriving online business.`;
    }
  }

  // Generic simulated content for other URLs
  return `This is simulated content for ${url}. The actual content could not be fetched due to technical limitations. ` +
         `This would normally contain the full text content from the webpage at ${domain}${path}. ` +
         `For research purposes, please try visiting the URL directly in your browser.`;
}

// Initialize content script on extension installation or update
chrome.runtime.onInstalled.addListener(() => {
  // Get all existing tabs and inject the content script
  chrome.tabs.query({}, tabs => {
    for (const tab of tabs) {
      // Skip chrome:// and edge:// URLs as they don't allow content script injection
      if (!tab.url.startsWith('chrome://') && !tab.url.startsWith('edge://') &&
          !tab.url.startsWith('about:') && !tab.url.startsWith('chrome-extension://')) {

        // Verify the tab still exists before proceeding
        chrome.tabs.get(tab.id).then(currentTab => {
          // Determine if this is a YouTube page or PDF
          const isYouTube = currentTab.url && (currentTab.url.includes('youtube.com') || currentTab.url.includes('youtu.be'));
          const isPDF = currentTab.url && (currentTab.url.toLowerCase().endsWith('.pdf') ||
                       currentTab.url.toLowerCase().includes('pdf') ||
                       currentTab.url.toLowerCase().includes('viewer.html'));

          let contentScriptFile = 'content/content.js';
          if (isYouTube) {
            contentScriptFile = 'content/youtube-content.js';
          } else if (isPDF) {
            contentScriptFile = 'content/pdf-content.js';
          }

          // Try to inject the content script
          chrome.scripting.executeScript({
            target: { tabId: currentTab.id },
            files: [contentScriptFile]
          })
          .then(() => {
            console.log(`${isYouTube ? 'YouTube' : 'Regular'} content script injected into existing tab ${currentTab.id}`);
            // Request content extraction after a short delay
            setTimeout(() => {
              chrome.tabs.sendMessage(currentTab.id, { action: 'extractPageContent' })
                .catch(err => console.warn(`Failed to extract content from tab ${currentTab.id}:`, err));
            }, 500);
          })
          .catch(error => {
            console.warn(`Could not inject ${isYouTube ? 'YouTube' : 'regular'} content script into tab ${currentTab.id}:`, error);
          });
        }).catch(error => {
          console.warn(`Tab ${tab.id} no longer exists or cannot be accessed:`, error);
        });
      }
    }
  });
});

// Handle tab removals to clean up cache
chrome.tabs.onRemoved.addListener((tabId) => {
  delete pageContentCache[tabId];
});

/**
 * Makes an API request to the specified provider
 * @param {string} provider - The AI provider (openai, anthropic, deepseek, gemini)
 * @param {string} endpoint - The specific API endpoint
 * @param {object} data - Request data
 * @returns {Promise} - Promise resolving to the API response
 */
async function handleAPIRequest(provider, endpoint, data) {
  try {
    // Get API keys from storage
    const { apiKeys } = await chrome.storage.local.get('apiKeys');

    console.log('handleAPIRequest - Provider:', provider);
    console.log('handleAPIRequest - Endpoint:', endpoint);
    console.log('handleAPIRequest - API keys available:', {
      hasOpenAI: !!apiKeys?.openai,
      hasGemini: !!apiKeys?.gemini,
      hasOpenRouter: !!apiKeys?.openrouter,
      hasAnthropicKey: !!apiKeys?.anthropic,
      hasDeepSeekKey: !!apiKeys?.deepseek,
      requestedProvider: provider
    });

    if (!apiKeys || !apiKeys[provider]) {
      console.error(`No API key found for ${provider}`);
      throw new Error(`No API key found for ${provider}`);
    }

    // Log API key length for debugging
    console.log(`API key for ${provider} is ${apiKeys[provider] ? apiKeys[provider].length : 0} characters long`);

    let response;

    switch (provider) {
      case 'openai':
        console.log('Sending request to OpenAI API');
        response = await makeOpenAIRequest(endpoint, data, apiKeys.openai);
        break;
      case 'anthropic':
        console.log('Sending request to Anthropic API');
        response = await makeAnthropicRequest(endpoint, data, apiKeys.anthropic);
        break;
      case 'deepseek':
        console.log('Sending request to DeepSeek API');
        response = await makeDeepSeekRequest(endpoint, data, apiKeys.deepseek);
        break;
      case 'gemini':
        console.log('Sending request to Gemini API with endpoint:', endpoint);
        console.log('Gemini API key length:', apiKeys.gemini ? apiKeys.gemini.length : 0);
        response = await makeGeminiRequest(endpoint, data, apiKeys.gemini);
        break;
      case 'huggingface':
        console.log('Hugging Face API has been removed as requested');
        response = {
          success: false,
          error: "Hugging Face API has been removed from this extension."
        };
        break;
      default:
        console.error(`Unknown provider: ${provider}`);
        throw new Error(`Unknown provider: ${provider}`);
    }

    return response;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}

/**
 * Makes a request to the OpenAI API
 */
async function makeOpenAIRequest(endpoint, data, apiKey) {
  const url = `https://api.openai.com/v1/${endpoint}`;

  console.log('OpenAI API - Making request to:', endpoint);
  console.log('OpenAI API - Request data structure:', {
    hasModel: !!data.model,
    model: data.model,
    hasMessages: !!data.messages,
    messageCount: data.messages ? data.messages.length : 0
  });

  // Create fetch options
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify(data)
  };

  console.log('OpenAI API - Request URL:', url);
  console.log('OpenAI API - Request method:', options.method);

  try {
    const response = await fetch(url, options);
    console.log('OpenAI API - Response status:', response.status);

    if (!response.ok) {
      // Get the error response as text for better debugging
      const errorText = await response.text();
      console.error('OpenAI API error response:', errorText);

      try {
        // Try to parse the error as JSON for more details
        const errorJson = JSON.parse(errorText);
        console.error('Parsed error:', errorJson);

        // Check for specific error types
        if (errorJson.error && errorJson.error.message) {
          if (errorJson.error.message.includes('API key')) {
            throw new Error('OpenAI API key is not valid. Please check your API key in the settings.');
          } else if (errorJson.error.message.includes('rate limit')) {
            throw new Error('OpenAI API rate limit exceeded. Please try again later.');
          } else if (errorJson.error.message.includes('model')) {
            throw new Error(`OpenAI model error: ${errorJson.error.message}. Try using a different model.`);
          }

          throw new Error(`OpenAI API error: ${errorJson.error.message}`);
        }
      } catch (parseError) {
        // If parsing fails, use the raw error text
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
      }
    }

    // Get the response data
    const responseText = await response.text();
    console.log('OpenAI API - Raw response:', responseText.substring(0, 200) + '...');

    try {
      const responseData = JSON.parse(responseText);

      // Log the response structure for debugging
      console.log('OpenAI API - Response structure:', {
        hasData: !!responseData,
        hasChoices: responseData && responseData.choices ? responseData.choices.length : 0,
        hasUsage: responseData && !!responseData.usage,
        modelUsed: responseData && responseData.model ? responseData.model : 'unknown'
      });

      return responseData;
    } catch (parseError) {
      console.error('Error parsing OpenAI response as JSON:', parseError);
      throw new Error(`Failed to parse OpenAI response: ${responseText.substring(0, 100)}...`);
    }
  } catch (error) {
    // If the error is already formatted, just rethrow it
    if (error.message.includes('OpenAI API error:')) {
      throw error;
    }

    // Otherwise, format the error
    console.error('OpenAI request failed:', error);
    throw new Error(`OpenAI API error: ${error.message}`);
  }
}

/**
 * Makes a request to the Anthropic Claude API
 */
async function makeAnthropicRequest(endpoint, data, apiKey) {
  const url = `https://api.anthropic.com/v1/${endpoint}`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01',
      'anthropic-beta': 'max-tokens-3-7-0' // Required for claude-3-7 models
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Claude API error: ${error.error?.message || 'Unknown error'}`);
  }

  return await response.json();
}

/**
 * Makes a request to the DeepSeek API
 */
async function makeDeepSeekRequest(endpoint, data, apiKey) {
  const url = `https://api.deepseek.com/v1/${endpoint}`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`DeepSeek API error: ${error.error?.message || 'Unknown error'}`);
  }

  return await response.json();
}

/**
 * Makes a request to the Google Gemini API
 */
async function makeGeminiRequest(endpoint, data, apiKey) {
  // Validate API key
  if (!apiKey) {
    console.error('No Gemini API key provided');
    throw new Error('No Gemini API key provided. Please add your API key in the settings.');
  }

  console.log('Gemini API key available:', !!apiKey);
  console.log('Gemini API key length:', apiKey ? apiKey.length : 0);

  // For Gemini, the API key is typically passed as a URL parameter
  // Use v1 for all endpoints to ensure compatibility with the latest models
  const apiVersion = 'v1';

  // Handle model paths correctly
  let fullEndpoint = endpoint;

  // If this is a model endpoint but doesn't start with 'models/', add it
  if (endpoint !== 'models' && !endpoint.startsWith('models/')) {
    fullEndpoint = `models/${endpoint}`;
  }

  // Log the original endpoint for debugging
  console.log('Gemini API - Original endpoint:', endpoint);
  console.log('Gemini API - Full endpoint:', fullEndpoint);

  // Handle model normalization for Gemini models
  if (fullEndpoint.includes('gemini-1.5-flash-latest')) {
    console.log('Normalizing gemini-1.5-flash-latest to gemini-1.5-flash');
    fullEndpoint = fullEndpoint.replace('gemini-1.5-flash-latest', 'gemini-1.5-flash');
  } else if (fullEndpoint.includes('gemini-1.5-pro-latest')) {
    console.log('Normalizing problematic gemini-1.5-pro-latest to gemini-1.5-flash');
    fullEndpoint = fullEndpoint.replace('gemini-1.5-pro-latest', 'gemini-1.5-flash');
  } else if (fullEndpoint.includes('gemini-1.5-pro')) {
    console.log('Normalizing problematic gemini-1.5-pro to gemini-1.5-flash');
    fullEndpoint = fullEndpoint.replace('gemini-1.5-pro', 'gemini-1.5-flash');
  } else if (fullEndpoint.includes('gemini-pro-vision')) {
    console.log('Replacing deprecated gemini-pro-vision with gemini-1.5-flash');
    fullEndpoint = fullEndpoint.replace('gemini-pro-vision', 'gemini-1.5-flash');
  } else if (fullEndpoint.includes('gemini-pro') && !fullEndpoint.includes('gemini-pro-vision') && !fullEndpoint.includes('gemini-1.0-pro')) {
    console.log('Replacing deprecated gemini-pro with gemini-1.5-flash');
    fullEndpoint = fullEndpoint.replace('gemini-pro', 'gemini-1.5-flash');
  } else if (fullEndpoint.includes('gemini-1.0-pro-vision')) {
    console.log('Replacing deprecated gemini-1.0-pro-vision with gemini-1.5-flash');
    fullEndpoint = fullEndpoint.replace('gemini-1.0-pro-vision', 'gemini-1.5-flash');
  } else if (fullEndpoint.includes('gemini-1.0-pro')) {
    console.log('Replacing deprecated gemini-1.0-pro with gemini-1.5-flash');
    fullEndpoint = fullEndpoint.replace('gemini-1.0-pro', 'gemini-1.5-flash');
  }

  // Log the model being used
  console.log('Gemini API request - model being used:', fullEndpoint);

  // Ensure the model has the correct format for the API request
  // First, make sure we don't have any duplicate formatting
  if (fullEndpoint.includes('models/models/')) {
    fullEndpoint = fullEndpoint.replace('models/models/', 'models/');
  }

  // Make sure we have the correct format for the generateContent endpoint
  if (!fullEndpoint.includes('models/')) {
    console.log('Adding models/ prefix to endpoint');
    fullEndpoint = `models/${fullEndpoint}`;
  }

  // Ensure the model name is properly formatted with :generateContent
  if (!fullEndpoint.includes(':generateContent')) {
    console.log('Adding :generateContent to Gemini model endpoint');
    fullEndpoint = `${fullEndpoint}:generateContent`;
  }

  console.log('Final Gemini endpoint:', fullEndpoint);

  console.log('Gemini API - Final endpoint after normalization:', fullEndpoint);

  const url = `https://generativelanguage.googleapis.com/${apiVersion}/${fullEndpoint}?key=${apiKey}`;
  console.log('Gemini API - Request URL (API key redacted):', url.replace(apiKey, 'API_KEY_REDACTED'));

  try {
    // Use GET for listing models, POST for everything else
    const method = endpoint === 'models' ? 'GET' : 'POST';
    console.log('Gemini API - Request method:', method);

    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    // Only add body for POST requests
    if (method === 'POST') {
      // Ensure safety settings are properly set to avoid blocking
      if (data && data.safetySettings) {
        // Make sure safety settings are properly formatted
        data.safetySettings = data.safetySettings.map(setting => ({
          category: setting.category,
          threshold: setting.threshold || "BLOCK_ONLY_HIGH"
        }));
      } else if (data) {
        // Add default safety settings if none exist
        data.safetySettings = [
          { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" },
          { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
          { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
          { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" }
        ];
      }

      console.log('Gemini API - Request data structure:', {
        hasContents: !!data.contents,
        contentsLength: data.contents ? data.contents.length : 0,
        hasGenerationConfig: !!data.generationConfig,
        hasSafetySettings: !!data.safetySettings
      });

      options.body = JSON.stringify(data);

      // Log the request body for debugging
      console.log('Gemini request body:', JSON.stringify(data).substring(0, 200) + '...');
    }

    console.log(`Making Gemini API request to: ${apiVersion}/${fullEndpoint}`);
    console.log('Gemini API - Request URL (API key redacted):', url.replace(apiKey, 'API_KEY_REDACTED'));

    const response = await fetch(url, options);

    // Log the response status
    console.log(`Gemini API response status: ${response.status}`);

    if (!response.ok) {
      // Get the raw response text first
      const responseText = await response.text();
      console.error('Gemini API error raw response:', responseText);

      // Try to parse as JSON
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        // If not valid JSON, use the raw text
        throw new Error(`Gemini API error: ${responseText}`);
      }

      console.error('Gemini API error details:', errorData);

      // Extract detailed error message
      const errorMessage = errorData.error?.message || 'Unknown error';
      const errorDetails = errorData.error?.details ? JSON.stringify(errorData.error.details) : '';

      // Check for specific error types for better error messages
      if (errorMessage.includes('API key not valid') || errorMessage.includes('invalid API key')) {
        throw new Error('Gemini API key is not valid. Please check your API key in the settings.');
      } else if (errorMessage.includes('quota')) {
        throw new Error('Gemini API quota exceeded. Please try again later or check your API usage limits.');
      } else if (errorMessage.includes('model')) {
        throw new Error(`Gemini model error: ${errorMessage}. Try using a different model.`);
      } else if (errorMessage.includes('not found') || errorMessage.includes('does not exist')) {
        throw new Error(`Gemini model not found: ${fullEndpoint}. Please select a valid model like gemini-1.5-flash or gemini-1.5-pro.`);
      } else if (errorMessage.includes('permission') || errorMessage.includes('access')) {
        throw new Error(`Gemini API permission error: ${errorMessage}. Your API key may not have access to this model.`);
      }

      throw new Error(`Gemini API error: ${errorMessage} ${errorDetails}`);
    }

    // Get the raw response text first
    const responseText = await response.text();
    console.log('Gemini API raw response:', responseText.substring(0, 200) + '...');

    // Parse the response as JSON
    try {
      const responseData = JSON.parse(responseText);

      // Log the response structure for debugging
      console.log('Gemini API - Response structure:', {
        hasData: !!responseData,
        hasCandidates: responseData && responseData.candidates ? responseData.candidates.length : 0,
        hasPromptFeedback: responseData && !!responseData.promptFeedback,
        modelUsed: responseData && responseData.candidates && responseData.candidates[0] ?
          responseData.candidates[0].modelId || 'unknown' : 'unknown'
      });

      return responseData;
    } catch (e) {
      console.error('Error parsing Gemini response as JSON:', e);
      throw new Error(`Failed to parse Gemini response: ${responseText.substring(0, 100)}...`);
    }
  } catch (error) {
    // If the error is already formatted, just rethrow it
    if (error.message.includes('Gemini API error:')) {
      throw error;
    }

    // Otherwise, format the error
    console.error('Gemini request failed:', error);
    throw new Error(`Gemini API error: ${error.message}`);
  }
}

/**
 * Hugging Face API handling has been removed as requested
 * This function is kept as a stub to prevent errors
 * @param {string} endpoint - The model endpoint or API path
 * @param {object} data - The request data
 * @param {string} apiKey - The Hugging Face API key
 * @returns {Promise<object>} - Error response
 */
async function makeHuggingFaceRequest(endpoint, data, apiKey) {
  console.log('Hugging Face API has been removed as requested');
  return {
    success: false,
    error: "Hugging Face API has been removed from this extension."
  };
}

/**
 * Updates usage statistics for a provider
 * @param {string} provider - The AI provider name
 * @param {number} tokens - Number of tokens used
 * @param {string} model - The model used (optional)
 * @param {number} responseTime - Response time in milliseconds (optional)
 */
async function updateStats(provider, tokens, model, responseTime) {
  try {
    const { stats = {} } = await chrome.storage.local.get('stats');

    // Initialize stats object if it doesn't exist
    if (!stats[provider]) {
      stats[provider] = {
        requests: 0,
        tokens: 0,
        modelStats: {},
        modelTokens: {},
        responseTimes: []
      };
    }

    // Ensure all required properties exist
    if (!stats[provider].modelStats) stats[provider].modelStats = {};
    if (!stats[provider].modelTokens) stats[provider].modelTokens = {};
    if (!stats[provider].responseTimes) stats[provider].responseTimes = [];

    // Update the general statistics
    stats[provider].requests += 1;
    stats[provider].tokens += tokens;

    // Update model-specific statistics if model is provided
    if (model && model !== 'unknown') {
      // Initialize model stats if they don't exist
      if (!stats[provider].modelStats[model]) {
        stats[provider].modelStats[model] = 0;
        stats[provider].modelTokens[model] = 0;
      }

      // Update model-specific stats
      stats[provider].modelStats[model] += 1;
      stats[provider].modelTokens[model] += tokens;
    }

    // Track response time if provided
    if (responseTime && typeof responseTime === 'number' && responseTime > 0) {
      // Add to response times array (limit to last 100 requests to avoid excessive storage)
      stats[provider].responseTimes.push(responseTime);

      // Keep only the last 100 response times
      if (stats[provider].responseTimes.length > 100) {
        stats[provider].responseTimes = stats[provider].responseTimes.slice(-100);
      }
    }

    // Save back to storage
    await chrome.storage.local.set({ stats });

    return true;
  } catch (error) {
    console.error('Error updating stats:', error);
    throw error;
  }
}

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((message) => {
  console.log('Background script received message:', message);

  // Handle different message actions
  if (message.action === 'openPopup') {
    console.log('Opening popup from message', message.params);

    // Build the URL with parameters
    let url = 'popup/popup.html';
    if (message.params) {
      const params = new URLSearchParams();

      // Add all parameters
      for (const [key, value] of Object.entries(message.params)) {
        params.append(key, value);
      }

      // Add a timestamp to prevent caching issues
      params.append('t', new Date().getTime());

      url += '?' + params.toString();
    }

    // Open the popup in a new tab
    chrome.tabs.create({
      url: chrome.runtime.getURL(url)
    });

    return true; // Keep the message channel open for the async response
  }
});