'use strict';

/**
 * Compromise NLP Manager for advanced natural language understanding
 * Uses the compromise library for lightweight but powerful NLP
 */
class CompromiseNLPManager {
  /**
   * Create a new Compromise NLP Manager
   * @param {APIManager} apiManager - The API Manager instance
   */
  constructor(apiManager) {
    this.apiManager = apiManager;
    this.nlp = window.nlp; // Compromise library
    
    // Initialize intent patterns
    this.intentPatterns = {
      greeting: ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening', 'greetings', 'howdy'],
      farewell: ['goodbye', 'bye', 'see you', 'farewell', 'until next time', 'talk to you later'],
      help: ['help', 'assist', 'support', 'guide', 'how do i', 'how do you', 'what can you do', 'show me how'],
      summarize: ['summarize', 'summary', 'summarization', 'give me a summary', 'tldr', 'brief overview'],
      analyze: ['analyze', 'analysis', 'examine', 'inspect', 'review'],
      search: ['search', 'find', 'look for', 'where is', 'can you find'],
      translate: ['translate', 'translation'],
      explain: ['explain', 'explanation', 'clarify', 'elaborate', 'tell me about'],
      compare: ['compare', 'comparison', 'contrast', 'difference between', 'similarities between'],
      code: ['code', 'program', 'script', 'function', 'algorithm', 'implementation'],
      clearContent: ['clear scanned content', 'clear scanned tab', 'return to current tab']
    };
  }

  /**
   * Analyze user input to detect intent
   * @param {string} userInput - The user's message
   * @returns {object} - The detected intent and confidence
   */
  detectIntent(userInput) {
    if (!userInput || typeof userInput !== 'string') {
      return { intent: 'unknown', confidence: 0 };
    }

    try {
      // Parse the input with compromise
      const doc = this.nlp(userInput);
      
      // Check for questions
      const isQuestion = doc.questions().text() ? true : false;
      
      // Check for commands (imperative verbs)
      const isCommand = doc.verbs().isImperative().text() ? true : false;
      
      // Check for specific intents based on patterns
      const normalizedInput = userInput.trim().toLowerCase();
      let highestConfidence = 0;
      let detectedIntent = 'unknown';
      
      for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
        for (const pattern of patterns) {
          if (normalizedInput.includes(pattern)) {
            // Calculate confidence based on pattern length relative to input
            const confidence = Math.min(pattern.length / normalizedInput.length, 0.9);
            if (confidence > highestConfidence) {
              highestConfidence = confidence;
              detectedIntent = intent;
            }
          }
        }
      }
      
      // If we found a specific intent with good confidence, return it
      if (highestConfidence > 0.4) {
        return {
          intent: detectedIntent,
          confidence: highestConfidence,
          isQuestion: isQuestion,
          isCommand: isCommand
        };
      }
      
      // Otherwise, determine general intent type
      if (isQuestion) {
        return {
          intent: 'question',
          confidence: 0.7,
          isQuestion: true,
          isCommand: false
        };
      }
      
      if (isCommand) {
        return {
          intent: 'command',
          confidence: 0.6,
          isQuestion: false,
          isCommand: true
        };
      }
      
      // Default to conversation
      return {
        intent: 'conversation',
        confidence: 0.4,
        isQuestion: false,
        isCommand: false
      };
    } catch (error) {
      console.error('Error in detectIntent:', error);
      return { intent: 'unknown', confidence: 0, error: error.message };
    }
  }
  
  /**
   * Extract entities from user input
   * @param {string} userInput - The user's message
   * @returns {Array} - Array of extracted entities
   */
  extractEntities(userInput) {
    if (!userInput || typeof userInput !== 'string') {
      return [];
    }
    
    try {
      const doc = this.nlp(userInput);
      const entities = [];
      
      // Extract people
      const people = doc.people().out('array');
      people.forEach(person => {
        entities.push({
          type: 'person',
          value: person,
          text: person
        });
      });
      
      // Extract places
      const places = doc.places().out('array');
      places.forEach(place => {
        entities.push({
          type: 'place',
          value: place,
          text: place
        });
      });
      
      // Extract organizations
      const orgs = doc.organizations().out('array');
      orgs.forEach(org => {
        entities.push({
          type: 'organization',
          value: org,
          text: org
        });
      });
      
      // Extract dates
      const dates = doc.dates().out('array');
      dates.forEach(date => {
        entities.push({
          type: 'date',
          value: date,
          text: date
        });
      });
      
      // Extract values (numbers, money, etc)
      const values = doc.values().out('array');
      values.forEach(value => {
        entities.push({
          type: 'value',
          value: value,
          text: value
        });
      });
      
      // Extract nouns as potential entities
      const nouns = doc.nouns().out('array');
      nouns.forEach(noun => {
        // Only add if not already added as a specific entity type
        if (!entities.some(e => e.text === noun)) {
          entities.push({
            type: 'noun',
            value: noun,
            text: noun
          });
        }
      });
      
      return entities;
    } catch (error) {
      console.error('Error in extractEntities:', error);
      return [];
    }
  }
  
  /**
   * Analyze page context to enhance understanding
   * @param {object} pageContent - The page content object
   * @returns {object} - Context analysis results
   */
  async analyzeContext(pageContent) {
    if (!pageContent) {
      try {
        pageContent = await this.apiManager.getPageContent();
      } catch (error) {
        console.error('Error getting page content for context analysis:', error);
        return { type: 'unknown', confidence: 0 };
      }
    }
    
    const url = pageContent.url?.toLowerCase() || '';
    const title = pageContent.title?.toLowerCase() || '';
    const content = pageContent.content?.toLowerCase() || '';
    
    // Detect page type based on URL, title, and content
    const contextTypes = {
      coding: {
        urlPatterns: ['github.com', 'stackoverflow.com', 'leetcode.com', 'hackerrank.com', 'codewars.com', 'codeforces.com'],
        titlePatterns: ['code', 'programming', 'developer', 'algorithm', 'problem', 'solution'],
        contentPatterns: ['function', 'class', 'method', 'variable', 'const', 'let', 'var', 'import', 'export', 'return']
      },
      news: {
        urlPatterns: ['news', 'article', 'blog', 'post'],
        titlePatterns: ['news', 'article', 'report', 'update', 'latest'],
        contentPatterns: ['published', 'author', 'reported', 'according to', 'source']
      },
      shopping: {
        urlPatterns: ['shop', 'store', 'product', 'item', 'buy', 'purchase'],
        titlePatterns: ['shop', 'store', 'product', 'item', 'price', 'discount', 'sale'],
        contentPatterns: ['price', 'buy now', 'add to cart', 'checkout', 'shipping', 'delivery']
      },
      video: {
        urlPatterns: ['youtube.com', 'vimeo.com', 'dailymotion.com', 'twitch.tv'],
        titlePatterns: ['video', 'watch', 'stream', 'episode', 'tutorial'],
        contentPatterns: ['watch', 'view', 'subscribe', 'channel', 'like', 'comment', 'share']
      },
      social: {
        urlPatterns: ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com', 'reddit.com'],
        titlePatterns: ['post', 'feed', 'timeline', 'profile', 'social'],
        contentPatterns: ['post', 'comment', 'like', 'share', 'follow', 'friend', 'connection']
      },
      documentation: {
        urlPatterns: ['docs', 'documentation', 'guide', 'tutorial', 'reference', 'manual'],
        titlePatterns: ['documentation', 'guide', 'tutorial', 'reference', 'manual', 'how to'],
        contentPatterns: ['documentation', 'guide', 'tutorial', 'reference', 'manual', 'example', 'usage']
      },
      chess: {
        urlPatterns: ['chess.com', 'lichess.org', 'chess24.com', 'chessbase.com'],
        titlePatterns: ['chess', 'game', 'match', 'tournament', 'puzzle'],
        contentPatterns: ['chess', 'board', 'piece', 'move', 'knight', 'bishop', 'rook', 'queen', 'king', 'pawn']
      }
    };
    
    // Calculate confidence scores for each context type
    const scores = {};
    
    for (const [type, patterns] of Object.entries(contextTypes)) {
      let score = 0;
      
      // Check URL patterns
      for (const pattern of patterns.urlPatterns) {
        if (url.includes(pattern)) {
          score += 2; // URL is a strong indicator
        }
      }
      
      // Check title patterns
      for (const pattern of patterns.titlePatterns) {
        if (title.includes(pattern)) {
          score += 1.5; // Title is a good indicator
        }
      }
      
      // Check content patterns
      let contentMatches = 0;
      for (const pattern of patterns.contentPatterns) {
        if (content.includes(pattern)) {
          contentMatches++;
        }
      }
      
      // Add score based on percentage of content patterns matched
      if (patterns.contentPatterns.length > 0) {
        score += (contentMatches / patterns.contentPatterns.length) * 1.5;
      }
      
      scores[type] = score;
    }
    
    // Find the context type with the highest score
    let maxScore = 0;
    let detectedType = 'general';
    
    for (const [type, score] of Object.entries(scores)) {
      if (score > maxScore) {
        maxScore = score;
        detectedType = type;
      }
    }
    
    // Calculate confidence (normalize score to 0-1 range)
    const confidence = Math.min(maxScore / 5, 0.95);
    
    return {
      type: detectedType,
      confidence: confidence,
      scores: scores
    };
  }
  
  /**
   * Generate a specialized system prompt based on intent and context
   * @param {string} intent - The detected intent
   * @param {string} contextType - The detected context type
   * @returns {string} - Specialized system prompt
   */
  generateSpecializedSystemPrompt(intent, contextType) {
    // Base system prompt that works for all contexts
    let systemPrompt = `You are a versatile AI assistant embedded in a Chrome extension that can see and analyze the current webpage and uploaded files. Your purpose is to help users with any request related to the webpage they're viewing or files they've uploaded.

You can:
- Analyze and explain webpage content
- Answer questions about what's visible on the page
- Provide summaries and extract key information
- Help with coding problems if on a programming site
- Assist with chess analysis if on a chess site
- Analyze uploaded documents, PDFs, spreadsheets, and text files
- Answer questions about uploaded files
- Compare file content with webpage content when relevant
- Offer general assistance for any other type of content

Adapt your responses based on the context of the webpage, uploaded files, and the user's question. Be helpful, informative, and concise. Format your responses with Markdown for better readability when appropriate.`;

    // Add intent-specific instructions
    if (intent === 'summarize') {
      systemPrompt += `\n\nThe user is asking for a summary. Focus on providing a concise overview of the main points, key information, and important details. Structure your summary with clear sections and bullet points where appropriate. Aim to be comprehensive yet brief.`;
    } else if (intent === 'analyze') {
      systemPrompt += `\n\nThe user is asking for analysis. Provide a detailed examination of the content, including patterns, insights, and implications. Consider different perspectives and highlight both strengths and weaknesses. Support your analysis with specific examples from the content.`;
    } else if (intent === 'explain') {
      systemPrompt += `\n\nThe user is asking for an explanation. Break down complex concepts into simpler terms, use analogies where helpful, and provide step-by-step clarification. Make sure your explanation is accessible and easy to understand.`;
    } else if (intent === 'code') {
      systemPrompt += `\n\nThe user is asking about code. Provide clear, well-commented code examples, explain the logic and approach, and consider efficiency and best practices. If debugging, identify potential issues and suggest fixes.`;
    } else if (intent === 'search') {
      systemPrompt += `\n\nThe user is searching for specific information. Focus on providing precise, relevant results that directly address their search query. Highlight the most important matches and provide context for each result.`;
    }

    // Add context-specific instructions
    if (contextType === 'coding') {
      systemPrompt += `\n\nThe current context is related to coding or programming. Focus on providing technically accurate information, code examples, algorithm explanations, or debugging help as appropriate. Consider efficiency, best practices, and potential edge cases in your responses.`;
    } else if (contextType === 'news') {
      systemPrompt += `\n\nThe current context is related to news or articles. Focus on providing factual information, summarizing key points, identifying main arguments, and distinguishing between facts and opinions. Consider the source and potential biases in your analysis.`;
    } else if (contextType === 'shopping') {
      systemPrompt += `\n\nThe current context is related to shopping or products. Focus on providing objective information about products, comparing features, discussing value propositions, and highlighting important considerations for purchasing decisions.`;
    } else if (contextType === 'video') {
      systemPrompt += `\n\nThe current context is related to video content. Focus on discussing the video content, summarizing key points, analyzing visual elements, and providing context about the creator or platform as relevant.`;
    } else if (contextType === 'chess') {
      systemPrompt += `\n\nThe current context is related to chess. Focus on analyzing positions, explaining moves, discussing strategies, and providing accurate chess notation. Consider both tactical and strategic elements in your analysis.`;
    } else if (contextType === 'documentation') {
      systemPrompt += `\n\nThe current context is related to documentation or guides. Focus on clarifying technical concepts, explaining procedures, providing examples of usage, and connecting related information to help the user understand the documentation better.`;
    }

    return systemPrompt;
  }
  
  /**
   * Process user input to enhance the prompt with NLP insights
   * @param {string} userInput - The user's message
   * @param {object} pageContent - The page content object
   * @returns {object} - Enhanced prompt information
   */
  async processUserInput(userInput, pageContent) {
    try {
      // Detect intent from user input
      const intentAnalysis = this.detectIntent(userInput);
      
      // Extract entities from user input
      const entities = this.extractEntities(userInput);
      
      // Analyze context from page content
      const contextAnalysis = await this.analyzeContext(pageContent);
      
      // Generate specialized system prompt
      const systemPrompt = this.generateSpecializedSystemPrompt(
        intentAnalysis.intent, 
        contextAnalysis.type
      );
      
      return {
        originalInput: userInput,
        intentAnalysis,
        entities,
        contextAnalysis,
        systemPrompt,
        enhancedPrompt: userInput // Keep original input for now
      };
    } catch (error) {
      console.error('Error in processUserInput:', error);
      return {
        originalInput: userInput,
        intentAnalysis: { intent: 'unknown', confidence: 0 },
        entities: [],
        contextAnalysis: { type: 'unknown', confidence: 0 },
        systemPrompt: this.generateSpecializedSystemPrompt('unknown', 'general'),
        enhancedPrompt: userInput,
        error: error.message
      };
    }
  }
}

// Export the class
window.CompromiseNLPManager = CompromiseNLPManager;
