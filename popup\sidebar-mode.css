/* Sidebar Mode Styles for Browzy AI */

/* Setting description styling */
.setting-description {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-top: 4px;
  margin-left: 24px;
  opacity: 0.8;
}

/* Sidebar mode toggle button styling */
#sidebarMode {
  margin-right: 8px;
}

/* Sidebar mode button in header */
.sidebar-toggle-btn {
  background: transparent;
  border: none;
  color: var(--text-color);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  margin-left: 5px;
}

.sidebar-toggle-btn.active {
  color: var(--primary-color);
}

/* Super Close button for sidebar mode */
#superCloseBtn {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 9999999; /* Ultra high z-index */
  cursor: pointer;
  pointer-events: auto !important;
}

#superCloseBtn > div {
  width: 50px;
  height: 50px;
  background-color: #FF0000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.9);
  border: 3px solid white;
}

#superCloseBtn span {
  color: white;
  font-size: 30px;
  font-weight: bold;
}

/* Ensure the button is always visible and clickable */
body.sidebar-mode #superCloseBtn {
  display: block !important;
  pointer-events: auto !important;
}

/* Add a special animation to draw attention to the close button */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

body.sidebar-mode #superCloseBtn > div {
  animation: pulse 2s infinite;
}

/* Popup modifications when in sidebar mode */
body.sidebar-mode {
  width: 400px !important; /* Fixed width */
  height: 100vh;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  border-radius: 0;
  position: fixed; /* Fixed position */
  top: 0;
  right: 0; /* Stick to right side */
  bottom: 0;
  z-index: 2147483647; /* Maximum z-index */
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.5);
  border-left: 1px solid rgba(242, 244, 247, 0.1); /* Fog Gray with transparency */
  background-color: #000000; /* Black */
  background-image: linear-gradient(to bottom,
    rgba(0, 0, 0, 0.95) 0%, /* Black with transparency */
    rgba(0, 0, 0, 0.9) 40%, /* Black with transparency */
    rgba(0, 0, 0, 0.9) 80%, /* Black with transparency */
    rgba(0, 0, 0, 0.9) 100%); /* Black with transparency */
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

body.sidebar-mode .container {
  border-radius: 0;
  max-height: 100vh;
  height: 100vh;
  min-height: 100vh;
  width: 400px !important; /* Fixed width */
  max-width: 400px !important; /* Fixed max-width */
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative; /* Relative position */
  background-color: transparent !important; /* Make container transparent to show gradient */
}

body.sidebar-mode header {
  border-radius: 0;
  padding: 10px 15px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
  background: rgba(0, 0, 0, 0.5) !important; /* Semi-transparent header to show gradient */
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Adjust chat container in sidebar mode */
body.sidebar-mode .chat-container {
  height: calc(100vh - 60px);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex child to shrink */
  width: 400px !important; /* Fixed width */
  max-width: 400px !important; /* Fixed max-width */
  box-sizing: border-box;
}

body.sidebar-mode .chat-messages {
  height: calc(100% - 60px);
  flex: 1;
  min-height: 0; /* Important for flex child to shrink */
  overflow-y: auto;
  width: 100% !important; /* Full width */
  box-sizing: border-box;
  background-color: transparent !important; /* Make chat messages container transparent */
}

/* Hide the sidebar toggle button in sidebar mode */
body.sidebar-mode #sidebarToggleBtn {
  display: none !important;
}

/* Adjust the input area in sidebar mode */
body.sidebar-mode .input-area {
  border-radius: 0;
  margin: 0;
  padding: 10px;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #000000; /* Black */
  width: 400px !important; /* Fixed width */
  max-width: 400px !important; /* Fixed max-width */
  box-sizing: border-box;
}

/* Fix input field overlapping buttons */
body.sidebar-mode .input-container {
  position: relative;
  z-index: 1;
  width: 100% !important;
  box-sizing: border-box;
}

body.sidebar-mode .input-field {
  width: calc(100% - 80px) !important; /* Make room for buttons */
  padding-right: 80px;
  box-sizing: border-box;
}

body.sidebar-mode .input-buttons {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  display: flex;
  gap: 5px;
}

/* Adjust the tabs in sidebar mode */
body.sidebar-mode .tabs {
  padding: 8px 12px;
  width: 100% !important;
  max-width: 400px !important;
  box-sizing: border-box;
  margin: 15px auto 10px;
  background-color: var(--nav-bg);
  border-radius: var(--nav-container-radius);
  justify-content: space-between;
  height: var(--nav-height);
}

/* Adjust the tab content in sidebar mode */
body.sidebar-mode .tab-content {
  padding: 10px;
  width: 100% !important;
  max-width: 400px !important;
  box-sizing: border-box;
}

/* Adjust the settings in sidebar mode */
body.sidebar-mode #settingsContent,
body.sidebar-mode #historyContent,
body.sidebar-mode #statsContent {
  padding: 10px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  width: 100% !important;
  max-width: 400px !important;
  box-sizing: border-box;
}

/* Adjust the scrollbar in sidebar mode */
body.sidebar-mode ::-webkit-scrollbar {
  width: 8px;
}

body.sidebar-mode ::-webkit-scrollbar-track {
  background: rgba(242, 244, 247, 0.05); /* Fog Gray with transparency */
}

body.sidebar-mode ::-webkit-scrollbar-thumb {
  background: rgba(0, 166, 192, 0.2); /* Bright Turquoise with transparency */
  border-radius: 4px;
}

body.sidebar-mode ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 166, 192, 0.3); /* Bright Turquoise with transparency */
}

/* Additional styles to ensure fixed width and position */
body.sidebar-mode * {
  max-width: 400px !important;
  box-sizing: border-box;
}

/* Override any Chrome Side Panel resizing */
@media (min-width: 0px) {
  body.sidebar-mode {
    width: 400px !important;
    min-width: 400px !important;
    max-width: 400px !important;
  }

  body.sidebar-mode .container {
    width: 400px !important;
    min-width: 400px !important;
    max-width: 400px !important;
  }
}

/* Special styles for blank page sidebar */
body.blank-page-sidebar {
  pointer-events: auto !important;
}

/* Force all elements to be clickable */
body.blank-page-sidebar * {
  pointer-events: auto !important;
}

/* Specific elements that need to be clickable */
body.blank-page-sidebar button,
body.blank-page-sidebar .tab-btn,
body.blank-page-sidebar #sendMessage,
body.blank-page-sidebar .input-field,
body.blank-page-sidebar #userInput,
body.blank-page-sidebar #providerSelector,
body.blank-page-sidebar .dropdown,
body.blank-page-sidebar a,
body.blank-page-sidebar input,
body.blank-page-sidebar select,
body.blank-page-sidebar textarea,
body.blank-page-sidebar .actions-dropdown-container,
body.blank-page-sidebar #actionsDropdownBtn,
body.blank-page-sidebar #toggleFloatingChat,
body.blank-page-sidebar #sidebarToggleBtn2,
body.blank-page-sidebar #clearChat,
body.blank-page-sidebar .dropdown-item,
body.blank-page-sidebar .tab-content,
body.blank-page-sidebar .chat-input-buttons,
body.blank-page-sidebar #fileUploadBtn,
body.blank-page-sidebar #destressBtn {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 9999 !important;
  position: relative !important;
}

/* Make sure the send button is visible and clickable */
body.blank-page-sidebar #sendMessage {
  background-color: var(--primary-color) !important;
  color: white !important;
  border: none !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  z-index: 9999 !important;
}

body.blank-page-sidebar #sendMessage:hover {
  transform: scale(1.1) !important;
  background-color: var(--accent-color) !important;
}

/* Make sure tab buttons are visible and clickable */
body.blank-page-sidebar .tab-btn {
  background-color: transparent !important;
  color: var(--text-color) !important;
  border: none !important;
  padding: 8px 12px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  z-index: 9999 !important;
}

body.blank-page-sidebar .tab-btn.active {
  color: var(--primary-color) !important;
  background-color: rgba(0, 166, 192, 0.1) !important;
  border-radius: var(--nav-btn-radius) !important;
}

body.blank-page-sidebar .tab-btn:hover {
  color: var(--primary-color) !important;
}

/* Make sure the input field is visible and clickable */
body.blank-page-sidebar #userInput {
  pointer-events: auto !important;
  z-index: 9999 !important;
  position: relative !important;
  background-color: var(--input-bg) !important;
  color: var(--text-color) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--input-radius) !important;
  padding: 10px !important;
  width: calc(100% - 80px) !important;
  resize: none !important;
  outline: none !important;
}

/* Make sure the dropdown menu is visible and clickable */
body.blank-page-sidebar .actions-dropdown-menu {
  pointer-events: auto !important;
  z-index: 10000 !important;
  position: absolute !important;
  background-color: var(--dropdown-bg) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--dropdown-radius) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Make sure the dropdown items are visible and clickable */
body.blank-page-sidebar .dropdown-item {
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 10001 !important;
  position: relative !important;
  padding: 8px 12px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  transition: background-color 0.2s ease !important;
}

body.blank-page-sidebar .dropdown-item:hover {
  background-color: var(--dropdown-hover-bg) !important;
}
