// Reset extension storage script
'use strict';

// Default settings
const defaultSettings = {
  maxTokens: 1000,
  temperature: 0.7,
  autoAnalyze: false,
  rememberHistory: true
};

// Default empty stats
const defaultStats = {
  gemini: { requests: 0, tokens: 0 },
  openrouter: { requests: 0, tokens: 0 },
  'direct-openai': { requests: 0, tokens: 0 },
  'direct-gemini': { requests: 0, tokens: 0 }
};

// Reset the extension storage
async function resetExtension() {
  try {
    // Reset to Gemini 1.5 Flash as the default model
    await chrome.storage.local.set({
      selectedProvider: 'direct-gemini/gemini-1.5-flash',
      settings: defaultSettings,
      stats: defaultStats,
      apiKeys: {}
    });

    console.log('Extension storage reset successfully');
    alert('Extension storage has been reset. Please close and reopen the extension.');
  } catch (error) {
    console.error('Error resetting extension:', error);
    alert('Error resetting extension: ' + error.message);
  }
}

// Call the reset function
resetExtension();
