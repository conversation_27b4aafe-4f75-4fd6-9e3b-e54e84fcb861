'use strict';

/**
 * NLP Manager for advanced natural language processing capabilities
 * Handles intent recognition, entity extraction, and context understanding
 */
class NLPManager {
  /**
   * Create a new NLP Manager
   * @param {APIManager} apiManager - The API Manager instance
   */
  constructor(apiManager) {
    this.apiManager = apiManager;

    // Intent patterns for recognition
    this.intentPatterns = {
      greeting: [
        /^(hello|hi|hey|good morning|good afternoon|good evening|greetings|howdy|whatsapp|wassup|whats up|what's up|sup|yo|hey there|hi there|hiya|hey ya|hey ya'll|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey yall|hey y'all|hey)\b/i,
        /^(hello|hi|hey)\s+\w+/i
      ],
      farewell: [
        /^(goodbye|bye|see you|farewell|until next time|talk to you later|good to see you)\b/i
      ],
      help: [
        /^(help|assist|support|guide|how do (i|you)|what can you do|show me how)\b/i
      ],
      summarize: [
        /^(summarize|summary|summarization|give me a summary|tldr|brief overview)\b/i,
        /(summarize|summarization) (this|the) (page|article|content|text)/i
      ],
      analyze: [
        /^(analyze|analysis|examine|inspect|review|analyze this|analyze the page|analyze the content|analyze the article)\b/i,
        /(analyze|analysis|examine|inspect|review) (this|the) (page|article|content|text)/i
      ],
      search: [
        /^(search|find|look|see|show|get|tell|find me|show me|get me|look up) (for|up)?\s+(.+)$/i,
        /where is (.+)/i,
        /can you find (.+)/i
      ],
      translate: [
        /^translate (.+) (to|into) (.+)$/i,
        /^(translate|translation) (this|the) (page|article|content|text)/i
      ],
      explain: [
        /^(explain|explanation|clarify|elaborate on|tell me about)\b/i,
        /(explain|explanation|clarify|elaborate on) (this|the|what) (.+)/i
      ],
      compare: [
        /^(compare|comparison|contrast|difference between|similarities between)\b/i,
        /(compare|comparison|contrast|difference|similarities) (between|of) (.+) and (.+)/i
      ],
      code: [
        /^(code|program|script|function|algorithm|implementation)\b/i,
        /(write|create|generate|implement) (a|an|the) (code|program|script|function|algorithm)/i,
        /(debug|fix|optimize|refactor|improve) (this|the|my) (code|program|script|function)/i
      ],
      clearContent: [
        /^(clear|remove|delete) (scanned|saved) (content|tab|page)/i,
        /^return to (current|this) tab/i
      ]
    };

    // Entity types for extraction
    this.entityTypes = {
      language: [
        'english', 'spanish', 'french', 'german', 'italian', 'portuguese', 'russian',
        'japanese', 'chinese', 'korean', 'arabic', 'hindi', 'dutch', 'swedish', 'finnish',
        'norwegian', 'danish', 'polish', 'turkish', 'greek', 'hebrew', 'thai', 'vietnamese'
      ],
      programmingLanguage: [
        'javascript', 'python', 'java', 'cpp', 'csharp', 'php', 'ruby', 'swift', 'kotlin',
        'go', 'rust', 'typescript', 'scala', 'perl', 'r', 'matlab', 'bash', 'powershell',
        'sql', 'html', 'css', 'xml', 'json', 'yaml', 'markdown'
      ],
      fileType: [
        'pdf', 'doc', 'docx', 'txt', 'csv', 'xlsx', 'xls', 'ppt', 'pptx', 'json', 'xml',
        'html', 'css', 'js', 'py', 'java', 'cpp', 'cs', 'php', 'rb', 'swift', 'kt', 'go',
        'rs', 'ts', 'scala', 'pl', 'r', 'm', 'sh', 'ps1', 'sql', 'md'
      ],
      timeframe: [
        'today', 'yesterday', 'tomorrow', 'this week', 'last week', 'next week',
        'this month', 'last month', 'next month', 'this year', 'last year', 'next year'
      ]
    };
  }

  /**
   * Analyze user input to detect intent
   * @param {string} userInput - The user's message
   * @returns {object} - The detected intent and confidence
   */
  detectIntent(userInput) {
    if (!userInput || typeof userInput !== 'string') {
      return { intent: 'unknown', confidence: 0, entities: [] };
    }

    const normalizedInput = userInput.trim().toLowerCase();

    // Check each intent pattern
    for (const [intent, patterns] of Object.entries(this.intentPatterns)) {
      for (const pattern of patterns) {
        const match = normalizedInput.match(pattern);
        if (match) {
          // Calculate confidence based on how much of the input matches the pattern
          const matchLength = match[0].length;
          const confidence = Math.min(matchLength / normalizedInput.length, 0.95);

          // Extract entities
          const entities = this.extractEntities(userInput);

          return {
            intent,
            confidence: confidence,
            pattern: pattern.toString(),
            entities
          };
        }
      }
    }

    // If no specific intent is detected, try to determine a general category
    return this.detectGeneralIntent(userInput);
  }

  /**
   * Detect general intent when no specific pattern matches
   * @param {string} userInput - The user's message
   * @returns {object} - The detected general intent
   */
  detectGeneralIntent(userInput) {
    const normalizedInput = userInput.trim().toLowerCase();

    // Check for question patterns
    if (normalizedInput.startsWith('what') ||
        normalizedInput.startsWith('how') ||
        normalizedInput.startsWith('why') ||
        normalizedInput.startsWith('when') ||
        normalizedInput.startsWith('where') ||
        normalizedInput.startsWith('who') ||
        normalizedInput.startsWith('which') ||
        normalizedInput.endsWith('?')) {
      return {
        intent: 'question',
        confidence: 0.7,
        entities: this.extractEntities(userInput)
      };
    }

    // Check for command patterns (imperative verbs at the beginning)
    const commandVerbs = ['find', 'get', 'show', 'tell', 'give', 'list', 'create', 'make', 'build', 'write'];
    for (const verb of commandVerbs) {
      if (normalizedInput.startsWith(verb + ' ')) {
        return {
          intent: 'command',
          confidence: 0.6,
          entities: this.extractEntities(userInput)
        };
      }
    }

    // Default to conversation
    return {
      intent: 'conversation',
      confidence: 0.4,
      entities: this.extractEntities(userInput)
    };
  }

  /**
   * Extract entities from user input
   * @param {string} userInput - The user's message
   * @returns {Array} - Array of extracted entities
   */
  extractEntities(userInput) {
    const entities = [];
    const normalizedInput = userInput.toLowerCase();

    // Extract entities for each type
    for (const [entityType, entityValues] of Object.entries(this.entityTypes)) {
      for (const value of entityValues) {
        try {
          // Escape special regex characters in the value
          const escapedValue = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

          // Look for whole word matches using word boundaries
          const regex = new RegExp(`\\b${escapedValue}\\b`, 'i');
          if (regex.test(normalizedInput)) {
            entities.push({
              type: entityType,
              value: value,
              position: normalizedInput.indexOf(value.toLowerCase())
            });
          }
        } catch (error) {
          console.error(`Error creating regex for entity '${value}':`, error);
          // Try a simple string match as fallback
          if (normalizedInput.includes(value.toLowerCase())) {
            entities.push({
              type: entityType,
              value: value,
              position: normalizedInput.indexOf(value.toLowerCase())
            });
          }
        }
      }
    }

    // Sort entities by position in the text
    entities.sort((a, b) => a.position - b.position);

    return entities;
  }

  /**
   * Analyze page context to enhance understanding
   * @param {object} pageContent - The page content object
   * @returns {object} - Context analysis results
   */
  async analyzeContext(pageContent) {
    if (!pageContent) {
      try {
        pageContent = await this.apiManager.getPageContent();
      } catch (error) {
        console.error('Error getting page content for context analysis:', error);
        return { type: 'unknown', confidence: 0 };
      }
    }

    const url = pageContent.url?.toLowerCase() || '';
    const title = pageContent.title?.toLowerCase() || '';
    const content = pageContent.content?.toLowerCase() || '';

    // Detect page type based on URL, title, and content
    const contextTypes = {
      coding: {
        urlPatterns: ['github.com', 'stackoverflow.com', 'leetcode.com', 'hackerrank.com', 'codewars.com', 'codeforces.com'],
        titlePatterns: ['code', 'programming', 'developer', 'algorithm', 'problem', 'solution'],
        contentPatterns: ['function', 'class', 'method', 'variable', 'const', 'let', 'var', 'import', 'export', 'return']
      },
      news: {
        urlPatterns: ['news', 'article', 'blog', 'post'],
        titlePatterns: ['news', 'article', 'report', 'update', 'latest'],
        contentPatterns: ['published', 'author', 'reported', 'according to', 'source']
      },
      shopping: {
        urlPatterns: ['shop', 'store', 'product', 'item', 'buy', 'purchase'],
        titlePatterns: ['shop', 'store', 'product', 'item', 'price', 'discount', 'sale'],
        contentPatterns: ['price', 'buy now', 'add to cart', 'checkout', 'shipping', 'delivery']
      },
      video: {
        urlPatterns: ['youtube.com', 'vimeo.com', 'dailymotion.com', 'twitch.tv'],
        titlePatterns: ['video', 'watch', 'stream', 'episode', 'tutorial'],
        contentPatterns: ['watch', 'view', 'subscribe', 'channel', 'like', 'comment', 'share']
      },
      social: {
        urlPatterns: ['facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com', 'reddit.com'],
        titlePatterns: ['post', 'feed', 'timeline', 'profile', 'social'],
        contentPatterns: ['post', 'comment', 'like', 'share', 'follow', 'friend', 'connection']
      },
      documentation: {
        urlPatterns: ['docs', 'documentation', 'guide', 'tutorial', 'reference', 'manual'],
        titlePatterns: ['documentation', 'guide', 'tutorial', 'reference', 'manual', 'how to'],
        contentPatterns: ['documentation', 'guide', 'tutorial', 'reference', 'manual', 'example', 'usage']
      },
      chess: {
        urlPatterns: ['chess.com', 'lichess.org', 'chess24.com', 'chessbase.com'],
        titlePatterns: ['chess', 'game', 'match', 'tournament', 'puzzle'],
        contentPatterns: ['chess', 'board', 'piece', 'move', 'knight', 'bishop', 'rook', 'queen', 'king', 'pawn']
      }
    };

    // Calculate confidence scores for each context type
    const scores = {};

    for (const [type, patterns] of Object.entries(contextTypes)) {
      let score = 0;

      // Check URL patterns
      for (const pattern of patterns.urlPatterns) {
        if (url.includes(pattern)) {
          score += 2; // URL is a strong indicator
        }
      }

      // Check title patterns
      for (const pattern of patterns.titlePatterns) {
        if (title.includes(pattern)) {
          score += 1.5; // Title is a good indicator
        }
      }

      // Check content patterns
      let contentMatches = 0;
      for (const pattern of patterns.contentPatterns) {
        try {
          // Escape special regex characters in the pattern
          const escapedPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

          // Use regex to find whole word matches
          const regex = new RegExp(`\\b${escapedPattern}\\b`, 'i');
          if (regex.test(content)) {
            contentMatches++;
          }
        } catch (error) {
          console.error(`Error creating regex for pattern '${pattern}':`, error);
          // Try a simple string match as fallback
          if (content.includes(pattern)) {
            contentMatches++;
          }
        }
      }

      // Add score based on percentage of content patterns matched
      if (patterns.contentPatterns.length > 0) {
        score += (contentMatches / patterns.contentPatterns.length) * 1.5;
      }

      scores[type] = score;
    }

    // Find the context type with the highest score
    let maxScore = 0;
    let detectedType = 'general';

    for (const [type, score] of Object.entries(scores)) {
      if (score > maxScore) {
        maxScore = score;
        detectedType = type;
      }
    }

    // Calculate confidence (normalize score to 0-1 range)
    const confidence = Math.min(maxScore / 5, 0.95);

    return {
      type: detectedType,
      confidence: confidence,
      scores: scores
    };
  }

  /**
   * Generate a specialized system prompt based on intent and context
   * @param {string} intent - The detected intent
   * @param {string} contextType - The detected context type
   * @returns {string} - Specialized system prompt
   */
  generateSpecializedSystemPrompt(intent, contextType) {
    // Base system prompt that works for all contexts
    let systemPrompt = `You are a versatile AI assistant embedded in a Chrome extension that can see and analyze the current webpage and uploaded files. Your purpose is to help users with any request related to the webpage they're viewing or files they've uploaded.

You can:
- Analyze and explain webpage content
- Answer questions about what's visible on the page
- Provide summaries and extract key information
- Help with coding problems if on a programming site
- Assist with chess analysis if on a chess site
- Analyze uploaded documents, PDFs, spreadsheets, and text files
- Answer questions about uploaded files
- Compare file content with webpage content when relevant
- Offer general assistance for any other type of content

Adapt your responses based on the context of the webpage, uploaded files, and the user's question. Be helpful, informative, and concise. Format your responses with Markdown for better readability when appropriate.`;

    // Add intent-specific instructions
    if (intent === 'summarize') {
      systemPrompt += `\n\nThe user is asking for a summary. Focus on providing a concise overview of the main points, key information, and important details. Structure your summary with clear sections and bullet points where appropriate. Aim to be comprehensive yet brief.`;
    } else if (intent === 'analyze') {
      systemPrompt += `\n\nThe user is asking for analysis. Provide a detailed examination of the content, including patterns, insights, and implications. Consider different perspectives and highlight both strengths and weaknesses. Support your analysis with specific examples from the content.`;
    } else if (intent === 'explain') {
      systemPrompt += `\n\nThe user is asking for an explanation. Break down complex concepts into simpler terms, use analogies where helpful, and provide step-by-step clarification. Make sure your explanation is accessible and easy to understand.`;
    } else if (intent === 'code') {
      systemPrompt += `\n\nThe user is asking about code. Provide clear, well-commented code examples, explain the logic and approach, and consider efficiency and best practices. If debugging, identify potential issues and suggest fixes.`;
    } else if (intent === 'search') {
      systemPrompt += `\n\nThe user is searching for specific information. Focus on providing precise, relevant results that directly address their search query. Highlight the most important matches and provide context for each result.`;
    }

    // Add context-specific instructions
    if (contextType === 'coding') {
      systemPrompt += `\n\nThe current context is related to coding or programming. Focus on providing technically accurate information, code examples, algorithm explanations, or debugging help as appropriate. Consider efficiency, best practices, and potential edge cases in your responses.`;
    } else if (contextType === 'news') {
      systemPrompt += `\n\nThe current context is related to news or articles. Focus on providing factual information, summarizing key points, identifying main arguments, and distinguishing between facts and opinions. Consider the source and potential biases in your analysis.`;
    } else if (contextType === 'shopping') {
      systemPrompt += `\n\nThe current context is related to shopping or products. Focus on providing objective information about products, comparing features, discussing value propositions, and highlighting important considerations for purchasing decisions.`;
    } else if (contextType === 'video') {
      systemPrompt += `\n\nThe current context is related to video content. Focus on discussing the video content, summarizing key points, analyzing visual elements, and providing context about the creator or platform as relevant.`;
    } else if (contextType === 'chess') {
      systemPrompt += `\n\nThe current context is related to chess. Focus on analyzing positions, explaining moves, discussing strategies, and providing accurate chess notation. Consider both tactical and strategic elements in your analysis.`;
    } else if (contextType === 'documentation') {
      systemPrompt += `\n\nThe current context is related to documentation or guides. Focus on clarifying technical concepts, explaining procedures, providing examples of usage, and connecting related information to help the user understand the documentation better.`;
    }

    return systemPrompt;
  }

  /**
   * Process user input to enhance the prompt with NLP insights
   * @param {string} userInput - The user's message
   * @param {object} pageContent - The page content object
   * @returns {object} - Enhanced prompt information
   */
  async processUserInput(userInput, pageContent) {
    // Detect intent from user input
    const intentAnalysis = this.detectIntent(userInput);

    // Analyze context from page content
    const contextAnalysis = await this.analyzeContext(pageContent);

    // Generate specialized system prompt
    const systemPrompt = this.generateSpecializedSystemPrompt(
      intentAnalysis.intent,
      contextAnalysis.type
    );

    return {
      originalInput: userInput,
      intentAnalysis,
      contextAnalysis,
      systemPrompt,
      enhancedPrompt: userInput // Keep original input for now, could be modified in future versions
    };
  }
}

// Export the class
window.NLPManager = NLPManager;
