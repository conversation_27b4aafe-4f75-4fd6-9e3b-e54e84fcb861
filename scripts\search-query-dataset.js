'use strict';

/**
 * Search Query Dataset
 * Provides a dataset of common search queries and their intents for training the NLP model
 */
class SearchQueryDataset {
  constructor() {
    // Initialize the dataset with common search patterns
    this.dataset = {
      // YouTube search patterns
      youtube: [
        { query: "funny cat videos", intent: "youtube" },
        { query: "how to make pasta", intent: "youtube" },
        { query: "latest music videos", intent: "youtube" },
        { query: "gaming tutorials", intent: "youtube" },
        { query: "movie trailers 2024", intent: "youtube" },
        { query: "tech reviews", intent: "youtube" },
        { query: "workout routines", intent: "youtube" },
        { query: "DIY home projects", intent: "youtube" },
        { query: "travel vlogs", intent: "youtube" },
        { query: "cooking recipes", intent: "youtube" },
        { query: "carryminati", intent: "youtube" },
        { query: "pewdiepie latest video", intent: "youtube" },
        { query: "mr beast challenge", intent: "youtube" },
        { query: "trending videos", intent: "youtube" },
        { query: "music playlist", intent: "youtube" },
        { query: "documentary", intent: "youtube" },
        { query: "podcast episode", intent: "youtube" },
        { query: "live stream", intent: "youtube" },
        { query: "news update", intent: "youtube" },
        { query: "product unboxing", intent: "youtube" }
      ],
      
      // Instagram search patterns
      instagram: [
        { query: "fashion influencers", intent: "instagram" },
        { query: "food photography", intent: "instagram" },
        { query: "travel destinations", intent: "instagram" },
        { query: "fitness motivation", intent: "instagram" },
        { query: "celebrity profiles", intent: "instagram" },
        { query: "makeup tutorials", intent: "instagram" },
        { query: "home decor ideas", intent: "instagram" },
        { query: "art inspiration", intent: "instagram" },
        { query: "pet accounts", intent: "instagram" },
        { query: "photography tips", intent: "instagram" },
        { query: "trending hashtags", intent: "instagram" },
        { query: "reels", intent: "instagram" },
        { query: "stories", intent: "instagram" },
        { query: "highlights", intent: "instagram" },
        { query: "profile", intent: "instagram" }
      ],
      
      // Twitter/X search patterns
      twitter: [
        { query: "breaking news", intent: "twitter" },
        { query: "trending topics", intent: "twitter" },
        { query: "political updates", intent: "twitter" },
        { query: "sports highlights", intent: "twitter" },
        { query: "tech announcements", intent: "twitter" },
        { query: "celebrity tweets", intent: "twitter" },
        { query: "viral memes", intent: "twitter" },
        { query: "business news", intent: "twitter" },
        { query: "science discoveries", intent: "twitter" },
        { query: "health information", intent: "twitter" },
        { query: "elon musk", intent: "twitter" },
        { query: "hashtag", intent: "twitter" },
        { query: "trending", intent: "twitter" },
        { query: "viral tweet", intent: "twitter" },
        { query: "thread", intent: "twitter" }
      ],
      
      // Pinterest search patterns
      pinterest: [
        { query: "home decor inspiration", intent: "pinterest" },
        { query: "DIY crafts", intent: "pinterest" },
        { query: "wedding ideas", intent: "pinterest" },
        { query: "outfit inspiration", intent: "pinterest" },
        { query: "recipe collections", intent: "pinterest" },
        { query: "garden design", intent: "pinterest" },
        { query: "art projects", intent: "pinterest" },
        { query: "organization tips", intent: "pinterest" },
        { query: "beauty hacks", intent: "pinterest" },
        { query: "travel planning", intent: "pinterest" },
        { query: "boards", intent: "pinterest" },
        { query: "pins", intent: "pinterest" },
        { query: "ideas", intent: "pinterest" },
        { query: "inspiration", intent: "pinterest" },
        { query: "design", intent: "pinterest" }
      ],
      
      // GitHub search patterns
      github: [
        { query: "javascript libraries", intent: "github" },
        { query: "python frameworks", intent: "github" },
        { query: "open source projects", intent: "github" },
        { query: "web development tools", intent: "github" },
        { query: "machine learning repositories", intent: "github" },
        { query: "mobile app templates", intent: "github" },
        { query: "game development resources", intent: "github" },
        { query: "data visualization libraries", intent: "github" },
        { query: "blockchain projects", intent: "github" },
        { query: "AI research code", intent: "github" },
        { query: "repository", intent: "github" },
        { query: "code", intent: "github" },
        { query: "developer", intent: "github" },
        { query: "programming", intent: "github" },
        { query: "source code", intent: "github" }
      ],
      
      // Stack Overflow search patterns
      stackoverflow: [
        { query: "javascript error", intent: "stackoverflow" },
        { query: "python exception", intent: "stackoverflow" },
        { query: "css layout problem", intent: "stackoverflow" },
        { query: "database query optimization", intent: "stackoverflow" },
        { query: "api integration issue", intent: "stackoverflow" },
        { query: "react component lifecycle", intent: "stackoverflow" },
        { query: "git merge conflict", intent: "stackoverflow" },
        { query: "docker container networking", intent: "stackoverflow" },
        { query: "unit testing best practices", intent: "stackoverflow" },
        { query: "performance optimization", intent: "stackoverflow" },
        { query: "code error", intent: "stackoverflow" },
        { query: "debugging", intent: "stackoverflow" },
        { query: "programming question", intent: "stackoverflow" },
        { query: "development issue", intent: "stackoverflow" },
        { query: "coding problem", intent: "stackoverflow" }
      ],
      
      // Amazon search patterns
      amazon: [
        { query: "wireless headphones", intent: "amazon" },
        { query: "bestselling books", intent: "amazon" },
        { query: "kitchen gadgets", intent: "amazon" },
        { query: "fitness equipment", intent: "amazon" },
        { query: "smart home devices", intent: "amazon" },
        { query: "laptop deals", intent: "amazon" },
        { query: "beauty products", intent: "amazon" },
        { query: "outdoor furniture", intent: "amazon" },
        { query: "children's toys", intent: "amazon" },
        { query: "pet supplies", intent: "amazon" },
        { query: "buy", intent: "amazon" },
        { query: "purchase", intent: "amazon" },
        { query: "shop", intent: "amazon" },
        { query: "product", intent: "amazon" },
        { query: "price", intent: "amazon" }
      ],
      
      // Google search patterns (default)
      google: [
        { query: "weather forecast", intent: "google" },
        { query: "nearby restaurants", intent: "google" },
        { query: "currency conversion", intent: "google" },
        { query: "flight status", intent: "google" },
        { query: "movie showtimes", intent: "google" },
        { query: "stock market updates", intent: "google" },
        { query: "translation services", intent: "google" },
        { query: "job openings", intent: "google" },
        { query: "educational resources", intent: "google" },
        { query: "health symptoms", intent: "google" },
        { query: "information", intent: "google" },
        { query: "search", intent: "google" },
        { query: "find", intent: "google" },
        { query: "lookup", intent: "google" },
        { query: "research", intent: "google" }
      ],
      
      // Spotify search patterns
      spotify: [
        { query: "new album releases", intent: "spotify" },
        { query: "workout playlists", intent: "spotify" },
        { query: "relaxing music", intent: "spotify" },
        { query: "top 40 hits", intent: "spotify" },
        { query: "indie artists", intent: "spotify" },
        { query: "podcast recommendations", intent: "spotify" },
        { query: "classical compositions", intent: "spotify" },
        { query: "jazz collections", intent: "spotify" },
        { query: "meditation sounds", intent: "spotify" },
        { query: "party music", intent: "spotify" },
        { query: "song", intent: "spotify" },
        { query: "music", intent: "spotify" },
        { query: "album", intent: "spotify" },
        { query: "artist", intent: "spotify" },
        { query: "playlist", intent: "spotify" }
      ]
    };
    
    // Conversational patterns with their corresponding intents
    this.conversationalPatterns = [
      { pattern: "show me videos about", intent: "youtube" },
      { pattern: "i want to watch videos on", intent: "youtube" },
      { pattern: "find videos of", intent: "youtube" },
      { pattern: "search for videos about", intent: "youtube" },
      { pattern: "youtube videos on", intent: "youtube" },
      { pattern: "show me photos of", intent: "instagram" },
      { pattern: "find instagram profiles for", intent: "instagram" },
      { pattern: "i want to see pictures of", intent: "instagram" },
      { pattern: "instagram posts about", intent: "instagram" },
      { pattern: "check out instagram", intent: "instagram" },
      { pattern: "latest tweets about", intent: "twitter" },
      { pattern: "what's trending on twitter about", intent: "twitter" },
      { pattern: "find twitter posts on", intent: "twitter" },
      { pattern: "twitter updates for", intent: "twitter" },
      { pattern: "check twitter for", intent: "twitter" },
      { pattern: "pinterest ideas for", intent: "pinterest" },
      { pattern: "find inspiration for", intent: "pinterest" },
      { pattern: "show me pins about", intent: "pinterest" },
      { pattern: "pinterest boards on", intent: "pinterest" },
      { pattern: "design ideas for", intent: "pinterest" },
      { pattern: "github repositories for", intent: "github" },
      { pattern: "find code for", intent: "github" },
      { pattern: "open source projects for", intent: "github" },
      { pattern: "developer tools for", intent: "github" },
      { pattern: "coding examples for", intent: "github" },
      { pattern: "how to fix", intent: "stackoverflow" },
      { pattern: "coding problem with", intent: "stackoverflow" },
      { pattern: "error in my code", intent: "stackoverflow" },
      { pattern: "debugging help for", intent: "stackoverflow" },
      { pattern: "programming solution for", intent: "stackoverflow" },
      { pattern: "buy online", intent: "amazon" },
      { pattern: "shop for", intent: "amazon" },
      { pattern: "purchase", intent: "amazon" },
      { pattern: "best deals on", intent: "amazon" },
      { pattern: "price of", intent: "amazon" },
      { pattern: "listen to", intent: "spotify" },
      { pattern: "play music by", intent: "spotify" },
      { pattern: "find songs from", intent: "spotify" },
      { pattern: "music playlist for", intent: "spotify" },
      { pattern: "spotify playlist with", intent: "spotify" }
    ];
  }

  /**
   * Get the full dataset for training
   * @returns {Object} The complete dataset
   */
  getFullDataset() {
    return {
      intents: this.dataset,
      conversationalPatterns: this.conversationalPatterns
    };
  }

  /**
   * Get training examples for a specific intent
   * @param {string} intent - The intent to get examples for
   * @returns {Array} Array of query examples
   */
  getExamplesForIntent(intent) {
    return this.dataset[intent] || [];
  }

  /**
   * Get all conversational patterns
   * @returns {Array} Array of conversational patterns
   */
  getConversationalPatterns() {
    return this.conversationalPatterns;
  }
}

// Export the class
window.SearchQueryDataset = SearchQueryDataset;
