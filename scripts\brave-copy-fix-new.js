/**
 * Special fix for copy functionality in Brave browser
 */

// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('Brave copy fix loaded');

  // Add a global click handler for copy buttons
  document.body.addEventListener('click', function(event) {
    // Check if the clicked element is a copy button
    if (event.target.closest('.copy-button')) {
      // Get the button that was clicked
      const button = event.target.closest('.copy-button');
      const codeId = button.getAttribute('data-target');

      if (codeId) {
        const codeElement = document.getElementById(codeId);
        if (codeElement) {
          console.log('Brave copy fix: Found code element', codeId);

          // Get the code text
          const codeText = codeElement.textContent;

          // Create a full-screen overlay
          const overlay = document.createElement('div');
          overlay.style.position = 'fixed';
          overlay.style.top = '0';
          overlay.style.left = '0';
          overlay.style.width = '100%';
          overlay.style.height = '100%';
          overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
          overlay.style.zIndex = '9999';
          overlay.style.display = 'flex';
          overlay.style.justifyContent = 'center';
          overlay.style.alignItems = 'center';

          // Create a visible textarea with the code
          const textarea = document.createElement('textarea');
          textarea.value = codeText;
          textarea.style.width = '80%';
          textarea.style.maxWidth = '600px';
          textarea.style.height = '200px';
          textarea.style.padding = '10px';
          textarea.style.backgroundColor = '#1e1e2e';
          textarea.style.color = '#ffffff';
          textarea.style.border = '1px solid #555';
          textarea.style.borderRadius = '4px';
          textarea.style.fontSize = '14px';
          textarea.style.fontFamily = 'monospace';
          textarea.style.marginBottom = '60px'; // Space for the button
          textarea.readOnly = true; // Make it read-only

          // Create a container for the textarea and button
          const container = document.createElement('div');
          container.style.display = 'flex';
          container.style.flexDirection = 'column';
          container.style.alignItems = 'center';
          container.style.width = '80%';
          container.style.maxWidth = '600px';

          // Create a button to copy and close
          const copyButton = document.createElement('button');
          copyButton.textContent = 'Copy & Close';
          copyButton.style.marginTop = '20px';
          copyButton.style.padding = '10px 20px';
          copyButton.style.backgroundColor = '#4CAF50';
          copyButton.style.color = 'white';
          copyButton.style.border = 'none';
          copyButton.style.borderRadius = '4px';
          copyButton.style.fontSize = '16px';
          copyButton.style.cursor = 'pointer';

          // Add elements to the container
          container.appendChild(textarea);
          container.appendChild(copyButton);

          // Add the container to the overlay
          overlay.appendChild(container);

          // Add the overlay to the body
          document.body.appendChild(overlay);

          // Select all text in the textarea
          textarea.select();

          // Add click event to the copy button
          copyButton.addEventListener('click', function() {
            // Try to copy using document.execCommand
            try {
              // Make sure the text is selected
              textarea.select();

              // Execute the copy command
              const success = document.execCommand('copy');
              console.log('Copy result:', success ? 'success' : 'failed');

              // Show success message
              const statusMessage = document.getElementById('statusMessage');
              if (statusMessage) {
                statusMessage.textContent = 'Code copied to clipboard!';
                statusMessage.style.display = 'block';
                setTimeout(() => {
                  statusMessage.style.display = 'none';
                }, 3000);
              }

              // Update the original button
              button.innerHTML = '<i class="fas fa-check"></i> Copied!';
              setTimeout(() => {
                button.innerHTML = '<i class="fas fa-copy"></i> Copy';
              }, 2000);
            } catch (err) {
              console.error('Copy failed:', err);
            }

            // Remove the overlay
            document.body.removeChild(overlay);
          });

          // Prevent the original event
          event.preventDefault();
          event.stopPropagation();
        }
      }
    }
  });
});
