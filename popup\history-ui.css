/* Modern History UI Styles */

/* History Header */
.history-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.history-header h2 {
  font-size: 1.3rem;
  color: var(--text-color);
  position: relative;
  padding-bottom: 8px;
  font-family: var(--font-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.history-header h2 i {
  color: var(--primary-color);
}

.history-header h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: 3px;
}

.history-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.history-search {
  flex: 1;
  position: relative;
}

.history-search input {
  width: 100%;
  padding: 10px 35px 10px 15px;
  border: 1px solid rgba(123, 179, 144, 0.1); /* <PERSON> Veil with transparency */
  border-radius: 8px;
  background-color: rgba(17, 31, 39, 0.6); /* Midnight Carbon with transparency */
  color: var(--text-color);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.history-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(123, 179, 144, 0.2); /* Sage Veil with transparency */
}

.history-search i {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 0.9rem;
  pointer-events: none;
}

.history-filter select {
  padding: 10px 30px 10px 15px;
  border: 1px solid rgba(123, 179, 144, 0.1); /* Sage Veil with transparency */
  border-radius: 8px;
  background-color: rgba(17, 31, 39, 0.6); /* Midnight Carbon with transparency */
  color: var(--text-color);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23F2F1DC" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  cursor: pointer;
}

.history-filter select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(123, 179, 144, 0.2); /* Sage Veil with transparency */
}

/* History Container */
.history-container {
  background-color: rgba(17, 31, 39, 0.7); /* Midnight Carbon with transparency */
  border-radius: 12px;
  border: 1px solid rgba(123, 179, 144, 0.08); /* Sage Veil with transparency */
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.history-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #FC5A50, #CC3333); /* Algerian Coral to Persian Red */
  z-index: 1;
}

.history-list {
  max-height: 350px;
  overflow-y: auto;
  padding: 10px;
  scrollbar-width: thin;
  scrollbar-color: #FC5A50 rgba(17, 31, 39, 0.5); /* Algerian Coral and Midnight Carbon */
}

.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(17, 31, 39, 0.5); /* Midnight Carbon with transparency */
  border-radius: 10px;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: #FC5A50; /* Algerian Coral */
  border-radius: 10px;
}

/* Empty History State */
.empty-history-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-history-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(17, 31, 39, 0.5); /* Midnight Carbon with transparency */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 1.8rem;
  color: #FC5A50; /* Algerian Coral */
  opacity: 0.7;
}

.empty-history-message {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 8px;
  font-weight: 600;
}

.empty-history-hint {
  font-size: 0.9rem;
  color: var(--text-light);
  max-width: 250px;
  line-height: 1.5;
}

/* History Item */
.history-item {
  background-color: rgba(17, 31, 39, 0.5); /* Midnight Carbon with transparency */
  border-radius: 10px;
  margin-bottom: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(123, 179, 144, 0.05); /* Sage Veil with transparency */
}

.history-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(252, 90, 80, 0.3); /* Algerian Coral with transparency */
}

.history-item-header {
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(10, 18, 24, 0.7); /* Darker Midnight Carbon with transparency */
  border-bottom: 1px solid rgba(123, 179, 144, 0.05); /* Sage Veil with transparency */
}

.history-item-title {
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.history-item-model {
  font-size: 0.75rem;
  padding: 3px 8px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.history-item-model.openai {
  background-color: rgba(197, 197, 211, 0.2); /* Kiri Mist with transparency */
  color: #C5C5D3; /* Kiri Mist */
}

.history-item-model.gemini {
  background-color: rgba(236, 41, 56, 0.2); /* Imperial Red with transparency */
  color: #EC2938; /* Imperial Red */
}

.history-item-model.claude {
  background-color: rgba(250, 250, 250, 0.2); /* Dr. White with transparency */
  color: #FAFAFA; /* Dr. White */
}

.history-item-model.mistral {
  background-color: rgba(223, 223, 229, 0.2); /* Festive Ferret with transparency */
  color: #DFDFE5; /* Festive Ferret */
}

.history-item-model.deepseek {
  background-color: rgba(208, 24, 40, 0.2); /* Darker Imperial Red with transparency */
  color: #D01828; /* Darker Imperial Red */
}

.history-item-date {
  font-size: 0.8rem;
  color: var(--text-light);
}

.history-item-content {
  padding: 15px;
}

.history-item-preview {
  font-size: 0.9rem;
  color: var(--text-light);
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-actions {
  display: flex;
  gap: 10px;
}

.history-action-btn {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-action-btn.restore {
  background-color: rgba(123, 179, 144, 0.1); /* Sage Veil with transparency */
  color: #7BB390; /* Sage Veil */
  border: 1px solid rgba(123, 179, 144, 0.3); /* Sage Veil with transparency */
}

.history-action-btn.restore:hover {
  background-color: rgba(123, 179, 144, 0.2); /* Sage Veil with transparency */
  transform: translateY(-2px);
}

.history-action-btn.delete {
  background-color: rgba(252, 90, 80, 0.1); /* Algerian Coral with transparency */
  color: #FC5A50; /* Algerian Coral */
  border: 1px solid rgba(252, 90, 80, 0.3); /* Algerian Coral with transparency */
}

.history-action-btn.delete:hover {
  background-color: rgba(252, 90, 80, 0.2); /* Algerian Coral with transparency */
  transform: translateY(-2px);
}

/* History Footer */
.history-footer {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.danger-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #FC5A50, #CC3333); /* Algerian Coral to Persian Red */
  color: #F2F1DC; /* Hazy Grove */
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 250px;
}

.danger-btn:hover {
  background: linear-gradient(135deg, #CC3333, #FC5A50); /* Reversed gradient */
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.danger-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.history-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--text-light);
  opacity: 0.8;
}

.history-info i {
  color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  .history-actions {
    flex-direction: column;
  }

  .history-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .history-item-actions {
    flex-direction: column;
    gap: 8px;
  }
}
