'use strict';

/**
 * Scanner content script - specifically designed for extracting content from tabs
 * This is a simplified version of content.js focused only on content extraction
 */

// Function to extract page content
function extractPageContent() {
  try {
    // Get the page title
    const title = document.title || 'Untitled Page';
    
    // Get the page URL
    const url = window.location.href || '';
    
    // Get the main content
    let mainContent = '';
    
    try {
      // Clone the body to avoid modifying the actual page
      const clonedBody = document.body.cloneNode(true);
      
      // Remove scripts, styles, and other non-content elements
      const elementsToRemove = clonedBody.querySelectorAll('script, style, noscript, svg, img, iframe, video, audio, canvas, [aria-hidden="true"], .hidden, [style*="display: none"], [style*="display:none"], [style*="visibility: hidden"], [style*="visibility:hidden"]');
      elementsToRemove.forEach(el => {
        try { el.remove(); } catch (e) { /* ignore removal errors */ }
      });
      
      // Get the text content
      mainContent = clonedBody.innerText || '';
      
      // Trim and normalize whitespace
      mainContent = mainContent.replace(/\s+/g, ' ').trim();
    } catch (error) {
      console.error('Error extracting content:', error);
      // Fallback to simple extraction
      mainContent = document.body ? (document.body.innerText || '') : '';
      mainContent = mainContent.replace(/\s+/g, ' ').trim();
    }
    
    // Compile the page data
    const pageData = {
      url: url,
      title: title,
      content: mainContent || 'No content available',
      timestamp: new Date().toISOString()
    };
    
    return pageData;
  } catch (error) {
    console.error('Error in extractPageContent:', error);
    // Return minimal fallback data
    return {
      url: window.location.href || '',
      title: document.title || 'Untitled Page',
      content: 'Content extraction failed. Please try again.',
      timestamp: new Date().toISOString()
    };
  }
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    if (!message || !message.action) {
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }
    
    // Simple ping to check if content script is loaded
    if (message.action === 'ping') {
      sendResponse({ success: true, status: 'scanner_active' });
      return true;
    }
    
    // Extract page content
    if (message.action === 'extractPageContent') {
      try {
        const content = extractPageContent();
        sendResponse({ success: true, content: content });
      } catch (error) {
        console.error('Error extracting page content:', error);
        sendResponse({ 
          success: false, 
          error: 'Content extraction failed: ' + error.message,
          content: {
            url: window.location.href || '',
            title: document.title || 'Untitled Page',
            content: 'Content extraction failed. Please try again.',
            timestamp: new Date().toISOString()
          }
        });
      }
      return true;
    }
    
    // Unknown action
    sendResponse({ success: false, error: 'Unknown action' });
    return true;
  } catch (error) {
    console.error('Critical error in message listener:', error);
    sendResponse({ success: false, error: 'Internal extension error: ' + error.message });
    return true;
  }
});

// Log that the scanner script has been loaded
console.log('BlowAI Scanner content script loaded');
