/* Creative Studio Dialog Styles */
.drawing-tools {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 15px 0;
  background-color: rgba(30, 30, 30, 0.5);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tool-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tool-section h4 {
  margin: 0;
  font-size: 14px;
  color: #f2f4f7;
  font-weight: 600;
}

.tool-buttons {
  display: flex;
  gap: 10px;
}

.tool-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #f2f4f7;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background-color: rgba(60, 60, 60, 0.8);
  transform: translateY(-2px);
}

.tool-btn.active {
  background-color: #00a6c0;
  color: white;
  border-color: #00a6c0;
  box-shadow: 0 0 10px rgba(0, 166, 192, 0.5);
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.color-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-btn:hover {
  transform: scale(1.1);
}

.color-btn.active {
  box-shadow: 0 0 0 2px white, 0 0 0 4px #00a6c0;
}



/* Floating Drawing Controls */
.drawing-floating-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(40, 59, 72, 0.85);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  z-index: 10000000;
  padding: 5px;
  display: flex;
  align-items: center;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.3s ease-out;
  cursor: move;
  user-select: none;
  touch-action: none;
}

.drawing-tools-container {
  display: flex;
  align-items: center;
  gap: 3px;
}

.floating-tool-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #f2f4f7;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 2px;
}

.floating-tool-btn:hover {
  background-color: rgba(60, 60, 60, 0.8);
  transform: translateY(-2px);
}

.floating-tool-btn.active {
  background-color: #00a6c0;
  color: white;
  border-color: #00a6c0;
  box-shadow: 0 0 10px rgba(0, 166, 192, 0.5);
}

.floating-color-picker {
  display: flex;
  gap: 4px;
  margin: 0 3px;
}

.floating-color-btn {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 1px;
}

.floating-color-btn:hover {
  transform: scale(1.1);
}

.floating-color-btn.active {
  box-shadow: 0 0 0 2px white, 0 0 0 3px #00a6c0;
}

.stop-drawing-btn {
  background-color: rgba(220, 53, 69, 0.8);
  color: white;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.stop-drawing-btn:hover {
  background-color: rgba(220, 53, 69, 1);
  transform: translateY(-2px);
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Modal Header */
.modal-header {
  background: #1a2a36; /* Darker Teal/Blue */
  border-radius: 12px 12px 0 0;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.modal-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 166, 192, 0.3);
}

.modal-icon i {
  color: white;
  font-size: 20px;
}

.modal-title h3 {
  margin: 0;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.modal-title p {
  margin: 4px 0 0 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
}

/* Enhanced Modal Content */
.modal-content {
  background: #1a2a36; /* Darker Teal/Blue */
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-body {
  padding: 20px;
  color: #f2f4f7;
}

.modal-body p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.5;
  margin-top: 0;
}

.modal-footer {
  padding: 15px 20px 20px;
  display: flex;
  justify-content: center;
}

.close-btn {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: white;
}

/* Creative Studio Dialog Specific Styles */
.creative-studio-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000000;
}

.creative-studio-dialog .modal-content {
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  margin: 0 auto;
}

/* Canvas Styles */
#drawingCanvas {
  pointer-events: auto;
}

/* Cursor Styles */
.pen-cursor {
  cursor: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'><path fill='%23000000' d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25z'/></svg>") 0 24, auto !important;
}

.highlighter-cursor {
  cursor: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'><path fill='%23FFFF00' d='M10 20H5v2h5v-2zm9-18h-5v2h5V2zm-9 18H5v2h5v-2z'/></svg>") 0 24, auto !important;
}

.eraser-cursor {
  cursor: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'><path fill='%23FF0000' d='M15.14 3c-.51 0-1.02.2-1.41.59L2.59 14.73c-.78.77-.78 2.04 0 2.83L5.03 20h7.66l8.72-8.72c.79-.78.79-2.05 0-2.83l-4.85-4.86c-.39-.39-.9-.59-1.42-.59z'/></svg>") 0 24, auto !important;
}

/* Interactive Elements Hover */
#drawingFloatingControls button:hover,
#drawingFloatingControls .floating-color-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

/* Ensure the floating controls are always on top */
.drawing-floating-controls {
  z-index: 10000001; /* Higher than the canvas */
}

/* Active dragging state */
.drawing-floating-controls.dragging {
  opacity: 0.85;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(30, 30, 30, 0.9);
}

/* Enhanced Start Drawing Button */
#startDrawingBtn {
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 166, 192, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  margin-top: 15px;
  position: relative;
  overflow: hidden;
}

#startDrawingBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}

#startDrawingBtn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(0, 166, 192, 0.5);
}

#startDrawingBtn:hover::before {
  left: 100%;
}

#startDrawingBtn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(0, 166, 192, 0.4);
}

#startDrawingBtn i {
  font-size: 18px;
}

/* Button glow effect */
.button-glow {
  position: absolute;
  width: 30px;
  height: 100%;
  top: 0;
  left: -30px;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: button-glow 2s infinite;
}

@keyframes button-glow {
  0% {
    left: -30px;
  }
  30% {
    left: 110%;
  }
  100% {
    left: 110%;
  }
}
