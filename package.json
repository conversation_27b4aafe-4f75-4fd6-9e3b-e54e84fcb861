{"name": "browzyai-extension", "version": "1.0.1", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "npm run dev:api & npm run dev:dashboard", "dev:api": "cd api && npm run dev", "dev:dashboard": "cd dashboard && npm run dev", "build": "npm run build:extension", "build:extension": "echo \"Extension build complete\"", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:dev": "docker-compose -f docker-compose.dev.yml up"}, "keywords": ["chrome-extension", "ai", "assistant", "docker"], "author": "BrowzyAI Team", "license": "ISC", "description": "BrowzyAI - Your intelligent AI assistant for any task, powered by multiple AI models", "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "openai": "^4.95.0"}, "devDependencies": {"concurrently": "^8.2.2"}}