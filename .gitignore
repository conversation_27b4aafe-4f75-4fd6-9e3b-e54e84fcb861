# Dependency directories
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Distribution directories
dist/
build/
out/
*.zip
*.crx
*.xpi

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.pem
secrets.json
api_keys.json

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# Browser extension specific
.chrome/
.firefox/
.edge/
.opera/
.brave/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage/
.nyc_output/
test-results/

# Temporary files
tmp/
temp/
.tmp/
.temp/
.cache/

# Replit specific files
.replit
replit.nix
.replit/
.config/
pyproject.toml
uv.lock
.breakpoints
.upm/
.nix/
venv/
poetry.lock

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Compiled files
*.min.js
*.min.css
*.bundle.js
*.bundle.css

# Local development files
local/
dev/
.dev/
