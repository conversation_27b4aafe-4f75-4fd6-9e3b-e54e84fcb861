{"name": "<PERSON><PERSON>ai-api", "version": "1.0.1", "description": "BrowzyAI Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["api", "ai", "chrome-extension"], "author": "BrowzyAI Team", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "pg": "^8.11.3", "redis": "^4.6.10", "winston": "^3.11.0", "multer": "^1.4.5-lts.1", "@anthropic-ai/sdk": "^0.37.0", "openai": "^4.95.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}