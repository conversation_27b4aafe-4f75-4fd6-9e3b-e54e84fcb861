/* Web MCP Enhanced Styles - Modern Redesign 2.0 */

/* Main dialog container */
.web-mcp-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.web-mcp-dialog[style*="display: flex"] {
  opacity: 1;
  visibility: visible;
  animation: dialogFadeIn 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.web-mcp-wrapper {
  width: 90%;
  max-width: 900px;
  height: 85%;
  max-height: 700px;
  background-color: #1a1a1a;
  background-image:
    radial-gradient(circle at 15% 85%, rgba(0, 216, 224, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(0, 166, 192, 0.08) 0%, transparent 50%),
    linear-gradient(to bottom, rgba(10, 15, 20, 0.95), rgba(5, 10, 15, 0.98));
  border-radius: 16px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(0, 166, 192, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(0, 216, 224, 0.15);
  animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Header and controls */
.web-mcp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px;
  background: linear-gradient(135deg, rgba(0, 166, 192, 0.9), rgba(0, 216, 224, 0.8));
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
  position: relative;
  overflow: hidden;
}

.web-mcp-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  z-index: 0;
}

.web-mcp-title {
  display: flex;
  align-items: center;
  gap: 14px;
  position: relative;
  z-index: 1;
}

.web-mcp-logo {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: transform 0.3s ease;
}

.web-mcp-logo:hover {
  transform: scale(1.05) rotate(5deg);
}

.web-mcp-title-text {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.web-mcp-title-text h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.web-mcp-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.85);
  margin-top: 2px;
  font-weight: 500;
}

.web-mcp-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

/* Button styles */
.web-mcp-btn {
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.web-mcp-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.web-mcp-btn:hover::before {
  opacity: 1;
}

.web-mcp-btn.primary {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.web-mcp-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.web-mcp-btn.primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.web-mcp-btn.secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.web-mcp-btn.secondary:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.web-mcp-btn.close {
  background-color: transparent;
  color: rgba(255, 255, 255, 0.8);
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.web-mcp-btn.close:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 1);
  transform: rotate(90deg);
}

.web-mcp-btn i {
  font-size: 18px;
  position: relative;
  z-index: 1;
}

/* Main content area - Enlarged for better chat experience */
.web-mcp-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  overflow: hidden;
  gap: 18px;
  position: relative;
}

/* Status bar - Enhanced design with animations */
.web-mcp-status-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 18px;
  background-color: rgba(10, 15, 20, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 216, 224, 0.15);
  margin-bottom: 5px; /* Reduced margin to give more space to chat */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.web-mcp-status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, #00a6c0, #48d7ce);
  opacity: 0.8;
}

.web-mcp-status-bar .status-icon {
  color: #00a6c0;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  position: relative;
}

.web-mcp-status-bar .status-icon i {
  transition: all 0.3s ease;
}

.web-mcp-status-bar .status-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  flex: 1;
}

.status-actions {
  display: flex;
  gap: 8px;
}

.status-action-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.status-action-btn:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.status-action-btn i {
  font-size: 12px;
}

/* Status states with enhanced visual cues */
.web-mcp-status-bar.scanning {
  background-color: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.3);
  animation: pulse 2s infinite;
}

.web-mcp-status-bar.scanning::before {
  background: linear-gradient(to bottom, #ffc107, #ffeb3b);
}

.web-mcp-status-bar.scanning .status-icon {
  color: #ffc107;
}

.web-mcp-status-bar.scanning .status-icon i {
  animation: spin 1.5s linear infinite;
}

.web-mcp-status-bar.scanned {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}

.web-mcp-status-bar.scanned::before {
  background: linear-gradient(to bottom, #4caf50, #8bc34a);
}

.web-mcp-status-bar.scanned .status-icon {
  color: #4caf50;
}

.web-mcp-status-bar.error {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
}

.web-mcp-status-bar.error::before {
  background: linear-gradient(to bottom, #f44336, #ff9800);
}

.web-mcp-status-bar.error .status-icon {
  color: #f44336;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
  70% { box-shadow: 0 0 0 6px rgba(255, 193, 7, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Chat container - Enhanced design with better visuals */
.web-mcp-chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: rgba(10, 15, 20, 0.6);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(0, 216, 224, 0.15);
  min-height: 0; /* Important for flex child to shrink properly */
  height: calc(100% - 50px); /* Maximize height */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: relative;
}

.web-mcp-chat-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(to bottom, rgba(10, 15, 20, 0.3), transparent);
  pointer-events: none;
  z-index: 1;
}

.web-mcp-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0; /* Important for flex child to shrink properly */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 216, 224, 0.3) transparent;
  position: relative;
}

/* Custom scrollbar for Webkit browsers */
.web-mcp-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.web-mcp-chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.web-mcp-chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(0, 216, 224, 0.2);
  border-radius: 6px;
}

.web-mcp-chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 216, 224, 0.4);
}

/* Message styling with enhanced visuals */
.mcp-message {
  display: flex;
  position: relative;
  max-width: 85%;
  animation: messageAppear 0.3s ease-out;
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mcp-message.user {
  flex-direction: row-reverse;
  align-self: flex-end;
}

.mcp-message.ai {
  align-self: flex-start;
}

.message-avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 12px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(10, 15, 20, 0.6);
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.mcp-message:hover .message-avatar img {
  transform: scale(1.05);
}

.message-avatar i {
  font-size: 22px;
  color: rgba(255, 255, 255, 0.8);
}

.message-content {
  padding: 16px 20px;
  border-radius: 18px;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.mcp-message:hover .message-content {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.mcp-message.user .message-content {
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  color: white;
  border-top-right-radius: 4px;
}

.mcp-message.ai .message-content {
  background-color: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.95);
  border-top-left-radius: 4px;
  border: 1px solid rgba(0, 216, 224, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.message-content p {
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
  font-weight: 400;
}

/* Code blocks */
.message-content code {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'IBM Plex Mono', monospace;
  font-size: 13px;
  color: #48d7ce;
}

.mcp-message.user .message-content code {
  background-color: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* Blockquotes */
.message-content blockquote {
  border-left: 3px solid #00a6c0;
  padding: 4px 12px;
  margin: 10px 0;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  background-color: rgba(0, 166, 192, 0.1);
  border-radius: 0 4px 4px 0;
}

/* Copy button */
.copy-message-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  opacity: 0;
  transition: all 0.2s ease;
}

.message-content:hover .copy-message-btn {
  opacity: 1;
}

.copy-message-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* Thinking indicator */
.mcp-message.thinking .message-content {
  background-color: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.7);
}

.mcp-message.thinking .message-content i {
  margin-right: 8px;
  color: #00a6c0;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* Chat input - Enhanced with modern styling */
.web-mcp-chat-input {
  display: flex;
  padding: 18px 20px;
  background: linear-gradient(to top, rgba(10, 15, 20, 0.9), rgba(10, 15, 20, 0.7));
  border-top: 1px solid rgba(0, 216, 224, 0.15);
  gap: 14px;
  position: relative;
  z-index: 2;
}

.web-mcp-chat-input::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(10, 15, 20, 0.9), transparent);
  pointer-events: none;
}

#mcpUserInput {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: 24px;
  padding: 14px 20px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 15px;
  font-weight: 400;
  resize: none;
  outline: none;
  max-height: 120px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#mcpUserInput:focus {
  border-color: rgba(0, 216, 224, 0.5);
  background-color: rgba(255, 255, 255, 0.07);
  box-shadow: 0 0 0 2px rgba(0, 216, 224, 0.15), 0 4px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

#mcpUserInput::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

.web-mcp-send-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00a6c0, #48d7ce);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  align-self: flex-end;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(0, 216, 224, 0.1);
  position: relative;
  overflow: hidden;
}

.web-mcp-send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.web-mcp-send-btn:hover::before {
  opacity: 1;
}

.web-mcp-send-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25), 0 0 0 3px rgba(0, 216, 224, 0.15);
}

.web-mcp-send-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(0, 216, 224, 0.1);
}

.web-mcp-send-btn i {
  font-size: 20px;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .web-mcp-wrapper {
    width: 95%;
    height: 90%;
  }

  .web-mcp-header {
    padding: 12px 15px;
  }

  .web-mcp-body {
    padding: 15px;
  }

  .message-content {
    max-width: 90%;
  }

  /* Ensure chat container takes full height on mobile */
  .web-mcp-chat-container {
    height: calc(100% - 40px);
  }

  /* Make status bar more compact on mobile */
  .web-mcp-status-bar {
    padding: 6px 12px;
    margin-bottom: 8px;
  }
}

  .web-mcp-title-text h3 {
    font-size: 16px;
  }

  .web-mcp-subtitle {
    display: none;
  }

  .web-mcp-btn {
    padding: 6px 10px;
    font-size: 12px;
  }


/* Markdown formatting */
.message-content h3,
.message-content h4,
.message-content h5 {
  margin-top: 12px;
  margin-bottom: 8px;
  font-weight: 600;
}

.message-content h3 {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.95);
}

.message-content h4 {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
}

.message-content h5 {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
}

/* Links */
.message-content a {
  color: #48d7ce;
  text-decoration: none;
  transition: all 0.2s ease;
}

.message-content a:hover {
  color: #00a6c0;
  text-decoration: underline;
}

/* Lists */
.message-content ul,
.message-content ol {
  margin: 10px 0;
  padding-left: 20px;
}

.message-content li {
  margin-bottom: 5px;
}

/* Tables */
.message-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 15px 0;
  font-size: 14px;
}

.message-content th,
.message-content td {
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  text-align: left;
}

.message-content th {
  background-color: rgba(0, 166, 192, 0.1);
  color: #48d7ce;
}

.message-content tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Animation for dialog opening */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.web-mcp-wrapper {
  animation: slideIn 0.3s ease-out;
}

/* Light mode overrides (if needed) */
@media (prefers-color-scheme: light) {
  .web-mcp-wrapper {
    background-color: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .web-mcp-header {
    background-color: #283b48;
  }

  .web-mcp-body {
    background-color: #f8f9fa;
  }

  .web-mcp-status-bar {
    background-color: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .web-mcp-status-bar .status-text {
    color: #333;
  }

  .info-card {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .info-label {
    color: #666;
  }

  .web-mcp-chat-container {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .mcp-message.ai .message-content {
    background-color: #f0f2f5;
    color: #333;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .message-content code {
    background-color: rgba(0, 0, 0, 0.05);
    color: #00a6c0;
  }

  .message-content blockquote {
    color: #555;
    background-color: rgba(0, 166, 192, 0.05);
  }

  .web-mcp-chat-input {
    background-color: #f0f2f5;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  #mcpUserInput {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #333;
  }

  #mcpUserInput::placeholder {
    color: #999;
  }

  .message-avatar i {
    color: #666;
  }

  .message-content h3,
  .message-content h4,
  .message-content h5 {
    color: #333;
  }
}
