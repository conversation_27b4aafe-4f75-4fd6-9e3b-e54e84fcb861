'use strict';

/**
 * Content script for extracting detailed DOM information for website analysis
 */

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    if (!message || !message.action) {
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }
    
    // Extract DOM content for website analysis
    if (message.action === 'extractDOMContent') {
      try {
        const content = extractDOMContent();
        sendResponse({ success: true, content: content });
      } catch (error) {
        console.error('Error extracting DOM content:', error);
        sendResponse({ 
          success: false, 
          error: 'DOM content extraction failed: ' + error.message
        });
      }
      return true;
    }
    
    // Default response for unknown actions
    sendResponse({ success: false, error: 'Unknown action: ' + message.action });
    return true;
  } catch (error) {
    console.error('Error in message handler:', error);
    sendResponse({ success: false, error: error.message });
    return true;
  }
});

/**
 * Extract detailed DOM content for website analysis
 * @returns {Object} - The extracted DOM content
 */
function extractDOMContent() {
  try {
    // Extract meta tags
    const metaTags = Array.from(document.querySelectorAll('meta, link[rel="canonical"]')).map(tag => {
      const attributes = {};
      Array.from(tag.attributes).forEach(attr => {
        attributes[attr.name] = attr.value;
      });
      return attributes;
    });
    
    // Extract headings
    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(heading => {
      return {
        level: parseInt(heading.tagName.substring(1)),
        text: heading.textContent.trim(),
        id: heading.id || null
      };
    });
    
    // Extract images
    const images = Array.from(document.querySelectorAll('img')).map(img => {
      // Estimate image size based on dimensions (very rough estimate)
      let estimatedSize = null;
      if (img.naturalWidth && img.naturalHeight) {
        // Very rough size estimation based on dimensions
        estimatedSize = Math.round(img.naturalWidth * img.naturalHeight * 0.1); // 0.1 bytes per pixel as a rough estimate
      }
      
      return {
        src: img.src,
        alt: img.alt || null,
        width: img.naturalWidth || img.width,
        height: img.naturalHeight || img.height,
        size: estimatedSize
      };
    });
    
    // Extract links
    const links = Array.from(document.querySelectorAll('a')).map(link => {
      return {
        href: link.href,
        text: link.textContent.trim(),
        isExternal: link.hostname !== window.location.hostname,
        hasNofollow: link.rel && link.rel.includes('nofollow')
      };
    });
    
    // Extract scripts
    const scripts = Array.from(document.querySelectorAll('script')).map(script => {
      return {
        src: script.src || null,
        async: script.async,
        defer: script.defer,
        type: script.type || 'text/javascript',
        isInline: !script.src
      };
    });
    
    // Extract stylesheets
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"], style')).map(style => {
      if (style.tagName.toLowerCase() === 'link') {
        return {
          href: style.href,
          isExternal: true
        };
      } else {
        return {
          isInline: true,
          content: style.textContent.length // Just the length for size estimation
        };
      }
    });
    
    // Extract forms and inputs
    const forms = Array.from(document.querySelectorAll('form')).map(form => {
      const inputs = Array.from(form.querySelectorAll('input, select, textarea')).map(input => {
        // Check if input has an associated label
        const inputId = input.id;
        const hasLabel = inputId ? !!document.querySelector(`label[for="${inputId}"]`) : false;
        
        return {
          type: input.type || input.tagName.toLowerCase(),
          name: input.name || null,
          id: inputId || null,
          hasLabel: hasLabel,
          required: input.required || false
        };
      });
      
      return {
        action: form.action || null,
        method: form.method || 'get',
        inputs: inputs
      };
    });
    
    // Check for ARIA attributes
    const ariaElements = document.querySelectorAll('[aria-*]');
    const ariaAttributes = Array.from(ariaElements).map(el => {
      const attributes = {};
      Array.from(el.attributes).forEach(attr => {
        if (attr.name.startsWith('aria-')) {
          attributes[attr.name] = attr.value;
        }
      });
      return attributes;
    });
    
    // Detect potential render-blocking resources
    const renderBlockingResources = [
      ...Array.from(document.querySelectorAll('link[rel="stylesheet"]:not([media="print"])')).map(link => ({
        type: 'stylesheet',
        url: link.href
      })),
      ...Array.from(document.querySelectorAll('script:not([async]):not([defer])')).filter(script => script.src).map(script => ({
        type: 'script',
        url: script.src
      }))
    ];
    
    // Detect potential mixed content
    const mixedContent = [];
    if (window.location.protocol === 'https:') {
      // Check for HTTP resources on HTTPS page
      document.querySelectorAll('img, script, iframe, link, source').forEach(el => {
        const src = el.src || el.href;
        if (src && src.startsWith('http:')) {
          mixedContent.push({
            type: el.tagName.toLowerCase(),
            url: src
          });
        }
      });
    }
    
    // Estimate page size (very rough)
    const pageSize = document.documentElement.outerHTML.length;
    
    // Check for duplicate content (simplified)
    const paragraphs = Array.from(document.querySelectorAll('p')).map(p => p.textContent.trim());
    const duplicateContent = paragraphs.filter((p, index) => 
      p.length > 50 && paragraphs.indexOf(p) !== index
    );
    
    // Compile the DOM data
    return {
      url: window.location.href,
      title: document.title,
      metaTags: metaTags,
      headings: headings,
      images: images,
      links: links,
      scripts: scripts,
      styles: styles,
      forms: forms,
      ariaAttributes: ariaAttributes,
      renderBlockingResources: renderBlockingResources,
      mixedContent: mixedContent,
      pageSize: pageSize,
      duplicateContent: duplicateContent,
      // We can't reliably check these from a content script, but include placeholders
      securityHeaders: [],
      contrastIssues: [],
      keyboardNavIssues: [],
      brokenLinks: []
    };
  } catch (error) {
    console.error('Error in extractDOMContent:', error);
    // Return minimal fallback data
    return {
      url: window.location.href,
      title: document.title,
      metaTags: [],
      headings: [],
      images: [],
      links: [],
      scripts: [],
      styles: []
    };
  }
}
