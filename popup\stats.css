/* Enhanced Stats Page Styles */
.stats-header {
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.stats-header h2 {
  font-size: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
  margin-bottom: 10px;
}

.stats-header h2 i {
  color: #FF6B3C;
  font-size: 1.5rem;
  background: rgba(255, 107, 60, 0.1);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(255, 107, 60, 0.2);
  transition: all 0.3s ease;
}

.stats-header:hover h2 i {
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(255, 107, 60, 0.25);
}

.stats-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, #FF6B3C, transparent);
  border-radius: 3px;
}

.stats-header p {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-top: 0;
  opacity: 0.8;
  max-width: 80%;
}

.stats-overview {
  margin-bottom: 35px;
  position: relative;
}

.stats-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(30, 185, 128, 0.08), transparent 70%),
              radial-gradient(circle at bottom left, rgba(255, 207, 68, 0.05), transparent 70%);
  border-radius: 12px;
  z-index: -1;
  opacity: 0.8;
  pointer-events: none;
}

.stats-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 25px;
}

.summary-card {
  background-color: rgba(18, 18, 18, 0.6);
  border-radius: 16px;
  padding: 22px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  opacity: 0.9;
}

.summary-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.2), transparent);
  opacity: 0.4;
  z-index: -1;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
}

.summary-card:nth-child(1)::before {
  background: linear-gradient(90deg, #FF6B3C, #FF8F6C); /* Orange gradient */
}

.summary-card:nth-child(2)::before {
  background: linear-gradient(90deg, #1EB980, #4DD8A5); /* Green gradient */
}

.summary-card:nth-child(3)::before {
  background: linear-gradient(90deg, #FFCF44, #FFE07D); /* Yellow gradient */
}

.summary-icon {
  font-size: 1.5rem;
  margin-bottom: 15px;
  width: 58px;
  height: 58px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.summary-card:hover .summary-icon {
  transform: scale(1.1);
}

.summary-card:nth-child(1) .summary-icon {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.15);
  box-shadow: 0 4px 12px rgba(255, 107, 60, 0.2);
}

.summary-card:nth-child(2) .summary-icon {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.15);
  box-shadow: 0 4px 12px rgba(30, 185, 128, 0.2);
}

.summary-card:nth-child(3) .summary-icon {
  color: #FFCF44;
  background: rgba(255, 207, 68, 0.15);
  box-shadow: 0 4px 12px rgba(255, 207, 68, 0.2);
}

.summary-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: relative;
  display: inline-block;
}

.summary-label::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  border-radius: 1px;
  opacity: 0.6;
}

.summary-card:nth-child(1) .summary-label::after {
  background: #FF6B3C;
}

.summary-card:nth-child(2) .summary-label::after {
  background: #1EB980;
}

.summary-card:nth-child(3) .summary-label::after {
  background: #FFCF44;
}

.summary-value {
  font-size: 2.4rem;
  font-weight: 700;
  margin-top: 10px;
  line-height: 1.2;
  position: relative;
  display: inline-block;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.summary-card:nth-child(1) .summary-value {
  color: #FF6B3C;
}

.summary-card:nth-child(2) .summary-value {
  color: #1EB980;
}

.summary-card:nth-child(3) .summary-value {
  color: #FFCF44;
}

.summary-subtext {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-top: 10px;
  opacity: 0.7;
}

.stats-section {
  margin-bottom: 35px;
  position: relative;
}

.section-title {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: var(--text-color);
  padding-bottom: 10px;
  position: relative;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--primary-gradient);
  border-radius: 2px;
}

.stats-container {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.stats-card {
  background-color: rgba(18, 18, 18, 0.5);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0.8;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.provider-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.provider-icon {
  font-size: 1.7rem;
  margin-right: 12px;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.provider-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-color);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.stats-metrics {
  display: flex;
  justify-content: space-around;
  background: rgba(10, 10, 10, 0.3);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

.metric {
  text-align: center;
  padding: 10px 15px;
  flex: 1;
  position: relative;
}

.metric:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  position: relative;
  display: inline-block;
}

.metric-value::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--primary-gradient);
  border-radius: 2px;
  opacity: 0.7;
}

/* Provider-specific colors */
.openrouter .provider-icon {
  color: #FFCF44; /* Vibrant Yellow */
  background: rgba(255, 207, 68, 0.1);
}



.gemini .provider-icon {
  color: #1EB980; /* Vibrant Green */
  background: rgba(30, 185, 128, 0.1);
}

.openai .provider-icon {
  color: #FF6B3C; /* Vibrant Orange */
  background: rgba(255, 107, 60, 0.1);
}

/* Cost Estimation Section */
.cost-estimation {
  background-color: rgba(18, 18, 18, 0.5);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.cost-estimation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #FF6B3C, #1EB980);
  opacity: 0.8;
}

.cost-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.cost-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.cost-title i {
  color: #FF6B3C;
  font-size: 1rem;
}

.cost-disclaimer {
  font-size: 0.75rem;
  color: var(--text-light);
  opacity: 0.7;
  margin-top: 5px;
}

.cost-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.cost-card {
  background-color: rgba(10, 10, 10, 0.3);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.03);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
}

.cost-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.cost-provider {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cost-provider i {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 1.1rem;
}

.cost-provider.openai i {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.1);
}

.cost-provider.gemini i {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.1);
}

.cost-provider.anthropic i {
  color: #9C27B0;
  background: rgba(156, 39, 176, 0.1);
}



.cost-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 5px 0;
}

.cost-provider.openai + .cost-value {
  color: #FF6B3C;
}

.cost-provider.gemini + .cost-value {
  color: #1EB980;
}

.cost-provider.anthropic + .cost-value {
  color: #9C27B0;
}



.cost-label {
  font-size: 0.8rem;
  color: var(--text-light);
  opacity: 0.8;
}

.total-cost {
  margin-top: 25px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.total-cost-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.total-cost-value {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(90deg, #FF6B3C, #1EB980);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Reset Stats Button */
.reset-stats {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  margin-bottom: 25px;
}

.reset-btn {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.2));
  color: var(--text-color);
  border: 1px solid rgba(239, 68, 68, 0.3);
  padding: 14px 28px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.reset-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.reset-btn:hover::before {
  transform: translateX(100%);
}

.reset-btn i {
  color: #ef4444;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.3));
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.reset-btn:hover i {
  transform: rotate(180deg);
}

.reset-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Enhanced usage graph section */
.usage-graph {
  background-color: rgba(18, 18, 18, 0.5);
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 35px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.usage-graph::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1EB980, #FFCF44, #FF6B3C);
  opacity: 0.9;
}

.usage-graph::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.2), transparent);
  opacity: 0.4;
  z-index: 0;
  pointer-events: none;
}

.usage-graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding-bottom: 15px;
}

.usage-graph-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.usage-graph-title i {
  color: #1EB980;
  font-size: 1.1rem;
  background: rgba(30, 185, 128, 0.15);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(30, 185, 128, 0.2);
  transition: all 0.3s ease;
}

.usage-graph-title:hover i {
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(30, 185, 128, 0.25);
}

.usage-graph-controls {
  display: flex;
  gap: 10px;
}

.graph-period-selector {
  background: rgba(30, 185, 128, 0.1);
  border: 1px solid rgba(30, 185, 128, 0.2);
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 0.85rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.graph-period-selector:hover {
  background: rgba(30, 185, 128, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.graph-period-selector.active {
  background: rgba(30, 185, 128, 0.3);
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(30, 185, 128, 0.2);
}

.graph-container {
  height: 220px;
  position: relative;
  margin-top: 20px;
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding-bottom: 35px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.graph-container::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 35px;
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.03) 0%,
    rgba(255, 255, 255, 0.03) 25%,
    rgba(255, 255, 255, 0.02) 25%,
    rgba(255, 255, 255, 0.02) 50%,
    rgba(255, 255, 255, 0.01) 50%,
    rgba(255, 255, 255, 0.01) 75%,
    transparent 75%);
  pointer-events: none;
  z-index: 0;
  border-radius: 8px;
}

.graph-bar-group {
  flex: 1;
  display: flex;
  gap: 3px;
  align-items: flex-end;
  position: relative;
  min-width: 40px;
  max-width: 70px;
  height: 100%;
}

.graph-bar {
  flex: 1;
  border-radius: 8px 8px 0 0;
  position: relative;
  transition: all 0.3s ease;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.graph-bar-openai {
  background: linear-gradient(to top, #FF6B3C, #FF8F6C);
}

.graph-bar-gemini {
  background: linear-gradient(to top, #1EB980, #4DD8A5);
}

.graph-bar-openrouter {
  background: linear-gradient(to top, #FFCF44, #FFE07D);
}



.graph-bar-group:hover {
  transform: translateY(-8px);
}

.graph-bar-group:hover .graph-bar {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.graph-bar-tooltip {
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(10, 10, 10, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 0.8rem;
  color: var(--text-color);
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: none;
  z-index: 10;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  transform: translateX(-50%) translateY(5px);
}

.graph-bar-group:hover .graph-bar-tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.graph-bar-label {
  position: absolute;
  bottom: -28px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.8rem;
  color: var(--text-light);
  white-space: nowrap;
  font-weight: 500;
}

.graph-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 45px;
  flex-wrap: wrap;
  padding: 10px;
  background: rgba(10, 10, 10, 0.2);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--text-light);
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.legend-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.legend-openai {
  background: linear-gradient(90deg, #FF6B3C, #FF8F6C);
}

.legend-gemini {
  background: linear-gradient(90deg, #1EB980, #4DD8A5);
}

.legend-openrouter {
  background: linear-gradient(90deg, #FFCF44, #FFE07D);
}



/* Enhanced Model Usage Styles */
.models-section {
  position: relative;
}

.models-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.models-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.models-title i {
  color: #FF6B3C;
  font-size: 1rem;
}

.models-controls {
  display: flex;
  gap: 10px;
}

.models-filter {
  background: rgba(255, 107, 60, 0.1);
  border: 1px solid rgba(255, 107, 60, 0.2);
  border-radius: 6px;
  padding: 5px 10px;
  font-size: 0.8rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.models-filter:hover {
  background: rgba(255, 107, 60, 0.2);
}

.models-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 18px;
  margin-top: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.model-column {
  background-color: rgba(18, 18, 18, 0.5);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-width: 160px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  position: relative;
  display: flex;
  flex-direction: column;
}

.model-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(30, 185, 128, 0.05), transparent 70%),
              radial-gradient(circle at bottom left, rgba(255, 207, 68, 0.03), transparent 70%);
  border-radius: 16px;
  z-index: -1;
  opacity: 0.8;
  pointer-events: none;
}

.model-column::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.2), transparent);
  opacity: 0.4;
  z-index: -1;
  pointer-events: none;
}

.model-column:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
}

.model-column-header {
  padding: 18px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  background: rgba(10, 10, 10, 0.3);
}

.model-column-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  opacity: 0.9;
}

.model-column-header.openai::before {
  background: linear-gradient(90deg, #FF6B3C, #FF8F6C);
}

.model-column-header.gemini::before {
  background: linear-gradient(90deg, #1EB980, #4DD8A5);
}



.model-column-header.openrouter::before {
  background: linear-gradient(90deg, #FFCF44, #FFE07D);
}

.model-column-header.anthropic::before {
  background: linear-gradient(90deg, #9C27B0, #BA68C8);
}

.model-column-header.mistral::before {
  background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.model-column-header.cohere::before {
  background: linear-gradient(90deg, #FF5722, #FF8A65);
}

.model-column-header.meta::before {
  background: linear-gradient(90deg, #3F51B5, #7986CB);
}

.model-column-header.stability::before {
  background: linear-gradient(90deg, #009688, #4DB6AC);
}

.model-column-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10%;
  right: 10%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.model-column-header i {
  font-size: 1.2rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.model-column:hover .model-column-header i {
  transform: scale(1.1);
}

.model-column-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.model-column-header .model-count {
  margin-left: auto;
  font-size: 0.8rem;
  color: var(--text-light);
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 500;
}

.model-list {
  padding: 10px 0;
  flex: 1;
  overflow-y: auto;
  max-height: 250px;
}

.model-list::-webkit-scrollbar {
  width: 4px;
}

.model-list::-webkit-scrollbar-track {
  background: rgba(10, 10, 10, 0.2);
}

.model-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.model-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

.model-item {
  padding: 12px 18px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
  transition: all 0.2s ease;
  position: relative;
}

.model-item:last-child {
  border-bottom: none;
}

.model-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateX(3px);
}

.model-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  opacity: 0.5;
}

.model-item:last-child::after {
  display: none;
}

.model-name {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
  transition: all 0.2s ease;
}

.model-item:hover .model-name {
  color: #fff;
}

.model-usage {
  font-size: 0.85rem;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  min-width: 30px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.model-item:hover .model-usage {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.openai .model-usage {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.15);
}

.gemini .model-usage {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.15);
}



.openrouter .model-usage {
  color: #FFCF44;
  background: rgba(255, 207, 68, 0.15);
}

.anthropic .model-usage {
  color: #9C27B0;
  background: rgba(156, 39, 176, 0.15);
}

.mistral .model-usage {
  color: #2196F3;
  background: rgba(33, 150, 243, 0.15);
}

.cohere .model-usage {
  color: #FF5722;
  background: rgba(255, 87, 34, 0.15);
}

.meta .model-usage {
  color: #3F51B5;
  background: rgba(63, 81, 181, 0.15);
}

.stability .model-usage {
  color: #009688;
  background: rgba(0, 150, 136, 0.15);
}

/* Provider-specific icon styles */
.model-column-header.openai i {
  color: #FF6B3C;
  background: rgba(255, 107, 60, 0.15);
  box-shadow: 0 4px 12px rgba(255, 107, 60, 0.2);
}

.model-column-header.gemini i {
  color: #1EB980;
  background: rgba(30, 185, 128, 0.15);
  box-shadow: 0 4px 12px rgba(30, 185, 128, 0.2);
}



.model-column-header.openrouter i {
  color: #FFCF44;
  background: rgba(255, 207, 68, 0.15);
  box-shadow: 0 4px 12px rgba(255, 207, 68, 0.2);
}

.model-column-header.anthropic i {
  color: #9C27B0;
  background: rgba(156, 39, 176, 0.15);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.2);
}

.model-column-header.mistral i {
  color: #2196F3;
  background: rgba(33, 150, 243, 0.15);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.model-column-header.cohere i {
  color: #FF5722;
  background: rgba(255, 87, 34, 0.15);
  box-shadow: 0 4px 12px rgba(255, 87, 34, 0.2);
}

.model-column-header.meta i {
  color: #3F51B5;
  background: rgba(63, 81, 181, 0.15);
  box-shadow: 0 4px 12px rgba(63, 81, 181, 0.2);
}

.model-column-header.stability i {
  color: #009688;
  background: rgba(0, 150, 136, 0.15);
  box-shadow: 0 4px 12px rgba(0, 150, 136, 0.2);
}

/* Provider-specific column header styles */
.model-column-header.openai i {
  color: #FF6B3C; /* Vibrant Orange */
  background: rgba(255, 107, 60, 0.1);
}

.model-column-header.gemini i {
  color: #1EB980; /* Vibrant Green */
  background: rgba(30, 185, 128, 0.1);
}



.model-column-header.openrouter i {
  color: #FFCF44; /* Vibrant Yellow */
  background: rgba(255, 207, 68, 0.1);
}

.model-column-header.anthropic i {
  color: #9C27B0; /* Purple */
  background: rgba(156, 39, 176, 0.1);
}

.model-column-header.mistral i {
  color: #2196F3; /* Blue */
  background: rgba(33, 150, 243, 0.1);
}

.model-column-header.cohere i {
  color: #FF5722; /* Deep Orange */
  background: rgba(255, 87, 34, 0.1);
}

.model-column-header.meta i {
  color: #3F51B5; /* Indigo */
  background: rgba(63, 81, 181, 0.1);
}

.model-column-header.stability i {
  color: #009688; /* Teal */
  background: rgba(0, 150, 136, 0.1);
}

/* Responsive adjustments for model columns */
@media (max-width: 1200px) {
  .models-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .models-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .models-container {
    grid-template-columns: 1fr;
  }

  .model-column {
    min-width: 100%;
  }
}

/* Custom scrollbar for models container */
.models-container::-webkit-scrollbar {
  height: 6px;
}

.models-container::-webkit-scrollbar-track {
  background: rgba(10, 10, 10, 0.2);
  border-radius: 3px;
}

.models-container::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #1EB980, #FFCF44);
  border-radius: 3px;
}

.models-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #3DD598, #FFD166);
}
