'use strict';

/**
 * Focus Mode Manager for handling focus timer functionality
 */
class FocusModeManager {
  /**
   * Initialize the Focus Mode Manager
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(uiManager) {
    this.uiManager = uiManager;
    this.isActive = false;
    this.isPaused = false;
    this.startTime = null;
    this.endTime = null;
    this.duration = 0; // in minutes
    this.remainingTime = 0; // in seconds
    this.timerInterval = null;
    this.storageKey = 'focusModeData';
    
    // DOM elements - will be initialized when setupDOM is called
    this.container = null;
    this.timeDisplay = null;
    this.sandTop = null;
    this.sandBottom = null;
    this.sandFalling = null;
    this.startButton = null;
    this.pauseButton = null;
    this.timeInput = null;
    this.statusMessage = null;
    
    // Initialize
    this.setupDOM();
    this.loadState();
    this.setupEventListeners();
  }
  
  /**
   * Set up DOM references
   */
  setupDOM() {
    this.container = document.getElementById('focusModeContainer');
    this.timeDisplay = document.getElementById('focusModeTime');
    this.sandTop = document.getElementById('sandTop');
    this.sandBottom = document.getElementById('sandBottom');
    this.sandFalling = document.getElementById('sandFalling');
    this.startButton = document.getElementById('focusModeStart');
    this.pauseButton = document.getElementById('focusModePause');
    this.timeInput = document.getElementById('focusTimeInput');
    this.statusMessage = document.getElementById('focusModeStatus');
    
    // Preset buttons
    this.preset25 = document.getElementById('focusPreset25');
    this.preset45 = document.getElementById('focusPreset45');
    this.preset60 = document.getElementById('focusPreset60');
  }
  
  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Start button
    this.startButton.addEventListener('click', () => {
      const minutes = parseInt(this.timeInput.value);
      if (isNaN(minutes) || minutes <= 0 || minutes > 180) {
        this.statusMessage.textContent = 'Please enter a valid time between 1 and 180 minutes.';
        return;
      }
      
      this.startFocusMode(minutes);
    });
    
    // Pause button
    this.pauseButton.addEventListener('click', () => {
      if (this.isPaused) {
        this.resumeFocusMode();
      } else {
        this.pauseFocusMode();
      }
    });
    
    // Close button
    document.getElementById('focusModeClose').addEventListener('click', () => {
      this.closeFocusMode();
    });
    
    // Preset buttons
    this.preset25.addEventListener('click', () => {
      this.timeInput.value = '25';
    });
    
    this.preset45.addEventListener('click', () => {
      this.timeInput.value = '45';
    });
    
    this.preset60.addEventListener('click', () => {
      this.timeInput.value = '60';
    });
  }
  
  /**
   * Start the focus mode timer
   * @param {number} minutes - Duration in minutes
   */
  startFocusMode(minutes) {
    this.duration = minutes;
    this.remainingTime = minutes * 60;
    this.startTime = Date.now();
    this.endTime = this.startTime + (this.remainingTime * 1000);
    this.isActive = true;
    this.isPaused = false;
    
    // Update UI
    this.container.classList.add('active', 'running');
    this.updateTimerDisplay();
    this.updateHourglassAnimation(1); // Start with full sand
    this.sandFalling.classList.add('active');
    
    // Start timer
    this.timerInterval = setInterval(() => {
      this.updateTimer();
    }, 1000);
    
    // Save state
    this.saveState();
    
    // Notify user
    this.uiManager.showStatus(`Focus mode started for ${minutes} minutes`, false, 3000);
  }
  
  /**
   * Update the timer
   */
  updateTimer() {
    if (this.isPaused) return;
    
    const now = Date.now();
    this.remainingTime = Math.max(0, Math.ceil((this.endTime - now) / 1000));
    
    // Update UI
    this.updateTimerDisplay();
    
    // Calculate progress (1 to 0)
    const progress = this.remainingTime / (this.duration * 60);
    this.updateHourglassAnimation(progress);
    
    // Check if timer is complete
    if (this.remainingTime <= 0) {
      this.completeFocusMode();
    } else {
      // Save state periodically
      this.saveState();
    }
  }
  
  /**
   * Update the timer display
   */
  updateTimerDisplay() {
    const minutes = Math.floor(this.remainingTime / 60);
    const seconds = this.remainingTime % 60;
    this.timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  
  /**
   * Update the hourglass animation
   * @param {number} progress - Progress from 1 (full) to 0 (empty)
   */
  updateHourglassAnimation(progress) {
    // Update sand heights
    const topHeight = Math.max(0, Math.min(100, progress * 100));
    const bottomHeight = Math.max(0, Math.min(100, (1 - progress) * 100));
    
    this.sandTop.style.height = `${topHeight}%`;
    this.sandBottom.style.height = `${bottomHeight}%`;
    
    // Update sand falling animation
    if (progress <= 0 || this.isPaused) {
      this.sandFalling.classList.remove('active');
    } else {
      this.sandFalling.classList.add('active');
    }
  }
  
  /**
   * Pause the focus mode timer
   */
  pauseFocusMode() {
    if (!this.isActive || this.isPaused) return;
    
    this.isPaused = true;
    clearInterval(this.timerInterval);
    
    // Update UI
    this.pauseButton.textContent = 'Resume Focus';
    this.pauseButton.classList.add('paused');
    this.pauseButton.innerHTML = '<i class="fas fa-play"></i> Resume Focus';
    this.sandFalling.classList.remove('active');
    
    // Save state
    this.saveState();
    
    // Notify user
    this.uiManager.showStatus('Focus mode paused', false, 2000);
  }
  
  /**
   * Resume the focus mode timer
   */
  resumeFocusMode() {
    if (!this.isActive || !this.isPaused) return;
    
    // Recalculate end time based on remaining time
    this.endTime = Date.now() + (this.remainingTime * 1000);
    this.isPaused = false;
    
    // Update UI
    this.pauseButton.textContent = 'Pause Focus';
    this.pauseButton.classList.remove('paused');
    this.pauseButton.innerHTML = '<i class="fas fa-pause"></i> Pause Focus';
    this.sandFalling.classList.add('active');
    
    // Restart timer
    this.timerInterval = setInterval(() => {
      this.updateTimer();
    }, 1000);
    
    // Save state
    this.saveState();
    
    // Notify user
    this.uiManager.showStatus('Focus mode resumed', false, 2000);
  }
  
  /**
   * Complete the focus mode timer
   */
  completeFocusMode() {
    this.isActive = false;
    this.isPaused = false;
    clearInterval(this.timerInterval);
    
    // Update UI
    this.container.classList.remove('running');
    this.sandFalling.classList.remove('active');
    this.updateHourglassAnimation(0); // Empty hourglass
    
    // Clear state
    this.clearState();
    
    // Notify user
    this.uiManager.showStatus('Focus session completed! Great job!', false, 5000);
    
    // Show completion message
    document.getElementById('focusModeMessage').textContent = 'Focus session completed! Great job!';
    
    // Play sound if available
    if (typeof Audio !== 'undefined') {
      try {
        const audio = new Audio(chrome.runtime.getURL('sounds/complete.mp3'));
        audio.play();
      } catch (error) {
        console.log('Could not play completion sound');
      }
    }
  }
  
  /**
   * Close the focus mode
   */
  closeFocusMode() {
    // If active, just hide the UI but keep the timer running
    if (this.isActive) {
      this.container.classList.remove('active');
      this.uiManager.showStatus('Focus mode is still running in the background', false, 3000);
    } else {
      // If not active, fully close and reset
      this.container.classList.remove('active', 'running');
      this.clearState();
    }
  }
  
  /**
   * Open the focus mode UI
   */
  openFocusMode() {
    this.container.classList.add('active');
    
    // If already running, show the timer view
    if (this.isActive) {
      this.container.classList.add('running');
      this.updateTimerDisplay();
      this.updateHourglassAnimation(this.remainingTime / (this.duration * 60));
      
      if (!this.isPaused) {
        this.sandFalling.classList.add('active');
      }
    } else {
      // Otherwise show the setup view
      this.container.classList.remove('running');
      this.timeInput.value = '25'; // Default to 25 minutes
    }
  }
  
  /**
   * Save the current state to storage
   */
  saveState() {
    const state = {
      isActive: this.isActive,
      isPaused: this.isPaused,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.duration,
      remainingTime: this.remainingTime,
      timestamp: Date.now()
    };
    
    chrome.storage.local.set({ [this.storageKey]: state }, () => {
      console.log('Focus mode state saved');
    });
  }
  
  /**
   * Load the state from storage
   */
  loadState() {
    chrome.storage.local.get([this.storageKey], (result) => {
      const state = result[this.storageKey];
      
      if (state && state.isActive) {
        console.log('Restoring focus mode state', state);
        
        // Calculate the correct remaining time based on when it was saved
        if (!state.isPaused) {
          const elapsedSinceLastSave = (Date.now() - state.timestamp) / 1000;
          state.remainingTime = Math.max(0, state.remainingTime - elapsedSinceLastSave);
        }
        
        // If the timer has completed while the extension was closed
        if (state.remainingTime <= 0) {
          this.clearState();
          return;
        }
        
        // Restore state
        this.isActive = state.isActive;
        this.isPaused = state.isPaused;
        this.startTime = state.startTime;
        this.endTime = state.isPaused ? state.endTime : Date.now() + (state.remainingTime * 1000);
        this.duration = state.duration;
        this.remainingTime = state.remainingTime;
        
        // Update UI if needed
        if (this.container) {
          if (this.isActive) {
            this.container.classList.add('running');
            this.updateTimerDisplay();
            this.updateHourglassAnimation(this.remainingTime / (this.duration * 60));
            
            if (this.isPaused) {
              this.pauseButton.textContent = 'Resume Focus';
              this.pauseButton.classList.add('paused');
              this.pauseButton.innerHTML = '<i class="fas fa-play"></i> Resume Focus';
              this.sandFalling.classList.remove('active');
            } else {
              this.sandFalling.classList.add('active');
              
              // Restart timer
              this.timerInterval = setInterval(() => {
                this.updateTimer();
              }, 1000);
            }
          }
        }
      }
    });
  }
  
  /**
   * Clear the saved state
   */
  clearState() {
    chrome.storage.local.remove([this.storageKey], () => {
      console.log('Focus mode state cleared');
    });
    
    this.isActive = false;
    this.isPaused = false;
    this.startTime = null;
    this.endTime = null;
    this.duration = 0;
    this.remainingTime = 0;
    clearInterval(this.timerInterval);
  }
  
  /**
   * Check if focus mode is currently active
   * @returns {boolean} - Whether focus mode is active
   */
  isFocusModeActive() {
    return this.isActive;
  }
}
