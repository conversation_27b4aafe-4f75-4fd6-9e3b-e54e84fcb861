/**
 * Web MCP Content Script
 * Extracts content from web pages for analysis with enhanced crawling capabilities
 */

// Crawling status object to track progress
let currentCrawlingStatus = {
  inProgress: false,
  pagesProcessed: 0,
  totalPages: 0,
  currentUrl: '',
  startTime: null,
  errors: []
};

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'extractPageContent') {
    try {
      console.log('Web MCP: Starting content extraction with crawling');

      // Always use crawling technique - no fallbacks to simple extraction
      crawlPageContent(request.options || {})
        .then(crawledContent => {
          console.log('Web MCP: Crawling successful');
          sendResponse({
            success: true,
            content: crawledContent.content,
            contentSize: crawledContent.contentSize,
            paragraphs: crawledContent.paragraphs,
            images: crawledContent.images,
            links: crawledContent.links,
            crawledPages: crawledContent.crawledPages,
            extractionMethod: 'crawling'
          });
        })
        .catch(error => {
          console.error('Web MCP: Crawling failed:', error);

          // No fallback to simple extraction - return error directly
          sendResponse({
            success: false,
            error: `Crawling failed: ${error.message}. Please try refreshing the page or try a different page.`
          });
        });
    } catch (error) {
      console.error('Web MCP: Error in extraction process:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }

    // Return true to indicate we'll send a response asynchronously
    return true;
  }

  // Handle emergency extraction request
  if (request.action === 'emergencyExtraction') {
    try {
      console.log('Web MCP: Starting emergency content extraction');

      // Use the most aggressive extraction method
      emergencyExtractContent()
        .then(content => {
          console.log('Web MCP: Emergency extraction successful');
          sendResponse({
            success: true,
            content: content
          });
        })
        .catch(error => {
          console.error('Web MCP: Emergency extraction failed:', error);
          sendResponse({
            success: false,
            error: error.message
          });
        });
    } catch (error) {
      console.error('Web MCP: Error in emergency extraction process:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }

    // Return true to indicate we'll send a response asynchronously
    return true;
  }

  // Handle crawling status updates
  if (request.action === 'getCrawlingStatus') {
    sendResponse({
      success: true,
      crawlingStatus: currentCrawlingStatus
    });
  }

  // Return true to indicate we will send a response asynchronously
  return true;
});

/**
 * Extract content from the current page
 * @returns {Promise<Object>} - Promise resolving to object containing extracted content and metadata
 */
async function extractPageContent() {
  return new Promise(async (resolve, reject) => {
    try {
      console.log('Web MCP: Starting standard content extraction');

      // Get page title and URL
      const pageTitle = document.title;
      const pageUrl = window.location.href;

      // Initialize content extraction
      let content = '';
      let paragraphCount = 0;
      let imageCount = 0;

      // Add page metadata
      content += `Title: ${pageTitle}\n`;
      content += `URL: ${pageUrl}\n\n`;

      // Extract main content with timeout to prevent hanging
      try {
        const mainContent = await Promise.race([
          extractMainContent(),
          new Promise((_, timeoutReject) =>
            setTimeout(() => timeoutReject(new Error('Content extraction timed out')), 5000)
          )
        ]);

        content += mainContent;
      } catch (error) {
        console.warn('Web MCP: Main content extraction timed out or failed:', error);
        // Continue with what we have, fallback will handle if content is insufficient
      }

      // Count paragraphs and images
      paragraphCount = document.querySelectorAll('p').length;
      imageCount = document.querySelectorAll('img').length;

      // Calculate content size in bytes
      const contentSize = new Blob([content]).size;

      // Check if we have meaningful content (more than just metadata)
      if (content.length < 100 || content.split('\n').length < 5) {
        return reject(new Error('Insufficient content extracted'));
      }

      resolve({
        content,
        contentSize,
        paragraphs: paragraphCount,
        images: imageCount,
        extractionMethod: 'standard'
      });
    } catch (error) {
      console.error('Web MCP: Error in standard extraction:', error);
      reject(error);
    }
  });
}

/**
 * Extract the main content from the page
 * @returns {Promise<string>} - Promise resolving to extracted content
 */
async function extractMainContent() {
  return new Promise((resolve) => {
    let content = '';

    // Expanded list of main content selectors
    const mainContentSelectors = [
      'main',
      'article',
      '#content',
      '.content',
      '.main',
      '.article',
      '.post',
      '.post-content',
      '.entry-content',
      '#main',
      '#article',
      '.page-content',
      '.main-content',
      '[role="main"]',
      '[itemprop="mainContentOfPage"]',
      '.body-content',
      '.site-content',
      '.page',
      '.container',
      '.wrapper'
    ];

    // Try to find main content using selectors
    let mainElement = null;
    for (const selector of mainContentSelectors) {
      try {
        const element = document.querySelector(selector);
        if (element && element.textContent.trim().length > 200) {
          mainElement = element;
          console.log(`Web MCP: Found main content using selector: ${selector}`);
          break;
        }
      } catch (error) {
        console.warn(`Web MCP: Error finding element with selector ${selector}:`, error);
      }
    }

    // If no main content found, try to find the element with the most text content
    if (!mainElement) {
      console.log('Web MCP: No main content found with selectors, using content density analysis');
      mainElement = findElementWithMostContent();
    }

    // If still no main content found, use body
    if (!mainElement) {
      console.log('Web MCP: Using document body as fallback');
      mainElement = document.body;
    }

    // Extract headings
    try {
      const headings = mainElement.querySelectorAll('h1, h2, h3, h4, h5, h6');
      for (const heading of headings) {
        if (isVisible(heading) && !isBoilerplate(heading)) {
          const level = heading.tagName.charAt(1);
          const prefix = '#'.repeat(parseInt(level));
          content += `${prefix} ${heading.textContent.trim()}\n\n`;
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting headings:', error);
    }

    // Extract paragraphs
    try {
      const paragraphs = mainElement.querySelectorAll('p');
      for (const paragraph of paragraphs) {
        if (isVisible(paragraph) && !isBoilerplate(paragraph)) {
          const text = paragraph.textContent.trim();
          if (text.length > 0) {
            content += `${text}\n\n`;
          }
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting paragraphs:', error);
    }

    // Extract divs that might contain content (for sites that don't use proper paragraph tags)
    try {
      if (content.trim().length < 500) {
        console.log('Web MCP: Not enough paragraph content, extracting from divs');
        const contentDivs = mainElement.querySelectorAll('div');
        for (const div of contentDivs) {
          // Only process divs that likely contain content (not too short, not too long)
          const text = div.textContent.trim();
          if (isVisible(div) && !isBoilerplate(div) &&
              text.length > 50 && text.length < 2000 &&
              div.children.length < 5) {
            content += `${text}\n\n`;
          }
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting content divs:', error);
    }

    // Extract lists
    try {
      const lists = mainElement.querySelectorAll('ul, ol');
      for (const list of lists) {
        if (isVisible(list) && !isBoilerplate(list)) {
          const items = list.querySelectorAll('li');
          for (const item of items) {
            const text = item.textContent.trim();
            if (text.length > 0) {
              content += `- ${text}\n`;
            }
          }
          content += '\n';
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting lists:', error);
    }

    // Extract tables
    try {
      const tables = mainElement.querySelectorAll('table');
      for (const table of tables) {
        if (isVisible(table) && !isBoilerplate(table)) {
          content += 'Table:\n';

          // Get table headers
          const headers = table.querySelectorAll('th');
          if (headers.length > 0) {
            const headerTexts = Array.from(headers).map(th => th.textContent.trim());
            content += `| ${headerTexts.join(' | ')} |\n`;
            content += `| ${headerTexts.map(() => '---').join(' | ')} |\n`;
          }

          // Get table rows
          const rows = table.querySelectorAll('tr');
          for (const row of rows) {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
              const cellTexts = Array.from(cells).map(td => td.textContent.trim());
              content += `| ${cellTexts.join(' | ')} |\n`;
            }
          }

          content += '\n';
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting tables:', error);
    }

    // Extract image alt text and captions
    try {
      const figures = mainElement.querySelectorAll('figure');
      for (const figure of figures) {
        if (isVisible(figure) && !isBoilerplate(figure)) {
          const img = figure.querySelector('img');
          const figcaption = figure.querySelector('figcaption');

          let imageText = 'Image';

          if (img && img.alt && img.alt.trim() !== '') {
            imageText += `: ${img.alt.trim()}`;
          }

          if (figcaption) {
            imageText += ` - Caption: ${figcaption.textContent.trim()}`;
          }

          content += `${imageText}\n\n`;
        }
      }

      // Also check for standalone images with alt text
      const images = mainElement.querySelectorAll('img');
      for (const image of images) {
        if (isVisible(image) && !isBoilerplate(image) && image.alt && image.alt.trim() !== '') {
          content += `Image: ${image.alt.trim()}\n\n`;
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting images:', error);
    }

    // If content is still empty, extract all text
    if (content.trim() === '') {
      console.log('Web MCP: No structured content found, extracting all text');
      try {
        content = mainElement.textContent.trim()
          .replace(/\s+/g, ' ')
          .replace(/\n+/g, '\n\n');
      } catch (error) {
        console.warn('Web MCP: Error extracting all text:', error);
      }
    }

    resolve(content);
  });
}

/**
 * Find the element with the most content
 * @returns {Element} - The element with the most content
 */
function findElementWithMostContent() {
  // Get all potential content elements
  const contentElements = document.querySelectorAll('div, section, main, article');
  let bestElement = null;
  let maxContentScore = 0;

  for (const element of contentElements) {
    try {
      if (!isVisible(element) || isBoilerplate(element)) {
        continue;
      }

      // Calculate content score based on text length and element characteristics
      let contentScore = element.textContent.trim().length;

      // Boost score for elements with many paragraphs
      const paragraphs = element.querySelectorAll('p');
      contentScore += paragraphs.length * 50;

      // Boost score for elements with headings
      const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
      contentScore += headings.length * 100;

      // Penalize very short elements
      if (contentScore < 200) {
        contentScore = 0;
      }

      // Penalize elements that are likely navigation, sidebars, etc.
      if (element.querySelectorAll('nav, header, footer').length > 0) {
        contentScore *= 0.5;
      }

      // Update best element if this one has a higher score
      if (contentScore > maxContentScore) {
        maxContentScore = contentScore;
        bestElement = element;
      }
    } catch (error) {
      console.warn('Web MCP: Error analyzing element for content score:', error);
    }
  }

  return bestElement;
}

/**
 * Check if an element is visible
 * @param {Element} element - The element to check
 * @returns {boolean} - True if the element is visible
 */
function isVisible(element) {
  try {
    if (!element) return false;

    // Quick check for null dimensions
    if (element.offsetWidth === 0 && element.offsetHeight === 0) {
      return false;
    }

    // Check computed style
    const style = window.getComputedStyle(element);
    if (style.display === 'none' ||
        style.visibility === 'hidden' ||
        style.opacity === '0') {
      return false;
    }

    // Check if element is within viewport bounds (at least partially)
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      return false;
    }

    // Check if element has reasonable size (not too small)
    if (rect.width < 10 || rect.height < 10) {
      return false;
    }

    // Check if element has text content
    const text = element.textContent.trim();
    if (element.tagName !== 'IMG' && text.length === 0) {
      return false;
    }

    return true;
  } catch (error) {
    console.warn('Web MCP: Error checking visibility:', error);
    // Default to visible in case of error
    return true;
  }
}

/**
 * Emergency content extraction method
 * This is the most aggressive approach that tries multiple methods to get any content
 * @returns {Promise<string>} - Promise resolving to extracted content
 */
async function emergencyExtractContent() {
  return new Promise(async (resolve, reject) => {
    try {
      console.log('Web MCP: Starting emergency content extraction');

      // Initialize content
      let content = '';

      // Add page metadata
      content += `# ${document.title || 'Page Content'}\n\n`;
      content += `URL: ${window.location.href}\n\n`;

      // Try multiple extraction methods and combine results

      // 1. Try to get all text content from the body
      try {
        content += await extractAllTextContent(document);
      } catch (error) {
        console.warn('Web MCP: Error in emergency text extraction:', error);
      }

      // 2. Try to get all visible text nodes
      if (content.length < 500) {
        try {
          content += await extractAllVisibleText();
        } catch (error) {
          console.warn('Web MCP: Error in visible text extraction:', error);
        }
      }

      // 3. Try to get all links with their text
      try {
        const linkContent = await extractAllLinks();
        content += '\n\n## Links on this page\n\n' + linkContent;
      } catch (error) {
        console.warn('Web MCP: Error extracting links:', error);
      }

      // 4. Try to get all image alt text
      try {
        const imageContent = await extractAllImageAlt();
        content += '\n\n## Images on this page\n\n' + imageContent;
      } catch (error) {
        console.warn('Web MCP: Error extracting image alt text:', error);
      }

      // If we still don't have enough content, use a really aggressive approach
      if (content.length < 500) {
        try {
          content += '\n\n## Raw Text Content\n\n';
          content += document.body.innerText
            .replace(/\\s+/g, ' ')
            .replace(/\\n+/g, '\n\n');
        } catch (error) {
          console.warn('Web MCP: Error in raw text extraction:', error);
        }
      }

      // If we still don't have content, return an error
      if (content.length < 100) {
        return reject(new Error('Could not extract any meaningful content from the page'));
      }

      resolve(content);
    } catch (error) {
      console.error('Web MCP: Error in emergency extraction:', error);
      reject(error);
    }
  });
}

/**
 * Extract all visible text from the document
 * @returns {Promise<string>} - Promise resolving to extracted text
 */
async function extractAllVisibleText() {
  return new Promise((resolve) => {
    let content = '';

    // Function to recursively process text nodes
    function processNode(node) {
      // Skip script, style, and hidden elements
      if (node.nodeName === 'SCRIPT' ||
          node.nodeName === 'STYLE' ||
          node.nodeName === 'NOSCRIPT') {
        return;
      }

      // If it's a text node with content, add it
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent.trim();
        if (text.length > 10) {
          content += text + '\n\n';
        }
        return;
      }

      // If it's an element node, check if it's visible
      if (node.nodeType === Node.ELEMENT_NODE) {
        // Skip invisible elements
        try {
          if (!isVisible(node)) {
            return;
          }
        } catch (e) {
          // If visibility check fails, continue anyway
        }

        // Process child nodes
        for (let i = 0; i < node.childNodes.length; i++) {
          processNode(node.childNodes[i]);
        }
      }
    }

    // Start processing from body
    processNode(document.body);

    // Clean up the content
    content = content
      .replace(/\\n{3,}/g, '\n\n')
      .trim();

    resolve(content);
  });
}

/**
 * Extract all links from the document
 * @returns {Promise<string>} - Promise resolving to extracted links
 */
async function extractAllLinks() {
  return new Promise((resolve) => {
    let content = '';

    try {
      const links = document.querySelectorAll('a[href]');

      for (const link of links) {
        try {
          if (isVisible(link)) {
            const href = link.href;
            const text = link.textContent.trim();

            if (href && text && text.length > 0) {
              content += `- [${text}](${href})\n`;
            }
          }
        } catch (e) {
          // Skip this link if there's an error
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting all links:', error);
    }

    resolve(content);
  });
}

/**
 * Extract all image alt text
 * @returns {Promise<string>} - Promise resolving to extracted image alt text
 */
async function extractAllImageAlt() {
  return new Promise((resolve) => {
    let content = '';

    try {
      const images = document.querySelectorAll('img');

      for (const img of images) {
        try {
          if (isVisible(img) && img.alt && img.alt.trim().length > 0) {
            content += `- Image: ${img.alt.trim()}\n`;
          }
        } catch (e) {
          // Skip this image if there's an error
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting all image alt text:', error);
    }

    resolve(content);
  });
}

/**
 * Check if an element is likely boilerplate content
 * @param {Element} element - The element to check
 * @returns {boolean} - True if the element is likely boilerplate
 */
function isBoilerplate(element) {
  try {
    if (!element) return true;

    // Expanded list of boilerplate patterns
    const boilerplatePatterns = [
      'footer',
      'header',
      'nav',
      'sidebar',
      'menu',
      'comment',
      'widget',
      'ad',
      'banner',
      'cookie',
      'popup',
      'modal',
      'subscribe',
      'newsletter',
      'social',
      'share',
      'related',
      'recommended',
      'popular',
      'trending',
      'promo',
      'advertisement',
      'sponsored',
      'search',
      'login',
      'signup',
      'register',
      'author',
      'byline',
      'timestamp',
      'date',
      'time',
      'copyright'
    ];

    // Check element and its parents for boilerplate patterns (up to 3 levels)
    let current = element;
    let level = 0;

    while (current && level < 3) {
      // Check tag name
      const tagName = current.tagName.toLowerCase();
      if (tagName === 'nav' ||
          tagName === 'header' ||
          tagName === 'footer' ||
          tagName === 'aside') {
        return true;
      }

      // Check role attribute
      const role = current.getAttribute('role');
      if (role === 'navigation' ||
          role === 'banner' ||
          role === 'complementary') {
        return true;
      }

      // Check id and class attributes
      const id = current.id ? current.id.toLowerCase() : '';
      const className = current.className ?
                        (typeof current.className === 'string' ?
                         current.className.toLowerCase() : '') : '';

      for (const pattern of boilerplatePatterns) {
        // Check for exact matches or word boundaries
        const idMatch = id === pattern ||
                       id.includes(`-${pattern}`) ||
                       id.includes(`${pattern}-`) ||
                       id.includes(`_${pattern}`) ||
                       id.includes(`${pattern}_`);

        const classMatch = className.split(' ').some(cls =>
          cls === pattern ||
          cls.includes(`-${pattern}`) ||
          cls.includes(`${pattern}-`) ||
          cls.includes(`_${pattern}`) ||
          cls.includes(`${pattern}_`)
        );

        if (idMatch || classMatch) {
          return true;
        }
      }

      // Move up to parent
      current = current.parentElement;
      level++;
    }

    // Check for common ad patterns
    if (element.id && (
        element.id.includes('google_ad') ||
        element.id.includes('div-gpt-ad')
    )) {
      return true;
    }

    // Check for very short text that's likely not main content
    if (element.textContent &&
        element.textContent.trim().length < 20 &&
        element.tagName !== 'H1' &&
        element.tagName !== 'H2' &&
        element.tagName !== 'H3' &&
        element.tagName !== 'H4' &&
        element.tagName !== 'H5' &&
        element.tagName !== 'H6' &&
        element.tagName !== 'LI') {
      return true;
    }

    return false;
  } catch (error) {
    console.warn('Web MCP: Error checking boilerplate:', error);
    // Default to not boilerplate in case of error
    return false;
  }
}

/**
 * Fallback extraction method when standard extraction fails
 * Uses a more aggressive approach to extract content from the page
 * @returns {Promise<Object>} - Promise resolving to object containing extracted content and metadata
 */
async function fallbackExtraction() {
  return new Promise(async (resolve, reject) => {
    try {
      console.log('Web MCP: Starting fallback content extraction');

      // Get page title and URL
      const pageTitle = document.title;
      const pageUrl = window.location.href;

      // Initialize content extraction
      let content = '';
      let paragraphCount = 0;
      let imageCount = 0;

      // Add page metadata
      content += `Title: ${pageTitle}\n`;
      content += `URL: ${pageUrl}\n\n`;

      // Method 1: Extract all text nodes with reasonable length
      console.log('Web MCP: Trying text node extraction method');
      try {
        content += await extractTextNodes();
      } catch (error) {
        console.warn('Web MCP: Text node extraction failed:', error);
      }

      // Method 2: Extract all visible text from the page
      if (content.length < 500) {
        console.log('Web MCP: Trying visible text extraction method');
        try {
          content += await extractAllVisibleText();
        } catch (error) {
          console.warn('Web MCP: Visible text extraction failed:', error);
        }
      }

      // Method 3: Extract content using readability algorithm
      if (content.length < 500) {
        console.log('Web MCP: Trying readability-based extraction');
        try {
          content += await extractWithReadability();
        } catch (error) {
          console.warn('Web MCP: Readability extraction failed:', error);
        }
      }

      // Method 4: Last resort - extract all text from the document
      if (content.length < 500) {
        console.log('Web MCP: Using last resort extraction method');
        try {
          content += document.body.innerText
            .replace(/\\s+/g, ' ')
            .replace(/\\n+/g, '\\n\\n');
        } catch (error) {
          console.warn('Web MCP: Last resort extraction failed:', error);
        }
      }

      // Count paragraphs and images
      paragraphCount = document.querySelectorAll('p').length;
      imageCount = document.querySelectorAll('img').length;

      // Calculate content size in bytes
      const contentSize = new Blob([content]).size;

      // Check if we have any meaningful content
      if (content.length < 100) {
        return reject(new Error('Failed to extract any meaningful content'));
      }

      resolve({
        content,
        contentSize,
        paragraphs: paragraphCount,
        images: imageCount,
        extractionMethod: 'fallback'
      });
    } catch (error) {
      console.error('Web MCP: Error in fallback extraction:', error);
      reject(error);
    }
  });
}

/**
 * Extract all text nodes with reasonable length
 * @returns {Promise<string>} - Promise resolving to extracted content
 */
async function extractTextNodes() {
  return new Promise((resolve) => {
    let content = '';

    // Function to recursively process text nodes
    function processNode(node) {
      // Process text nodes
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent.trim();
        if (text.length > 20) {
          content += text + '\\n\\n';
        }
        return;
      }

      // Skip script, style, and hidden elements
      if (node.tagName === 'SCRIPT' ||
          node.tagName === 'STYLE' ||
          node.tagName === 'NOSCRIPT' ||
          !isVisible(node) ||
          isBoilerplate(node)) {
        return;
      }

      // Process child nodes
      for (const child of node.childNodes) {
        processNode(child);
      }
    }

    // Start processing from body
    processNode(document.body);

    resolve(content);
  });
}

/**
 * Extract all visible text from the page
 * @returns {Promise<string>} - Promise resolving to extracted content
 */
async function extractAllVisibleText() {
  return new Promise((resolve) => {
    let content = '';

    // Get all elements that might contain content
    const elements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, td, div');

    for (const element of elements) {
      try {
        // Skip invisible or boilerplate elements
        if (!isVisible(element) || isBoilerplate(element)) {
          continue;
        }

        // Skip elements with very little text
        const text = element.textContent.trim();
        if (text.length < 20) {
          continue;
        }

        // Skip elements that are likely to be containers
        if (element.children.length > 5) {
          continue;
        }

        // Add text to content
        content += text + '\\n\\n';
      } catch (error) {
        console.warn('Web MCP: Error processing element for visible text:', error);
      }
    }

    resolve(content);
  });
}

/**
 * Extract content using a simplified readability algorithm
 * @returns {Promise<string>} - Promise resolving to extracted content
 */
async function extractWithReadability() {
  return new Promise((resolve) => {
    let content = '';

    try {
      // Clone the document to avoid modifying the original
      const docClone = document.cloneNode(true);

      // Remove known non-content elements
      const elementsToRemove = [
        'script', 'style', 'noscript', 'iframe', 'nav', 'footer',
        'header', 'aside', 'form', 'button', 'input', 'textarea'
      ];

      elementsToRemove.forEach(tag => {
        const elements = docClone.querySelectorAll(tag);
        for (const element of elements) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }
      });

      // Remove elements with boilerplate classes/ids
      const boilerplateSelectors = [
        '[id*="nav"]', '[class*="nav"]',
        '[id*="header"]', '[class*="header"]',
        '[id*="footer"]', '[class*="footer"]',
        '[id*="menu"]', '[class*="menu"]',
        '[id*="sidebar"]', '[class*="sidebar"]',
        '[id*="ad"]', '[class*="ad"]',
        '[id*="banner"]', '[class*="banner"]'
      ];

      boilerplateSelectors.forEach(selector => {
        try {
          const elements = docClone.querySelectorAll(selector);
          for (const element of elements) {
            if (element.parentNode) {
              element.parentNode.removeChild(element);
            }
          }
        } catch (error) {
          console.warn(`Web MCP: Error removing elements with selector ${selector}:`, error);
        }
      });

      // Find paragraphs with substantial content
      const paragraphs = docClone.querySelectorAll('p');
      for (const paragraph of paragraphs) {
        const text = paragraph.textContent.trim();
        if (text.length > 40) {
          content += text + '\\n\\n';
        }
      }

      // If not enough content, try to find divs with substantial content
      if (content.length < 500) {
        const divs = docClone.querySelectorAll('div');
        for (const div of divs) {
          // Skip divs with many children (likely containers)
          if (div.children.length > 5) {
            continue;
          }

          const text = div.textContent.trim();
          if (text.length > 100 && text.length < 2000) {
            content += text + '\\n\\n';
          }
        }
      }
    } catch (error) {
      console.warn('Web MCP: Error in readability extraction:', error);
    }

    resolve(content);
  });
}

/**
 * Crawl the current page and related pages to extract content
 * @param {Object} options - Crawling options
 * @param {number} options.maxDepth - Maximum crawl depth (default: 1)
 * @param {number} options.maxPages - Maximum number of pages to crawl (default: 5)
 * @param {boolean} options.stayOnDomain - Whether to stay on the same domain (default: true)
 * @param {Array<string>} options.excludePatterns - URL patterns to exclude
 * @returns {Promise<Object>} - Promise resolving to object containing crawled content and metadata
 */
async function crawlPageContent(options = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      console.log('Web MCP: Starting enhanced crawling with options:', options);

      // Set default options with improved crawling parameters
      const crawlOptions = {
        maxDepth: options.maxDepth || 2,        // Increased default depth to 2
        maxPages: options.maxPages || 8,        // Increased default max pages to 8
        stayOnDomain: options.stayOnDomain !== false, // Default to true
        timeout: options.timeout || 15000,      // 15 second timeout for fetching pages
        retryCount: options.retryCount || 2,    // Retry failed requests twice
        excludePatterns: options.excludePatterns || [
          /\\.(jpg|jpeg|png|gif|svg|webp|bmp|ico)$/i,
          /\\.(css|js|json|xml)$/i,
          /\\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$/i,
          /\\.(zip|rar|tar|gz)$/i,
          /\\.(mp3|mp4|avi|mov|wmv|flv|ogg|webm)$/i,
          /\\#/,
          /\\/tag\\//,
          /\\/category\\//,
          /\\/author\\//,
          /\\/date\\//,
          /\\/page\\//,
          /\\/search\\//,
          /\\/login\\//,
          /\\/register\\//,
          /\\/signup\\//,
          /\\/account\\//,
          /\\/cart\\//,
          /\\/checkout\\//,
          /\\/privacy\\//,
          /\\/terms\\//,
          /\\/contact\\//,
          /\\/about\\//,
          /\\/faq\\//,
          /\\/help\\//,
          /\\/support\\//
        ]
      };

      // Initialize crawling status
      currentCrawlingStatus = {
        inProgress: true,
        pagesProcessed: 0,
        totalPages: 1, // Start with at least the current page
        currentUrl: window.location.href,
        startTime: new Date(),
        errors: []
      };

      // Get the current page URL and domain
      const startUrl = window.location.href;
      const startDomain = new URL(startUrl).hostname;

      // Initialize data structures for crawling
      const crawledUrls = new Set(); // URLs that have been processed
      const urlsToProcess = [{ url: startUrl, depth: 0 }]; // Queue of URLs to process
      const pageContents = []; // Content from each page
      const allLinks = new Set(); // All links found during crawling

      // Extract content from the current page using our enhanced crawling approach
      console.log('Web MCP: Starting with current page extraction');

      try {
        // First, extract content directly from the current page DOM
        const currentPageContent = {
          title: document.title,
          content: '',
          links: []
        };

        // Try multiple extraction methods for the current page
        try {
          // First try with main content extraction
          const mainElement = findMainContent(document);
          currentPageContent.content = extractContentFromElement(mainElement);

          // If content is too short, try extracting from the entire body
          if (currentPageContent.content.length < 200) {
            console.log('Web MCP: Main content too short, extracting from entire body');
            currentPageContent.content = extractContentFromElement(document.body);
          }

          // If still too short, try aggressive extraction
          if (currentPageContent.content.length < 200) {
            console.log('Web MCP: Body content too short, trying aggressive extraction');
            currentPageContent.content = extractAllTextContent(document);
          }
        } catch (extractError) {
          console.warn('Web MCP: Error in standard extraction, using aggressive method:', extractError);
          currentPageContent.content = extractAllTextContent(document);
        }

        // If we still don't have content, add a placeholder
        if (!currentPageContent.content || currentPageContent.content.length < 100) {
          console.warn('Web MCP: Failed to extract meaningful content from current page');
          currentPageContent.content = `# ${document.title || 'Current Page'}\n\nThis page appears to have content protection or dynamic content that prevents extraction. I'll try to analyze linked pages instead.\n\n`;
        }

        // Extract links from current page
        currentPageContent.links = extractLinks();

        // Add to crawled pages
        pageContents.push({
          url: startUrl,
          title: currentPageContent.title,
          content: currentPageContent.content
        });

        // Mark as crawled
        crawledUrls.add(startUrl);
        currentCrawlingStatus.pagesProcessed++;

        // Add links to processing queue
        for (const link of currentPageContent.links) {
          if (!crawledUrls.has(link) && isValidLink(link, startDomain, crawlOptions)) {
            urlsToProcess.push({ url: link, depth: 1 });
            allLinks.add(link);
          }
        }

        console.log(`Web MCP: Extracted ${currentPageContent.content.length} chars and ${currentPageContent.links.length} links from current page`);
      } catch (error) {
        console.error('Web MCP: Error extracting content from current page:', error);
        currentCrawlingStatus.errors.push({
          url: startUrl,
          error: error.message
        });

        // Even if extraction fails, try to get links
        try {
          const links = extractLinks();
          console.log(`Web MCP: Extracted ${links.length} links despite content extraction failure`);

          // Add links to processing queue
          for (const link of links) {
            if (!crawledUrls.has(link) && isValidLink(link, startDomain, crawlOptions)) {
              urlsToProcess.push({ url: link, depth: 1 });
              allLinks.add(link);
            }
          }
        } catch (linkError) {
          console.error('Web MCP: Failed to extract links:', linkError);
        }
      }

      // Update total pages count
      currentCrawlingStatus.totalPages = Math.min(urlsToProcess.length + 1, crawlOptions.maxPages);
      console.log(`Web MCP: Planning to crawl up to ${currentCrawlingStatus.totalPages} pages`);

      // Process the queue until we reach maxPages or the queue is empty
      while (urlsToProcess.length > 0 && pageContents.length < crawlOptions.maxPages) {
        const { url, depth } = urlsToProcess.shift();

        // Skip if we've already crawled this URL or if we've reached max depth
        if (crawledUrls.has(url) || depth > crawlOptions.maxDepth) {
          continue;
        }

        // Update current URL in status
        currentCrawlingStatus.currentUrl = url;
        console.log(`Web MCP: Crawling ${url} (depth ${depth}/${crawlOptions.maxDepth})`);

        try {
          // Fetch and extract content from the URL with retry logic
          const pageContent = await fetchAndExtractContent(url, crawlOptions);

          // Add to crawled pages
          pageContents.push({
            url: url,
            title: pageContent.title,
            content: pageContent.content
          });

          // Mark as crawled
          crawledUrls.add(url);
          currentCrawlingStatus.pagesProcessed++;
          console.log(`Web MCP: Successfully crawled ${url} (${pageContents.length}/${crawlOptions.maxPages} pages)`);

          // Extract and queue links if we haven't reached max depth
          if (depth < crawlOptions.maxDepth) {
            const pageLinks = pageContent.links || [];
            console.log(`Web MCP: Found ${pageLinks.length} links on ${url}`);

            // Prioritize links that seem more relevant (e.g., contain similar keywords)
            const prioritizedLinks = prioritizeLinks(pageLinks, startUrl, pageContent.title);

            for (const link of prioritizedLinks) {
              if (!crawledUrls.has(link) && !urlsToProcess.some(item => item.url === link) &&
                  isValidLink(link, startDomain, crawlOptions)) {
                urlsToProcess.push({ url: link, depth: depth + 1 });
                allLinks.add(link);
              }
            }
          }
        } catch (error) {
          console.warn(`Web MCP: Error crawling ${url}:`, error);
          currentCrawlingStatus.errors.push({
            url: url,
            error: error.message
          });
        }
      }

      // Combine all content
      let combinedContent = '';
      let totalParagraphs = 0;
      let totalImages = 0;

      // Add a summary of crawled pages
      combinedContent += `# Web Crawl Results\n\n`;
      combinedContent += `Crawled ${pageContents.length} pages starting from ${startUrl}\n\n`;

      // Add table of contents
      combinedContent += `## Table of Contents\n\n`;
      for (let i = 0; i < pageContents.length; i++) {
        const page = pageContents[i];
        combinedContent += `${i+1}. [${page.title}](${page.url})\n`;
      }
      combinedContent += `\n\n`;

      // Add content from each page
      for (let i = 0; i < pageContents.length; i++) {
        const page = pageContents[i];
        combinedContent += `## ${i+1}. ${page.title}\n`;
        combinedContent += `URL: ${page.url}\n\n`;
        combinedContent += page.content;
        combinedContent += `\n\n---\n\n`;

        // Count paragraphs (rough estimate)
        const paragraphMatches = page.content.match(/\\n\\n/g);
        totalParagraphs += paragraphMatches ? paragraphMatches.length + 1 : 1;

        // Count images (rough estimate)
        const imageMatches = page.content.match(/Image:/g);
        totalImages += imageMatches ? imageMatches.length : 0;
      }

      // Add list of all discovered links
      combinedContent += `## All Discovered Links\n\n`;
      const linksList = Array.from(allLinks);
      for (const link of linksList) {
        combinedContent += `- ${link}\n`;
      }

      // Calculate content size
      const contentSize = new Blob([combinedContent]).size;

      // Reset crawling status
      currentCrawlingStatus.inProgress = false;

      // Return the combined content and metadata
      resolve({
        content: combinedContent,
        contentSize: contentSize,
        paragraphs: totalParagraphs,
        images: totalImages,
        links: linksList,
        crawledPages: pageContents.length,
        extractionMethod: 'crawling'
      });
    } catch (error) {
      console.error('Web MCP: Error during crawling:', error);
      currentCrawlingStatus.inProgress = false;
      currentCrawlingStatus.errors.push({
        url: window.location.href,
        error: error.message
      });
      reject(error);
    }
  });
}

/**
 * Extract links from the current page
 * @returns {Array<string>} - Array of absolute URLs
 */
function extractLinks() {
  const links = [];
  const anchorElements = document.querySelectorAll('a[href]');

  for (const anchor of anchorElements) {
    try {
      const href = anchor.href;
      if (href && typeof href === 'string' && href.trim() !== '') {
        // Convert to absolute URL
        const absoluteUrl = new URL(href, window.location.href).href;
        links.push(absoluteUrl);
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting link:', error);
    }
  }

  return [...new Set(links)]; // Remove duplicates
}

/**
 * Check if a link is valid for crawling based on options
 * @param {string} url - The URL to check
 * @param {string} startDomain - The domain of the starting URL
 * @param {Object} options - Crawling options
 * @returns {boolean} - Whether the link is valid for crawling
 */
function isValidLink(url, startDomain, options) {
  try {
    // Parse URL
    const parsedUrl = new URL(url);

    // Check if URL is HTTP or HTTPS
    if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
      return false;
    }

    // Check if we should stay on the same domain
    if (options.stayOnDomain && parsedUrl.hostname !== startDomain) {
      return false;
    }

    // Check against exclude patterns
    for (const pattern of options.excludePatterns) {
      if (pattern.test(url)) {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.warn('Web MCP: Error validating link:', error);
    return false;
  }
}

/**
 * Fetch and extract content from a URL
 * @param {string} url - The URL to fetch and extract content from
 * @returns {Promise<Object>} - Promise resolving to object containing extracted content and metadata
 */
async function fetchAndExtractContent(url, options = {}, retryCount = 0) {
  return new Promise(async (resolve, reject) => {
    try {
      console.log(`Web MCP: Fetching content from ${url} (attempt ${retryCount + 1})`);

      // Determine which extraction method to use
      // Try direct fetch first, then iframe if that fails
      try {
        // Try direct fetch method first (works better for many sites)
        const directFetchResult = await fetchWithDirectMethod(url, options);
        if (directFetchResult && directFetchResult.content && directFetchResult.content.length > 100) {
          console.log(`Web MCP: Successfully extracted content using direct fetch method (${directFetchResult.content.length} chars)`);
          resolve(directFetchResult);
          return;
        }
      } catch (directFetchError) {
        console.warn(`Web MCP: Direct fetch method failed for ${url}, falling back to iframe:`, directFetchError);
        // Continue to iframe method
      }

      // Create an iframe to load the page (fallback method)
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';

      // Set sandbox attributes to help with some content protection mechanisms
      iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts');

      // Add to document
      document.body.appendChild(iframe);

      // Set a timeout for the fetch operation using the options timeout or default
      const timeoutDuration = options.timeout || 15000; // 15 second default timeout
      const timeoutId = setTimeout(() => {
        console.warn(`Web MCP: Timeout fetching ${url} after ${timeoutDuration}ms`);

        try {
          document.body.removeChild(iframe);
        } catch (e) {
          console.error('Error removing iframe:', e);
        }

        // Try alternative extraction method before giving up
        if (retryCount === 0) {
          console.log(`Web MCP: Trying alternative extraction method for ${url}`);
          fetchWithAlternativeMethod(url, options)
            .then(resolve)
            .catch(altError => {
              // Retry with regular method if we haven't reached max retries
              const maxRetries = options.retryCount || 2;
              if (retryCount < maxRetries) {
                console.log(`Web MCP: Retrying fetch for ${url} (${retryCount + 1}/${maxRetries})`);
                fetchAndExtractContent(url, options, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              } else {
                reject(new Error(`Failed to extract content from ${url} after multiple attempts`));
              }
            });
        } else {
          // Retry if we haven't reached max retries
          const maxRetries = options.retryCount || 2;
          if (retryCount < maxRetries) {
            console.log(`Web MCP: Retrying fetch for ${url} (${retryCount + 1}/${maxRetries})`);
            fetchAndExtractContent(url, options, retryCount + 1)
              .then(resolve)
              .catch(reject);
          } else {
            reject(new Error(`Timeout fetching ${url} after ${maxRetries + 1} attempts`));
          }
        }
      }, timeoutDuration);

      // Load the URL in the iframe
      iframe.onload = async () => {
        try {
          clearTimeout(timeoutId);

          // Get the document from the iframe
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

          // Check if we got a valid document
          if (!iframeDoc || !iframeDoc.body) {
            throw new Error('Could not access document content');
          }

          // Extract content
          let content = '';
          let title = '';
          let links = [];

          try {
            // Get title
            title = iframeDoc.title || url;

            // Extract main content
            const mainElement = findMainContent(iframeDoc);
            content = extractContentFromElement(mainElement);

            // If content is too short, try extracting from the entire body
            if (content.length < 200) {
              console.log('Web MCP: Content too short, extracting from entire body');
              content = extractContentFromElement(iframeDoc.body);
            }

            // Extract links
            links = extractLinksFromDocument(iframeDoc, url);

            // Log success
            console.log(`Web MCP: Successfully extracted content from ${url} (${content.length} chars, ${links.length} links)`);
          } catch (extractError) {
            console.warn(`Web MCP: Error extracting content from ${url}:`, extractError);

            // Try a more aggressive extraction approach
            try {
              console.log('Web MCP: Trying aggressive extraction approach');
              content = extractAllTextContent(iframeDoc);

              if (content.length < 100) {
                throw new Error('Insufficient content extracted with aggressive approach');
              }
            } catch (aggressiveError) {
              console.warn('Web MCP: Aggressive extraction failed:', aggressiveError);
              content = `[Page crawled but content extraction had issues. The page may have content protection mechanisms.]`;
            }
          }

          // Clean up
          try {
            document.body.removeChild(iframe);
          } catch (e) {
            console.error('Error removing iframe:', e);
          }

          // Validate content
          if (content.length < 100 && retryCount === 0) {
            // Try alternative method if content is too short
            console.log(`Web MCP: Content too short (${content.length} chars), trying alternative method`);
            fetchWithAlternativeMethod(url, options)
              .then(resolve)
              .catch(altError => {
                // Return what we have if alternative method fails
                resolve({
                  title: title,
                  content: content,
                  links: links,
                  url: url
                });
              });
          } else {
            resolve({
              title: title,
              content: content,
              links: links,
              url: url
            });
          }
        } catch (error) {
          clearTimeout(timeoutId);

          try {
            document.body.removeChild(iframe);
          } catch (e) {
            console.error('Error removing iframe:', e);
          }

          // Try alternative method first before retrying
          if (retryCount === 0) {
            console.log(`Web MCP: Trying alternative extraction method after iframe error`);
            fetchWithAlternativeMethod(url, options)
              .then(resolve)
              .catch(altError => {
                // Retry with regular method if alternative fails
                const maxRetries = options.retryCount || 2;
                if (retryCount < maxRetries) {
                  console.log(`Web MCP: Retrying fetch for ${url} due to error: ${error.message}`);
                  fetchAndExtractContent(url, options, retryCount + 1)
                    .then(resolve)
                    .catch(reject);
                } else {
                  reject(error);
                }
              });
          } else {
            // Retry if we haven't reached max retries
            const maxRetries = options.retryCount || 2;
            if (retryCount < maxRetries) {
              console.log(`Web MCP: Retrying fetch for ${url} due to error: ${error.message}`);
              fetchAndExtractContent(url, options, retryCount + 1)
                .then(resolve)
                .catch(reject);
            } else {
              reject(error);
            }
          }
        }
      };

      // Handle load errors
      iframe.onerror = (error) => {
        clearTimeout(timeoutId);

        try {
          document.body.removeChild(iframe);
        } catch (e) {
          console.error('Error removing iframe:', e);
        }

        // Try alternative method first before retrying
        if (retryCount === 0) {
          console.log(`Web MCP: Trying alternative extraction method after iframe load error`);
          fetchWithAlternativeMethod(url, options)
            .then(resolve)
            .catch(altError => {
              // Retry with regular method if alternative fails
              const maxRetries = options.retryCount || 2;
              if (retryCount < maxRetries) {
                console.log(`Web MCP: Retrying fetch for ${url} after load error`);
                fetchAndExtractContent(url, options, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              } else {
                reject(new Error(`Failed to load ${url} after multiple attempts: ${error.message}`));
              }
            });
        } else {
          // Retry if we haven't reached max retries
          const maxRetries = options.retryCount || 2;
          if (retryCount < maxRetries) {
            console.log(`Web MCP: Retrying fetch for ${url} after load error`);
            fetchAndExtractContent(url, options, retryCount + 1)
              .then(resolve)
              .catch(reject);
          } else {
            reject(new Error(`Failed to load ${url} after ${maxRetries + 1} attempts: ${error.message}`));
          }
        }
      };

      // Set the iframe source to the URL
      iframe.src = url;
    } catch (error) {
      console.error(`Web MCP: Error in fetchAndExtractContent for ${url}:`, error);
      reject(error);
    }
  });
}

/**
 * Fetch content using direct fetch API method
 * This can bypass some content protection mechanisms
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - Promise resolving to extracted content
 */
async function fetchWithDirectMethod(url, options = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      console.log(`Web MCP: Trying direct fetch method for ${url}`);

      // Use fetch API to get the HTML content
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        credentials: 'omit',
        redirect: 'follow',
        mode: 'cors',
        cache: 'no-cache'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get the HTML text
      const html = await response.text();

      if (!html || html.length < 100) {
        throw new Error('Insufficient content received');
      }

      // Create a DOM parser to parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // Extract title
      const title = doc.title || url;

      // Extract main content
      const mainElement = findMainContent(doc);
      let content = extractContentFromElement(mainElement);

      // If content is too short, try extracting from the entire body
      if (content.length < 200) {
        console.log('Web MCP: Content too short, extracting from entire body');
        content = extractContentFromElement(doc.body);
      }

      // Extract links
      const links = extractLinksFromDocument(doc, url);

      // Return the extracted content
      resolve({
        title: title,
        content: content,
        links: links,
        url: url
      });
    } catch (error) {
      console.warn(`Web MCP: Direct fetch method failed for ${url}:`, error);
      reject(error);
    }
  });
}

/**
 * Fetch content using an alternative method (XHR)
 * This can bypass different content protection mechanisms than fetch or iframe
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} - Promise resolving to extracted content
 */
async function fetchWithAlternativeMethod(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      console.log(`Web MCP: Trying alternative fetch method (XHR) for ${url}`);

      // Create XHR request
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);

      // Set headers to mimic a browser
      xhr.setRequestHeader('Accept', 'text/html,application/xhtml+xml,application/xml');
      xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

      // Set timeout
      xhr.timeout = options.timeout || 15000;

      xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            // Create a DOM parser to parse the HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(xhr.responseText, 'text/html');

            // Extract title
            const title = doc.title || url;

            // Try different extraction methods
            let content = '';

            // First try with readability-like algorithm
            try {
              const mainElement = findMainContent(doc);
              content = extractContentFromElement(mainElement);
            } catch (extractError) {
              console.warn('Web MCP: Main content extraction failed:', extractError);
            }

            // If content is too short, try extracting all text
            if (content.length < 200) {
              console.log('Web MCP: Content too short, extracting all text');
              content = extractAllTextContent(doc);
            }

            // Extract links
            const links = extractLinksFromDocument(doc, url);

            // Return the extracted content
            resolve({
              title: title,
              content: content,
              links: links,
              url: url
            });
          } catch (parseError) {
            console.error('Web MCP: Error parsing HTML:', parseError);
            reject(parseError);
          }
        } else {
          reject(new Error(`HTTP error! status: ${xhr.status}`));
        }
      };

      xhr.onerror = function() {
        reject(new Error('Network error occurred'));
      };

      xhr.ontimeout = function() {
        reject(new Error('Request timed out'));
      };

      xhr.send();
    } catch (error) {
      console.error(`Web MCP: Alternative fetch method failed for ${url}:`, error);
      reject(error);
    }
  });
}

/**
 * Extract all text content from a document
 * More aggressive approach that gets all text regardless of structure
 * @param {Document} doc - The document to extract from
 * @returns {string} - The extracted text content
 */
function extractAllTextContent(doc) {
  let content = '';

  try {
    // Get title
    if (doc.title) {
      content += `# ${doc.title}\n\n`;
    }

    // Function to recursively extract text from nodes
    function extractTextFromNode(node) {
      // Skip script, style, and hidden elements
      if (node.nodeName === 'SCRIPT' ||
          node.nodeName === 'STYLE' ||
          node.nodeName === 'NOSCRIPT') {
        return;
      }

      // If it's a text node, add its content
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent.trim();
        if (text.length > 10) {  // Only add substantial text
          content += text + '\n\n';
        }
        return;
      }

      // If it's an element node, process its children
      if (node.nodeType === Node.ELEMENT_NODE) {
        // Special handling for headings
        if (/^H[1-6]$/.test(node.nodeName)) {
          const level = node.nodeName.charAt(1);
          const text = node.textContent.trim();
          if (text.length > 0) {
            content += `${'#'.repeat(parseInt(level))} ${text}\n\n`;
            return;  // Skip processing children as we've already added the text
          }
        }

        // Special handling for paragraphs
        if (node.nodeName === 'P') {
          const text = node.textContent.trim();
          if (text.length > 0) {
            content += `${text}\n\n`;
            return;  // Skip processing children
          }
        }

        // Special handling for list items
        if (node.nodeName === 'LI') {
          const text = node.textContent.trim();
          if (text.length > 0) {
            content += `- ${text}\n`;
            return;  // Skip processing children
          }
        }

        // Process children for other elements
        for (let i = 0; i < node.childNodes.length; i++) {
          extractTextFromNode(node.childNodes[i]);
        }
      }
    }

    // Start extraction from body
    extractTextFromNode(doc.body);

    // Clean up the content
    content = content
      .replace(/\n{3,}/g, '\n\n')  // Replace multiple newlines with just two
      .trim();

    return content;
  } catch (error) {
    console.error('Web MCP: Error in extractAllTextContent:', error);
    return doc.body.textContent.trim()
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n\n');
  }
}

/**
 * Find the main content element in a document
 * @param {Document} doc - The document to search
 * @returns {Element} - The main content element
 */
function findMainContent(doc) {
  // Try to find main content using common selectors
  const mainContentSelectors = [
    'main',
    'article',
    '#content',
    '.content',
    '.main',
    '.article',
    '.post',
    '.post-content',
    '.entry-content',
    '#main',
    '#article',
    '.page-content',
    '.main-content',
    '[role="main"]',
    '[itemprop="mainContentOfPage"]'
  ];

  for (const selector of mainContentSelectors) {
    try {
      const element = doc.querySelector(selector);
      if (element && element.textContent.trim().length > 200) {
        return element;
      }
    } catch (error) {
      console.warn(`Web MCP: Error finding element with selector ${selector}:`, error);
    }
  }

  // If no main content found, use body
  return doc.body;
}

/**
 * Extract content from an element
 * @param {Element} element - The element to extract content from
 * @returns {string} - The extracted content
 */
function extractContentFromElement(element) {
  let content = '';

  // Extract headings
  try {
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    for (const heading of headings) {
      const text = heading.textContent.trim();
      if (text.length > 0) {
        const level = heading.tagName.charAt(1);
        const prefix = '#'.repeat(parseInt(level));
        content += `${prefix} ${text}\n\n`;
      }
    }
  } catch (error) {
    console.warn('Web MCP: Error extracting headings:', error);
  }

  // Extract paragraphs
  try {
    const paragraphs = element.querySelectorAll('p');
    for (const paragraph of paragraphs) {
      const text = paragraph.textContent.trim();
      if (text.length > 0) {
        content += `${text}\n\n`;
      }
    }
  } catch (error) {
    console.warn('Web MCP: Error extracting paragraphs:', error);
  }

  // Extract lists
  try {
    const lists = element.querySelectorAll('ul, ol');
    for (const list of lists) {
      const items = list.querySelectorAll('li');
      for (const item of items) {
        const text = item.textContent.trim();
        if (text.length > 0) {
          content += `- ${text}\n`;
        }
      }
      content += '\n';
    }
  } catch (error) {
    console.warn('Web MCP: Error extracting lists:', error);
  }

  // If content is still empty, extract all text
  if (content.trim() === '') {
    content = element.textContent.trim()
      .replace(/\\s+/g, ' ')
      .replace(/\\n+/g, '\n\n');
  }

  return content;
}

/**
 * Extract links from a document
 * @param {Document} doc - The document to extract links from
 * @param {string} baseUrl - The base URL for resolving relative links
 * @returns {Array<string>} - Array of absolute URLs
 */
function extractLinksFromDocument(doc, baseUrl) {
  const links = [];
  const anchorElements = doc.querySelectorAll('a[href]');

  for (const anchor of anchorElements) {
    try {
      const href = anchor.href;
      if (href && typeof href === 'string' && href.trim() !== '') {
        // Convert to absolute URL
        const absoluteUrl = new URL(href, baseUrl).href;
        links.push(absoluteUrl);
      }
    } catch (error) {
      console.warn('Web MCP: Error extracting link from document:', error);
    }
  }

  return [...new Set(links)]; // Remove duplicates
}

/**
 * Prioritize links based on relevance to the current page
 * @param {Array<string>} links - Array of links to prioritize
 * @param {string} currentUrl - The current page URL
 * @param {string} pageTitle - The current page title
 * @returns {Array<string>} - Prioritized array of links
 */
function prioritizeLinks(links, currentUrl, pageTitle) {
  if (!links || links.length === 0) return [];

  console.log('Web MCP: Prioritizing links for relevance');

  try {
    // Extract keywords from the current URL and title
    const currentUrlObj = new URL(currentUrl);
    const pathSegments = currentUrlObj.pathname.split('/').filter(segment => segment.length > 0);

    // Create a set of keywords from the URL path and title
    const keywords = new Set();

    // Add path segments as keywords
    pathSegments.forEach(segment => {
      // Split by non-alphanumeric characters and add each part
      segment.split(/[^a-zA-Z0-9]/).forEach(part => {
        if (part.length > 3) { // Only consider parts with more than 3 characters
          keywords.add(part.toLowerCase());
        }
      });
    });

    // Add words from title as keywords
    if (pageTitle) {
      pageTitle.split(/\s+/).forEach(word => {
        // Remove punctuation and only consider words with more than 3 characters
        const cleanWord = word.replace(/[^\w]/g, '').toLowerCase();
        if (cleanWord.length > 3) {
          keywords.add(cleanWord);
        }
      });
    }

    // Score each link based on keyword matches
    const scoredLinks = links.map(link => {
      try {
        const url = new URL(link);
        let score = 0;

        // Same domain gets a bonus
        if (url.hostname === currentUrlObj.hostname) {
          score += 10;
        }

        // Same path prefix gets a bonus
        if (url.pathname.startsWith(currentUrlObj.pathname) && url.pathname !== currentUrlObj.pathname) {
          score += 5;
        }

        // Check for keyword matches in the URL
        keywords.forEach(keyword => {
          if (url.pathname.toLowerCase().includes(keyword)) {
            score += 3;
          }
          if (url.hostname.toLowerCase().includes(keyword)) {
            score += 1;
          }
        });

        // Penalize URLs with many query parameters (often not content pages)
        const queryParams = url.search.split('&').length - 1;
        score -= queryParams;

        // Penalize very deep paths (often not main content)
        const pathDepth = url.pathname.split('/').length - 1;
        if (pathDepth > 3) {
          score -= (pathDepth - 3);
        }

        return { link, score };
      } catch (e) {
        return { link, score: 0 };
      }
    });

    // Sort by score (descending) and return just the links
    scoredLinks.sort((a, b) => b.score - a.score);

    console.log('Web MCP: Links prioritized by relevance score');
    return scoredLinks.map(item => item.link);
  } catch (error) {
    console.error('Web MCP: Error prioritizing links:', error);
    return links; // Return original links if there's an error
  }
}