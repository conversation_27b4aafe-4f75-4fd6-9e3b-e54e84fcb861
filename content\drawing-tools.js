/**
 * Drawing Tools Content Script
 * Handles drawing functionality on the webpage
 */

console.log('Drawing Tools content script loaded');

// Initialize drawing tools and notify background script
(function initializeDrawingTools() {
  try {
    console.log('Initializing drawing tools...');

    // Send a message to the background script to confirm loading
    chrome.runtime.sendMessage({
      action: 'drawingToolsLoaded',
      url: window.location.href,
      timestamp: new Date().toISOString()
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error in drawing tools initialization:', chrome.runtime.lastError);
      } else {
        console.log('Drawing tools initialization response:', response);
      }
    });

    console.log('Drawing tools initialization message sent');
  } catch (error) {
    console.error('Failed to initialize drawing tools:', error);
  }
})();

// Drawing state
let canvas = null;
let ctx = null;
let isDrawing = false;
let lastX = 0;
let lastY = 0;
let currentTool = 'pen';
let currentColor = '#FF4F18';
let lineWidth = 3;
let highlighterOpacity = 0.5;

/**
 * Create a canvas for drawing on the webpage
 */
function createDrawingCanvas() {
  console.log('Creating drawing canvas...');

  // Check if canvas already exists
  if (canvas) {
    console.log('Canvas already exists, returning existing canvas');
    return { success: true, message: 'Canvas already exists' };
  }

  try {
    console.log('Creating new canvas element');

    // Create canvas element
    canvas = document.createElement('canvas');
    canvas.id = 'drawingCanvas';

    // Get the full document size (including scrollable area)
    const docWidth = Math.max(
      document.body.scrollWidth || 0,
      document.documentElement.scrollWidth || 0,
      document.body.offsetWidth || 0,
      document.documentElement.offsetWidth || 0,
      document.body.clientWidth || 0,
      document.documentElement.clientWidth || 0,
      window.innerWidth || 0
    );

    const docHeight = Math.max(
      document.body.scrollHeight || 0,
      document.documentElement.scrollHeight || 0,
      document.body.offsetHeight || 0,
      document.documentElement.offsetHeight || 0,
      document.body.clientHeight || 0,
      document.documentElement.clientHeight || 0,
      window.innerHeight || 0
    );

    console.log(`Document dimensions: ${docWidth}x${docHeight}`);

    // Set canvas size to full document size
    canvas.width = docWidth;
    canvas.height = docHeight;

    // Position canvas to cover the entire document (not fixed)
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = docWidth + 'px';
    canvas.style.height = docHeight + 'px';
    canvas.style.zIndex = '9999999';
    canvas.style.pointerEvents = 'auto';
    canvas.style.cursor = 'crosshair';
    canvas.style.backgroundColor = 'transparent';

    console.log('Adding canvas to document body');

    // Add canvas to body
    document.body.appendChild(canvas);

    // Get context
    ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }

    console.log('Canvas context obtained successfully');

    // Make canvas transparent
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    console.log('Adding event listeners to canvas');

    // Add event listeners
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Add touch support
    canvas.addEventListener('touchstart', handleTouchStart);
    canvas.addEventListener('touchmove', handleTouchMove);
    canvas.addEventListener('touchend', handleTouchEnd);

    // Add resize listener
    window.addEventListener('resize', resizeCanvas);

    console.log('Drawing canvas created successfully');
    return { success: true, message: 'Canvas created successfully' };
  } catch (error) {
    console.error('Error creating drawing canvas:', error);
    console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    return { success: false, error: error.message || 'Unknown error creating canvas' };
  }
}

/**
 * Remove the drawing canvas
 */
function removeDrawingCanvas() {
  if (canvas) {
    // Remove event listeners
    canvas.removeEventListener('mousedown', startDrawing);
    canvas.removeEventListener('mousemove', draw);
    canvas.removeEventListener('mouseup', stopDrawing);
    canvas.removeEventListener('mouseout', stopDrawing);

    canvas.removeEventListener('touchstart', handleTouchStart);
    canvas.removeEventListener('touchmove', handleTouchMove);
    canvas.removeEventListener('touchend', handleTouchEnd);

    window.removeEventListener('resize', resizeCanvas);

    // Remove canvas from DOM
    document.body.removeChild(canvas);

    // Reset variables
    canvas = null;
    ctx = null;
    isDrawing = false;

    return { success: true, message: 'Canvas removed successfully' };
  }

  return { success: false, message: 'No canvas to remove' };
}

/**
 * Resize the canvas when window is resized
 */
function resizeCanvas() {
  if (canvas) {
    // Save current drawing
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

    // Get the full document size (including scrollable area)
    const docWidth = Math.max(
      document.body.scrollWidth,
      document.documentElement.scrollWidth,
      document.body.offsetWidth,
      document.documentElement.offsetWidth,
      document.body.clientWidth,
      document.documentElement.clientWidth
    );

    const docHeight = Math.max(
      document.body.scrollHeight,
      document.documentElement.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.offsetHeight,
      document.body.clientHeight,
      document.documentElement.clientHeight
    );

    // Resize canvas to full document size
    canvas.width = docWidth;
    canvas.height = docHeight;
    canvas.style.width = docWidth + 'px';
    canvas.style.height = docHeight + 'px';

    // Keep canvas transparent
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Restore drawing
    ctx.putImageData(imageData, 0, 0);
  }
}

/**
 * Start drawing on mousedown
 * @param {MouseEvent} e - The mouse event
 */
function startDrawing(e) {
  isDrawing = true;
  // Use pageX/pageY instead of clientX/clientY to account for scrolling
  [lastX, lastY] = [e.pageX, e.pageY];
}

/**
 * Handle drawing on mousemove
 * @param {MouseEvent} e - The mouse event
 */
function draw(e) {
  if (!isDrawing) return;

  // Set drawing properties based on current tool
  switch (currentTool) {
    case 'pen':
      ctx.globalCompositeOperation = 'source-over';
      ctx.strokeStyle = currentColor;
      ctx.lineWidth = lineWidth;
      ctx.lineJoin = 'round';
      ctx.lineCap = 'round';
      ctx.globalAlpha = 1;
      break;
    case 'highlighter':
      ctx.globalCompositeOperation = 'source-over';
      ctx.strokeStyle = currentColor;
      ctx.lineWidth = lineWidth * 3; // Highlighter is thicker
      ctx.lineJoin = 'round';
      ctx.lineCap = 'round';
      ctx.globalAlpha = highlighterOpacity;
      break;
    case 'eraser':
      ctx.globalCompositeOperation = 'destination-out';
      ctx.lineWidth = lineWidth * 4; // Make eraser even thicker for better erasing
      ctx.lineJoin = 'round';
      ctx.lineCap = 'round';
      ctx.globalAlpha = 1;
      break;
  }

  // Draw line
  ctx.beginPath();
  ctx.moveTo(lastX, lastY);
  ctx.lineTo(e.pageX, e.pageY);
  ctx.stroke();

  // Update last position
  [lastX, lastY] = [e.pageX, e.pageY];
}

/**
 * Stop drawing on mouseup or mouseout
 */
function stopDrawing() {
  isDrawing = false;
}

/**
 * Handle touch start event
 * @param {TouchEvent} e - The touch event
 */
function handleTouchStart(e) {
  e.preventDefault();
  if (e.touches.length === 1) {
    const touch = e.touches[0];
    startDrawing({
      pageX: touch.pageX,
      pageY: touch.pageY
    });
  }
}

/**
 * Handle touch move event
 * @param {TouchEvent} e - The touch event
 */
function handleTouchMove(e) {
  e.preventDefault();
  if (e.touches.length === 1) {
    const touch = e.touches[0];
    draw({
      pageX: touch.pageX,
      pageY: touch.pageY
    });
  }
}

/**
 * Handle touch end event
 * @param {TouchEvent} e - The touch event
 */
function handleTouchEnd(e) {
  e.preventDefault();
  stopDrawing();
}

/**
 * Set the current drawing tool
 * @param {string} tool - The tool to set (pen, highlighter, eraser)
 */
function setTool(tool) {
  currentTool = tool;

  // Update cursor
  if (canvas) {
    // Define custom cursors for each tool
    const penCursor = 'url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'><path fill=\'%23000000\' d=\'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25z\'/></svg>") 0 24, auto';
    const highlighterCursor = 'url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'><path fill=\'%23FFFF00\' d=\'M10 20H5v2h5v-2zm9-18h-5v2h5V2zm-9 18H5v2h5v-2z\'/></svg>") 0 24, auto';
    const eraserCursor = 'url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'><path fill=\'%23FF0000\' d=\'M15.14 3c-.51 0-1.02.2-1.41.59L2.59 14.73c-.78.77-.78 2.04 0 2.83L5.03 20h7.66l8.72-8.72c.79-.78.79-2.05 0-2.83l-4.85-4.86c-.39-.39-.9-.59-1.42-.59z\'/></svg>") 0 24, auto';
    const textCursor = 'url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\'><path fill=\'%23FFFFFF\' d=\'M5 4v3h5.5v12h3V7H19V4z\'/></svg>") 0 24, auto';

    // Set the cursor based on the selected tool
    switch (tool) {
      case 'pen':
        canvas.style.cursor = penCursor;
        break;
      case 'highlighter':
        canvas.style.cursor = highlighterCursor;
        break;
      case 'eraser':
        canvas.style.cursor = eraserCursor;
        break;
      case 'text':
        canvas.style.cursor = textCursor;
        break;
      default:
        canvas.style.cursor = 'crosshair';
    }

    // Add event listeners to handle cursor changes when hovering over UI elements
    setupCursorInteractions(tool, penCursor, highlighterCursor, eraserCursor, textCursor);
  }
}

/**
 * Setup cursor interactions to change cursor when hovering over UI elements
 * @param {string} tool - The current tool
 * @param {string} penCursor - The pen cursor CSS
 * @param {string} highlighterCursor - The highlighter cursor CSS
 * @param {string} eraserCursor - The eraser cursor CSS
 * @param {string} textCursor - The text cursor CSS
 */
function setupCursorInteractions(tool, penCursor, highlighterCursor, eraserCursor, textCursor) {
  // Find the floating controls and sidebar elements
  const floatingControls = document.getElementById('drawingFloatingControls');
  const sidebar = document.querySelector('.sidebar-container, .container');

  // Function to get the appropriate cursor based on the tool
  const getToolCursor = () => {
    switch (tool) {
      case 'pen': return penCursor;
      case 'highlighter': return highlighterCursor;
      case 'eraser': return eraserCursor;
      case 'text': return textCursor;
      default: return 'crosshair';
    }
  };

  // Remove any existing event listeners
  if (floatingControls) {
    floatingControls.onmouseenter = null;
    floatingControls.onmouseleave = null;
  }

  if (sidebar) {
    sidebar.onmouseenter = null;
    sidebar.onmouseleave = null;
  }

  // Add event listeners to floating controls
  if (floatingControls) {
    // Change to default cursor when hovering over floating controls
    floatingControls.addEventListener('mouseenter', () => {
      canvas.style.cursor = 'default';
    });

    // Change back to tool cursor when leaving floating controls
    floatingControls.addEventListener('mouseleave', () => {
      canvas.style.cursor = getToolCursor();
    });
  }

  // Add event listeners to sidebar if it exists
  if (sidebar) {
    // Change to default cursor when hovering over sidebar
    sidebar.addEventListener('mouseenter', () => {
      canvas.style.cursor = 'default';
    });

    // Change back to tool cursor when leaving sidebar
    sidebar.addEventListener('mouseleave', () => {
      canvas.style.cursor = getToolCursor();
    });
  }

  // Also handle any buttons or interactive elements on the page
  const interactiveElements = document.querySelectorAll('button, a, input, select, textarea, [role="button"]');
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      canvas.style.cursor = 'pointer';
    });

    element.addEventListener('mouseleave', () => {
      // Check if we're still over the floating controls or sidebar
      const isOverFloatingControls = floatingControls && floatingControls.matches(':hover');
      const isOverSidebar = sidebar && sidebar.matches(':hover');

      if (!isOverFloatingControls && !isOverSidebar) {
        canvas.style.cursor = getToolCursor();
      }
    });
  });
}

/**
 * Set the current drawing color
 * @param {string} color - The color to set (hex code)
 */
function setColor(color) {
  currentColor = color;
}

/**
 * Add text to the canvas
 * @param {string} text - The text to add
 * @param {string} color - The color of the text
 */
function addText(text, color) {
  if (!ctx || !canvas) return;

  // Set text properties
  ctx.font = '24px Arial';
  ctx.fillStyle = color || currentColor;
  ctx.textBaseline = 'middle';

  // Calculate position (center of viewport, accounting for scroll)
  const x = window.scrollX + (window.innerWidth / 2);
  const y = window.scrollY + (window.innerHeight / 2);

  // Add text to canvas
  ctx.globalAlpha = 1;
  ctx.globalCompositeOperation = 'source-over';
  ctx.textAlign = 'center';
  ctx.fillText(text, x, y);
}

/**
 * Clear the canvas
 */
function clearCanvas() {
  if (ctx) {
    // Clear the canvas to make it transparent
    ctx.clearRect(0, 0, canvas.width, canvas.height);
  }
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Drawing tools received message:', message.action);

  try {
    if (message.action === 'createDrawingCanvas') {
      console.log('Creating drawing canvas from message handler');
      const result = createDrawingCanvas();
      console.log('Drawing canvas creation result:', result);
      sendResponse(result);
    } else if (message.action === 'removeDrawingCanvas') {
      console.log('Removing drawing canvas');
      const result = removeDrawingCanvas();
      console.log('Drawing canvas removal result:', result);
      sendResponse(result);
    } else if (message.action === 'setDrawingTool') {
      console.log('Setting drawing tool to:', message.tool);
      setTool(message.tool);
      sendResponse({ success: true, tool: message.tool });
    } else if (message.action === 'setDrawingColor') {
      console.log('Setting drawing color to:', message.color);
      setColor(message.color);
      sendResponse({ success: true, color: message.color });
    } else if (message.action === 'clearDrawingCanvas') {
      console.log('Clearing drawing canvas');
      clearCanvas();
      sendResponse({ success: true, message: 'Canvas cleared' });
    } else if (message.action === 'addText') {
      console.log('Adding text to canvas:', message.text);
      addText(message.text, message.color);
      sendResponse({ success: true, message: 'Text added' });
    } else if (message.action === 'ping') {
      // Simple ping to check if the script is loaded and responsive
      console.log('Received ping message');
      sendResponse({
        success: true,
        message: 'Drawing tools are active',
        canvasExists: !!canvas,
        timestamp: new Date().toISOString()
      });
    } else {
      console.warn('Unknown message action received:', message.action);
      sendResponse({ success: false, error: `Unknown action: ${message.action}` });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    console.error('Error details:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    sendResponse({
      success: false,
      error: error.message || 'Unknown error in message handler',
      action: message.action
    });
  }

  return true; // Keep the message channel open for async response
});

// Remove canvas when page is unloaded
window.addEventListener('beforeunload', () => {
  if (canvas) {
    removeDrawingCanvas();
  }
});
