'use strict';

/**
 * Learning Dialog class for handling learning tools in a simple dialog
 */
class LearningDialog {
  /**
   * Initialize the Learning Dialog
   * @param {FeatureManager} featureManager - The feature manager instance
   */
  constructor(featureManager) {
    this.featureManager = featureManager;
    this.dialogElement = null;

    // Create the dialog element
    this.createDialog();
  }

  /**
   * Create the learning tools dialog
   */
  createDialog() {
    // Create the dialog element if it doesn't exist
    if (!this.dialogElement) {
      this.dialogElement = document.createElement('div');
      this.dialogElement.className = 'learning-tools-dialog';
      this.dialogElement.style.display = 'none';

      // Add the dialog content
      this.dialogElement.innerHTML = `
        <div class="learning-tools-dialog-content">
          <div class="learning-tools-dialog-header">
            <h3><i class="fas fa-graduation-cap"></i> Learning Tools</h3>
            <button class="learning-tools-close-btn"><i class="fas fa-times"></i></button>
          </div>
          <div class="learning-tools-dialog-body">
            <div class="learning-tools-section">
              <h4><i class="fas fa-clone"></i> Flashcards</h4>
              <div class="learning-tools-buttons">
                <button class="learning-tool-btn" data-action="create-flashcards">
                  <i class="fas fa-plus"></i> Create Flashcards
                </button>
                <button class="learning-tool-btn" data-action="review-flashcards">
                  <i class="fas fa-sync-alt"></i> Review Flashcards
                </button>
                <button class="learning-tool-btn" data-action="import-flashcards">
                  <i class="fas fa-file-import"></i> Import Content
                </button>
              </div>
            </div>

            <div class="learning-tools-section">
              <h4><i class="fas fa-question-circle"></i> Quizzes</h4>
              <div class="learning-tools-buttons">
                <button class="learning-tool-btn" data-action="create-quiz">
                  <i class="fas fa-plus"></i> Create Quiz
                </button>
                <button class="learning-tool-btn" data-action="take-quiz">
                  <i class="fas fa-play"></i> Take Quiz
                </button>
                <button class="learning-tool-btn" data-action="generate-questions">
                  <i class="fas fa-lightbulb"></i> Generate Questions
                </button>
              </div>
            </div>

            <div class="learning-tools-section">
              <h4><i class="fas fa-book"></i> Study Materials</h4>
              <div class="learning-tools-buttons">
                <button class="learning-tool-btn" data-action="create-study-guide">
                  <i class="fas fa-file-alt"></i> Create Study Guide
                </button>
                <button class="learning-tool-btn" data-action="summarize-content">
                  <i class="fas fa-compress-alt"></i> Summarize Content
                </button>
                <button class="learning-tool-btn" data-action="concept-map">
                  <i class="fas fa-project-diagram"></i> Create Concept Map
                </button>
              </div>
            </div>

            <div class="learning-tools-section">
              <h4><i class="fas fa-pencil-alt"></i> Note Taking</h4>
              <div class="learning-tools-buttons">
                <button class="learning-tool-btn" data-action="smart-notes">
                  <i class="fas fa-brain"></i> Smart Notes
                </button>
                <button class="learning-tool-btn" data-action="organize-notes">
                  <i class="fas fa-sort"></i> Organize Notes
                </button>
                <button class="learning-tool-btn" data-action="extract-key-points">
                  <i class="fas fa-list"></i> Extract Key Points
                </button>
              </div>
            </div>
          </div>
          <div class="learning-tools-dialog-footer">
            <div class="learning-status-message" id="learningStatusMessage"></div>
          </div>
        </div>
      `;

      // Add the dialog to the document
      document.body.appendChild(this.dialogElement);

      // Add event listeners
      this.addEventListeners();

      // Add styles
      this.addStyles();
    }
  }

  /**
   * Add event listeners to the dialog
   */
  addEventListeners() {
    // Close button
    const closeButton = this.dialogElement.querySelector('.learning-tools-close-btn');
    closeButton.addEventListener('click', () => {
      this.hideDialog();
    });

    // Tool buttons
    const toolButtons = this.dialogElement.querySelectorAll('.learning-tool-btn');
    toolButtons.forEach(button => {
      button.addEventListener('click', () => {
        const action = button.getAttribute('data-action');
        this.handleAction(action);
      });
    });

    // Close dialog when clicking outside
    this.dialogElement.addEventListener('click', (event) => {
      if (event.target === this.dialogElement) {
        this.hideDialog();
      }
    });

    // Close dialog on Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && this.dialogElement.style.display === 'flex') {
        this.hideDialog();
      }
    });
  }

  /**
   * Add styles for the dialog
   */
  addStyles() {
    // Create a style element if it doesn't exist
    let styleElement = document.getElementById('learning-dialog-styles');
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'learning-dialog-styles';
      styleElement.textContent = `
        .learning-tools-dialog {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999;
        }

        .learning-tools-dialog-content {
          background-color: #2D2D2D;
          border-radius: 8px;
          width: 90%;
          max-width: 600px;
          max-height: 90vh;
          overflow-y: auto;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          display: flex;
          flex-direction: column;
        }

        .learning-tools-dialog-header {
          padding: 15px;
          border-bottom: 1px solid #444;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .learning-tools-dialog-header h3 {
          margin: 0;
          color: #00b4d8;
          font-size: 18px;
          display: flex;
          align-items: center;
        }

        .learning-tools-dialog-header h3 i {
          margin-right: 8px;
        }

        .learning-tools-close-btn {
          background: none;
          border: none;
          color: #aaa;
          font-size: 16px;
          cursor: pointer;
          padding: 5px;
          border-radius: 4px;
        }

        .learning-tools-close-btn:hover {
          color: #fff;
          background-color: rgba(255, 255, 255, 0.1);
        }

        .learning-tools-dialog-body {
          padding: 15px;
          overflow-y: auto;
        }

        .learning-tools-section {
          margin-bottom: 20px;
        }

        .learning-tools-section h4 {
          margin: 0 0 10px 0;
          color: #eee;
          font-size: 16px;
          display: flex;
          align-items: center;
        }

        .learning-tools-section h4 i {
          margin-right: 8px;
          color: #00b4d8;
        }

        .learning-tools-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          gap: 10px;
        }

        .learning-tool-btn {
          background-color: #3a3a3a;
          border: none;
          color: #eee;
          padding: 10px;
          border-radius: 4px;
          cursor: pointer;
          text-align: left;
          display: flex;
          align-items: center;
          transition: background-color 0.2s;
        }

        .learning-tool-btn i {
          margin-right: 8px;
          color: #00b4d8;
          width: 16px;
          text-align: center;
        }

        .learning-tool-btn:hover {
          background-color: #4a4a4a;
        }

        .learning-tool-btn:active {
          background-color: #555;
        }

        .learning-tools-dialog-footer {
          padding: 10px 15px;
          border-top: 1px solid #444;
        }

        .learning-status-message {
          color: #00b4d8;
          font-size: 14px;
          text-align: center;
          padding: 5px;
          font-weight: bold;
          min-height: 20px;
        }
      `;
      document.head.appendChild(styleElement);
    }
  }

  /**
   * Show the learning tools dialog
   */
  showDialog() {
    // Show the dialog
    this.dialogElement.style.display = 'flex';

    // Update the status message
    const statusMessage = document.getElementById('learningStatusMessage');
    if (statusMessage) {
      statusMessage.textContent = 'Select a learning tool to enhance your study experience.';
      statusMessage.style.color = '#aaa';
    }
  }

  /**
   * Hide the learning tools dialog
   */
  hideDialog() {
    this.dialogElement.style.display = 'none';
  }

  /**
   * Handle a learning tool action
   * @param {string} action - The action to handle
   */
  async handleAction(action) {
    // Hide the dialog
    this.hideDialog();

    try {
      // Check if the feature manager has the required methods
      if (!this.featureManager) {
        throw new Error('Feature manager is not available');
      }

      // Show status message in the UI
      const statusElement = document.getElementById('learningStatusMessage');
      if (statusElement) {
        statusElement.textContent = `Processing ${action.replace(/-/g, ' ')}...`;
      }

      // Handle different actions with method existence checks
      switch (action) {
        case 'create-flashcards':
          if (typeof this.featureManager.createFlashcards !== 'function') {
            throw new Error('Create flashcards feature is not available');
          }
          await this.featureManager.createFlashcards();
          break;
        case 'review-flashcards':
          if (typeof this.featureManager.reviewFlashcards !== 'function') {
            throw new Error('Review flashcards feature is not available');
          }
          await this.featureManager.reviewFlashcards();
          break;
        case 'import-flashcards':
          if (typeof this.featureManager.importFlashcards !== 'function') {
            throw new Error('Import flashcards feature is not available');
          }
          await this.featureManager.importFlashcards();
          break;
        case 'create-quiz':
          if (typeof this.featureManager.createQuiz !== 'function') {
            throw new Error('Create quiz feature is not available');
          }
          await this.featureManager.createQuiz();
          break;
        case 'take-quiz':
          if (typeof this.featureManager.takeQuiz !== 'function') {
            throw new Error('Take quiz feature is not available');
          }
          await this.featureManager.takeQuiz();
          break;
        case 'generate-questions':
          if (typeof this.featureManager.generateQuestions !== 'function') {
            throw new Error('Generate questions feature is not available');
          }
          await this.featureManager.generateQuestions();
          break;
        case 'create-study-guide':
          if (typeof this.featureManager.createStudyGuide !== 'function') {
            throw new Error('Create study guide feature is not available');
          }
          await this.featureManager.createStudyGuide();
          break;
        case 'summarize-content':
          if (typeof this.featureManager.summarizeContent !== 'function') {
            throw new Error('Summarize content feature is not available');
          }
          await this.featureManager.summarizeContent();
          break;
        case 'concept-map':
          if (typeof this.featureManager.createConceptMap !== 'function') {
            throw new Error('Create concept map feature is not available');
          }
          await this.featureManager.createConceptMap();
          break;
        case 'smart-notes':
          if (typeof this.featureManager.createSmartNotes !== 'function') {
            throw new Error('Smart notes feature is not available');
          }
          await this.featureManager.createSmartNotes();
          break;
        case 'organize-notes':
          if (typeof this.featureManager.organizeNotes !== 'function') {
            throw new Error('Organize notes feature is not available');
          }
          await this.featureManager.organizeNotes();
          break;
        case 'extract-key-points':
          if (typeof this.featureManager.extractKeyPoints !== 'function') {
            throw new Error('Extract key points feature is not available');
          }
          await this.featureManager.extractKeyPoints();
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      // Clear status message
      if (statusElement) {
        statusElement.textContent = 'Action completed successfully';
        statusElement.style.color = '#00b4d8';
        setTimeout(() => {
          statusElement.textContent = '';
          statusElement.style.color = '';
        }, 3000);
      }
    } catch (error) {
      console.error('Error handling learning action:', error);

      // Show error in UI instead of alert
      const statusElement = document.getElementById('learningStatusMessage');
      if (statusElement) {
        statusElement.textContent = `Error: ${error.message}`;
        statusElement.style.color = 'red';
        setTimeout(() => {
          statusElement.textContent = '';
          statusElement.style.color = '';
        }, 5000);
      } else {
        // Fallback to alert if status element not found
        alert(`Error: ${error.message}`);
      }
    }
  }
}
