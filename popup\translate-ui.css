/* Enhanced Translation UI Styles */

/* Language Selection Modal */
.translate-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.translate-modal-content {
  background-color: var(--bg-dark);
  background-image: 
    radial-gradient(circle at 15% 85%, rgba(0, 216, 224, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(0, 166, 192, 0.08) 0%, transparent 50%),
    linear-gradient(to bottom, rgba(10, 15, 20, 0.95), rgba(5, 10, 15, 0.98));
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 420px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 166, 192, 0.2);
  border: 1px solid rgba(0, 216, 224, 0.15);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
  padding: 0;
}

.translate-header {
  background: linear-gradient(135deg, rgba(0, 166, 192, 0.9), rgba(0, 216, 224, 0.8));
  padding: 18px 20px;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
}

.translate-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  z-index: 0;
}

.translate-header h3 {
  margin: 0;
  color: var(--white);
  font-size: 1.3rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.translate-header h3 i {
  font-size: 1.4rem;
}

.translate-search-container {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 216, 224, 0.1);
  position: relative;
}

.translate-search {
  width: 100%;
  padding: 12px 15px 12px 40px;
  background-color: rgba(10, 15, 20, 0.6);
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: 12px;
  color: var(--white);
  font-size: 14px;
  transition: all 0.2s ease;
}

.translate-search:focus {
  outline: none;
  border-color: rgba(0, 216, 224, 0.4);
  box-shadow: 0 0 0 2px rgba(0, 216, 224, 0.1);
  background-color: rgba(10, 15, 20, 0.8);
}

.translate-search-icon {
  position: absolute;
  left: 35px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  pointer-events: none;
  transition: all 0.2s ease;
}

.translate-search:focus + .translate-search-icon {
  color: var(--primary-light);
}

.translate-categories {
  display: flex;
  padding: 0 20px;
  margin-top: 15px;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  gap: 10px;
}

.translate-categories::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.translate-category {
  padding: 8px 15px;
  background-color: rgba(10, 15, 20, 0.4);
  border: 1px solid rgba(0, 216, 224, 0.1);
  border-radius: 20px;
  color: var(--text-color);
  font-size: 13px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.translate-category:hover {
  background-color: rgba(0, 166, 192, 0.1);
  border-color: rgba(0, 216, 224, 0.2);
  transform: translateY(-1px);
}

.translate-category.active {
  background-color: rgba(0, 166, 192, 0.15);
  border-color: rgba(0, 216, 224, 0.3);
  color: var(--primary-light);
  font-weight: 500;
}

.translate-languages {
  padding: 15px 20px;
  max-height: 350px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) var(--bg-dark);
}

.translate-languages::-webkit-scrollbar {
  width: 6px;
}

.translate-languages::-webkit-scrollbar-track {
  background: rgba(10, 15, 20, 0.4);
  border-radius: 10px;
}

.translate-languages::-webkit-scrollbar-thumb {
  background-color: rgba(0, 166, 192, 0.3);
  border-radius: 10px;
}

.language-option {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 5px;
  border: 1px solid transparent;
}

.language-option:hover {
  background-color: rgba(0, 166, 192, 0.1);
  border-color: rgba(0, 216, 224, 0.2);
  transform: translateY(-1px);
}

.language-option.selected {
  background-color: rgba(0, 166, 192, 0.15);
  border-color: rgba(0, 216, 224, 0.3);
}

.language-flag {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 12px;
  background-size: cover;
  background-position: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.language-name {
  flex: 1;
  font-size: 14px;
  color: var(--text-color);
}

.language-option.selected .language-name {
  color: var(--primary-light);
  font-weight: 500;
}

.language-native {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 2px;
}

.no-results {
  padding: 20px;
  text-align: center;
  color: var(--text-light);
  font-style: italic;
}

.translate-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 216, 224, 0.1);
}

.translate-cancel {
  padding: 10px 15px;
  background-color: rgba(10, 15, 20, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.translate-cancel:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Translation Result Styles */
.translation-box {
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin: 14px 0;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  background-color: rgba(10, 15, 20, 0.6);
  position: relative;
  animation: fadeInUp 0.3s ease-out;
}

.translation-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  z-index: 1;
}

.translation-header {
  background-color: rgba(0, 166, 192, 0.1);
  padding: 12px 16px;
  font-weight: 500;
  border-bottom: 1px solid rgba(0, 216, 224, 0.15);
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.translation-header i {
  color: var(--primary-light);
  font-size: 1.1rem;
}

.translation-content {
  padding: 16px;
  line-height: 1.6;
  color: var(--text-color);
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.translation-actions {
  padding: 10px 16px;
  border-top: 1px solid rgba(0, 216, 224, 0.1);
  display: flex;
  justify-content: flex-end;
}

.translation-copy-btn {
  background-color: rgba(0, 166, 192, 0.1);
  color: var(--primary-light);
  border: 1px solid rgba(0, 216, 224, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.translation-copy-btn:hover {
  background-color: rgba(0, 166, 192, 0.2);
  transform: translateY(-1px);
}

.translation-copy-btn.success {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border-color: rgba(76, 175, 80, 0.3);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
