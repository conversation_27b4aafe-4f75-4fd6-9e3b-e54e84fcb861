'use strict';

/**
 * Learning Tools class for creating flashcards, quizzes, and study materials
 */
class LearningTools {
  /**
   * Initialize the Learning Tools
   * @param {APIManager} apiManager - The API manager instance
   * @param {UIManager} uiManager - The UI manager instance
   * @param {StorageManager} storageManager - The storage manager instance
   */
  constructor(apiManager, uiManager, storageManager) {
    this.apiManager = apiManager;
    this.uiManager = uiManager;
    this.storageManager = storageManager;

    // Storage keys for learning materials
    this.STORAGE_KEYS = {
      FLASHCARDS: 'learning_flashcards',
      QUIZZES: 'learning_quizzes',
      STUDY_GUIDES: 'learning_study_guides',
      NOTES: 'learning_notes'
    };

    // Initialize storage for learning materials
    this.initStorage();
  }

  /**
   * Initialize storage for learning materials
   */
  async initStorage() {
    try {
      // Check if learning materials storage exists
      const data = await chrome.storage.local.get([
        this.STORAGE_KEYS.FLASHCARDS,
        this.STORAGE_KEYS.QUIZZES,
        this.STORAGE_KEYS.STUDY_GUIDES,
        this.STORAGE_KEYS.NOTES
      ]);

      // Initialize flashcards if not present
      if (!data[this.STORAGE_KEYS.FLASHCARDS]) {
        await chrome.storage.local.set({ [this.STORAGE_KEYS.FLASHCARDS]: [] });
      }

      // Initialize quizzes if not present
      if (!data[this.STORAGE_KEYS.QUIZZES]) {
        await chrome.storage.local.set({ [this.STORAGE_KEYS.QUIZZES]: [] });
      }

      // Initialize study guides if not present
      if (!data[this.STORAGE_KEYS.STUDY_GUIDES]) {
        await chrome.storage.local.set({ [this.STORAGE_KEYS.STUDY_GUIDES]: [] });
      }

      // Initialize notes if not present
      if (!data[this.STORAGE_KEYS.NOTES]) {
        await chrome.storage.local.set({ [this.STORAGE_KEYS.NOTES]: [] });
      }
    } catch (error) {
      console.error('Error initializing learning tools storage:', error);
    }
  }

  /**
   * Create flashcards from content
   * @param {string} content - The content to create flashcards from
   * @param {string} title - The title for the flashcard set
   * @returns {Promise<Array>} - The created flashcards
   */
  async createFlashcardsFromContent(content, title) {
    try {
      // Generate flashcards using AI
      const prompt = `
        Please create a set of flashcards based on the following content:
        
        ${content}
        
        Format each flashcard as a JSON object with "front" and "back" properties.
        Create between 5-10 flashcards that cover the most important concepts.
        Return ONLY a valid JSON array of flashcard objects without any additional text.
        Example format:
        [
          {"front": "Question or term 1", "back": "Answer or definition 1"},
          {"front": "Question or term 2", "back": "Answer or definition 2"}
        ]
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at creating educational flashcards. 
        Create concise, clear flashcards that effectively test knowledge.
        Always return a valid JSON array of flashcard objects with "front" and "back" properties.
        Do not include any explanatory text outside the JSON array.`
      });

      // Parse the flashcards from the response
      let flashcards = [];
      try {
        // Extract JSON array from the response
        const jsonMatch = response.text.match(/\[\s*\{.*\}\s*\]/s);
        if (jsonMatch) {
          flashcards = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('Could not extract valid JSON from the response');
        }
      } catch (parseError) {
        console.error('Error parsing flashcards:', parseError);
        throw new Error('Failed to parse flashcards. Please try again.');
      }

      // Create a flashcard set
      const flashcardSet = {
        id: Date.now().toString(),
        title: title || 'Untitled Flashcard Set',
        createdAt: new Date().toISOString(),
        cards: flashcards
      };

      // Save the flashcard set
      await this.saveFlashcardSet(flashcardSet);

      return flashcardSet;
    } catch (error) {
      console.error('Error creating flashcards:', error);
      throw error;
    }
  }

  /**
   * Save a flashcard set
   * @param {Object} flashcardSet - The flashcard set to save
   */
  async saveFlashcardSet(flashcardSet) {
    try {
      // Get existing flashcard sets
      const { [this.STORAGE_KEYS.FLASHCARDS]: flashcardSets = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.FLASHCARDS);

      // Add the new flashcard set
      flashcardSets.push(flashcardSet);

      // Save the updated flashcard sets
      await chrome.storage.local.set({ [this.STORAGE_KEYS.FLASHCARDS]: flashcardSets });
    } catch (error) {
      console.error('Error saving flashcard set:', error);
      throw error;
    }
  }

  /**
   * Get all flashcard sets
   * @returns {Promise<Array>} - All flashcard sets
   */
  async getFlashcardSets() {
    try {
      const { [this.STORAGE_KEYS.FLASHCARDS]: flashcardSets = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.FLASHCARDS);
      return flashcardSets;
    } catch (error) {
      console.error('Error getting flashcard sets:', error);
      throw error;
    }
  }

  /**
   * Create a quiz from content
   * @param {string} content - The content to create a quiz from
   * @param {string} title - The title for the quiz
   * @returns {Promise<Object>} - The created quiz
   */
  async createQuizFromContent(content, title) {
    try {
      // Generate quiz using AI
      const prompt = `
        Please create a quiz based on the following content:
        
        ${content}
        
        Create a quiz with 5 multiple-choice questions.
        Format the quiz as a JSON object with the following structure:
        {
          "title": "Quiz title",
          "questions": [
            {
              "question": "Question text",
              "options": ["Option A", "Option B", "Option C", "Option D"],
              "correctAnswer": 0, // Index of the correct answer (0-based)
              "explanation": "Explanation of the correct answer"
            },
            // More questions...
          ]
        }
        
        Return ONLY a valid JSON object without any additional text.
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at creating educational quizzes.
        Create clear, challenging questions that effectively test understanding.
        Always return a valid JSON object with the quiz structure.
        Do not include any explanatory text outside the JSON object.`
      });

      // Parse the quiz from the response
      let quiz = null;
      try {
        // Extract JSON object from the response
        const jsonMatch = response.text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          quiz = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('Could not extract valid JSON from the response');
        }
      } catch (parseError) {
        console.error('Error parsing quiz:', parseError);
        throw new Error('Failed to parse quiz. Please try again.');
      }

      // Add metadata to the quiz
      quiz.id = Date.now().toString();
      quiz.title = title || quiz.title || 'Untitled Quiz';
      quiz.createdAt = new Date().toISOString();

      // Save the quiz
      await this.saveQuiz(quiz);

      return quiz;
    } catch (error) {
      console.error('Error creating quiz:', error);
      throw error;
    }
  }

  /**
   * Save a quiz
   * @param {Object} quiz - The quiz to save
   */
  async saveQuiz(quiz) {
    try {
      // Get existing quizzes
      const { [this.STORAGE_KEYS.QUIZZES]: quizzes = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.QUIZZES);

      // Add the new quiz
      quizzes.push(quiz);

      // Save the updated quizzes
      await chrome.storage.local.set({ [this.STORAGE_KEYS.QUIZZES]: quizzes });
    } catch (error) {
      console.error('Error saving quiz:', error);
      throw error;
    }
  }

  /**
   * Get all quizzes
   * @returns {Promise<Array>} - All quizzes
   */
  async getQuizzes() {
    try {
      const { [this.STORAGE_KEYS.QUIZZES]: quizzes = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.QUIZZES);
      return quizzes;
    } catch (error) {
      console.error('Error getting quizzes:', error);
      throw error;
    }
  }

  /**
   * Create a study guide from content
   * @param {string} content - The content to create a study guide from
   * @param {string} title - The title for the study guide
   * @returns {Promise<Object>} - The created study guide
   */
  async createStudyGuideFromContent(content, title) {
    try {
      // Generate study guide using AI
      const prompt = `
        Please create a comprehensive study guide based on the following content:
        
        ${content}
        
        The study guide should include:
        1. A summary of key concepts
        2. Important definitions
        3. Main topics organized in a logical structure
        4. Key points to remember
        5. Sample questions for self-testing
        
        Format the study guide in markdown with clear headings, bullet points, and sections.
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at creating educational study guides.
        Create clear, well-organized study materials that effectively summarize complex topics.
        Use markdown formatting to create a structured, readable document.`
      });

      // Create a study guide object
      const studyGuide = {
        id: Date.now().toString(),
        title: title || 'Untitled Study Guide',
        createdAt: new Date().toISOString(),
        content: response.text
      };

      // Save the study guide
      await this.saveStudyGuide(studyGuide);

      return studyGuide;
    } catch (error) {
      console.error('Error creating study guide:', error);
      throw error;
    }
  }

  /**
   * Save a study guide
   * @param {Object} studyGuide - The study guide to save
   */
  async saveStudyGuide(studyGuide) {
    try {
      // Get existing study guides
      const { [this.STORAGE_KEYS.STUDY_GUIDES]: studyGuides = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.STUDY_GUIDES);

      // Add the new study guide
      studyGuides.push(studyGuide);

      // Save the updated study guides
      await chrome.storage.local.set({ [this.STORAGE_KEYS.STUDY_GUIDES]: studyGuides });
    } catch (error) {
      console.error('Error saving study guide:', error);
      throw error;
    }
  }

  /**
   * Get all study guides
   * @returns {Promise<Array>} - All study guides
   */
  async getStudyGuides() {
    try {
      const { [this.STORAGE_KEYS.STUDY_GUIDES]: studyGuides = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.STUDY_GUIDES);
      return studyGuides;
    } catch (error) {
      console.error('Error getting study guides:', error);
      throw error;
    }
  }

  /**
   * Create smart notes from content
   * @param {string} content - The content to create notes from
   * @param {string} title - The title for the notes
   * @returns {Promise<Object>} - The created notes
   */
  async createSmartNotesFromContent(content, title) {
    try {
      // Generate smart notes using AI
      const prompt = `
        Please create organized, structured notes based on the following content:
        
        ${content}
        
        The notes should:
        1. Extract and organize the most important information
        2. Use a clear hierarchical structure with headings and subheadings
        3. Highlight key concepts, definitions, and relationships
        4. Include bullet points for easy scanning
        5. Add brief explanations where needed for clarity
        
        Format the notes in markdown with proper headings, lists, and emphasis.
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at creating educational notes.
        Create clear, well-structured notes that effectively organize information.
        Use markdown formatting to create a readable, scannable document.`
      });

      // Create a notes object
      const notes = {
        id: Date.now().toString(),
        title: title || 'Untitled Notes',
        createdAt: new Date().toISOString(),
        content: response.text
      };

      // Save the notes
      await this.saveNotes(notes);

      return notes;
    } catch (error) {
      console.error('Error creating smart notes:', error);
      throw error;
    }
  }

  /**
   * Save notes
   * @param {Object} notes - The notes to save
   */
  async saveNotes(notes) {
    try {
      // Get existing notes
      const { [this.STORAGE_KEYS.NOTES]: allNotes = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.NOTES);

      // Add the new notes
      allNotes.push(notes);

      // Save the updated notes
      await chrome.storage.local.set({ [this.STORAGE_KEYS.NOTES]: allNotes });
    } catch (error) {
      console.error('Error saving notes:', error);
      throw error;
    }
  }

  /**
   * Get all notes
   * @returns {Promise<Array>} - All notes
   */
  async getNotes() {
    try {
      const { [this.STORAGE_KEYS.NOTES]: notes = [] } = 
        await chrome.storage.local.get(this.STORAGE_KEYS.NOTES);
      return notes;
    } catch (error) {
      console.error('Error getting notes:', error);
      throw error;
    }
  }

  /**
   * Generate a concept map from content
   * @param {string} content - The content to create a concept map from
   * @param {string} title - The title for the concept map
   * @returns {Promise<string>} - The concept map in text format
   */
  async generateConceptMap(content, title) {
    try {
      // Generate concept map using AI
      const prompt = `
        Please create a concept map based on the following content:
        
        ${content}
        
        The concept map should:
        1. Identify the main concepts and ideas
        2. Show relationships between concepts
        3. Use a hierarchical structure from general to specific
        4. Include brief descriptions of relationships
        
        Since we can't create a visual diagram, please represent the concept map as a text-based structure using indentation, arrows (->), and relationship labels.
        For example:
        
        Main Concept
          |-- Related Concept 1 (relationship: "influences")
              |-- Subconcept A (relationship: "is a type of")
              |-- Subconcept B (relationship: "contains")
          |-- Related Concept 2 (relationship: "contrasts with")
              |-- Subconcept C (relationship: "example of")
        
        Make the concept map comprehensive but clear and readable.
      `;

      const response = await this.apiManager.sendRequest(prompt, {
        systemPrompt: `You are an expert at creating educational concept maps.
        Create clear, well-structured concept maps that effectively show relationships between ideas.
        Use text formatting to create a readable representation of a concept map.`
      });

      return response.text;
    } catch (error) {
      console.error('Error generating concept map:', error);
      throw error;
    }
  }
}
