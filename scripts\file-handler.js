'use strict';

/**
 * File Handler class for processing uploaded files
 */
class FileHandler {
  /**
   * Initialize the File Handler
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(uiManager) {
    this.uiManager = uiManager;
    this.currentFile = null;
    this.fileContent = null;
    this.fileType = null;
    this.fileName = null;
    this.fileSize = 0;
    this.fileSource = 'local'; // 'local' or 'drive'
    this.driveFileId = null;
    this.maxFileSizeMB = 10; // Maximum file size in MB
    this.supportedTypes = [
      'application/pdf',
      'text/plain',
      'text/csv',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
      'application/msword', // doc
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
      'application/vnd.ms-excel', // xls
      'application/json',
      'text/markdown',
      'text/html',
      'application/xml',
      'text/xml'
    ];

    // Google Drive integration has been removed
  }

  /**
   * Check if a file is valid
   * @param {File} file - The file to check
   * @returns {boolean} - Whether the file is valid
   */
  isValidFile(file) {
    // Check file size
    if (file.size > this.maxFileSizeMB * 1024 * 1024) {
      this.uiManager.showStatus(`File too large. Maximum size is ${this.maxFileSizeMB}MB.`, true);
      return false;
    }

    // Check file type
    if (!this.supportedTypes.includes(file.type) &&
        !file.name.endsWith('.pdf') &&
        !file.name.endsWith('.txt') &&
        !file.name.endsWith('.csv') &&
        !file.name.endsWith('.docx') &&
        !file.name.endsWith('.doc') &&
        !file.name.endsWith('.xlsx') &&
        !file.name.endsWith('.xls') &&
        !file.name.endsWith('.json') &&
        !file.name.endsWith('.md') &&
        !file.name.endsWith('.html') &&
        !file.name.endsWith('.xml')) {
      this.uiManager.showStatus('Unsupported file type. Please upload a PDF, text, Word, Excel, or other supported document.', true);
      return false;
    }

    return true;
  }

  /**
   * Process an uploaded file
   * @param {File} file - The file to process
   * @returns {Promise<boolean>} - Whether the file was processed successfully
   */
  async processFile(file) {
    try {
      if (!this.isValidFile(file)) {
        return false;
      }

      this.currentFile = file;
      this.fileName = file.name;
      this.fileSize = file.size;
      this.fileType = file.type || this.getFileTypeFromExtension(file.name);

      this.uiManager.showStatus(`Processing ${this.fileName}...`, false);

      // Process different file types
      if (this.fileType.includes('pdf') || this.fileName.endsWith('.pdf')) {
        await this.processPDF(file);
      } else if (this.fileType.includes('word') || this.fileName.endsWith('.docx') || this.fileName.endsWith('.doc')) {
        await this.processWord(file);
      } else if (this.fileType.includes('excel') || this.fileName.endsWith('.xlsx') || this.fileName.endsWith('.xls')) {
        await this.processExcel(file);
      } else {
        // For text files, CSV, JSON, etc.
        await this.processTextFile(file);
      }

      this.uiManager.showStatus(`File processed: ${this.fileName}`, false, 3000);

      // Show file info in chat
      this.displayFileInfo();

      return true;
    } catch (error) {
      console.error('Error processing file:', error);
      this.uiManager.showStatus(`Error processing file: ${error.message}`, true);
      return false;
    }
  }

  /**
   * Process a PDF file
   * @param {File} file - The PDF file to process
   * @returns {Promise<void>}
   */
  async processPDF(file) {
    try {
      // Use PDF.js to extract text from the PDF
      const arrayBuffer = await file.arrayBuffer();

      // We'll use the existing PDF processor if available
      if (typeof PDFProcessor !== 'undefined') {
        const pdfProcessor = new PDFProcessor();
        this.fileContent = await pdfProcessor.extractTextFromPDFBuffer(arrayBuffer);
      } else {
        // Fallback to a simpler method if PDFProcessor is not available
        this.fileContent = await this.extractTextFromPDF(arrayBuffer);
      }
    } catch (error) {
      console.error('Error processing PDF:', error);
      throw new Error('Could not process PDF file. ' + error.message);
    }
  }

  /**
   * Extract text from a PDF using PDF.js
   * @param {ArrayBuffer} arrayBuffer - The PDF file as an array buffer
   * @returns {Promise<string>} - The extracted text
   */
  async extractTextFromPDF(arrayBuffer) {
    try {
      // Load PDF.js from CDN if not already loaded
      if (typeof pdfjsLib === 'undefined') {
        await this.loadPDFJS();
      }

      const pdf = await pdfjsLib.getDocument({data: arrayBuffer}).promise;
      let text = '';

      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const content = await page.getTextContent();
        const strings = content.items.map(item => item.str);
        text += strings.join(' ') + '\n';
      }

      return text;
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      throw new Error('Could not extract text from PDF. ' + error.message);
    }
  }

  /**
   * Load PDF.js from CDN
   * @returns {Promise<void>}
   */
  loadPDFJS() {
    return new Promise((resolve, reject) => {
      // Due to Content Security Policy restrictions, we can't load external scripts
      // For now, we'll show an error message
      console.error('PDF processing is disabled due to Content Security Policy restrictions');
      this.uiManager.showStatus('PDF processing is currently disabled. Please use text files instead.', true);
      reject(new Error('PDF processing is disabled due to Content Security Policy restrictions'));

      // In a production extension, you would need to:
      // 1. Bundle PDF.js with your extension
      // 2. Or use a different PDF processing approach that doesn't require external scripts
    });
  }

  /**
   * Process a Word document
   * @param {File} file - The Word document to process
   * @returns {Promise<void>}
   */
  async processWord(file) {
    try {
      // For Word documents, we'll use a simple text extraction for now
      // In a production environment, you might want to use a more robust solution
      const text = await this.extractTextFromFile(file);
      this.fileContent = text || 'Could not extract text from Word document.';
    } catch (error) {
      console.error('Error processing Word document:', error);
      throw new Error('Could not process Word document. ' + error.message);
    }
  }

  /**
   * Process an Excel file
   * @param {File} file - The Excel file to process
   * @returns {Promise<void>}
   */
  async processExcel(file) {
    try {
      // For Excel files, we'll use a simple text extraction for now
      const text = await this.extractTextFromFile(file);
      this.fileContent = text || 'Could not extract text from Excel file.';
    } catch (error) {
      console.error('Error processing Excel file:', error);
      throw new Error('Could not process Excel file. ' + error.message);
    }
  }

  /**
   * Process a text file
   * @param {File} file - The text file to process
   * @returns {Promise<void>}
   */
  async processTextFile(file) {
    try {
      const text = await this.extractTextFromFile(file);
      this.fileContent = text;
    } catch (error) {
      console.error('Error processing text file:', error);
      throw new Error('Could not process text file. ' + error.message);
    }
  }

  /**
   * Extract text from a file
   * @param {File} file - The file to extract text from
   * @returns {Promise<string>} - The extracted text
   */
  extractTextFromFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        resolve(event.target.result);
      };

      reader.onerror = (error) => {
        reject(error);
      };

      reader.readAsText(file);
    });
  }

  /**
   * Get file type from extension
   * @param {string} fileName - The file name
   * @returns {string} - The file type
   */
  getFileTypeFromExtension(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();

    const typeMap = {
      'pdf': 'application/pdf',
      'txt': 'text/plain',
      'csv': 'text/csv',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'doc': 'application/msword',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'xls': 'application/vnd.ms-excel',
      'json': 'application/json',
      'md': 'text/markdown',
      'html': 'text/html',
      'xml': 'text/xml'
    };

    return typeMap[extension] || 'application/octet-stream';
  }

  /**
   * Process a file from Google Drive - REMOVED
   * This method has been removed as Google Drive integration is no longer supported
   */





  /**
   * Process a PDF blob
   * @param {Blob} blob - The PDF blob
   * @returns {Promise<void>}
   */
  async processPDFBlob(blob) {
    try {
      const arrayBuffer = await blob.arrayBuffer();

      // Use PDF.js to extract text from the PDF
      if (typeof PDFProcessor !== 'undefined') {
        const pdfProcessor = new PDFProcessor();
        this.fileContent = await pdfProcessor.extractTextFromPDFBuffer(arrayBuffer);
      } else {
        // Fallback to a simpler method if PDFProcessor is not available
        this.fileContent = await this.extractTextFromPDF(arrayBuffer);
      }
    } catch (error) {
      console.error('Error processing PDF blob:', error);
      throw new Error('Could not process PDF file. ' + error.message);
    }
  }

  /**
   * Process a Word document blob
   * @param {Blob} blob - The Word document blob
   * @returns {Promise<void>}
   */
  async processWordBlob(blob) {
    try {
      // For Word documents, we'll use a simple text extraction for now
      const text = await this.extractTextFromBlob(blob);
      this.fileContent = text || 'Could not extract text from Word document.';
    } catch (error) {
      console.error('Error processing Word blob:', error);
      throw new Error('Could not process Word document. ' + error.message);
    }
  }

  /**
   * Process an Excel file blob
   * @param {Blob} blob - The Excel file blob
   * @returns {Promise<void>}
   */
  async processExcelBlob(blob) {
    try {
      // For Excel files, we'll use a simple text extraction for now
      const text = await this.extractTextFromBlob(blob);
      this.fileContent = text || 'Could not extract text from Excel file.';
    } catch (error) {
      console.error('Error processing Excel blob:', error);
      throw new Error('Could not process Excel file. ' + error.message);
    }
  }

  /**
   * Process a text file blob
   * @param {Blob} blob - The text file blob
   * @returns {Promise<void>}
   */
  async processTextBlob(blob) {
    try {
      const text = await this.extractTextFromBlob(blob);
      this.fileContent = text;
    } catch (error) {
      console.error('Error processing text blob:', error);
      throw new Error('Could not process text file. ' + error.message);
    }
  }

  /**
   * Extract text from a blob
   * @param {Blob} blob - The blob to extract text from
   * @returns {Promise<string>} - The extracted text
   */
  extractTextFromBlob(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        resolve(event.target.result);
      };

      reader.onerror = (error) => {
        reject(error);
      };

      reader.readAsText(blob);
    });
  }

  /**
   * Display file information in the chat
   */
  displayFileInfo() {
    if (!this.fileName) return;

    const fileSizeKB = Math.round(this.fileSize / 1024);
    const fileSizeMB = (this.fileSize / (1024 * 1024)).toFixed(2);
    const sizeText = fileSizeKB < 1024 ? `${fileSizeKB} KB` : `${fileSizeMB} MB`;

    const sourceIcon = this.fileSource === 'drive' ? '☁️' : '📎';
    const sourceText = this.fileSource === 'drive' ? 'Google Drive' : 'Local';
    const fileInfo = `${sourceIcon} **File attached (${sourceText}):** ${this.fileName} (${sizeText})`;

    this.uiManager.addMessageToChat(fileInfo, 'user', 'file-info');
  }

  /**
   * Get the current file content
   * @returns {Object} - The file information
   */
  getFileInfo() {
    if (!this.fileName || !this.fileContent) return null;

    return {
      content: this.fileContent,
      name: this.fileName,
      type: this.fileType,
      size: this.fileSize,
      source: this.fileSource,
      driveFileId: this.driveFileId
    };
  }

  /**
   * Clear the current file
   */
  clearFile() {
    this.currentFile = null;
    this.fileContent = null;
    this.fileType = null;
    this.fileName = null;
    this.fileSize = 0;
    this.fileSource = 'local';
    this.driveFileId = null;
  }
}
