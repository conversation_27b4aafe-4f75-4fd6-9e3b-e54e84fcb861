'use strict';

/**
 * Webpage Access class for fetching and analyzing external webpages
 */
class WebpageAccess {
  /**
   * Initialize the Webpage Access
   * @param {FeatureManager} featureManager - The feature manager instance
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(featureManager, uiManager) {
    this.featureManager = featureManager;
    this.uiManager = uiManager;
    this.dialogElement = null;

    // Create the dialog element
    this.createDialog();

    // Add event listeners
    this.addEventListeners();
  }

  /**
   * Create the webpage access dialog
   */
  createDialog() {
    // Create dialog element if it doesn't exist
    if (!this.dialogElement) {
      const dialog = document.createElement('div');
      dialog.className = 'webpage-access-dialog';
      dialog.innerHTML = `
        <div class="webpage-access-content">
          <div class="webpage-access-header">
            <h3><i class="fas fa-globe"></i> Access Any Webpage</h3>
            <button class="webpage-access-close-btn"><i class="fas fa-times"></i></button>
          </div>
          <div class="webpage-access-body">
            <p>Enter a URL to access and analyze any webpage:</p>
            <div class="webpage-access-input-container">
              <input type="url" id="webpageUrlInput" placeholder="https://example.com" class="webpage-url-input">
              <button id="fetchWebpageBtn" class="fetch-webpage-btn">Fetch</button>
            </div>
            <div class="webpage-access-options">
              <label class="option-label">
                <input type="checkbox" id="includeImagesCheckbox"> Include image descriptions
              </label>
              <label class="option-label">
                <input type="checkbox" id="deepAnalysisCheckbox"> Perform deep analysis
              </label>
              <label class="option-label">
                <input type="checkbox" id="enableSearchCheckbox" checked> Enable content search
              </label>
            </div>
            <div id="webpageAccessStatus" class="webpage-access-status"></div>
          </div>
        </div>
      `;

      // Add to document
      document.body.appendChild(dialog);
      this.dialogElement = dialog;
    }
  }

  /**
   * Add event listeners to the dialog
   */
  addEventListeners() {
    // Close button
    const closeButton = this.dialogElement.querySelector('.webpage-access-close-btn');
    closeButton.addEventListener('click', () => {
      this.hideDialog();
    });

    // Fetch button
    const fetchButton = this.dialogElement.querySelector('#fetchWebpageBtn');
    fetchButton.addEventListener('click', () => {
      this.fetchWebpage();
    });

    // URL input enter key
    const urlInput = this.dialogElement.querySelector('#webpageUrlInput');
    urlInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter') {
        this.fetchWebpage();
      }
    });

    // Close dialog when clicking outside
    this.dialogElement.addEventListener('click', (event) => {
      if (event.target === this.dialogElement) {
        this.hideDialog();
      }
    });
  }

  /**
   * Show the webpage access dialog
   */
  showDialog() {
    if (this.dialogElement) {
      this.dialogElement.style.display = 'flex';

      // Focus the URL input
      setTimeout(() => {
        const urlInput = this.dialogElement.querySelector('#webpageUrlInput');
        urlInput.focus();
      }, 100);
    }
  }

  /**
   * Hide the webpage access dialog
   */
  hideDialog() {
    if (this.dialogElement) {
      this.dialogElement.style.display = 'none';

      // Clear the URL input
      const urlInput = this.dialogElement.querySelector('#webpageUrlInput');
      urlInput.value = '';

      // Clear the status
      this.setStatus('');
    }
  }

  /**
   * Set the status message
   * @param {string} message - The status message
   * @param {boolean} isError - Whether the message is an error
   */
  setStatus(message, isError = false) {
    const statusElement = this.dialogElement.querySelector('#webpageAccessStatus');
    if (statusElement) {
      statusElement.textContent = message;
      statusElement.className = 'webpage-access-status';
      if (isError) {
        statusElement.classList.add('error');
      }
    }
  }

  /**
   * Validate a URL
   * @param {string} url - The URL to validate
   * @returns {boolean} - Whether the URL is valid
   */
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Fetch and analyze a webpage
   */
  async fetchWebpage() {
    try {
      // Get the URL
      const urlInput = this.dialogElement.querySelector('#webpageUrlInput');
      const url = urlInput.value.trim();

      // Validate the URL
      if (!url) {
        this.setStatus('Please enter a URL', true);
        return;
      }

      if (!this.isValidUrl(url)) {
        this.setStatus('Please enter a valid URL (e.g., https://example.com)', true);
        return;
      }

      // Get options
      const includeImages = this.dialogElement.querySelector('#includeImagesCheckbox').checked;
      const deepAnalysis = this.dialogElement.querySelector('#deepAnalysisCheckbox').checked;
      const enableSearch = this.dialogElement.querySelector('#enableSearchCheckbox').checked;

      // Show loading status
      this.setStatus('Fetching webpage...');

      // Fetch the webpage content
      const response = await this.fetchWebpageContent(url, includeImages, deepAnalysis);

      if (response.success) {
        // Hide the dialog
        this.hideDialog();

        // Show success message in chat
        this.uiManager.addMessageToChat(
          `Successfully accessed webpage: ${url}\n\nAnalyzing content...`,
          'ai'
        );

        // Process the webpage content
        await this.processWebpageContent(url, response.content, includeImages, deepAnalysis, enableSearch);
      } else {
        this.setStatus(`Error: ${response.error}`, true);
      }
    } catch (error) {
      console.error('Error fetching webpage:', error);
      this.setStatus(`Error: ${error.message}`, true);
    }
  }

  /**
   * Fetch webpage content
   * @param {string} url - The URL to fetch
   * @param {boolean} includeImages - Whether to include image descriptions
   * @param {boolean} deepAnalysis - Whether to perform deep analysis
   * @returns {Promise<Object>} - The response object
   */
  async fetchWebpageContent(url, includeImages, deepAnalysis) {
    try {
      // Use the background script to fetch the webpage
      const response = await chrome.runtime.sendMessage({
        action: 'fetchWebpage',
        url: url,
        includeImages: includeImages,
        deepAnalysis: deepAnalysis
      });

      return response;
    } catch (error) {
      console.error('Error sending message to background script:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process webpage content
   * @param {string} url - The webpage URL
   * @param {string} content - The webpage content
   * @param {boolean} includeImages - Whether to include image descriptions
   * @param {boolean} deepAnalysis - Whether to perform deep analysis
   * @param {boolean} enableSearch - Whether to enable content search
   */
  async processWebpageContent(url, content, includeImages, deepAnalysis, enableSearch = true) {
    try {
      // Create a page content object
      const pageContent = {
        url: url,
        title: this.extractTitle(content),
        content: content,
        includeImages: includeImages,
        deepAnalysis: deepAnalysis,
        enableSearch: enableSearch,
        timestamp: new Date().toISOString()
      };

      // Determine if this is a social media site
      const isSocialMedia = url.includes('instagram') || url.includes('facebook') ||
                           url.includes('twitter') || url.includes('x.com') ||
                           url.includes('linkedin');

      // Store the content in the API manager
      this.featureManager.apiManager.setScannedTabContent(pageContent);

      // Show that we're analyzing an external page
      this.uiManager.showStatus(`Analyzing external page: ${pageContent.title}`, false, 3000);

      // Add a message to the chat
      let searchInstructions = '';
      if (enableSearch) {
        searchInstructions = `\n\n**Search within this webpage:**\n` +
          `- You can search for specific information by asking "search for [term]"\n` +
          `- For example: "search for pricing information" or "find mentions of AI"\n` +
          `- I'll highlight and extract the relevant sections for you`;
      }

      // Add social media specific instructions if applicable
      let socialMediaInstructions = '';
      if (isSocialMedia) {
        socialMediaInstructions = `\n\n**Social Media Content:**\n` +
          `- I've extracted available public content from this ${this.getSocialMediaPlatformName(url)} page\n` +
          `- Note that I can only access public content that doesn't require authentication\n` +
          `- For more complete results, you may want to log in directly on the site`;
      }

      // Add site-specific instructions based on the URL
      let siteSpecificInstructions = this.getSiteSpecificInstructions(url);

      this.uiManager.addMessageToChat(
        `# External Webpage Analysis\n\n` +
        `I've analyzed the content from: [${pageContent.title}](${url})\n\n` +
        `You can now ask me questions about this webpage. I'll analyze the content and provide insights based on what I find.\n\n` +
        `Some things you might ask:\n` +
        `- Summarize the main points of this webpage\n` +
        `- What are the key topics discussed?\n` +
        `- Extract important information from this page\n` +
        `- Compare this with the current tab's content` +
        searchInstructions +
        socialMediaInstructions +
        siteSpecificInstructions + `\n\n` +
        `Type "clear scanned content" when you want to return to the current tab.`,
        'ai'
      );
    } catch (error) {
      console.error('Error processing webpage content:', error);
      this.uiManager.addMessageToChat(
        `Error processing webpage content: ${error.message}`,
        'ai',
        'error-message'
      );
    }
  }

  /**
   * Get the name of the social media platform from the URL
   * @param {string} url - The URL
   * @returns {string} - The platform name
   */
  getSocialMediaPlatformName(url) {
    if (url.includes('instagram')) return 'Instagram';
    if (url.includes('facebook')) return 'Facebook';
    if (url.includes('twitter') || url.includes('x.com')) return 'Twitter/X';
    if (url.includes('linkedin')) return 'LinkedIn';
    return 'social media';
  }

  /**
   * Get site-specific instructions based on the URL
   * @param {string} url - The URL
   * @returns {string} - Site-specific instructions
   */
  getSiteSpecificInstructions(url) {
    // E-commerce sites
    if (url.includes('amazon') || url.includes('ebay') || url.includes('walmart') ||
        url.includes('etsy') || url.includes('shopify')) {
      return `\n\n**Shopping Site Features:**\n` +
        `- I can help analyze product information, reviews, and pricing\n` +
        `- Ask me to compare products or summarize reviews\n` +
        `- I can extract key product specifications`;
    }

    // News sites
    if (url.includes('cnn') || url.includes('bbc') || url.includes('nytimes') ||
        url.includes('washingtonpost') || url.includes('reuters') || url.includes('news')) {
      return `\n\n**News Article Analysis:**\n` +
        `- I can summarize the key points of this news article\n` +
        `- Ask me to identify the main people, organizations, or events mentioned\n` +
        `- I can analyze the tone and perspective of the article`;
    }

    // Technical/documentation sites
    if (url.includes('github') || url.includes('stackoverflow') || url.includes('docs.') ||
        url.includes('documentation') || url.includes('developer')) {
      return `\n\n**Technical Content Analysis:**\n` +
        `- I can explain technical concepts from this page\n` +
        `- Ask me to extract code examples or implementation details\n` +
        `- I can summarize technical documentation`;
    }

    // Academic/research sites
    if (url.includes('scholar') || url.includes('research') || url.includes('edu') ||
        url.includes('academic') || url.includes('study')) {
      return `\n\n**Research Content Analysis:**\n` +
        `- I can summarize research findings and methodologies\n` +
        `- Ask me to extract key statistics or data points\n` +
        `- I can help interpret academic content`;
    }

    // Default - no specific instructions
    return '';
  }

  /**
   * Extract title from HTML content
   * @param {string} html - The HTML content
   * @returns {string} - The extracted title
   */
  extractTitle(html) {
    try {
      const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      return titleMatch ? titleMatch[1].trim() : 'External Webpage';
    } catch (error) {
      console.error('Error extracting title:', error);
      return 'External Webpage';
    }
  }
}
