'use strict';

// Simple test to check if APIManager is defined
console.log('Testing APIManager definition');
console.log('APIManager type:', typeof APIManager);

// Try to create an instance
try {
  const dummyStorage = { 
    init: () => Promise.resolve(),
    getAPIKeys: () => Promise.resolve({}),
    getSettings: () => Promise.resolve({})
  };
  
  const apiManager = new APIManager(dummyStorage);
  console.log('Successfully created APIManager instance');
} catch (error) {
  console.error('Failed to create APIManager instance:', error);
}
