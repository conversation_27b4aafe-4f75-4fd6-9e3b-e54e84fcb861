'use strict';

/**
 * OpenRouter models list
 * This file contains the list of available models from OpenRouter
 */
const OpenRouterModels = {
  // Free models (no API key required)
  free: [
    {
      id: 'meta-llama/llama-4-maverick:free',
      name: 'Llama 4 Maverick (Free)',
      description: 'Meta\'s Llama 4 Maverick model - free tier',
      contextLength: 256000
    },
    {
      id: 'meta-llama/llama-4-scout:free',
      name: 'Llama 4 Scout (Free)',
      description: 'Meta\'s Llama 4 Scout model - free tier',
      contextLength: 512000
    },
    {
      id: 'nvidia/llama-3.1-nemotron-nano-8b-v1:free',
      name: 'Llama 3.1 Nemotron Nano 8B (Free)',
      description: 'NVIDIA\'s Llama 3.1 Nemotron Nano 8B model - free tier',
      contextLength: 131072
    },
    {
      id: 'nvidia/llama-3.3-nemotron-super-49b-v1:free',
      name: 'Llama 3.3 Nemotron Super 49B (Free)',
      description: 'NVIDIA\'s Llama 3.3 Nemotron Super 49B model - free tier',
      contextLength: 131072
    },
    {
      id: 'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
      name: 'Llama 3.1 Nemotron Ultra 253B (Free)',
      description: 'NVIDIA\'s Llama 3.1 Nemotron Ultra 253B model - free tier',
      contextLength: 131072
    },
    {
      id: 'moonshotai/kimi-vl-a3b-thinking:free',
      name: 'Kimi VL A3B Thinking (Free)',
      description: 'Moonshot AI\'s Kimi VL A3B Thinking model - free tier',
      contextLength: 131072
    },
    {
      id: 'agentica-org/deepcoder-14b-preview:free',
      name: 'Deepcoder 14B (Free)',
      description: 'Agentica\'s Deepcoder 14B Preview model - free tier',
      contextLength: 96000
    },
    {
      id: 'arliai/qwq-32b-arliai-rpr-v1:free',
      name: 'QwQ 32B RpR v1 (Free)',
      description: 'ArliAI\'s QwQ 32B RpR v1 model - free tier',
      contextLength: 32768
    },
    {
      id: 'thudm/glm-4-32b:free',
      name: 'GLM 4 32B (Free)',
      description: 'THUDM\'s GLM 4 32B model - free tier',
      contextLength: 32768
    },
    {
      id: 'thudm/glm-z1-32b:free',
      name: 'GLM Z1 32B (Free)',
      description: 'THUDM\'s GLM Z1 32B model - free tier',
      contextLength: 32768
    },
    {
      id: 'shisa-ai/shisa-v2-llama3.3-70b:free',
      name: 'Shisa V2 Llama 3.3 70B (Free)',
      description: 'Shisa AI\'s Shisa V2 Llama 3.3 70B model - free tier',
      contextLength: 32768
    },

  ],

  // Paid models (API key required)
  paid: [
    // OpenAI models
    {
      id: 'openai/gpt-4o-mini',
      name: 'GPT-4o Mini (OpenAI)',
      description: 'OpenAI\'s GPT-4o Mini model - efficient and cost-effective',
      contextLength: 128000
    },
    {
      id: 'openai/gpt-4-turbo',
      name: 'GPT-4 Turbo (OpenAI)',
      description: 'OpenAI\'s GPT-4 Turbo model - powerful with good performance',
      contextLength: 128000
    },
    {
      id: 'openai/gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo (OpenAI)',
      description: 'OpenAI\'s GPT-3.5 Turbo model - fast and cost-effective',
      contextLength: 16385
    },
    {
      id: 'openai/dall-e-3',
      name: 'DALL-E 3 (OpenAI)',
      description: 'OpenAI\'s DALL-E 3 image generation model',
      contextLength: 4096
    },

    // Google models
    {
      id: 'google/gemini-1.5-pro',
      name: 'Gemini 1.5 Pro (Google)',
      description: 'Google\'s Gemini 1.5 Pro model - powerful multimodal capabilities',
      contextLength: 1000000
    },
    {
      id: 'google/gemini-1.5-flash',
      name: 'Gemini 1.5 Flash (Google)',
      description: 'Google\'s Gemini 1.5 Flash model - fast and efficient',
      contextLength: 1000000
    },

    // Mistral models
    {
      id: 'mistralai/mistral-medium-3',
      name: 'Mistral Medium 3 (Mistral)',
      description: 'Mistral\'s Medium 3 model - balanced performance',
      contextLength: 32768
    },
    {
      id: 'mistralai/mistral-large-2',
      name: 'Mistral Large 2 (Mistral)',
      description: 'Mistral\'s Large 2 model - previous generation',
      contextLength: 32768
    },

    // Meta models
    {
      id: 'meta-llama/llama-4-1-8b-instruct',
      name: 'Llama 4.1 8B (Meta)',
      description: 'Meta\'s Llama 4.1 8B model - latest generation',
      contextLength: 128000
    },
    {
      id: 'meta-llama/llama-4-1-70b-instruct',
      name: 'Llama 4.1 70B (Meta)',
      description: 'Meta\'s Llama 4.1 70B model - latest generation, high performance',
      contextLength: 128000
    },
    {
      id: 'meta-llama/llama-3-70b-instruct',
      name: 'Llama 3 70B (Meta)',
      description: 'Meta\'s Llama 3 70B model - powerful open model',
      contextLength: 8192
    },
    {
      id: 'meta-llama/llama-3-8b-instruct',
      name: 'Llama 3 8B (Meta)',
      description: 'Meta\'s Llama 3 8B model - efficient open model',
      contextLength: 8192
    },

    // DeepSeek models
    {
      id: 'deepseek/deepseek-v3-pro',
      name: 'DeepSeek V3 Pro (DeepSeek)',
      description: 'DeepSeek\'s V3 Pro model - high performance',
      contextLength: 32768
    },
    {
      id: 'deepseek/deepseek-coder-v2',
      name: 'DeepSeek Coder V2 (DeepSeek)',
      description: 'DeepSeek\'s specialized coding model',
      contextLength: 32768
    },

    // Cohere models
    {
      id: 'cohere/command-r-plus',
      name: 'Command R+ (Cohere)',
      description: 'Cohere\'s Command R+ model - high performance',
      contextLength: 128000
    },
    {
      id: 'cohere/command-r',
      name: 'Command R (Cohere)',
      description: 'Cohere\'s Command R model - balanced performance',
      contextLength: 128000
    },

    // Stability AI models
    {
      id: 'stability/stable-diffusion-3',
      name: 'Stable Diffusion 3 (Stability)',
      description: 'Stability AI\'s image generation model',
      contextLength: 4096
    },

    // Perplexity models
    {
      id: 'perplexity/pplx-70b-online',
      name: 'PPLX 70B Online (Perplexity)',
      description: 'Perplexity\'s 70B model with internet access',
      contextLength: 12000
    },
    {
      id: 'perplexity/pplx-7b-online',
      name: 'PPLX 7B Online (Perplexity)',
      description: 'Perplexity\'s 7B model with internet access',
      contextLength: 12000
    },

    // Qwen models
    {
      id: 'qwen/qwen-2-72b',
      name: 'Qwen 2 72B (Qwen)',
      description: 'Qwen\'s 2 72B model - high performance',
      contextLength: 32768
    },
    {
      id: 'qwen/qwen-2-7b',
      name: 'Qwen 2 7B (Qwen)',
      description: 'Qwen\'s 2 7B model - efficient performance',
      contextLength: 32768
    }
  ],

  // Get all models combined
  getAll() {
    return [...this.free, ...this.paid];
  },

  // Get model by ID
  getById(id) {
    return this.getAll().find(model => model.id === id);
  },

  // Get default model
  getDefault() {
    // Return GPT-4o Mini as the default model
    return this.paid.find(model => model.id === 'openai/gpt-4o-mini') || this.paid[0];
  }
};

// Export the models
if (typeof module !== 'undefined') {
  module.exports = OpenRouterModels;
}
