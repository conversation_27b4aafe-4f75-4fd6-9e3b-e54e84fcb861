{"name": "browzyai-dashboard", "version": "1.0.1", "description": "BrowzyAI Dashboard", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 5173", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port 5173", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix"}, "keywords": ["dashboard", "react", "vite", "ai"], "author": "BrowzyAI Team", "license": "ISC", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "styled-components": "^6.1.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "recharts": "^2.8.0", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0"}}