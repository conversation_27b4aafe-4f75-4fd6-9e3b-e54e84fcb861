'use strict';

/**
 * Google Drive Integration class for handling file selection from Google Drive
 */
class DriveIntegration {
  /**
   * Initialize the Drive Integration
   * @param {UIManager} uiManager - The UI manager instance
   */
  constructor(uiManager) {
    this.uiManager = uiManager;
    this.apiKey = null;
    this.clientId = null;
    this.accessToken = null;
    this.pickerApiLoaded = false;
    this.oauthToken = null;

    // Google Drive API scopes
    this.scope = 'https://www.googleapis.com/auth/drive.readonly';

    // Load the necessary Google APIs
    this.loadGoogleApis();
  }

  /**
   * Load Google APIs
   */
  loadGoogleApis() {
    // Instead of loading external scripts directly, we'll use a different approach
    // We'll need to bundle these APIs with the extension or use a different method
    // For now, we'll show a message to the user
    console.log('Google Drive integration is disabled due to Content Security Policy restrictions');
    this.uiManager.showStatus('Google Drive integration is currently disabled. Please use local file upload instead.', true);

    // In a production extension, you would need to:
    // 1. Bundle the necessary Google API libraries with your extension
    // 2. Or use the chrome.identity API for Google authentication
    // 3. Or use a background script with fetch API to make requests to Google Drive API
  }

  /**
   * Handle API load
   */
  onApiLoad() {
    gapi.load('picker', () => {
      this.pickerApiLoaded = true;
    });
  }

  /**
   * Set API credentials
   * @param {string} apiKey - Google API key
   * @param {string} clientId - Google OAuth client ID
   */
  setCredentials(apiKey, clientId) {
    this.apiKey = apiKey || '';
    this.clientId = clientId || '';

    if (!this.apiKey || !this.clientId) {
      console.warn('Google Drive integration requires valid API credentials');
    }
  }

  /**
   * Create and render a Google Drive file picker
   * @returns {Promise<Object>} - Selected file information
   */
  async createPicker() {
    // Check if we have the necessary credentials
    if (!this.apiKey || !this.clientId) {
      // Use default credentials if available
      try {
        const response = await fetch('../config/google-credentials.json');
        if (response.ok) {
          const credentials = await response.json();
          this.apiKey = credentials.apiKey;
          this.clientId = credentials.clientId;
        } else {
          throw new Error('No Google API credentials available');
        }
      } catch (error) {
        // If we can't load credentials, show an error
        this.uiManager.showStatus('Google Drive integration requires API credentials. Please check the extension settings.', true);
        return null;
      }
    }

    // Check if the APIs are loaded
    if (!this.pickerApiLoaded || !window.google || !window.google.accounts || !window.google.accounts.oauth2) {
      this.uiManager.showStatus('Google Drive API is still loading. Please try again in a moment.', true);
      return null;
    }

    // Get OAuth token
    try {
      const tokenClient = google.accounts.oauth2.initTokenClient({
        client_id: this.clientId,
        scope: this.scope,
        callback: (tokenResponse) => {
          if (tokenResponse && tokenResponse.access_token) {
            this.oauthToken = tokenResponse.access_token;
            this.showPicker();
          }
        },
      });

      // Request the token
      tokenClient.requestAccessToken();
    } catch (error) {
      console.error('Error getting OAuth token:', error);
      this.uiManager.showStatus('Error authenticating with Google Drive. Please try again.', true);
      return null;
    }

    // Return a promise that will be resolved when a file is selected
    return new Promise((resolve) => {
      this.pickerCallback = (data) => {
        if (data.action === 'picked') {
          const file = data.docs[0];
          resolve({
            id: file.id,
            name: file.name,
            mimeType: file.mimeType,
            url: file.url,
            downloadUrl: `https://www.googleapis.com/drive/v3/files/${file.id}?alt=media`,
            iconUrl: file.iconUrl,
            description: file.description || '',
            sizeBytes: file.sizeBytes || 0
          });
        } else {
          resolve(null);
        }
      };
    });
  }

  /**
   * Show the Google Drive picker
   */
  showPicker() {
    if (!this.oauthToken) {
      this.uiManager.showStatus('Authentication required for Google Drive access.', true);
      return;
    }

    // Create the picker
    const view = new google.picker.View(google.picker.ViewId.DOCS);
    view.setMimeTypes('application/pdf,text/plain,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv,application/json,text/markdown,text/html,application/xml,text/xml');

    const picker = new google.picker.PickerBuilder()
      .enableFeature(google.picker.Feature.NAV_HIDDEN)
      .setAppId(this.clientId)
      .setOAuthToken(this.oauthToken)
      .addView(view)
      .setDeveloperKey(this.apiKey)
      .setCallback(this.pickerCallback)
      .setTitle('Select a file from Google Drive')
      .build();

    picker.setVisible(true);
  }

  /**
   * Download a file from Google Drive
   * @param {string} fileId - Google Drive file ID
   * @returns {Promise<Blob>} - File blob
   */
  async downloadFile(fileId) {
    if (!this.oauthToken) {
      throw new Error('Authentication required for Google Drive access.');
    }

    try {
      const response = await fetch(`https://www.googleapis.com/drive/v3/files/${fileId}?alt=media`, {
        headers: {
          'Authorization': `Bearer ${this.oauthToken}`
        }
      });

      if (!response.ok) {
        throw new Error(`Error downloading file: ${response.statusText}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Error downloading file from Google Drive:', error);
      throw error;
    }
  }

  /**
   * Get file metadata from Google Drive
   * @param {string} fileId - Google Drive file ID
   * @returns {Promise<Object>} - File metadata
   */
  async getFileMetadata(fileId) {
    if (!this.oauthToken) {
      throw new Error('Authentication required for Google Drive access.');
    }

    try {
      const response = await fetch(`https://www.googleapis.com/drive/v3/files/${fileId}?fields=id,name,mimeType,size,description,webContentLink,webViewLink`, {
        headers: {
          'Authorization': `Bearer ${this.oauthToken}`
        }
      });

      if (!response.ok) {
        throw new Error(`Error getting file metadata: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting file metadata from Google Drive:', error);
      throw error;
    }
  }
}
