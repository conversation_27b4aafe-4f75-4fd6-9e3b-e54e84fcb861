/* Focus Mode Styles */
.focus-mode-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(34, 0, 34, 0.85); /* Black Sabbath with transparency */
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  color: #FAFAFA; /* Dr. White */
  font-family: var(--font-primary);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

.focus-mode-container.active {
  opacity: 1;
  pointer-events: auto;
}

.focus-mode-header {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  align-items: center;
}

.focus-mode-title {
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.focus-mode-title i {
  color: var(--primary-color);
}

.focus-mode-close {
  background-color: rgba(220, 53, 69, 0.8);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(220, 53, 69, 0.3);
  display: flex;
  align-items: center;
  gap: 5px;
}

.focus-mode-close:hover {
  background-color: rgba(220, 53, 69, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.5);
}

.focus-mode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
  max-width: 500px;
  text-align: center;
  padding: 0 20px;
}

.focus-mode-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

.focus-mode-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.focus-mode-time {
  font-size: 3.5rem;
  font-weight: 700;
  color: #F2F1DC; /* Hazy Grove */
  font-family: 'Inter', monospace;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(123, 179, 144, 0.5); /* Sage Veil */
}

.focus-mode-progress {
  width: 200px;
  height: 200px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hourglass {
  width: 120px;
  height: 150px;
  position: relative;
  perspective: 300px;
  transform-style: preserve-3d;
  animation: hourglassSpin 2s ease-in-out infinite alternate;
}

.hourglass-top,
.hourglass-bottom {
  width: 100%;
  height: 50%;
  position: absolute;
  border: 3px solid rgba(123, 179, 144, 0.8); /* Sage Veil */
  border-radius: 50% / 10%;
  box-shadow: 0 0 20px rgba(123, 179, 144, 0.4); /* Sage Veil */
}

.hourglass-top {
  top: 0;
  border-bottom: none;
  transform: rotateX(0deg);
  background: linear-gradient(to bottom,
    rgba(123, 179, 144, 0.2) 0%, /* Sage Veil */
    rgba(123, 179, 144, 0.1) 100%); /* Sage Veil */
  overflow: hidden;
}

.hourglass-bottom {
  bottom: 0;
  border-top: none;
  transform: rotateX(180deg);
  background: linear-gradient(to bottom,
    rgba(123, 179, 144, 0.1) 0%, /* Sage Veil */
    rgba(123, 179, 144, 0.2) 100%); /* Sage Veil */
  overflow: hidden;
}

.hourglass-center {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(123, 179, 144, 0.8); /* Sage Veil */
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(123, 179, 144, 0.6); /* Sage Veil */
}

.sand-top {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(189, 223, 171, 0.6); /* Pastel Pistachio */
  transition: height 0.1s linear;
}

.sand-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(189, 223, 171, 0.6); /* Pastel Pistachio */
  transition: height 0.1s linear;
}

.sand-falling {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 10px;
  background-color: rgba(189, 223, 171, 0.8); /* Pastel Pistachio */
  transform: translate(-50%, -50%);
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(123, 179, 144, 0.6); /* Sage Veil */
  opacity: 0;
}

.sand-falling.active {
  animation: sandFall 1s linear infinite;
  opacity: 1;
}

@keyframes hourglassSpin {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(10deg);
  }
}

@keyframes sandFall {
  0% {
    top: 50%;
    opacity: 1;
    height: 4px;
  }
  100% {
    top: 75%;
    opacity: 0;
    height: 2px;
  }
}

.focus-mode-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 300px;
}

.focus-mode-input {
  display: flex;
  gap: 10px;
  width: 100%;
}

.focus-time-input {
  flex: 1;
  padding: 12px 15px;
  border-radius: 8px;
  border: 2px solid rgba(123, 179, 144, 0.3); /* Sage Veil */
  background-color: rgba(17, 31, 39, 0.8); /* Midnight Carbon */
  color: #F2F1DC; /* Hazy Grove */
  font-size: 1rem;
  font-family: var(--font-primary);
  transition: all 0.3s ease;
  text-align: center;
}

.focus-time-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(123, 179, 144, 0.2); /* Sage Veil */
}

.focus-mode-start {
  width: 100%;
  padding: 12px 20px;
  background: linear-gradient(135deg, #EC2938, #D01828); /* Imperial Red to Darker Imperial Red */
  color: #FAFAFA; /* Dr. White for better contrast */
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 2px 8px rgba(236, 41, 56, 0.3); /* Imperial Red */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-family: var(--font-primary);
  letter-spacing: 0.2px;
  position: relative;
  overflow: hidden;
}

.focus-mode-start::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(242, 241, 220, 0.2), rgba(242, 241, 220, 0)); /* Hazy Grove */
  opacity: 0.6;
  pointer-events: none;
}

.focus-mode-start:hover {
  background: linear-gradient(135deg, #BDDFAB, #7BB390); /* Reversed gradient */
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(123, 179, 144, 0.5); /* Sage Veil */
}

.focus-mode-start:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(123, 179, 144, 0.3); /* Sage Veil */
}

.focus-mode-footer {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
}

.focus-mode-status {
  margin-top: 10px;
  font-size: 1.1rem;
  color: var(--primary-color);
  font-weight: 500;
}

.focus-mode-presets {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.focus-preset-btn {
  padding: 8px 12px;
  background-color: rgba(123, 179, 144, 0.15); /* Sage Veil */
  color: var(--primary-color);
  border: 1px solid rgba(123, 179, 144, 0.3); /* Sage Veil */
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.focus-preset-btn:hover {
  background-color: rgba(123, 179, 144, 0.25); /* Sage Veil */
  transform: translateY(-1px);
}

/* States */
.focus-mode-setup {
  display: flex;
}

.focus-mode-active {
  display: none;
}

.focus-mode-container.running .focus-mode-setup {
  display: none;
}

.focus-mode-container.running .focus-mode-active {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.focus-mode-pause {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.4);
  border-radius: 6px;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.focus-mode-pause:hover {
  background-color: rgba(255, 193, 7, 0.3);
  transform: translateY(-1px);
}

.focus-mode-pause.paused {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border-color: rgba(40, 167, 69, 0.4);
}

.focus-mode-pause.paused:hover {
  background-color: rgba(40, 167, 69, 0.3);
}

.focus-mode-message {
  font-size: 1.2rem;
  font-weight: 500;
  color: white;
  margin-top: 20px;
  text-align: center;
  max-width: 400px;
}
