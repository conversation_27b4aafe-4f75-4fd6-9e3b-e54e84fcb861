/**
 * Stop Button Handler
 * Manages the stop button functionality for interrupting AI responses
 * and implements dynamic send/stop button functionality
 */

// Global variables to track the abort controller and request state
let currentAbortController = null;
let isGeneratingResponse = false;

document.addEventListener('DOMContentLoaded', () => {
  // Get DOM elements
  const stopButtonContainer = document.getElementById('stopButtonContainer');
  const stopResponseBtn = document.getElementById('stopResponseBtn');
  const sendButton = document.querySelector('.send-btn');
  const sendButtonIcon = sendButton ? sendButton.querySelector('i') : null;

  if (!stopButtonContainer || !stopResponseBtn) {
    console.error('Stop button elements not found in the DOM');
    return;
  }

  // Initialize the stop button
  initStopButton();

  /**
   * Initialize the stop button functionality
   */
  function initStopButton() {
    // Add click event listener to the stop button
    stopResponseBtn.addEventListener('click', handleStopButtonClick);

    // Listen for response generation start event
    document.addEventListener('responseGenerationStart', handleResponseStart);

    // Listen for response generation end event
    document.addEventListener('responseGenerationEnd', handleResponseEnd);

    console.log('Stop button initialized');

    // Initialize dynamic send/stop button if it exists
    if (sendButton) {
      console.log('Initializing dynamic send/stop button');
      initDynamicSendStopButton();
    }
  }

  /**
   * Initialize the dynamic send/stop button
   */
  function initDynamicSendStopButton() {
    // Add click event listener to the send button when in stop mode
    sendButton.addEventListener('click', (event) => {
      if (isGeneratingResponse && sendButton.classList.contains('stop-btn')) {
        event.preventDefault();
        handleStopButtonClick(event);
      }
    });
  }

  /**
   * Handle stop button click
   * @param {Event} event - The click event
   */
  function handleStopButtonClick(event) {
    event.preventDefault();

    console.log('Stop button clicked');

    // Abort the current request if there is one
    if (currentAbortController) {
      currentAbortController.abort();
      console.log('Request aborted by user');
    }

    // Hide the stop button
    hideStopButton();

    // Dispatch an event to notify that the response was stopped
    const stoppedEvent = new CustomEvent('responseGenerationStopped', {
      detail: { timestamp: Date.now() }
    });
    document.dispatchEvent(stoppedEvent);

    // Add a message to the chat indicating the response was stopped
    if (window.chatManager) {
      window.chatManager.addMessageToChat(
        'Message sending stopped.',
        'ai',
        'info-message',
        true
      );
    }
  }

  /**
   * Handle response generation start
   * @param {CustomEvent} event - The custom event
   */
  function handleResponseStart(event) {
    console.log('Response generation started');

    // Store the abort controller from the event
    if (event.detail && event.detail.abortController) {
      currentAbortController = event.detail.abortController;
    } else {
      // Create a new abort controller if none was provided
      currentAbortController = new AbortController();
    }

    // Set the generating flag
    isGeneratingResponse = true;

    // Show the stop button
    showStopButton();

    // Update send button to stop button
    updateSendButtonToStopMode();
  }

  /**
   * Handle response generation end
   */
  function handleResponseEnd() {
    console.log('Response generation ended');

    // Clear the abort controller
    currentAbortController = null;

    // Clear the generating flag
    isGeneratingResponse = false;

    // Hide the stop button
    hideStopButton();

    // Update stop button back to send button
    updateSendButtonToSendMode();
  }

  /**
   * Show the stop button
   */
  function showStopButton() {
    stopButtonContainer.classList.add('visible');
  }

  /**
   * Hide the stop button
   */
  function hideStopButton() {
    stopButtonContainer.classList.remove('visible');
  }

  /**
   * Update send button to stop mode
   */
  function updateSendButtonToStopMode() {
    if (sendButton) {
      sendButton.classList.add('stop-btn');
      if (sendButtonIcon) {
        sendButtonIcon.className = 'fas fa-times';
      }
      sendButton.setAttribute('title', 'Stop generating');

      // Add animation
      sendButton.style.animation = 'pulse 1.5s infinite';
    }
  }

  /**
   * Update stop button back to send mode
   */
  function updateSendButtonToSendMode() {
    if (sendButton) {
      sendButton.classList.remove('stop-btn');
      if (sendButtonIcon) {
        sendButtonIcon.className = 'fas fa-paper-plane';
      }
      sendButton.setAttribute('title', 'Send message');

      // Remove animation
      sendButton.style.animation = '';
    }
  }
});

/**
 * Create an abort controller for a new request
 * @returns {AbortController} - A new abort controller
 */
function createRequestAbortController() {
  // Create a new abort controller
  const abortController = new AbortController();

  // Dispatch an event to notify that response generation has started
  const startEvent = new CustomEvent('responseGenerationStart', {
    detail: {
      abortController: abortController,
      timestamp: Date.now()
    }
  });
  document.dispatchEvent(startEvent);

  return abortController;
}

/**
 * Signal that response generation has ended
 */
function signalResponseGenerationEnd() {
  // Dispatch an event to notify that response generation has ended
  const endEvent = new CustomEvent('responseGenerationEnd', {
    detail: { timestamp: Date.now() }
  });
  document.dispatchEvent(endEvent);
}

// Export functions for use in other modules
window.stopButtonHandler = {
  createRequestAbortController,
  signalResponseGenerationEnd,
  isGeneratingResponse: () => isGeneratingResponse
};
