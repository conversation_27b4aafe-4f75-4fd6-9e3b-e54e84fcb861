'use strict';

/**
 * PDF Content Script - Specifically designed for handling PDF documents
 * This script adds functionality to extract selected text from PDF documents
 */

console.log('BlowAI PDF content script loaded');

// Store the last selected text
let lastSelectedText = '';

// Function to get selected text from a PDF
function getSelectedTextFromPDF() {
  try {
    // Try multiple methods to get selected text

    // Method 1: Standard selection API
    const selection = window.getSelection();
    let selectedText = selection.toString().trim();

    // Method 2: Try document.getSelection() as a fallback
    if (!selectedText && document.getSelection) {
      const docSelection = document.getSelection();
      selectedText = docSelection.toString().trim();
    }

    // Method 3: Try to get selection from active element (for embedded PDFs)
    if (!selectedText && document.activeElement) {
      if (document.activeElement.tagName === 'IFRAME' ||
          document.activeElement.tagName === 'EMBED' ||
          document.activeElement.tagName === 'OBJECT') {
        try {
          const activeElement = document.activeElement;
          const contentWindow = activeElement.contentWindow;
          if (contentWindow && contentWindow.getSelection) {
            selectedText = contentWindow.getSelection().toString().trim();
          }
        } catch (iframeError) {
          console.log('Could not access iframe content:', iframeError);
        }
      }
    }

    // Method 4: Check for PDF.js specific selection
    if (!selectedText && window.PDFViewerApplication) {
      try {
        const pdfViewer = window.PDFViewerApplication.pdfViewer;
        if (pdfViewer && pdfViewer.getSelectedText) {
          selectedText = pdfViewer.getSelectedText().trim();
        }
      } catch (pdfJsError) {
        console.log('Could not access PDF.js selection:', pdfJsError);
      }
    }

    // Store the selected text for later use
    if (selectedText) {
      console.log('PDF text selected:', selectedText.substring(0, 50) + '...');
      lastSelectedText = selectedText;
    }

    return selectedText;
  } catch (error) {
    console.error('Error getting selected text from PDF:', error);
    return '';
  }
}

// Function to check if the current page is a PDF
function isPDF() {
  try {
    // Method 1: Check content type
    if (document.contentType === 'application/pdf') {
      console.log('PDF detected via content type');
      return true;
    }

    // Method 2: Check URL
    const url = window.location.href.toLowerCase();
    if (url.endsWith('.pdf') || url.includes('/pdf/') || url.includes('pdf.js')) {
      console.log('PDF detected via URL pattern');
      return true;
    }

    // Method 3: Check for PDF.js viewer
    if (window.PDFViewerApplication) {
      console.log('PDF detected via PDF.js viewer');
      return true;
    }

    // Method 4: Check for PDF embeds
    if (document.querySelector('embed[type="application/pdf"]') !== null ||
        document.querySelector('object[type="application/pdf"]') !== null ||
        document.querySelector('iframe[src*=".pdf"]') !== null) {
      console.log('PDF detected via embedded elements');
      return true;
    }

    // Method 5: Check for common PDF viewer elements
    if (document.querySelector('#viewer.pdfViewer') !== null ||
        document.querySelector('.pdfViewer') !== null ||
        document.querySelector('#viewerContainer') !== null) {
      console.log('PDF detected via viewer elements');
      return true;
    }

    // Method 6: Check for Chrome PDF Viewer
    if (document.body && document.body.dataset && document.body.dataset.pdfjs) {
      console.log('PDF detected via Chrome PDF viewer');
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking if page is PDF:', error);
    return false;
  }
}

// Function to extract PDF metadata if available
function extractPDFMetadata() {
  try {
    // Try to get metadata from PDF.js if it's being used
    if (window.PDFViewerApplication) {
      const pdfViewer = window.PDFViewerApplication;
      if (pdfViewer.pdfDocument) {
        return {
          title: pdfViewer.pdfDocument.documentInfo?.Title || document.title,
          author: pdfViewer.pdfDocument.documentInfo?.Author || 'Unknown',
          numPages: pdfViewer.pagesCount || 0,
          currentPage: pdfViewer.page || 1
        };
      }
    }

    // Fallback to basic metadata
    return {
      title: document.title || window.location.pathname.split('/').pop(),
      url: window.location.href,
      isPDF: true
    };
  } catch (error) {
    console.error('Error extracting PDF metadata:', error);
    return {
      title: document.title || window.location.pathname.split('/').pop(),
      url: window.location.href,
      isPDF: true,
      error: error.message
    };
  }
}

// Function to extract full PDF content
function getFullPDFContent() {
  try {
    // Try multiple methods to get all text
    if (window.PDFViewerApplication) {
      try {
        const pdfViewer = window.PDFViewerApplication.pdfViewer;
        const pageCount = pdfViewer.pagesCount || 0;
        let allText = [];
        let structuredContent = [];

        // Extract text from each page
        for (let i = 1; i <= pageCount; i++) {
          try {
            const pageIndex = i - 1;
            const page = pdfViewer.getPageView(pageIndex);
            if (page && page.textLayer && page.textLayer.textContent) {
              allText.push(page.textLayer.textContent);

              // Try to extract structured content
              structuredContent.push({
                pageNumber: i,
                content: page.textLayer.textContent,
                // Try to identify headings and sections based on font size and position
                elements: page.textLayer.textDivs ?
                  Array.from(page.textLayer.textDivs).map(div => ({
                    text: div.textContent,
                    fontSize: parseFloat(window.getComputedStyle(div).fontSize),
                    isBold: window.getComputedStyle(div).fontWeight >= 600,
                    position: {
                      top: div.style.top,
                      left: div.style.left
                    }
                  })) : []
              });
            }
          } catch (pageError) {
            console.log(`Error extracting text from page ${i}:`, pageError);
          }
        }

        if (allText.length > 0) {
          return {
            fullText: allText.join('\n\n'),
            structuredContent: structuredContent,
            metadata: extractPDFMetadata()
          };
        }
      } catch (pdfJsError) {
        console.log('Could not extract text using PDF.js:', pdfJsError);
      }
    }

    // Fallback to basic extraction
    return {
      fullText: document.body.innerText || '',
      metadata: extractPDFMetadata()
    };
  } catch (error) {
    console.error('Error extracting full PDF content:', error);
    return {
      fullText: '',
      error: error.message,
      metadata: extractPDFMetadata()
    };
  }
}

// Function to add annotation to PDF
function addAnnotationToPDF(pageNumber, text, annotationText, type = 'highlight') {
  try {
    if (!pageNumber || !text || !annotationText) {
      return { success: false, error: 'Missing required parameters' };
    }

    // Get the current annotations from storage
    const pdfUrl = window.location.href;
    const storageKey = `pdf_annotations_${pdfUrl}`;

    // Create a new annotation
    const newAnnotation = {
      text: text,
      annotation: annotationText,
      type: type,
      timestamp: new Date().toISOString()
    };

    // Try to highlight the text in the PDF
    if (window.PDFViewerApplication && type === 'highlight') {
      try {
        const pdfViewer = window.PDFViewerApplication.pdfViewer;
        const pageView = pdfViewer.getPageView(pageNumber - 1);

        if (pageView && pageView.textLayer) {
          // Find the text in the page
          const textDivs = pageView.textLayer.textDivs;
          for (let i = 0; i < textDivs.length; i++) {
            const div = textDivs[i];
            if (div.textContent.includes(text)) {
              // Create a highlight element
              const highlight = document.createElement('div');
              highlight.className = 'soulai-pdf-highlight';
              highlight.style.position = 'absolute';
              highlight.style.left = div.style.left;
              highlight.style.top = div.style.top;
              highlight.style.height = div.style.height;
              highlight.style.width = div.style.width;
              highlight.style.backgroundColor = 'rgba(255, 255, 0, 0.3)';
              highlight.style.pointerEvents = 'none';
              highlight.style.zIndex = '1';
              highlight.dataset.annotation = annotationText;

              // Add the highlight to the page
              pageView.div.appendChild(highlight);

              // Add tooltip functionality
              highlight.title = annotationText;
            }
          }
        }
      } catch (highlightError) {
        console.log('Could not highlight text:', highlightError);
      }
    }

    return {
      success: true,
      annotation: newAnnotation,
      message: 'Annotation added successfully'
    };
  } catch (error) {
    console.error('Error adding annotation:', error);
    return { success: false, error: error.message };
  }
}

// Function to extract table of contents
function extractTableOfContents() {
  try {
    if (window.PDFViewerApplication) {
      const pdfViewer = window.PDFViewerApplication;
      const outline = pdfViewer.pdfOutlineViewer?.outline || [];

      if (outline && outline.length > 0) {
        // Convert the outline to a structured TOC
        const toc = [];

        function processOutlineItems(items, level = 0) {
          const result = [];
          for (const item of items) {
            result.push({
              title: item.title,
              pageNumber: item.dest ? item.dest[0].num + 1 : null,
              level: level
            });

            if (item.items && item.items.length > 0) {
              result.push(...processOutlineItems(item.items, level + 1));
            }
          }
          return result;
        }

        return {
          success: true,
          toc: processOutlineItems(outline)
        };
      }
    }

    // If we couldn't extract a TOC, return an empty one
    return {
      success: false,
      error: 'Could not extract table of contents',
      toc: []
    };
  } catch (error) {
    console.error('Error extracting table of contents:', error);
    return {
      success: false,
      error: error.message,
      toc: []
    };
  }
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    if (!message || !message.action) {
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }

    // Simple ping to check if content script is loaded
    if (message.action === 'ping') {
      sendResponse({
        success: true,
        status: 'pdf_content_script_active',
        isPDF: isPDF()
      });
      return true;
    }

    // Get selected text from PDF
    if (message.action === 'getSelectedTextFromPDF') {
      const selectedText = getSelectedTextFromPDF();
      sendResponse({
        success: true,
        text: selectedText,
        lastSelected: lastSelectedText,
        metadata: extractPDFMetadata()
      });
      return true;
    }

    // Extract PDF metadata
    if (message.action === 'getPDFMetadata') {
      sendResponse({
        success: true,
        metadata: extractPDFMetadata()
      });
      return true;
    }

    // Get full PDF content
    if (message.action === 'getFullPDFContent') {
      const content = getFullPDFContent();
      sendResponse({
        success: true,
        content: content.fullText,
        structuredContent: content.structuredContent,
        metadata: content.metadata
      });
      return true;
    }

    // Add annotation to PDF
    if (message.action === 'addAnnotation') {
      const result = addAnnotationToPDF(
        message.pageNumber,
        message.text,
        message.annotation,
        message.type || 'highlight'
      );
      sendResponse(result);
      return true;
    }

    // Extract table of contents
    if (message.action === 'extractTableOfContents') {
      const result = extractTableOfContents();
      sendResponse(result);
      return true;
    }

    // Unknown action
    sendResponse({ success: false, error: 'Unknown action' });
    return true;
  } catch (error) {
    console.error('Error in PDF content script:', error);
    sendResponse({ success: false, error: 'PDF content script error: ' + error.message });
    return true;
  }
});

// Add multiple event listeners for text selection
['mouseup', 'keyup', 'selectionchange'].forEach(eventType => {
  document.addEventListener(eventType, () => {
  try {
    // Try multiple methods to get the selected text
    let selectedText = '';

    // Method 1: Standard selection API
    const selection = window.getSelection();
    selectedText = selection.toString().trim();

    // Method 2: Try document.getSelection() as a fallback
    if (!selectedText && document.getSelection) {
      const docSelection = document.getSelection();
      selectedText = docSelection.toString().trim();
    }

    // Method 3: Try to get selection from active element (for embedded PDFs)
    if (!selectedText && document.activeElement) {
      if (document.activeElement.tagName === 'IFRAME' ||
          document.activeElement.tagName === 'EMBED' ||
          document.activeElement.tagName === 'OBJECT') {
        try {
          const activeElement = document.activeElement;
          const contentWindow = activeElement.contentWindow;
          if (contentWindow && contentWindow.getSelection) {
            selectedText = contentWindow.getSelection().toString().trim();
          }
        } catch (iframeError) {
          console.log('Could not access iframe content:', iframeError);
        }
      }
    }

    // Method 4: Check for PDF.js specific selection
    if (!selectedText && window.PDFViewerApplication) {
      try {
        const pdfViewer = window.PDFViewerApplication.pdfViewer;
        if (pdfViewer && pdfViewer.getSelectedText) {
          selectedText = pdfViewer.getSelectedText().trim();
        }
      } catch (pdfJsError) {
        console.log('Could not access PDF.js selection:', pdfJsError);
      }
    }

    if (selectedText) {
      console.log('PDF text selected:', selectedText.substring(0, 50) + '...');
      lastSelectedText = selectedText;

      // Show the floating button immediately
      if (floatingButton) {
        floatingButton.classList.remove('hidden');
      }

      // Automatically send the selected text to the extension
      chrome.runtime.sendMessage({
        action: 'sendPDFTextToChat',
        text: selectedText,
        metadata: extractPDFMetadata()
      }).then(response => {
        console.log('PDF text sent automatically:', response);

        // Show a notification to the user
        if (floatingButton) {
          floatingButton.classList.add('success');
          floatingButton.innerHTML = `
            <div class="blowai-pdf-button-inner">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 6L9 17l-5-5"></path>
              </svg>
              <span>Text captured! Open BlowAI to chat</span>
            </div>
          `;

          setTimeout(() => {
            floatingButton.classList.remove('success');
            floatingButton.innerHTML = `
              <div class="blowai-pdf-button-inner">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                <span>Send to BlowAI</span>
              </div>
            `;
          }, 3000);
        }
      }).catch(error => {
        // Ignore errors when the extension popup is not open
        console.log('Could not send selection to extension (popup might be closed)');
      });
    }
  } catch (error) {
    console.error('Error in PDF text selection handler:', error);
  }
  });
});

// Function to check for selected text periodically
function checkForSelectedText() {
  try {
    // Try multiple methods to get the selected text
    let selectedText = getSelectedTextFromPDF();

    if (selectedText && selectedText !== lastSelectedText) {
      console.log('PDF text selected via polling:', selectedText.substring(0, 50) + '...');
      lastSelectedText = selectedText;

      // Show the floating button immediately
      if (floatingButton) {
        floatingButton.classList.remove('hidden');
      }

      // Automatically send the selected text to the extension
      chrome.runtime.sendMessage({
        action: 'sendPDFTextToChat',
        text: selectedText,
        metadata: extractPDFMetadata()
      }).catch(error => {
        // Ignore errors when the extension popup is not open
        console.log('Could not send selection to extension (popup might be closed)');
      });
    }
  } catch (error) {
    console.error('Error in polling for PDF text selection:', error);
  }
}

// Initialize by checking if this is a PDF
if (isPDF()) {
  console.log('PDF detected, enabling PDF-specific features');

  // Start polling for selected text every 2 seconds
  setInterval(checkForSelectedText, 2000);

  // Add a more prominent floating button to send selected text to BlowAI
  const floatingButton = document.createElement('div');
  floatingButton.id = 'blowai-pdf-button';
  floatingButton.innerHTML = `
    <div class="blowai-pdf-button-inner">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
      <span>Selected PDF Text Detected! Click to Chat</span>
    </div>
  `;

  // Add styles for the floating button
  const style = document.createElement('style');
  style.textContent = `
    #blowai-pdf-button {
      position: fixed;
      bottom: 30px;
      right: 30px;
      background: linear-gradient(135deg, #e63946, #9d0208);
      color: white;
      border-radius: 50px;
      padding: 12px 20px;
      font-family: Arial, sans-serif;
      font-size: 16px;
      font-weight: bold;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
      cursor: pointer;
      z-index: 999999;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;
      opacity: 0.95;
      border: 2px solid rgba(255, 255, 255, 0.7);
      animation: pulse 2s infinite;
      transform: scale(1.1); /* Make button slightly larger */
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(230, 57, 70, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(230, 57, 70, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(230, 57, 70, 0);
      }
    }

    #blowai-pdf-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
      opacity: 1;
      background: linear-gradient(135deg, #d00000, #6a040f);
    }

    .blowai-pdf-button-inner {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    #blowai-pdf-button.hidden {
      transform: translateY(100px);
      opacity: 0;
    }

    #blowai-pdf-button.success {
      background: linear-gradient(135deg, #06d6a0, #1b9aaa);
    }
  `;

  document.head.appendChild(style);
  document.body.appendChild(floatingButton);

  // Add click event to the floating button
  floatingButton.addEventListener('click', () => {
    try {
      // Try multiple methods to get the text
      let textToSend = '';

      // Method 1: Get the current selection
      const currentSelection = window.getSelection().toString().trim();
      if (currentSelection) {
        textToSend = currentSelection;
        console.log('Using current selection:', textToSend.substring(0, 50) + '...');
      }

      // Method 2: Use the last selected text
      else if (lastSelectedText) {
        textToSend = lastSelectedText;
        console.log('Using last selected text:', textToSend.substring(0, 50) + '...');
      }

      // Method 3: Try to get text from PDF.js
      else if (window.PDFViewerApplication) {
        try {
          const pdfViewer = window.PDFViewerApplication.pdfViewer;
          if (pdfViewer && pdfViewer.getSelectedText) {
            textToSend = pdfViewer.getSelectedText().trim();
            console.log('Using PDF.js selected text:', textToSend.substring(0, 50) + '...');
          }
        } catch (pdfJsError) {
          console.log('Could not get PDF.js text:', pdfJsError);
        }
      }

      // Method 4: Try to get all visible text as a last resort
      if (!textToSend) {
        try {
          // Get all text nodes that are visible
          const textNodes = [];
          const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            { acceptNode: node => node.nodeValue.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT },
            false
          );

          let node;
          while (node = walker.nextNode()) {
            textNodes.push(node.nodeValue.trim());
          }

          if (textNodes.length > 0) {
            textToSend = textNodes.join(' ').trim();
            console.log('Using all visible text as fallback:', textToSend.substring(0, 50) + '...');
          }
        } catch (visibleTextError) {
          console.log('Could not get visible text:', visibleTextError);
        }
      }

      if (textToSend) {
        // Show visual feedback immediately
        floatingButton.textContent = 'Sending to BlowAI...';
        floatingButton.style.opacity = '1';

        // Send the selected text to the extension
        chrome.runtime.sendMessage({
          action: 'sendPDFTextToChat',
          text: textToSend,
          metadata: extractPDFMetadata()
        }).then(response => {
          if (response && response.success) {
            // Show a success indicator
            floatingButton.classList.add('success');
            floatingButton.innerHTML = `
              <div class="blowai-pdf-button-inner">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6L9 17l-5-5"></path>
                </svg>
                <span>Sent to BlowAI!</span>
              </div>
            `;

            setTimeout(() => {
              floatingButton.classList.remove('success');
              floatingButton.innerHTML = `
                <div class="blowai-pdf-button-inner">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                  </svg>
                  <span>Selected PDF Text Detected! Click to Chat</span>
                </div>
              `;
            }, 3000);
          }
        }).catch(error => {
          console.error('Error sending PDF text to chat:', error);
          floatingButton.innerHTML = `
            <div class="blowai-pdf-button-inner">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
              <span>Selected PDF Text Detected! Click to Chat</span>
            </div>
          `;
        });
      } else {
        // Show a message about the Extract All feature
        alert('No text selection detected. For best results:\n\n1. Select text by clicking and dragging\n2. Or use the "Extract All PDF Text" option in the BlowAI menu to capture the entire document.');
      }
    } catch (error) {
      console.error('Error in PDF button click handler:', error);
      alert('Error processing PDF text. Please try selecting text again.');
    }
  });

  // Show/hide the button based on text selection
  document.addEventListener('selectionchange', () => {
    const hasSelection = window.getSelection().toString().trim().length > 0;

    if (hasSelection) {
      floatingButton.classList.remove('hidden');
    } else if (!lastSelectedText) {
      floatingButton.classList.add('hidden');
    }
  });
}
