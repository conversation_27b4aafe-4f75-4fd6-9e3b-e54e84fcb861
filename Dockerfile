# Multi-stage Dockerfile for BrowzyAI Extension
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    git \
    curl \
    bash

# Copy package files
COPY package*.json ./
COPY pyproject.toml ./

# Install Node.js dependencies
RUN npm install

# Install Python dependencies
RUN pip3 install --no-cache-dir -r <(echo "trafilatura>=2.0.0")

# Development stage
FROM base AS development

# Install development tools
RUN npm install -g nodemon concurrently

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p /app/api /app/dashboard /app/logs

# Set environment variables
ENV NODE_ENV=development
ENV API_PORT=5000
ENV DASHBOARD_PORT=5173

# Expose ports
EXPOSE 5000 5173 3000

# Start development servers
CMD ["npm", "run", "docker:dev"]

# Production stage
FROM base AS production

# Copy source code
COPY . .

# Set environment variables
ENV NODE_ENV=production
ENV API_PORT=5000
ENV DASHBOARD_PORT=5173

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S browzyai -u 1001

# Change ownership of the app directory
RUN chown -R browzyai:nodejs /app
USER browzyai

# Expose ports
EXPOSE 5000 5173

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Start production servers
CMD ["npm", "start"]
